name: Meilisearch

on:
  push:
    branches: [ main ]
    paths:
      - 'apps/web/**'
  pull_request:
    branches: [ main ]
    paths:
      - 'apps/web/**'

jobs:
  index:
    runs-on: ubuntu-latest
    services:
      meilisearch:
        image: getmeili/meilisearch:latest
        env:
          MEILI_MASTER_KEY: ${{ secrets.MEILI_MASTER_KEY }}
        ports:
          - 7700:7700

    steps:
      - uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.2'
          extensions: dom, curl, libxml, mbstring, zip, pcntl, pdo, sqlite, pdo_sqlite, bcmath, intl

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Enable Corepack
        run: corepack enable

      - name: Get yarn cache directory path
        id: yarn-cache-dir-path
        run: echo "dir=$(yarn config get cacheFolder)" >> $GITHUB_OUTPUT

      - name: Cache Yarn dependencies
        uses: actions/cache@v3
        with:
          path: ${{ steps.yarn-cache-dir-path.outputs.dir }}
          key: yarn-cache-${{ hashFiles('**/yarn.lock') }}
          restore-keys: |
            yarn-cache-

      - name: Install Dependencies
        run: |
          cd apps/web
          composer install -q --no-ansi --no-interaction --no-scripts --no-progress --prefer-dist
          cd ../..
          yarn install

      - name: Index Data
        working-directory: ./apps/web
        env:
          MEILISEARCH_HOST: http://localhost:7700
          MEILISEARCH_KEY: ${{ secrets.MEILI_MASTER_KEY }}
        run: php artisan scout:import "App\Models\*"
