# Dependencies
node_modules
vendor
.pnpm-store
.npm
.yarn
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions
.pnp.*

# Build outputs
dist
build
/public/build
/public/hot
/public/storage

# Environment files
.env
.env.*
!.env.example
.env.backup
.env.production

# Laravel specific
/storage/*.key
/storage/pail
/.phpunit.cache
.phpunit.result.cache
.phpactor.json
Homestead.json
Homestead.yaml
auth.json

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Repository Folders
/web3/

# Turbo
.turbo

# IDE and editor files
.idea
.vscode
.fleet
.nova
.zed
*.sublime-project
*.sublime-workspace
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Testing
coverage
.nyc_output

# Workspace specific
apps/**/public/build/
apps/**/public/hot/
apps/**/public/storage/
apps/**/storage/*.key
apps/**/vendor/
packages/**/vendor/
packages/**/node_modules/

apps/editor

# Composer
composer.phar
/.composer

# Cache
*.cache
.eslintcache
.stylelintcache
