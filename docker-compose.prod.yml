services:
  web:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        APP_DIR: apps/web
        APP_ENV: production
    working_dir: /var/www/html/apps/web
    ports:
      # Host port can be overridden with APP_PORT (default 8080)
      - "${APP_PORT:-8080}:80"
    environment:
      - APP_ENV=production
      - DB_HOST=mysql
      - DB_DATABASE=${DB_DATABASE}
      - DB_USERNAME=${DB_USERNAME}
      - DB_PASSWORD=${DB_PASSWORD}
      - REDIS_HOST=redis
      - MEILISEARCH_HOST=http://meilisearch:7700
    depends_on:
      - mysql
      - redis
      - meilisearch

  mysql:
    image: mysql:8.0
    environment:
      MYSQL_DATABASE: ${DB_DATABASE}
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD}
    volumes:
      - mysql_data:/var/lib/mysql

  postgres:
    image: postgres:17
    environment:
      POSTGRES_DB: ${DB_DATABASE}
      POSTGRES_USER: ${DB_USERNAME}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - pg_data:/var/lib/postgresql/data

  redis:
    image: redis:alpine
    environment: {}
    volumes:
      - redis_data:/data

  meilisearch:
    image: getmeili/meilisearch:v1.14
    environment:
      MEILI_MASTER_KEY: ${MEILISEARCH_KEY}
    volumes:
      - meilisearch_data:/meili_data

volumes:
  mysql_data:
  redis_data:
  meilisearch_data:
  pg_data:
