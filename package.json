{"name": "esbo", "private": true, "scripts": {"build": "turbo build", "dev": "turbo dev", "lint": "turbo lint", "test": "turbo run test", "test:web": "turbo run test:web", "web": "yarn workspace @esbo/web", "web:dev": "turbo run dev --filter=@esbo/web...", "web:build": "turbo run build --filter=@esbo/web...", "web:test": "turbo run test --filter=@esbo/web...", "web:lint": "turbo run lint --filter=@esbo/web...", "mobile": "yarn workspace @esbo/mobile", "mobile:dev": "turbo run dev --filter=@esbo/mobile...", "mobile:build": "turbo run build --filter=@esbo/mobile...", "mobile:test": "turbo run test --filter=@esbo/mobile...", "mobile:lint": "turbo run lint --filter=@esbo/mobile...", "format": "prettier --write \"**/*.{ts,tsx,md,php}\"", "prepare": "husky install", "clean": "turbo clean", "lib:build": "turbo run build --filter=\"./libs/*\"", "lib:dev": "turbo run dev --filter=\"./libs/*\"", "lib:clean": "turbo run clean --filter=\"./libs/*\"", "lib:typecheck": "turbo run typecheck --filter=\"./libs/*\"", "docs:types": "node scripts/generate-types-docs.js", "docs:components": "node scripts/generate-component-docs-wrapper.js", "docs:web": "node scripts/generate-web-docs.js", "docs:api": "yarn workspace @esbo/web docs:api", "docs:build": "node scripts/build-docs.js", "docs:dev": "cd docs && yarn start", "docs:full": "yarn docs:components && yarn docs:types && yarn docs:web && yarn docs:api && yarn docs:build", "docs:clean": "rimraf docs/api docs/components docs/libs docs/web docs/.docusaurus"}, "devDependencies": {"@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@eslint/eslintrc": "^3.3.0", "@types/node": "^22.13.15", "@vuedoc/md": "^4.0.0-beta8", "@vuedoc/parser": "^4.0.0-beta14", "husky": "^9.1.7", "prettier": "^3.5.3", "pug": "^3.0.3", "rimraf": "^5.0.10", "turbo": "^2.5.4"}, "packageManager": "yarn@4.9.2", "workspaces": ["apps/*", "libs/*", "docs"]}