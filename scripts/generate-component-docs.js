const { parseComponent } = require('@vuedoc/parser');
const { generateMarkdown } = require('@vuedoc/md');
const fs = require('fs');
const path = require('path');

// Generate documentation for web app components
const webComponentsDir = path.resolve(__dirname, '../apps/web/resources/js/Components');
const webOutputDir = path.resolve(__dirname, '../docs/components/web');

// Create output directory if it doesn't exist
if (!fs.existsSync(webOutputDir)) {
    fs.mkdirSync(webOutputDir, { recursive: true });
}

// Get all Vue files from web app
if (fs.existsSync(webComponentsDir)) {
    const webVueFiles = fs
        .readdirSync(webComponentsDir)
        .filter((file) => file.endsWith('.vue'));

    // Process each Vue file from web app
    webVueFiles.forEach(async (file) => {
        const filePath = path.join(webComponentsDir, file);
        const componentName = path.basename(file, '.vue');
        const outputPath = path.join(webOutputDir, `${componentName}.md`);

        try {
            const component = await parseComponent({
                filename: filePath,
                encoding: 'utf8',
            });

            const markdown = await generateMarkdown(component, {
                filename: outputPath,
            });

            fs.writeFileSync(outputPath, markdown);
            console.log(`Generated documentation for web component ${componentName}`);
        } catch (err) {
            console.error(`Error processing web component ${componentName}:`, err);
        }
    });
}

// Function to process components recursively
const processComponentsRecursively = async (directory, outputBaseDir, prefix = '') => {
    if (!fs.existsSync(directory)) {
        return;
    }
    
    const items = fs.readdirSync(directory);
    
    for (const item of items) {
        const fullPath = path.join(directory, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
            // Create corresponding output directory
            const outputSubDir = path.join(outputBaseDir, item);
            if (!fs.existsSync(outputSubDir)) {
                fs.mkdirSync(outputSubDir, { recursive: true });
            }
            
            // Process components in subdirectory
            await processComponentsRecursively(fullPath, outputSubDir, `${prefix}${item}/`);
        } else if (item.endsWith('.vue')) {
            const componentName = path.basename(item, '.vue');
            const outputPath = path.join(outputBaseDir, `${componentName}.md`);
            
            try {
                const component = await parseComponent({
                    filename: fullPath,
                    encoding: 'utf8',
                });

                const markdown = await generateMarkdown(component, {
                    filename: outputPath,
                });

                fs.writeFileSync(outputPath, markdown);
                console.log(`Generated documentation for ${prefix}${componentName}`);
            } catch (err) {
                console.error(`Error processing ${prefix}${componentName}:`, err);
            }
        }
    }
};

// Process components in libs directory
const libsComponentsDir = path.resolve(__dirname, '../libs');
const libsOutputDir = path.resolve(__dirname, '../docs/components/libs');

// Create output directory if it doesn't exist
if (!fs.existsSync(libsOutputDir)) {
    fs.mkdirSync(libsOutputDir, { recursive: true });
}

// Process libs components recursively
processComponentsRecursively(libsComponentsDir, libsOutputDir);

console.log('Component documentation generation complete!');
