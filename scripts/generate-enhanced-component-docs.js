/**
 * Enhanced component documentation generator
 * 
 * This script generates more detailed documentation for Vue components and
 * organizes them properly within the web app documentation structure.
 */
const fs = require('fs');
const path = require('path');

// Define directories
const webComponentsDir = path.resolve(__dirname, '../apps/web/resources/js/Components');
const webOutputDir = path.resolve(__dirname, '../docs/web/components');
const webIndexFile = path.resolve(webOutputDir, 'index.md');

// Create output directories if they don't exist
if (!fs.existsSync(webOutputDir)) {
    fs.mkdirSync(webOutputDir, { recursive: true });
}

/**
 * Extract JSDoc comments from a file
 * @param {string} content - File content
 * @returns {Object} - Extracted JSDoc information
 */
function extractJSDocInfo(content) {
    const result = {
        description: '',
        props: [],
        methods: [],
        emits: [],
        author: '',
        since: '',
        example: ''
    };

    // Extract component description
    const componentJSDocMatch = content.match(/\/\*\*\s*([\s\S]*?)\s*\*\/\s*<template>/);
    if (componentJSDocMatch && componentJSDocMatch[1]) {
        const jsDoc = componentJSDocMatch[1];
        
        // Extract description
        const descriptionMatch = jsDoc.match(/@description\s+([\s\S]*?)(\n\s*\*\s*@|\n\s*\*\/)/);
        if (descriptionMatch) {
            result.description = descriptionMatch[1].replace(/\n\s*\*\s*/g, ' ').trim();
        } else {
            // If no @description tag, use the first paragraph
            const descLines = jsDoc.split('\n').map(line => line.replace(/^\s*\*\s*/, '').trim()).filter(line => line && !line.startsWith('@'));
            result.description = descLines.join(' ');
        }
        
        // Extract author
        const authorMatch = jsDoc.match(/@author\s+(.*?)(\n|$)/);
        if (authorMatch) {
            result.author = authorMatch[1].trim();
        }
        
        // Extract since
        const sinceMatch = jsDoc.match(/@since\s+(.*?)(\n|$)/);
        if (sinceMatch) {
            result.since = sinceMatch[1].trim();
        }
        
        // Extract example
        const exampleMatch = jsDoc.match(/@example\s+([\s\S]*?)(\n\s*\*\s*@|\n\s*\*\/)/);
        if (exampleMatch) {
            result.example = exampleMatch[1].replace(/\n\s*\*\s*/g, '\n').trim();
        }
    }
    
    // Extract prop JSDoc comments
    const propJSDocRegex = /\/\*\*\s*([\s\S]*?)\s*\*\/\s*(\w+)\s*:/g;
    let propMatch;
    while ((propMatch = propJSDocRegex.exec(content)) !== null) {
        const propJSDoc = propMatch[1];
        const propName = propMatch[2];
        
        // Extract prop description
        let propDescription = '';
        const propDescriptionMatch = propJSDoc.match(/@description\s+([\s\S]*?)(\n\s*\*\s*@|\n\s*\*\/)/);
        if (propDescriptionMatch) {
            propDescription = propDescriptionMatch[1].replace(/\n\s*\*\s*/g, ' ').trim();
        } else {
            // If no @description tag, use the first paragraph
            const descLines = propJSDoc.split('\n').map(line => line.replace(/^\s*\*\s*/, '').trim()).filter(line => line && !line.startsWith('@'));
            propDescription = descLines.join(' ');
        }
        
        // Extract prop type
        let propType = 'Any';
        const typeMatch = content.match(new RegExp(propName + '\\s*:\\s*\\{[^}]*type\\s*:\\s*([^,}]*)', 's'));
        if (typeMatch) {
            propType = typeMatch[1].trim();
        }
        
        // Extract default value
        let defaultValue = '-';
        const defaultMatch = content.match(new RegExp(propName + '\\s*:\\s*\\{[^}]*default\\s*:\\s*([^,}]*)', 's'));
        if (defaultMatch) {
            defaultValue = defaultMatch[1].trim();
        }
        
        // Extract required
        let required = 'No';
        const requiredMatch = content.match(new RegExp(propName + '\\s*:\\s*\\{[^}]*required\\s*:\\s*([^,}]*)', 's'));
        if (requiredMatch) {
            required = requiredMatch[1].trim() === 'true' ? 'Yes' : 'No';
        }
        
        result.props.push({
            name: propName,
            description: propDescription,
            type: propType,
            default: defaultValue,
            required: required
        });
    }
    
    // Extract method JSDoc comments
    const methodJSDocRegex = /\/\*\*\s*([\s\S]*?)\s*\*\/\s*(\w+)\s*\([^)]*\)\s*\{/g;
    let methodMatch;
    while ((methodMatch = methodJSDocRegex.exec(content)) !== null) {
        const methodJSDoc = methodMatch[1];
        const methodName = methodMatch[2];
        
        // Extract method description
        let methodDescription = '';
        const methodDescriptionMatch = methodJSDoc.match(/@description\s+([\s\S]*?)(\n\s*\*\s*@|\n\s*\*\/)/);
        if (methodDescriptionMatch) {
            methodDescription = methodDescriptionMatch[1].replace(/\n\s*\*\s*/g, ' ').trim();
        } else {
            // If no @description tag, use the first paragraph
            const descLines = methodJSDoc.split('\n').map(line => line.replace(/^\s*\*\s*/, '').trim()).filter(line => line && !line.startsWith('@'));
            methodDescription = descLines.join(' ');
        }
        
        // Extract parameters
        const params = [];
        const paramRegex = /@param\s+\{([^}]*)\}\s+(\w+)\s+(.*?)(\n|$)/g;
        let paramMatch;
        while ((paramMatch = paramRegex.exec(methodJSDoc)) !== null) {
            params.push({
                type: paramMatch[1].trim(),
                name: paramMatch[2].trim(),
                description: paramMatch[3].trim()
            });
        }
        
        // Extract return value
        let returnValue = '';
        const returnMatch = methodJSDoc.match(/@returns?\s+\{([^}]*)\}\s+(.*?)(\n|$)/);
        if (returnMatch) {
            returnValue = `${returnMatch[1].trim()}: ${returnMatch[2].trim()}`;
        }
        
        result.methods.push({
            name: methodName,
            description: methodDescription,
            params: params,
            returns: returnValue
        });
    }
    
    // Extract emits
    const emitsMatch = content.match(/emits\s*:\s*\[(.*?)\]/);
    if (emitsMatch && emitsMatch[1]) {
        result.emits = emitsMatch[1].split(',').map(emit => emit.trim().replace(/['"]/g, ''));
    }
    
    return result;
}

/**
 * Extract component information from a Vue file
 * @param {string} filePath - Path to the Vue file
 * @param {string} componentName - Name of the component
 * @returns {Object} - Component information
 */
function extractComponentInfo(filePath, componentName) {
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        const jsDocInfo = extractJSDocInfo(content);
        
        // Extract template structure
        let template = 'No template available';
        const templateMatch = content.match(/<template>([\s\S]*?)<\/template>/);
        if (templateMatch) {
            template = templateMatch[1].trim();
        }
        
        // Extract script structure
        let script = 'No script available';
        const scriptMatch = content.match(/<script.*?>([\s\S]*?)<\/script>/);
        if (scriptMatch) {
            script = scriptMatch[1].trim();
        }
        
        return {
            name: componentName,
            description: jsDocInfo.description || 'No description available.',
            props: jsDocInfo.props,
            methods: jsDocInfo.methods,
            emits: jsDocInfo.emits,
            author: jsDocInfo.author,
            since: jsDocInfo.since,
            example: jsDocInfo.example,
            template: template,
            script: script
        };
    } catch (error) {
        console.error(`Error extracting information from ${filePath}:`, error);
        return {
            name: componentName,
            description: 'Error extracting component information.',
            props: [],
            methods: [],
            emits: [],
            author: '',
            since: '',
            example: '',
            template: '',
            script: ''
        };
    }
}

/**
 * Generate enhanced component documentation
 * @param {Object} componentInfo - Component information
 * @returns {string} - Markdown documentation
 */
function generateEnhancedDocs(componentInfo) {
    let markdown = `---
id: ${componentInfo.name}
title: ${componentInfo.name}
sidebar_position: 1
---

# ${componentInfo.name}

${componentInfo.description}

`;

    if (componentInfo.author) {
        markdown += `**Author:** ${componentInfo.author}\n\n`;
    }
    
    if (componentInfo.since) {
        markdown += `**Since:** ${componentInfo.since}\n\n`;
    }

    // Add props section
    if (componentInfo.props.length > 0) {
        markdown += '## Props\n\n';
        markdown += '| Name | Type | Default | Required | Description |\n';
        markdown += '|------|------|---------|----------|-------------|\n';
        
        componentInfo.props.forEach(prop => {
            markdown += `| ${prop.name} | \`${prop.type}\` | ${prop.default} | ${prop.required} | ${prop.description} |\n`;
        });
        
        markdown += '\n';
    }

    // Add methods section
    if (componentInfo.methods.length > 0) {
        markdown += '## Methods\n\n';
        
        componentInfo.methods.forEach(method => {
            markdown += `### ${method.name}()\n\n`;
            markdown += `${method.description}\n\n`;
            
            if (method.params.length > 0) {
                markdown += '**Parameters:**\n\n';
                method.params.forEach(param => {
                    markdown += `- \`${param.name}\` (\`${param.type}\`): ${param.description}\n`;
                });
                markdown += '\n';
            }
            
            if (method.returns) {
                markdown += `**Returns:** ${method.returns}\n\n`;
            }
        });
    }

    // Add emits section
    if (componentInfo.emits.length > 0) {
        markdown += '## Events\n\n';
        markdown += '| Event Name | Description |\n';
        markdown += '|------------|-------------|\n';
        
        componentInfo.emits.forEach(emit => {
            markdown += `| ${emit} | - |\n`;
        });
        
        markdown += '\n';
    }

    // Add example section
    if (componentInfo.example) {
        markdown += '## Example\n\n';
        markdown += '```vue\n' + componentInfo.example + '\n```\n\n';
    }

    // Add template section
    markdown += '## Template Structure\n\n';
    markdown += '```html\n' + componentInfo.template + '\n```\n\n';

    return markdown;
}

/**
 * Create index file for a component category
 * @param {string} categoryDir - Directory of the category
 * @param {string} categoryName - Name of the category
 * @param {Array<string>} components - List of components in the category
 */
function createCategoryIndex(categoryDir, categoryName, components) {
    const indexPath = path.join(categoryDir, 'index.md');
    
    let content = `---
id: index
title: ${categoryName} Components
sidebar_position: 1
---

# ${categoryName} Components

`;

    if (components.length > 0) {
        content += 'This section contains documentation for the following components:\n\n';
        
        components.forEach(component => {
            content += `- [${component}](./${component}.md)\n`;
        });
    } else {
        content += 'No components found in this category.';
    }
    
    fs.writeFileSync(indexPath, content);
    console.log(`Created index for ${categoryName} components`);
}

/**
 * Create main index file for web components
 * @param {Object} categories - Categories and their components
 */
function createMainIndex(categories) {
    let content = `---
id: index
title: Web Components
sidebar_position: 1
---

# Web Components

The ESB Online web application uses Vue.js components organized into the following categories:

`;

    Object.keys(categories).forEach(category => {
        content += `## ${category}\n\n`;
        
        if (categories[category].length > 0) {
            content += 'This category contains the following components:\n\n';
            
            categories[category].forEach(component => {
                content += `- [${component}](./${category.toLowerCase()}/${component}.md)\n`;
            });
            
            content += '\n';
        } else {
            content += 'No components found in this category.\n\n';
        }
    });
    
    fs.writeFileSync(webIndexFile, content);
    console.log('Created main index for web components');
}

/**
 * Process components in a directory
 * @param {string} directory - Directory containing components
 * @param {string} categoryName - Name of the category
 */
function processComponentCategory(directory, categoryName) {
    if (!fs.existsSync(directory)) {
        console.log(`Directory ${directory} does not exist. Skipping.`);
        return [];
    }
    
    const categoryDir = path.join(webOutputDir, categoryName.toLowerCase());
    if (!fs.existsSync(categoryDir)) {
        fs.mkdirSync(categoryDir, { recursive: true });
    }
    
    const components = [];
    
    try {
        const files = fs.readdirSync(directory);
        
        // Process Vue files
        files.filter(file => file.endsWith('.vue')).forEach(file => {
            const filePath = path.join(directory, file);
            const componentName = path.basename(file, '.vue');
            const outputPath = path.join(categoryDir, `${componentName}.md`);
            
            const componentInfo = extractComponentInfo(filePath, componentName);
            const markdown = generateEnhancedDocs(componentInfo);
            
            fs.writeFileSync(outputPath, markdown);
            console.log(`Generated enhanced documentation for ${categoryName}/${componentName}`);
            
            components.push(componentName);
        });
        
        // Create category index
        createCategoryIndex(categoryDir, categoryName, components);
        
        return components;
    } catch (error) {
        console.error(`Error processing directory ${directory}:`, error);
        return [];
    }
}

// Process web components by category
console.log('Generating enhanced web component documentation...');

const categories = {
    'BibleDisplay': [],
    'Navigation': [],
    'Search': [],
    'Common': []
};

// Process each category
categories.BibleDisplay = processComponentCategory(path.join(webComponentsDir, 'BibleDisplay'), 'BibleDisplay');
categories.Navigation = processComponentCategory(path.join(webComponentsDir, 'Navigation'), 'Navigation');
categories.Search = processComponentCategory(path.join(webComponentsDir, 'Search'), 'Search');
categories.Common = processComponentCategory(path.join(webComponentsDir, 'common'), 'Common');

// Create main index
createMainIndex(categories);

console.log('Enhanced component documentation generation complete!');
