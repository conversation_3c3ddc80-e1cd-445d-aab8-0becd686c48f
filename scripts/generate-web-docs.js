#!/usr/bin/env node

/**
 * Script to generate comprehensive documentation for the ESB Online web application
 * This script documents stores, composables, and their functions
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Paths
const rootDir = path.resolve(__dirname, '..');
const webDir = path.resolve(rootDir, 'apps/web');
const webJsDir = path.resolve(webDir, 'resources/js');
const storesDir = path.resolve(webJsDir, 'stores');
const composablesDir = path.resolve(webJsDir, 'composables');
const outputDir = path.resolve(rootDir, 'docs/web');
const storesOutputDir = path.resolve(outputDir, 'stores');
const composablesOutputDir = path.resolve(outputDir, 'composables');

// Ensure output directories exist
[storesOutputDir, composablesOutputDir].forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
});

/**
 * Extract JSDoc comments from a file
 * @param {string} content - File content
 * @returns {Object} - Extracted JSDoc information
 */
function extractJSDocInfo(content) {
  const result = {
    description: '',
    params: [],
    returns: null,
    examples: [],
    see: [],
    since: '',
    deprecated: false,
    author: '',
    type: ''
  };

  // Extract JSDoc blocks
  const jsDocBlocks = content.match(/\/\*\*\s*([\s\S]*?)\s*\*\//g) || [];
  
  for (const block of jsDocBlocks) {
    // Extract description
    const descriptionMatch = block.match(/@description\s+([\s\S]*?)(\n\s*\*\s*@|\n\s*\*\/)/);
    if (descriptionMatch) {
      result.description = descriptionMatch[1].replace(/\n\s*\*\s*/g, ' ').trim();
    } else {
      // If no @description tag, use the first paragraph
      const descLines = block.split('\n')
        .map(line => line.replace(/^\s*\*\s*/, '').trim())
        .filter(line => line && !line.startsWith('@'));
      if (descLines.length > 0 && !result.description) {
        result.description = descLines.join(' ');
      }
    }
    
    // Extract params
    const paramMatches = block.match(/@param\s+(?:{([^}]+)})?\s*(?:\[([^\]]+)\]|(\S+))\s*([\s\S]*?)(?=\n\s*\*\s*@|\n\s*\*\/)/g) || [];
    for (const paramMatch of paramMatches) {
      const parts = paramMatch.match(/@param\s+(?:{([^}]+)})?\s*(?:\[([^\]]+)\]|(\S+))\s*([\s\S]*?)(?=\n\s*\*\s*@|\n\s*\*\/)/);
      if (parts) {
        const [, type, optionalName, requiredName, description] = parts;
        result.params.push({
          name: optionalName || requiredName,
          type: type || 'any',
          description: description.replace(/\n\s*\*\s*/g, ' ').trim(),
          optional: !!optionalName
        });
      }
    }
    
    // Extract return value
    const returnMatch = block.match(/@returns?\s+(?:{([^}]+)})?\s*([\s\S]*?)(?=\n\s*\*\s*@|\n\s*\*\/)/);
    if (returnMatch) {
      const [, type, description] = returnMatch;
      result.returns = {
        type: type || 'any',
        description: description.replace(/\n\s*\*\s*/g, ' ').trim()
      };
    }
    
    // Extract examples
    const exampleMatches = block.match(/@example\s+([\s\S]*?)(?=\n\s*\*\s*@|\n\s*\*\/)/g) || [];
    for (const exampleMatch of exampleMatches) {
      const parts = exampleMatch.match(/@example\s+([\s\S]*?)(?=\n\s*\*\s*@|\n\s*\*\/)/);
      if (parts) {
        result.examples.push(parts[1].replace(/\n\s*\*\s*/g, '\n').trim());
      }
    }
    
    // Extract see references
    const seeMatches = block.match(/@see\s+([\s\S]*?)(?=\n\s*\*\s*@|\n\s*\*\/)/g) || [];
    for (const seeMatch of seeMatches) {
      const parts = seeMatch.match(/@see\s+([\s\S]*?)(?=\n\s*\*\s*@|\n\s*\*\/)/);
      if (parts) {
        result.see.push(parts[1].trim());
      }
    }
    
    // Extract since version
    const sinceMatch = block.match(/@since\s+([\s\S]*?)(?=\n\s*\*\s*@|\n\s*\*\/)/);
    if (sinceMatch) {
      result.since = sinceMatch[1].trim();
    }
    
    // Check if deprecated
    result.deprecated = block.includes('@deprecated');
    
    // Extract author
    const authorMatch = block.match(/@author\s+([\s\S]*?)(?=\n\s*\*\s*@|\n\s*\*\/)/);
    if (authorMatch) {
      result.author = authorMatch[1].trim();
    }

    // Extract type
    const typeMatch = block.match(/@type\s+(?:{([^}]+)})/);
    if (typeMatch) {
      result.type = typeMatch[1].trim();
    }
  }
  
  return result;
}

/**
 * Extract state properties with their types and descriptions
 * @param {string} content - File content
 * @returns {Array} - Array of state properties with type and description
 */
function extractStateProperties(content) {
  const stateProperties = [];
  
  // Find the state block
  const stateMatch = content.match(/state:\s*\(\)\s*=>\s*\(\{([\s\S]*?)\}\)/s);
  
  if (stateMatch) {
    const stateBlock = stateMatch[1];
    
    // Extract property declarations - split by line endings followed by a word character
    // This helps to properly separate properties even when they span multiple lines
    const propertyRegex = /(\w+)(?:\s*:\s*([^=,]+))?(?:\s*=\s*([^,]+))?(?:,|$)/g;
    let match;
    
    while ((match = propertyRegex.exec(stateBlock)) !== null) {
      const [fullMatch, name, rawType, rawInitialValue] = match;
      
      if (name) {
        // Clean up type and initial value
        let type = rawType ? rawType.trim() : 'any';
        let initialValue = rawInitialValue ? rawInitialValue.trim() : undefined;
        
        // Handle complex types like "null as number | null"
        if (type.includes('as')) {
          const asMatch = type.match(/(.+)\s+as\s+(.+)/);
          if (asMatch) {
            type = asMatch[2].trim();
          }
        }
        
        // Handle array types with object literals like "[] as Array<{ start: number; end: number }>"
        if (initialValue && initialValue.includes('as Array<')) {
          const arrayTypeMatch = initialValue.match(/as\s+Array<(.+)>/);
          if (arrayTypeMatch) {
            type = `Array<${arrayTypeMatch[1].trim()}>`;
            initialValue = '[]';
          }
        }
        
        // Look for JSDoc comment above this property
        const propertyIndex = stateBlock.indexOf(fullMatch);
        const blockBeforeProperty = stateBlock.substring(Math.max(0, propertyIndex - 300), propertyIndex);
        const propertyJSDoc = extractJSDocInfo(blockBeforeProperty);
        
        stateProperties.push({
          name: name.trim(),
          type: type || propertyJSDoc.type || 'any',
          initialValue: initialValue,
          description: propertyJSDoc.description || `State property ${name}`
        });
      }
    }
  }
  
  return stateProperties;
}

/**
 * Extract store information from a TypeScript file
 * @param {string} filePath - Path to the TypeScript file
 * @returns {Object} - Store information
 */
function extractStoreInfo(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const fileName = path.basename(filePath, '.ts');
  
  // Extract store name
  const storeNameMatch = content.match(/defineStore\(['"]([^'"]+)['"]/);
  const storeName = storeNameMatch ? storeNameMatch[1] : fileName;
  
  // Extract JSDoc info for the store
  const storeJSDocMatch = content.match(/\/\*\*\s*([\s\S]*?)\s*\*\/\s*export\s+const\s+use\w+Store/);
  const storeJSDoc = storeJSDocMatch 
    ? extractJSDocInfo(storeJSDocMatch[0])
    : extractJSDocInfo(content);
  
  // Extract state properties
  const stateProperties = extractStateProperties(content);
  
  // Extract getters
  const gettersMatch = content.match(/getters:\s*\{([\s\S]*?)\},?\s*(?:actions|$)/s);
  const getters = [];
  
  if (gettersMatch) {
    const gettersBlock = gettersMatch[1];
    
    // Find all getter functions
    const getterFunctions = gettersBlock.split(/,\s*\n\s*(?=\w+\s*\()/).filter(Boolean);
    
    for (const getterFunc of getterFunctions) {
      // Extract getter name and return type
      const getterNameMatch = getterFunc.match(/(\w+)\s*(?:\([^)]*\))?\s*(?::\s*([^{]+))?/);
      
      if (getterNameMatch) {
        const [, name, returnType] = getterNameMatch;
        
        // Extract JSDoc for this getter
        const getterJSDoc = extractJSDocInfo(getterFunc);
        
        getters.push({
          name: name.trim(),
          returnType: (returnType || getterJSDoc.returns?.type || 'unknown').trim(),
          description: getterJSDoc.description || `Getter for ${name}`,
          params: getterJSDoc.params,
          returns: getterJSDoc.returns || { 
            type: returnType?.trim() || 'unknown', 
            description: `Returns the ${name}` 
          },
          examples: getterJSDoc.examples,
          deprecated: getterJSDoc.deprecated
        });
      }
    }
  }
  
  // Extract actions
  const actionsMatch = content.match(/actions:\s*\{([\s\S]*?)\}\s*(?:\}\)|$)/s);
  const actions = [];
  
  if (actionsMatch) {
    const actionsBlock = actionsMatch[1];
    
    // Find all action functions
    const actionFunctions = actionsBlock.split(/,\s*\n\s*(?=(?:async\s+)?\w+\s*\()/).filter(Boolean);
    
    for (const actionFunc of actionFunctions) {
      // Extract action name, parameters, and return type
      const actionNameMatch = actionFunc.match(/(?:async\s+)?(\w+)\s*\(([^)]*)\)\s*(?::\s*([^{]+))?/);
      
      if (actionNameMatch) {
        const [, name, params, returnType] = actionNameMatch;
        
        // Extract JSDoc for this action
        const actionJSDoc = extractJSDocInfo(actionFunc);
        
        // Parse parameters
        const paramsList = [];
        if (params.trim()) {
          const paramItems = params.split(',').map(p => p.trim()).filter(Boolean);
          for (const param of paramItems) {
            const paramMatch = param.match(/(\w+)(?::\s*([^=]+))?(?:=\s*(.+))?$/);
            if (paramMatch) {
              const [, paramName, paramType, defaultValue] = paramMatch;
              
              // Find matching JSDoc param
              const jsDocParam = actionJSDoc.params.find(p => p.name === paramName);
              
              paramsList.push({
                name: paramName,
                type: (paramType || jsDocParam?.type || 'any').trim(),
                defaultValue: defaultValue ? defaultValue.trim() : undefined,
                description: jsDocParam?.description || `Parameter ${paramName}`,
                optional: param.includes('?') || !!defaultValue
              });
            }
          }
        }
        
        actions.push({
          name: name.trim(),
          isAsync: actionFunc.includes('async'),
          returnType: (returnType || actionJSDoc.returns?.type || (actionFunc.includes('async') ? 'Promise<void>' : 'void')).trim(),
          description: actionJSDoc.description || `Action to ${name.replace(/([A-Z])/g, ' $1').toLowerCase()}`,
          params: paramsList.length > 0 ? paramsList : actionJSDoc.params,
          returns: actionJSDoc.returns || (returnType ? { 
            type: returnType.trim(), 
            description: `Return value of ${name}` 
          } : null),
          examples: actionJSDoc.examples,
          deprecated: actionJSDoc.deprecated
        });
      }
    }
  }
  
  return {
    name: storeName,
    fileName,
    description: storeJSDoc.description || `Store for managing ${fileName.replace(/Store$/, '')} data`,
    author: storeJSDoc.author,
    since: storeJSDoc.since,
    see: storeJSDoc.see,
    examples: storeJSDoc.examples,
    deprecated: storeJSDoc.deprecated,
    state: stateProperties,
    getters,
    actions
  };
}

/**
 * Extract composable information from a TypeScript file
 * @param {string} filePath - Path to the TypeScript file
 * @returns {Object} - Composable information
 */
function extractComposableInfo(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const fileName = path.basename(filePath, '.ts');
  
  // Extract composable name (usually the same as the file name without extension)
  const composableName = fileName;
  
  // Extract JSDoc info for the composable
  const composableJSDocMatch = content.match(/\/\*\*\s*([\s\S]*?)\s*\*\/\s*export\s+function\s+use\w+/);
  const composableJSDoc = composableJSDocMatch 
    ? extractJSDocInfo(composableJSDocMatch[0])
    : extractJSDocInfo(content);
  
  // Extract function signature
  const functionMatch = content.match(/export\s+function\s+(\w+)\s*\(([^)]*)\)\s*(?::\s*([^{]+))?/);
  let functionName = '';
  let params = [];
  let returnType = '';
  
  if (functionMatch) {
    [, functionName, paramString, returnType] = functionMatch;
    
    // Parse parameters
    if (paramString.trim()) {
      const paramItems = paramString.split(',').map(p => p.trim()).filter(Boolean);
      for (const param of paramItems) {
        const paramMatch = param.match(/(\w+)(?::\s*([^=]+))?(?:=\s*(.+))?$/);
        if (paramMatch) {
          const [, paramName, paramType, defaultValue] = paramMatch;
          
          // Find matching JSDoc param
          const jsDocParam = composableJSDoc.params.find(p => p.name === paramName);
          
          params.push({
            name: paramName,
            type: (paramType || jsDocParam?.type || 'any').trim(),
            defaultValue: defaultValue ? defaultValue.trim() : undefined,
            description: jsDocParam?.description || `Parameter ${paramName}`,
            optional: param.includes('?') || !!defaultValue
          });
        }
      }
    }
  }
  
  // Extract helper functions within the composable
  const helperFunctions = [];
  const functionMatches = content.match(/function\s+(\w+)\s*\(([^)]*)\)\s*(?::\s*([^{]+))?\s*\{/g) || [];
  
  for (const funcMatch of functionMatches) {
    const funcNameMatch = funcMatch.match(/function\s+(\w+)\s*\(([^)]*)\)\s*(?::\s*([^{]+))?\s*\{/);
    if (funcNameMatch && funcNameMatch[1] !== functionName) {
      const [, helperName, helperParams, helperReturnType] = funcNameMatch;
      
      // Find JSDoc for this helper function
      const helperIndex = content.indexOf(funcMatch);
      const blockBeforeHelper = content.substring(Math.max(0, helperIndex - 500), helperIndex);
      const helperJSDoc = extractJSDocInfo(blockBeforeHelper);
      
      // Parse parameters
      const parsedParams = [];
      if (helperParams.trim()) {
        const paramItems = helperParams.split(',').map(p => p.trim()).filter(Boolean);
        for (const param of paramItems) {
          const paramMatch = param.match(/(\w+)(?::\s*([^=]+))?(?:=\s*(.+))?$/);
          if (paramMatch) {
            const [, paramName, paramType, defaultValue] = paramMatch;
            
            // Find matching JSDoc param
            const jsDocParam = helperJSDoc.params.find(p => p.name === paramName);
            
            parsedParams.push({
              name: paramName,
              type: (paramType || jsDocParam?.type || 'any').trim(),
              defaultValue: defaultValue ? defaultValue.trim() : undefined,
              description: jsDocParam?.description || `Parameter ${paramName}`,
              optional: param.includes('?') || !!defaultValue
            });
          }
        }
      }
      
      helperFunctions.push({
        name: helperName,
        params: parsedParams,
        returnType: (helperReturnType || helperJSDoc.returns?.type || 'void').trim(),
        description: helperJSDoc.description || `Helper function ${helperName}`,
        returns: helperJSDoc.returns || (helperReturnType ? { 
          type: helperReturnType.trim(), 
          description: `Return value of ${helperName}` 
        } : null),
        examples: helperJSDoc.examples,
        deprecated: helperJSDoc.deprecated
      });
    }
  }
  
  // Extract returned values/functions (for composables that return an object)
  const returnedValues = [];
  const returnObjectMatch = content.match(/return\s*\{([^}]*)\}/s);
  
  if (returnObjectMatch) {
    const returnBlock = returnObjectMatch[1];
    const returnItems = returnBlock.split(',').map(item => item.trim()).filter(Boolean);
    
    for (const item of returnItems) {
      const itemMatch = item.match(/(\w+)(?:\s*:\s*([^,]+))?/);
      if (itemMatch) {
        const [, name, value] = itemMatch;
        
        returnedValues.push({
          name: name.trim(),
          value: value ? value.trim() : name.trim(),
          description: `Returned value ${name}`
        });
      }
    }
  }
  
  // Extract references to other stores or composables
  const storeReferences = [];
  const storeMatches = content.match(/use\w+Store\(\)/g) || [];
  
  for (const storeMatch of storeMatches) {
    const storeName = storeMatch.match(/use(\w+)Store/)[1];
    storeReferences.push({
      name: `use${storeName}Store`,
      type: 'store',
      description: `Reference to the ${storeName} store`
    });
  }
  
  const composableReferences = [];
  const composableMatches = content.match(/use\w+\(\)/g) || [];
  
  for (const composableMatch of composableMatches) {
    if (composableMatch !== `${functionName}()`) {
      const referencedName = composableMatch.match(/use(\w+)/)[1];
      composableReferences.push({
        name: `use${referencedName}`,
        type: 'composable',
        description: `Reference to the ${referencedName} composable`
      });
    }
  }
  
  return {
    name: functionName || composableName,
    fileName,
    description: composableJSDoc.description || `Composable for ${composableName.replace(/^use/, '')}`,
    author: composableJSDoc.author,
    since: composableJSDoc.since,
    see: composableJSDoc.see,
    examples: composableJSDoc.examples,
    deprecated: composableJSDoc.deprecated,
    params,
    returnType: returnType ? returnType.trim() : 'unknown',
    returnValue: composableJSDoc.returns,
    helperFunctions,
    returnedValues,
    storeReferences,
    composableReferences
  };
}

/**
 * Generate documentation for a store
 * @param {Object} storeInfo - Store information
 * @returns {string} - Markdown documentation
 */
function generateStoreDocumentation(storeInfo) {
  return `---
id: ${storeInfo.fileName.replace(/\.ts$/, '')}
title: ${storeInfo.name} Store
sidebar_position: 2
---

# ${storeInfo.name} Store

${storeInfo.description}

${storeInfo.deprecated ? `> **Deprecated**: This store is deprecated and will be removed in a future version.` : ''}
${storeInfo.since ? `**Since:** ${storeInfo.since}` : ''}
${storeInfo.author ? `**Author:** ${storeInfo.author}` : ''}
${storeInfo.see.length > 0 ? `**See also:** ${storeInfo.see.join(', ')}` : ''}

## Examples

${storeInfo.examples.length > 0 ? storeInfo.examples.map(example => `\`\`\`typescript
${example}
\`\`\``).join('\n\n') : ''}

## State

\`\`\`typescript
interface State {
${storeInfo.state.map(item => `  ${item.name}: ${item.type};`).join('\n')}
}
\`\`\`

${storeInfo.state.map(item => `### ${item.name}

**Type:** \`${item.type}\`

${item.description}
${item.initialValue ? `\n**Default value:** \`${item.initialValue}\`` : ''}
`).join('\n')}

## Getters

${storeInfo.getters.map(getter => `### ${getter.name}

${getter.description}

**Returns:** \`${getter.returnType}\`

${getter.params.length > 0 ? `**Parameters:**\n\n${getter.params.map(param => `- \`${param.name}\`: \`${param.type}\` - ${param.description}`).join('\n')}` : ''}
`).join('\n')}

## Actions

${storeInfo.actions.map(action => `### ${action.name}

${action.description}

${action.params.length > 0 ? `**Parameters:**\n\n${action.params.map(param => `- \`${param.name}\`: \`${param.type}\` - ${param.description}`).join('\n')}` : ''}

${action.returns ? `**Returns:** \`${action.returns.type}\` - ${action.returns.description}` : ''}
`).join('\n')}
`;
}

/**
 * Generate documentation for a composable
 * @param {Object} composableInfo - Composable information
 * @returns {string} - Markdown documentation
 */
function generateComposableDocumentation(composableInfo) {
  return `---
id: ${composableInfo.fileName.replace(/\.ts$/, '')}
title: ${composableInfo.name}
sidebar_position: 2
---

# ${composableInfo.name}

${composableInfo.description}

${composableInfo.deprecated ? `> **Deprecated**: This composable is deprecated and will be removed in a future version.` : ''}
${composableInfo.since ? `**Since:** ${composableInfo.since}` : ''}
${composableInfo.author ? `**Author:** ${composableInfo.author}` : ''}
${composableInfo.see.length > 0 ? `**See also:** ${composableInfo.see.join(', ')}` : ''}

## Usage

\`\`\`typescript
import { ${composableInfo.name} } from '@/composables/${composableInfo.fileName.replace(/\.ts$/, '')}';

// Example usage
const ${composableInfo.name.replace(/^use/, '').charAt(0).toLowerCase() + composableInfo.name.replace(/^use/, '').slice(1)} = ${composableInfo.name}(${composableInfo.params.map(param => param.name).join(', ')});
\`\`\`

## Examples

${composableInfo.examples.length > 0 ? composableInfo.examples.map(example => `\`\`\`typescript
${example}
\`\`\``).join('\n\n') : ''}

## API

### Parameters

${composableInfo.params.length > 0 ? composableInfo.params.map(param => `- \`${param.name}\`: \`${param.type}\` - ${param.description}`).join('\n') : 'This composable does not take any parameters.'}

### Returns

${composableInfo.returnValue ? `\`${composableInfo.returnType}\` - ${composableInfo.returnValue.description}` : `\`${composableInfo.returnType}\``}

${composableInfo.returnedValues.length > 0 ? `
The composable returns an object with the following properties:

${composableInfo.returnedValues.map(value => `- \`${value.name}\` - ${value.description}`).join('\n')}
` : ''}

## Helper Functions

${composableInfo.helperFunctions.length > 0 ? composableInfo.helperFunctions.map(func => `### ${func.name}

${func.description}

${func.params.length > 0 ? `**Parameters:**\n\n${func.params.map(param => `- \`${param.name}\`: \`${param.type}\` - ${param.description}`).join('\n')}` : 'This function does not take any parameters.'}

${func.returns ? `**Returns:** \`${func.returnType}\` - ${func.returns.description}` : `**Returns:** \`${func.returnType}\``}

${func.examples.length > 0 ? `**Examples:**

${func.examples.map(example => `\`\`\`typescript
${example}
\`\`\``).join('\n\n')}` : ''}
${func.deprecated ? `> **Deprecated**: This function is deprecated.` : ''}
`).join('\n') : ''}

## Dependencies

${composableInfo.storeReferences.length > 0 || composableInfo.composableReferences.length > 0 ? `
### Stores

${composableInfo.storeReferences.length > 0 ? composableInfo.storeReferences.map(store => `- \`${store.name}\` - ${store.description}`).join('\n') : ''}

### Composables

${composableInfo.composableReferences.length > 0 ? composableInfo.composableReferences.map(comp => `- \`${comp.name}\` - ${comp.description}`).join('\n') : ''}
` : ''}
`;
}

/**
 * Generate index file for stores
 * @param {Array} stores - List of store information objects
 * @returns {string} - Markdown documentation
 */
function generateStoresIndex(stores) {
  return `---
id: index
title: Stores Overview
sidebar_position: 1
---

# Stores Overview

This section contains documentation for all the Pinia stores used in the ESB Online web application.
Stores are used to manage the application state and provide a centralized place for data management.

## Available Stores

${stores.map(store => `### [${store.name}](${store.fileName.replace(/\.ts$/, '')})

${store.description}

**State Properties:** ${store.state.length}  
**Getters:** ${store.getters.length}  
**Actions:** ${store.actions.length}
`).join('\n\n')}
`;
}

/**
 * Generate index file for composables
 * @param {Array} composables - List of composable information objects
 * @returns {string} - Markdown documentation
 */
function generateComposablesIndex(composables) {
  return `---
id: index
title: Composables Overview
sidebar_position: 1
---

# Composables Overview

This section contains documentation for all the Vue composables used in the ESB Online web application.
Composables are reusable pieces of logic that can be shared between components.

## Available Composables

${composables.map(composable => `### [${composable.name}](${composable.fileName.replace(/\.ts$/, '')})

${composable.description}

**Parameters:** ${composable.params.length}  
**Helper Functions:** ${composable.helperFunctions.length}  
**Returns:** \`${composable.returnType}\`
`).join('\n\n')}
`;
}

/**
 * Generate sidebar for web app documentation
 * @param {Array} stores - List of store names
 * @param {Array} composables - List of composable names
 */
function generateWebSidebar(stores, composables) {
  const sidebarContent = `
module.exports = {
  webSidebar: [
    {
      type: 'category',
      label: 'Overview',
      items: ['intro'],
    },
    {
      type: 'category',
      label: 'Stores',
      items: [
        'stores/index',
        ${stores.map(store => `'stores/${store.fileName.replace(/\.ts$/, '')}'`).join(',\n        ')}
      ],
    },
    {
      type: 'category',
      label: 'Composables',
      items: [
        'composables/index',
        ${composables.map(composable => `'composables/${composable.fileName.replace(/\.ts$/, '')}'`).join(',\n        ')}
      ],
    },
    {
      type: 'category',
      label: 'Types',
      items: ['types/README', 'types/store-types'],
    },
    {
      type: 'category',
      label: 'API',
      items: ['api/intro'],
    },
  ],
};
`;

  fs.writeFileSync(path.resolve(outputDir, 'sidebar.ts'), sidebarContent);
}

// Process all stores
console.log('Generating documentation for stores...');
const storeFiles = [];

// Process root stores
fs.readdirSync(storesDir).forEach(file => {
  if (file.endsWith('.ts') && !file.includes('index.ts')) {
    storeFiles.push(path.join(storesDir, file));
  }
});

// Process Bible stores
const bibleStoresDir = path.join(storesDir, 'bible');
if (fs.existsSync(bibleStoresDir)) {
  fs.readdirSync(bibleStoresDir).forEach(file => {
    if (file.endsWith('.ts') && !file.includes('index.ts') && !file.includes('.test.ts')) {
      storeFiles.push(path.join(bibleStoresDir, file));
    }
  });
}

const stores = [];

storeFiles.forEach(file => {
  try {
    const storeInfo = extractStoreInfo(file);
    stores.push(storeInfo);
    
    const markdown = generateStoreDocumentation(storeInfo);
    fs.writeFileSync(path.join(storesOutputDir, `${storeInfo.fileName.replace(/\.ts$/, '')}.md`), markdown);
    
    console.log(`Generated documentation for store: ${storeInfo.name}`);
  } catch (error) {
    console.error(`Error processing store ${file}:`, error);
  }
});

// Generate stores index
const storesIndexMarkdown = generateStoresIndex(stores);
fs.writeFileSync(path.join(storesOutputDir, 'index.md'), storesIndexMarkdown);

// Process all composables
console.log('Generating documentation for composables...');
const composableFiles = fs.readdirSync(composablesDir)
  .filter(file => file.endsWith('.ts'))
  .map(file => path.join(composablesDir, file));

const composables = [];

composableFiles.forEach(file => {
  try {
    const composableInfo = extractComposableInfo(file);
    composables.push(composableInfo);
    
    const markdown = generateComposableDocumentation(composableInfo);
    fs.writeFileSync(path.join(composablesOutputDir, `${composableInfo.fileName.replace(/\.ts$/, '')}.md`), markdown);
    
    console.log(`Generated documentation for composable: ${composableInfo.name}`);
  } catch (error) {
    console.error(`Error processing composable ${file}:`, error);
  }
});

// Generate composables index
const composablesIndexMarkdown = generateComposablesIndex(composables);
fs.writeFileSync(path.join(composablesOutputDir, 'index.md'), composablesIndexMarkdown);

// Generate web sidebar
generateWebSidebar(stores, composables);

console.log('Web documentation generation completed successfully!');
