/**
 * VueDoc.js Wrapper Script
 * 
 * This script creates a wrapper around VueDoc.js to handle the dependency issue
 * between @vuedoc/parser and @b613/utils.
 */
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Define directories
const rootDir = path.resolve(__dirname, '..');
const webComponentsDir = path.resolve(rootDir, 'apps/web/resources/js/Components');
const webOutputDir = path.resolve(rootDir, 'docs/web/components');
const tempDir = path.resolve(rootDir, 'temp_vuedoc');

// Create output directories if they don't exist
if (!fs.existsSync(webOutputDir)) {
    fs.mkdirSync(webOutputDir, { recursive: true });
}

// Create temporary directory for VueDoc.js
if (!fs.existsSync(tempDir)) {
    fs.mkdirSync(tempDir, { recursive: true });
}

/**
 * Create a temporary package.json for VueDoc.js
 */
function createTempPackageJson() {
    const packageJson = {
        "name": "temp-vuedoc",
        "version": "1.0.0",
        "private": true,
        "type": "commonjs",
        "dependencies": {
            "@vuedoc/parser": "3.4.0", // Use older version that doesn't have the dependency issue
            "@vuedoc/md": "3.2.0"
        }
    };
    
    fs.writeFileSync(path.join(tempDir, 'package.json'), JSON.stringify(packageJson, null, 2));
    console.log('Created temporary package.json for VueDoc.js');
}

/**
 * Install VueDoc.js dependencies in temporary directory
 */
function installDependencies() {
    try {
        console.log('Installing VueDoc.js dependencies...');
        execSync('yarn install', { 
            cwd: tempDir,
            stdio: 'inherit'
        });
        console.log('VueDoc.js dependencies installed successfully');
    } catch (error) {
        console.error('Error installing VueDoc.js dependencies:', error.message);
        process.exit(1);
    }
}

/**
 * Create VueDoc.js wrapper script
 */
function createVueDocWrapper() {
    const wrapperScript = `
const { parseComponent } = require('@vuedoc/parser');
const { generateMarkdown } = require('@vuedoc/md');
const fs = require('fs');
const path = require('path');

/**
 * Process a Vue component file
 * @param {string} filePath - Path to Vue component file
 * @param {string} outputPath - Path to output markdown file
 */
async function processComponent(filePath, outputPath) {
    try {
        const component = await parseComponent({
            filename: filePath,
            encoding: 'utf8',
        });

        const markdown = await generateMarkdown(component, {
            filename: outputPath,
        });

        fs.writeFileSync(outputPath, markdown);
        console.log(\`Generated documentation for \${path.basename(filePath, '.vue')}\`);
    } catch (err) {
        console.error(\`Error processing component \${path.basename(filePath, '.vue')}:\`, err);
    }
}

// Get command line arguments
const filePath = process.argv[2];
const outputPath = process.argv[3];

if (!filePath || !outputPath) {
    console.error('Usage: node vuedoc-wrapper-script.js <filePath> <outputPath>');
    process.exit(1);
}

// Process component
processComponent(filePath, outputPath);
    `;
    
    fs.writeFileSync(path.join(tempDir, 'vuedoc-wrapper-script.js'), wrapperScript);
    console.log('Created VueDoc.js wrapper script');
}

/**
 * Create category index file
 * @param {string} categoryDir - Directory of the category
 * @param {string} categoryName - Name of the category
 * @param {Array<string>} components - List of components in the category
 */
function createCategoryIndex(categoryDir, categoryName, components) {
    const indexPath = path.join(categoryDir, 'index.md');
    
    let content = `---
id: index
title: ${categoryName} Components
sidebar_position: 1
---

# ${categoryName} Components

`;

    if (components.length > 0) {
        content += 'This section contains documentation for the following components:\n\n';
        
        components.forEach(component => {
            content += `- [${component}](./${component}.md)\n`;
        });
    } else {
        content += 'No components found in this category.';
    }
    
    fs.writeFileSync(indexPath, content);
    console.log(`Created index for ${categoryName} components`);
}

/**
 * Create main index file for web components
 * @param {Object} categories - Categories and their components
 */
function createMainIndex(categories) {
    const indexPath = path.join(webOutputDir, 'index.md');
    
    let content = `---
id: index
title: Web Components
sidebar_position: 1
---

# Web Components

The ESB Online web application uses Vue.js components organized into the following categories:

`;

    Object.keys(categories).forEach(category => {
        content += `## ${category}\n\n`;
        
        if (categories[category].length > 0) {
            content += 'This category contains the following components:\n\n';
            
            categories[category].forEach(component => {
                content += `- [${component}](./${category.toLowerCase()}/${component}.md)\n`;
            });
            
            content += '\n';
        } else {
            content += 'No components found in this category.\n\n';
        }
    });
    
    fs.writeFileSync(indexPath, content);
    console.log('Created main index for web components');
}

/**
 * Process components in a directory
 * @param {string} directory - Directory containing components
 * @param {string} categoryName - Name of the category
 */
function processComponentCategory(directory, categoryName) {
    if (!fs.existsSync(directory)) {
        console.log(`Directory ${directory} does not exist. Skipping.`);
        return [];
    }
    
    const categoryDir = path.join(webOutputDir, categoryName.toLowerCase());
    if (!fs.existsSync(categoryDir)) {
        fs.mkdirSync(categoryDir, { recursive: true });
    }
    
    const components = [];
    
    try {
        const files = fs.readdirSync(directory);
        
        // Process Vue files
        files.filter(file => file.endsWith('.vue')).forEach(file => {
            const filePath = path.join(directory, file);
            const componentName = path.basename(file, '.vue');
            const outputPath = path.join(categoryDir, `${componentName}.md`);
            
            try {
                execSync(`node vuedoc-wrapper-script.js "${filePath}" "${outputPath}"`, { 
                    cwd: tempDir,
                    stdio: 'inherit'
                });
                
                components.push(componentName);
            } catch (error) {
                console.error(`Error generating documentation for ${componentName}:`, error.message);
            }
        });
        
        // Create category index
        createCategoryIndex(categoryDir, categoryName, components);
        
        return components;
    } catch (error) {
        console.error(`Error processing directory ${directory}:`, error);
        return [];
    }
}

// Main execution
console.log('Setting up VueDoc.js wrapper...');
createTempPackageJson();
installDependencies();
createVueDocWrapper();

// Process web components by category
console.log('Generating VueDoc.js component documentation...');

const categories = {
    'BibleDisplay': [],
    'Navigation': [],
    'Search': [],
    'Common': []
};

// Process each category
categories.BibleDisplay = processComponentCategory(path.join(webComponentsDir, 'BibleDisplay'), 'BibleDisplay');
categories.Navigation = processComponentCategory(path.join(webComponentsDir, 'Navigation'), 'Navigation');
categories.Search = processComponentCategory(path.join(webComponentsDir, 'Search'), 'Search');
categories.Common = processComponentCategory(path.join(webComponentsDir, 'common'), 'Common');

// Create main index
createMainIndex(categories);

console.log('VueDoc.js component documentation generation complete!');

// Clean up temporary directory
fs.rmSync(tempDir, { recursive: true, force: true });
console.log('Cleaned up temporary directory');
