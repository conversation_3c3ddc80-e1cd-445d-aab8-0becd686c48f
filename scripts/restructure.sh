#!/bin/bash

# Create temporary directory for the move
mkdir -p temp_move

# Move all Laravel files to web directory except the new structure
for item in *; do
    if [ "$item" != "apps" ] && [ "$item" != "packages" ] && [ "$item" != "scripts" ] && [ "$item" != ".github" ] && [ "$item" != "temp_move" ]; then
        mv "$item" "temp_move/"
    fi
done

# Move everything from temp to web directory
mv temp_move/* apps/web/

# Remove temp directory
rmdir temp_move

# Update composer.json in the web app to use local package
cd apps/web
composer config repositories.esbo-core '{"type": "path", "url": "../../packages/esbo-core"}'
composer require esbo/core:@dev

echo "Project restructuring completed!"
