const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Create necessary directories
const docsDir = path.resolve(__dirname, '../docs');
const webDir = path.resolve(docsDir, 'web');
const webApiDir = path.resolve(webDir, 'api');
const webTypesDir = path.resolve(webDir, 'types');
const webComponentsDir = path.resolve(webDir, 'components');
const webStoresDir = path.resolve(webDir, 'stores');
const webComposablesDir = path.resolve(webDir, 'composables');
const componentsDir = path.resolve(docsDir, 'components');
const libsDir = path.resolve(docsDir, 'libs');
const staticApiDocsDir = path.resolve(docsDir, 'static/api-docs');

// Ensure directories exist
[webDir, webApiDir, webTypesDir, webComponentsDir, webStoresDir, webComposablesDir, componentsDir, libsDir, staticApiDocsDir].forEach(dir => {
    if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
    }
});

// Generate basic component docs for backward compatibility
console.log('Generating basic component documentation...');
try {
    execSync('node scripts/generate-component-docs-wrapper.js', { 
        cwd: path.resolve(__dirname, '..'),
        stdio: 'inherit'
    });
    console.log('Basic component documentation generated successfully.');
} catch (error) {
    console.error('Error generating basic component documentation:', error.message);
    console.log('Continuing with the build process...');
}

// Generate detailed component docs using VueDoc.js with fallback
console.log('Generating detailed component documentation...');
try {
    execSync('node scripts/generate-vuedoc-component-docs.js', { 
        cwd: path.resolve(__dirname, '..'),
        stdio: 'inherit'
    });
    console.log('Detailed component documentation generated successfully.');
} catch (error) {
    console.error('Error generating detailed component documentation:', error.message);
    console.log('Continuing with the build process...');
}

// Generate TypeScript docs
console.log('Generating TypeScript documentation...');
try {
    execSync('node scripts/generate-types-docs.js', { 
        cwd: path.resolve(__dirname, '..'),
        stdio: 'inherit'
    });
    console.log('TypeScript documentation generated successfully.');
} catch (error) {
    console.error('Error generating TypeScript documentation:', error.message);
    console.log('Continuing with the build process...');
}

// Generate web stores and composables documentation
console.log('Generating web stores and composables documentation...');
try {
    execSync('node scripts/generate-web-docs.js', { 
        cwd: path.resolve(__dirname, '..'),
        stdio: 'inherit'
    });
    console.log('Web stores and composables documentation generated successfully.');
} catch (error) {
    console.error('Error generating web stores and composables documentation:', error.message);
    console.log('Continuing with the build process...');
}

// Generate API docs
console.log('Generating API documentation...');
try {
    execSync('yarn docs:api', { 
        cwd: path.resolve(__dirname, '..'),
        stdio: 'inherit'
    });
    console.log('API documentation generated successfully.');
} catch (error) {
    console.error('Error generating API documentation:', error.message);
    console.log('Continuing with the build process...');
}

// Build Docusaurus site
console.log('Building Docusaurus site...');
try {
    execSync('cd docs && yarn build', { 
        cwd: path.resolve(__dirname, '..'),
        stdio: 'inherit'
    });
    console.log('Docusaurus site built successfully.');
} catch (error) {
    console.error('Error building Docusaurus site:', error.message);
    process.exit(1);
}

/**
 * Creates an intro file for a documentation section if it doesn't exist
 * @param {string} dir - Directory to create the file in
 * @param {string} title - Title of the documentation section
 * @param {string} content - Content of the intro file
 */
function createIntroFile(dir, title, content) {
    const introFile = path.resolve(dir, 'intro.md');
    if (!fs.existsSync(introFile)) {
        const introContent = `---
id: intro
title: ${title}
sidebar_position: 1
---

# ${title}

${content}
`;
        fs.writeFileSync(introFile, introContent);
        console.log(`Created intro file for ${title}`);
    }
}

// Create intro files if they don't exist
createIntroFile(webDir, 'Web Application', 'Documentation for the ESB Online web application.');
createIntroFile(webApiDir, 'Web App API', 'API documentation for the ESB Online web application.');
createIntroFile(webTypesDir, 'Web App Types', 'TypeScript type documentation for the ESB Online web application.');
createIntroFile(webStoresDir, 'Web App Stores', 'Documentation for the Pinia stores used in the ESB Online web application.');
createIntroFile(webComposablesDir, 'Web App Composables', 'Documentation for the Vue composables used in the ESB Online web application.');
createIntroFile(componentsDir, 'Components', 'Documentation for the Vue components used in the ESB Online platform.');
createIntroFile(libsDir, 'Libraries', 'Documentation for the shared libraries used across the ESB Online platform.');

console.log('Documentation build completed successfully!');
