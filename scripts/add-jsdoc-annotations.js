/**
 * <PERSON><PERSON><PERSON> to add JSDoc annotations to key files in the ESB Online project
 * This script analyzes TypeScript files and adds appropriate JSDoc comments
 * based on the file type (store or composable) and content.
 */
const fs = require('fs');
const path = require('path');

// Define the base directory
const baseDir = path.resolve(__dirname, '../apps/web/resources/js');

// Define patterns to identify different code structures
const storePattern = /defineStore\(['"]([^'"]+)['"]/;
const composablePattern = /export\s+function\s+(\w+)/;
const statePattern = /state:\s*\(\)\s*=>\s*\({([^}]*)}/s;
const actionPattern = /actions:\s*{([^}]*)}/s;
const getterPattern = /getters:\s*{([^}]*)}/s;
const functionPattern = /(\w+)\s*\([^)]*\)\s*{/g;
const propertyPattern = /(\w+):\s*([^,]+),?/g;

// Files to process
const filesToProcess = [
    // Stores
    'stores/bible/bibleDataStore.ts',
    'stores/bible/bibleHighlightStore.ts',
    'stores/bible/bibleMemoryStore.ts',
    'stores/bible/bibleSectionStore.ts',
    'stores/searchSettingsStore.ts',
    'stores/searchStore.ts',
    'stores/textSettingsStore.ts',
    // Composables
    'composables/useDebounce.ts',
    'composables/useDropdown.ts',
    'composables/useScrollManager.ts',
    'composables/useSearchResults.ts',
    'composables/useTextSettings.ts',
    'composables/useThrottle.ts',
    'composables/useVerseReference.ts',
];

/**
 * Adds JSDoc comments to a store file
 * @param {string} filePath - Path to the store file
 * @param {string} content - Content of the file
 * @returns {string} - Updated content with JSDoc comments
 */
function addJSDocToStore(filePath, content) {
    const fileName = path.basename(filePath);
    const storeMatch = storePattern.exec(content);
    
    if (!storeMatch) return content;
    
    const storeName = storeMatch[1];
    const storeDescription = getStoreDescription(fileName, storeName);
    
    // Check if file already has a JSDoc comment for the store
    if (!content.includes('/**\n * Store for managing')) {
        // Add file-level JSDoc
        const fileJSDoc = `/**
 * @file ${storeDescription}
 * @module ${filePath.replace(baseDir + '/', '').replace('.ts', '')}
 */\n`;
        
        // Find the position to insert the store JSDoc
        const storeDefPosition = content.indexOf('export const');
        if (storeDefPosition === -1) return fileJSDoc + content;
        
        // Add store JSDoc
        const storeJSDoc = `/**
 * Store for managing ${getStoreDescription(fileName, storeName, true)}
 * 
 * @description ${storeDescription}
 */\n`;
        
        content = 
            fileJSDoc + 
            content.substring(0, storeDefPosition) + 
            storeJSDoc + 
            content.substring(storeDefPosition);
    }
    
    // Process state properties
    const stateMatch = statePattern.exec(content);
    if (stateMatch) {
        const stateBlock = stateMatch[1];
        let modifiedStateBlock = stateBlock;
        
        // Add JSDoc for state section if not present
        if (!content.includes('/**\n     * State properties')) {
            const statePosition = content.indexOf('state: () => ({');
            if (statePosition !== -1) {
                const stateJSDoc = `    /**
     * State properties for the store
     */\n    `;
                content = 
                    content.substring(0, statePosition) + 
                    stateJSDoc + 
                    content.substring(statePosition);
            }
        }
        
        // Add JSDoc for each property
        let propertyMatch;
        while ((propertyMatch = propertyPattern.exec(stateBlock)) !== null) {
            const propName = propertyMatch[1];
            const propType = propertyMatch[2];
            
            // Skip if property already has JSDoc
            if (modifiedStateBlock.includes(`/**\n        * ${propName}`)) continue;
            
            const propJSDoc = `        /**
         * ${getPropertyDescription(propName)}
         * @type {${getTypeFromValue(propType)}}
         */\n        `;
            
            const propPosition = modifiedStateBlock.indexOf(propName);
            if (propPosition !== -1) {
                modifiedStateBlock = 
                    modifiedStateBlock.substring(0, propPosition) + 
                    propJSDoc + 
                    modifiedStateBlock.substring(propPosition);
            }
        }
        
        // Replace the state block in the content
        content = content.replace(stateBlock, modifiedStateBlock);
    }
    
    // Process actions
    const actionsMatch = actionPattern.exec(content);
    if (actionsMatch) {
        const actionsBlock = actionsMatch[1];
        let modifiedActionsBlock = actionsBlock;
        
        // Find all functions in the actions block
        let functionMatch;
        const functionRegex = new RegExp(functionPattern);
        while ((functionMatch = functionRegex.exec(actionsBlock)) !== null) {
            const funcName = functionMatch[1];
            
            // Skip if function already has JSDoc
            if (modifiedActionsBlock.includes(`/**\n        * ${funcName}`)) continue;
            
            const funcJSDoc = `        /**
         * ${getActionDescription(funcName)}
         * @param {Object} params - Parameters for the action
         * @returns {void}
         */\n        `;
            
            const funcPosition = modifiedActionsBlock.indexOf(funcName);
            if (funcPosition !== -1) {
                modifiedActionsBlock = 
                    modifiedActionsBlock.substring(0, funcPosition) + 
                    funcJSDoc + 
                    modifiedActionsBlock.substring(funcPosition);
            }
        }
        
        // Replace the actions block in the content
        content = content.replace(actionsBlock, modifiedActionsBlock);
    }
    
    // Process getters
    const gettersMatch = getterPattern.exec(content);
    if (gettersMatch) {
        const gettersBlock = gettersMatch[1];
        let modifiedGettersBlock = gettersBlock;
        
        // Find all functions in the getters block
        let functionMatch;
        const functionRegex = new RegExp(functionPattern);
        while ((functionMatch = functionRegex.exec(gettersBlock)) !== null) {
            const funcName = functionMatch[1];
            
            // Skip if function already has JSDoc
            if (modifiedGettersBlock.includes(`/**\n        * ${funcName}`)) continue;
            
            const funcJSDoc = `        /**
         * ${getGetterDescription(funcName)}
         * @returns {any} The ${funcName.replace(/^get/, '').toLowerCase()}
         */\n        `;
            
            const funcPosition = modifiedGettersBlock.indexOf(funcName);
            if (funcPosition !== -1) {
                modifiedGettersBlock = 
                    modifiedGettersBlock.substring(0, funcPosition) + 
                    funcJSDoc + 
                    modifiedGettersBlock.substring(funcPosition);
            }
        }
        
        // Replace the getters block in the content
        content = content.replace(gettersBlock, modifiedGettersBlock);
    }
    
    return content;
}

/**
 * Adds JSDoc comments to a composable file
 * @param {string} filePath - Path to the composable file
 * @param {string} content - Content of the file
 * @returns {string} - Updated content with JSDoc comments
 */
function addJSDocToComposable(filePath, content) {
    const fileName = path.basename(filePath);
    const composableMatch = composablePattern.exec(content);
    
    if (!composableMatch) return content;
    
    const composableName = composableMatch[1];
    const composableDescription = getComposableDescription(fileName, composableName);
    
    // Check if file already has a JSDoc comment for the composable
    if (!content.includes('/**\n * Composable for')) {
        // Add file-level JSDoc
        const fileJSDoc = `/**
 * @file ${composableDescription}
 * @module ${filePath.replace(baseDir + '/', '').replace('.ts', '')}
 */\n`;
        
        // Find the position to insert the composable JSDoc
        const composableDefPosition = content.indexOf('export function');
        if (composableDefPosition === -1) return fileJSDoc + content;
        
        // Add composable JSDoc
        const composableJSDoc = `/**
 * Composable for ${composableDescription.toLowerCase()}
 * 
 * @description ${composableDescription}
 * @example
 * // Example usage
 * const result = ${composableName}()
 * 
 * @returns {Object} The returned object with its properties
 */\n`;
        
        content = 
            fileJSDoc + 
            content.substring(0, composableDefPosition) + 
            composableJSDoc + 
            content.substring(composableDefPosition);
    }
    
    // Process internal functions
    let functionMatch;
    const functionRegex = new RegExp(functionPattern);
    let modifiedContent = content;
    
    while ((functionMatch = functionRegex.exec(content)) !== null) {
        const funcName = functionMatch[1];
        
        // Skip the main composable function and functions that already have JSDoc
        if (funcName === composableName || 
            modifiedContent.includes(`/**\n    * ${funcName}`) ||
            modifiedContent.includes(`/**\n * ${funcName}`)) continue;
        
        const funcJSDoc = `    /**
     * ${getFunctionDescription(funcName)}
     * @param {any} params - Parameters for the function
     * @returns {any} The result of the function
     */\n    `;
        
        const funcPosition = modifiedContent.indexOf(`function ${funcName}`);
        if (funcPosition !== -1) {
            modifiedContent = 
                modifiedContent.substring(0, funcPosition) + 
                funcJSDoc + 
                modifiedContent.substring(funcPosition);
        } else {
            const constFuncPosition = modifiedContent.indexOf(`const ${funcName}`);
            if (constFuncPosition !== -1) {
                modifiedContent = 
                    modifiedContent.substring(0, constFuncPosition) + 
                    funcJSDoc + 
                    modifiedContent.substring(constFuncPosition);
            }
        }
    }
    
    return modifiedContent;
}

/**
 * Gets a description for a store based on its name
 * @param {string} fileName - Name of the file
 * @param {string} storeName - Name of the store
 * @param {boolean} short - Whether to return a short description
 * @returns {string} - Description of the store
 */
function getStoreDescription(fileName, storeName, short = false) {
    const storeDescriptions = {
        'bibleDataStore.ts': 'Bible data including books, chapters, and verses',
        'bibleHighlightStore.ts': 'Bible text highlighting functionality',
        'bibleMemoryStore.ts': 'Bible memorization features and tracking',
        'bibleSectionStore.ts': 'Bible sections and navigation',
        'searchSettingsStore.ts': 'Search configuration and settings',
        'searchStore.ts': 'Bible search functionality and results',
        'textSettingsStore.ts': 'Text display settings and preferences',
    };
    
    return storeDescriptions[fileName] || 
        (short ? storeName.replace(/-/g, ' ') : `${storeName.replace(/-/g, ' ')} functionality`);
}

/**
 * Gets a description for a composable based on its name
 * @param {string} fileName - Name of the file
 * @param {string} composableName - Name of the composable
 * @returns {string} - Description of the composable
 */
function getComposableDescription(fileName, composableName) {
    const composableDescriptions = {
        'useDebounce.ts': 'Provides debounce functionality to delay function execution',
        'useDropdown.ts': 'Manages dropdown UI component state and interactions',
        'useScrollManager.ts': 'Handles scroll events and position management',
        'useSearchResults.ts': 'Manages search results and search-related functionality',
        'useTextSettings.ts': 'Manages text display settings and preferences',
        'useThrottle.ts': 'Provides throttle functionality to limit function execution frequency',
        'useVerseReference.ts': 'Handles Bible verse references and navigation',
    };
    
    return composableDescriptions[fileName] || 
        `${composableName.replace(/^use/, '')} functionality`;
}

/**
 * Gets a description for a property based on its name
 * @param {string} propName - Name of the property
 * @returns {string} - Description of the property
 */
function getPropertyDescription(propName) {
    const propertyDescriptions = {
        'chapters': 'Map of chapter sections indexed by chapter ID',
        'books': 'Map of Bible books indexed by book ID',
        'bookOrder': 'Array of book slugs in canonical order',
        'loadingQueue': 'Queue of chapter IDs to be loaded',
        'currentViewportChapterId': 'ID of the chapter currently in the viewport',
        'highlights': 'Collection of text highlights',
        'activeHighlightId': 'ID of the currently active highlight',
        'memoryVerses': 'Collection of verses being memorized',
        'memoryStats': 'Statistics about memorization progress',
        'sections': 'Bible sections currently loaded',
        'currentSection': 'Currently active Bible section',
        'searchQuery': 'Current search query string',
        'searchResults': 'Results from the current search',
        'searchFilters': 'Filters applied to the search',
        'fontSize': 'Font size for text display',
        'lineSpacing': 'Line spacing for text display',
        'showVerseNumbers': 'Whether to display verse numbers',
        'showChapterNumbers': 'Whether to display chapter numbers',
        'showFootnotes': 'Whether to display footnotes',
        'darkMode': 'Whether dark mode is enabled',
    };
    
    return propertyDescriptions[propName] || 
        `${propName.replace(/([A-Z])/g, ' $1').toLowerCase()} for the store`;
}

/**
 * Gets a description for an action based on its name
 * @param {string} actionName - Name of the action
 * @returns {string} - Description of the action
 */
function getActionDescription(actionName) {
    const actionDescriptions = {
        'addChapter': 'Adds a chapter section to the store',
        'removeChapter': 'Removes a chapter section from the store',
        'initializeChapters': 'Initializes the store with chapter data',
        'fetchChapters': 'Fetches chapter data from the API',
        'loadBooksData': 'Loads book data from the API',
        'loadAdjacentChapters': 'Loads chapters adjacent to the current chapter',
        'addHighlight': 'Adds a new text highlight',
        'removeHighlight': 'Removes a text highlight',
        'updateHighlight': 'Updates an existing text highlight',
        'addMemoryVerse': 'Adds a verse to the memorization list',
        'removeMemoryVerse': 'Removes a verse from the memorization list',
        'updateMemoryStats': 'Updates memorization statistics',
        'setCurrentSection': 'Sets the current active Bible section',
        'navigateToChapter': 'Navigates to a specific chapter',
        'performSearch': 'Executes a search with the current query and filters',
        'clearSearch': 'Clears the current search results and query',
        'updateSearchFilters': 'Updates the search filters',
        'setFontSize': 'Sets the font size for text display',
        'setLineSpacing': 'Sets the line spacing for text display',
        'toggleVerseNumbers': 'Toggles the display of verse numbers',
        'toggleChapterNumbers': 'Toggles the display of chapter numbers',
        'toggleFootnotes': 'Toggles the display of footnotes',
        'toggleDarkMode': 'Toggles dark mode',
    };
    
    return actionDescriptions[actionName] || 
        `${actionName.replace(/([A-Z])/g, ' $1').toLowerCase()}`;
}

/**
 * Gets a description for a getter based on its name
 * @param {string} getterName - Name of the getter
 * @returns {string} - Description of the getter
 */
function getGetterDescription(getterName) {
    const getterDescriptions = {
        'getCurrentViewportChapter': 'Gets the chapter currently in the viewport',
        'getVisibleChapters': 'Gets all chapters currently visible in the viewport',
        'getBookBySlug': 'Gets a book by its slug',
        'getActiveHighlight': 'Gets the currently active highlight',
        'getHighlightById': 'Gets a highlight by its ID',
        'getMemoryVerseById': 'Gets a memory verse by its ID',
        'getCurrentSection': 'Gets the currently active Bible section',
        'getSearchResults': 'Gets the current search results',
        'getSearchFilters': 'Gets the current search filters',
        'getTextSettings': 'Gets all text display settings',
    };
    
    return getterDescriptions[getterName] || 
        `Gets the ${getterName.replace(/^get/, '').replace(/([A-Z])/g, ' $1').toLowerCase()}`;
}

/**
 * Gets a description for a function based on its name
 * @param {string} funcName - Name of the function
 * @returns {string} - Description of the function
 */
function getFunctionDescription(funcName) {
    const functionDescriptions = {
        'parseVerseReference': 'Parses a verse reference string into its components',
        'navigateToVerse': 'Navigates to a specific verse',
        'formatVerseReference': 'Formats a verse reference object into a string',
        'debounce': 'Debounces a function to limit its execution frequency',
        'throttle': 'Throttles a function to limit its execution frequency',
        'toggleDropdown': 'Toggles the dropdown open/closed state',
        'closeDropdown': 'Closes the dropdown',
        'handleScroll': 'Handles scroll events',
        'getScrollPosition': 'Gets the current scroll position',
        'scrollToElement': 'Scrolls to a specific element',
        'formatSearchResults': 'Formats search results for display',
        'filterSearchResults': 'Filters search results based on criteria',
    };
    
    return functionDescriptions[funcName] || 
        `${funcName.replace(/([A-Z])/g, ' $1').toLowerCase()}`;
}

/**
 * Gets a type from a value
 * @param {string} value - Value to get type from
 * @returns {string} - Type of the value
 */
function getTypeFromValue(value) {
    value = value.trim();
    
    if (value.includes('new Map')) return 'Map<string, any>';
    if (value.includes('[]')) return 'Array<any>';
    if (value.includes('{}')) return 'Object';
    if (value === 'true' || value === 'false') return 'boolean';
    if (value === '0' || value.match(/^\d+$/)) return 'number';
    if (value.match(/^['"].*['"]$/)) return 'string';
    
    return 'any';
}

/**
 * Processes a file by adding JSDoc comments
 * @param {string} filePath - Path to the file
 */
function processFile(filePath) {
    const fullPath = path.join(baseDir, filePath);
    
    // Check if file exists
    if (!fs.existsSync(fullPath)) {
        console.error(`File not found: ${fullPath}`);
        return;
    }
    
    // Read file content
    const content = fs.readFileSync(fullPath, 'utf8');
    
    // Process file based on type
    let updatedContent;
    if (filePath.includes('stores/')) {
        updatedContent = addJSDocToStore(filePath, content);
    } else if (filePath.includes('composables/')) {
        updatedContent = addJSDocToComposable(filePath, content);
    } else {
        console.log(`Skipping file: ${filePath} (unknown type)`);
        return;
    }
    
    // Write updated content back to file
    if (updatedContent !== content) {
        fs.writeFileSync(fullPath, updatedContent);
        console.log(`Updated file: ${filePath}`);
    } else {
        console.log(`No changes needed for: ${filePath}`);
    }
}

// Process all files
console.log('Adding JSDoc annotations to files...');
filesToProcess.forEach(processFile);
console.log('Done!');
