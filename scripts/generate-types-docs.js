#!/usr/bin/env node

/**
 * <PERSON>rip<PERSON> to generate TypeScript documentation for the ESB Online project
 * This script generates comprehensive documentation for all types and enums in the library
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Paths
const rootDir = path.resolve(__dirname, '..');
const libsTypesDir = path.resolve(rootDir, 'libs/types/src');
const libsEnumsDir = path.resolve(rootDir, 'libs/enums/src');
const webTypesDir = path.resolve(rootDir, 'apps/web/resources/js/types');
const libsDocsOutputDir = path.resolve(rootDir, 'docs/libs');
const webDocsOutputDir = path.resolve(rootDir, 'docs/web/types');

// Ensure output directories exist
if (!fs.existsSync(libsDocsOutputDir)) {
  fs.mkdirSync(libsDocsOutputDir, { recursive: true });
}

if (!fs.existsSync(webDocsOutputDir)) {
  fs.mkdirSync(webDocsOutputDir, { recursive: true });
}

/**
 * Read a TypeScript file and extract type definitions
 * @param {string} filePath - Path to the TypeScript file
 * @returns {Object} - Object containing extracted types and interfaces
 */
function extractTypeDefinitions(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const types = [];
  
  // Extract interfaces and types
  const interfaceRegex = /export\s+interface\s+(\w+)(?:<[^>]+>)?\s*(?:extends\s+[^{]+)?\s*{([^}]*)}/gs;
  const typeRegex = /export\s+type\s+(\w+)(?:<[^>]+>)?\s*=\s*([^;]+);/gs;
  
  let match;
  
  // Extract interfaces
  while ((match = interfaceRegex.exec(content)) !== null) {
    const name = match[1];
    const properties = match[2].trim();
    
    types.push({
      name,
      kind: 'interface',
      definition: `interface ${name} {\n${properties}\n}`,
      raw: match[0]
    });
  }
  
  // Extract types
  while ((match = typeRegex.exec(content)) !== null) {
    const name = match[1];
    const definition = match[2].trim();
    
    types.push({
      name,
      kind: 'type',
      definition: `type ${name} = ${definition}`,
      raw: match[0]
    });
  }
  
  return types;
}

/**
 * Extract enum definitions from a file
 * @param {string} filePath - Path to the TypeScript file
 * @returns {Array} - Array of extracted enums
 */
function extractEnumDefinitions(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const enums = [];
  
  // Extract enums
  const enumRegex = /export\s+enum\s+(\w+)\s*{([^}]*)}/gs;
  
  let match;
  while ((match = enumRegex.exec(content)) !== null) {
    const name = match[1];
    const values = match[2].trim();
    
    enums.push({
      name,
      definition: `enum ${name} {\n${values}\n}`,
      raw: match[0]
    });
  }
  
  return enums;
}

/**
 * Recursively scan a directory for TypeScript files
 * @param {string} dir - Directory to scan
 * @returns {Array} - Array of file paths
 */
function scanDirectory(dir) {
  const files = [];
  
  if (!fs.existsSync(dir)) {
    return files;
  }
  
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const itemPath = path.join(dir, item);
    const stat = fs.statSync(itemPath);
    
    if (stat.isDirectory()) {
      files.push(...scanDirectory(itemPath));
    } else if (item.endsWith('.ts') && !item.endsWith('.d.ts') && item !== 'index.ts') {
      files.push(itemPath);
    }
  }
  
  return files;
}

/**
 * Generate documentation for a category of types
 * @param {string} categoryName - Name of the category
 * @param {string} categoryDir - Directory containing the category files
 * @param {string} outputDir - Output directory for documentation
 */
function generateCategoryDocs(categoryName, categoryDir, outputDir) {
  console.log(`Generating documentation for ${categoryName} types...`);
  
  const files = scanDirectory(categoryDir);
  const allTypes = [];
  
  for (const file of files) {
    const types = extractTypeDefinitions(file);
    const relativePath = path.relative(libsTypesDir, file);
    
    for (const type of types) {
      type.file = relativePath;
      allTypes.push(type);
    }
  }
  
  // Create category documentation
  const categoryTitle = categoryName.charAt(0).toUpperCase() + categoryName.slice(1);
  let categoryContent = `# ${categoryTitle} Types\n\nThis module contains types related to ${categoryName} functionality.\n\n`;
  
  // Group types by file
  const typesByFile = {};
  
  for (const type of allTypes) {
    if (!typesByFile[type.file]) {
      typesByFile[type.file] = [];
    }
    
    typesByFile[type.file].push(type);
  }
  
  // Generate documentation for each file
  for (const file in typesByFile) {
    const fileTypes = typesByFile[file];
    const fileTitle = path.basename(file, '.ts');
    
    categoryContent += `## ${fileTitle}\n\n`;
    
    for (const type of fileTypes) {
      categoryContent += `### ${type.name}\n\n\`\`\`typescript\n${type.raw}\n\`\`\`\n\n`;
    }
  }
  
  fs.writeFileSync(path.join(outputDir, `${categoryName}-types.md`), categoryContent);
}

/**
 * Generate documentation for all enums
 * @param {string} enumsDir - Directory containing enum files
 * @param {string} outputDir - Output directory for documentation
 */
function generateEnumsDocs(enumsDir, outputDir) {
  console.log('Generating documentation for enums...');
  
  const files = scanDirectory(enumsDir);
  const allEnums = [];
  
  for (const file of files) {
    const enums = extractEnumDefinitions(file);
    const relativePath = path.relative(libsEnumsDir, file);
    
    for (const enumDef of enums) {
      enumDef.file = relativePath;
      allEnums.push(enumDef);
    }
  }
  
  // Create enums documentation
  let enumsContent = `# Enums\n\nThis module contains all enumerations used throughout the ESB Online application.\n\n`;
  
  for (const enumDef of allEnums) {
    enumsContent += `## ${enumDef.name}\n\n\`\`\`typescript\n${enumDef.raw}\n\`\`\`\n\n`;
  }
  
  fs.writeFileSync(path.join(outputDir, 'enums.md'), enumsContent);
}

/**
 * Generate main overview page with commonly used types
 * @param {string} outputDir - Output directory for documentation
 */
function generateOverviewPage(outputDir) {
  console.log('Generating overview page...');
  
  const overviewContent = `# ESB Online Library Types

This section contains comprehensive documentation for all TypeScript types and enums used in the ESB Online libraries.

## Type Categories

The core library's type system has been optimized for better tree-shakeability with the following structure:

1. **Bible Types**: Types related to Bible content, structure, and metadata
   - [View all Bible Types](bible-types.md)
   - Key types: BaseBook, Book, BookView, Chapter, Verse, Word, WordGroup, Footnote

2. **Common Types**: Types used across multiple modules
   - [View all Common Types](common-types.md)
   - Key types: User, FilterType

3. **Display Types**: Types related to UI and display logic
   - [View all Display Types](display-types.md)
   - Key types: NavigationState, Section, Window, ChapterWindow, FrontmatterWindow

4. **Search Types**: Types related to search functionality
   - [View all Search Types](search-types.md)
   - Key types: SearchQuery, SearchResult, SearchResults

5. **Text Types**: Types related to text formatting and structure
   - [View all Text Types](text-types.md)
   - Key types: Style, Paragraph, Text

## Enums

All enumerations used in the application are documented in the [Enums](enums.md) section.

Key enums include:
- Testament (OT, NT)
- BookCategory (LAW, HISTORY, WISDOM, etc.)
- OriginalLanguage (HEBREW, GREEK, ARAMAIC, MIXED)

## Type Dependencies

The type system is designed with clear dependencies between modules to ensure proper tree-shaking:

- Bible types may depend on Common types
- Display types may depend on Bible and Common types
- Search types may depend on Bible, Common, and Text types
- Text types may depend on Common types

This structure ensures that bundlers can properly tree-shake unused types and enums.
`;

  fs.writeFileSync(path.join(outputDir, 'README.md'), overviewContent);
}

/**
 * Generate sidebar for library types
 * @param {string} outputDir - Output directory for documentation
 */
function generateSidebar(outputDir) {
  console.log('Generating sidebar...');
  
  const sidebarContent = `# Library Types Sidebar

- [Overview](README.md)
- [Bible Types](bible-types.md)
- [Common Types](common-types.md)
- [Display Types](display-types.md)
- [Search Types](search-types.md)
- [Text Types](text-types.md)
- [Enums](enums.md)
`;

  fs.writeFileSync(path.join(outputDir, 'sidebar.md'), sidebarContent);
}

// Generate library types documentation
console.log('Generating library types documentation...');
try {
  // Generate overview page
  generateOverviewPage(libsDocsOutputDir);
  
  // Generate documentation for each category
  generateCategoryDocs('bible', path.join(libsTypesDir, 'bible'), libsDocsOutputDir);
  generateCategoryDocs('common', path.join(libsTypesDir, 'common'), libsDocsOutputDir);
  generateCategoryDocs('display', path.join(libsTypesDir, 'display'), libsDocsOutputDir);
  generateCategoryDocs('search', path.join(libsTypesDir, 'search'), libsDocsOutputDir);
  generateCategoryDocs('text', path.join(libsTypesDir, 'text'), libsDocsOutputDir);
  
  // Generate enums documentation
  generateEnumsDocs(libsEnumsDir, libsDocsOutputDir);
  
  // Generate sidebar
  generateSidebar(libsDocsOutputDir);

  console.log('Library types documentation generated successfully!');
} catch (error) {
  console.error('Error generating library types documentation:', error);
  process.exit(1);
}

// Generate web app types documentation
console.log('Generating web app types documentation...');
try {
  // Create README.md for web app types
  const webReadme = `# ESB Online Web App Types

This section contains documentation for the TypeScript types used in the ESB Online web application.

## Store Types

Types for Pinia stores in the web application.

## Component Types

Types for Vue components in the web application.

## Global Types

Types used throughout the web application.
`;

  fs.writeFileSync(path.resolve(webDocsOutputDir, 'README.md'), webReadme);

  // Generate documentation for store types
  const storeTypesReadme = `# Store Types

This module contains types for Pinia stores in the web application.

## BibleNavigationStore

\`\`\`typescript
interface BibleNavigationStore {
  currentBook: Book | null;
  currentChapter: Chapter | null;
  currentVerse: Verse | null;
  verseReference: string | null;
  isScrollHandlingEnabled: boolean;
  
  // Methods
  setCurrentBook(book: Book): void;
  setCurrentChapter(chapter: Chapter): void;
  setCurrentVerse(verse: Verse): void;
  setVerseReference(reference: string): void;
  enableScrollHandling(): void;
  disableScrollHandling(): void;
  updateCurrentViewportChapter(chapter: Chapter): void;
}
\`\`\`

## BibleDataStore

\`\`\`typescript
interface BibleDataStore {
  books: Book[];
  currentBook: Book | null;
  isLoading: boolean;
  
  // Methods
  loadBooks(): Promise<void>;
  loadBook(slug: string): Promise<Book>;
  loadChapter(bookSlug: string, chapterNumber: number): Promise<Chapter>;
}
\`\`\`
`;

  fs.writeFileSync(path.resolve(webDocsOutputDir, 'store-types.md'), storeTypesReadme);

  // Generate sidebar for web app types
  const webSidebar = `# Web App Types Sidebar

- [Overview](README.md)
- [Store Types](store-types.md)
`;

  fs.writeFileSync(path.resolve(webDocsOutputDir, 'sidebar.md'), webSidebar);

  console.log('Web app types documentation generated successfully!');
} catch (error) {
  console.error('Error generating web app types documentation:', error);
  process.exit(1);
}
