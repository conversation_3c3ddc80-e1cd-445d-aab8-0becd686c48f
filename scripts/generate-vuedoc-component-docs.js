/**
 * Vue component documentation generator using @vuedoc/parser and @vuedoc/md
 * This script attempts to use the original VueDoc.js packages and handles errors gracefully
 */
const fs = require('fs');
const path = require('path');

// Define directories
const webComponentsDir = path.resolve(__dirname, '../apps/web/resources/js/Components');
const webOutputDir = path.resolve(__dirname, '../docs/web/components');

// Create output directories if they don't exist
if (!fs.existsSync(webOutputDir)) {
    fs.mkdirSync(webOutputDir, { recursive: true });
}

// German translations for category names and labels
const translations = {
    'BibleDisplay': 'Bibelanzeige',
    'Navigation': 'Navigation',
    'Search': 'Suche',
    'Common': 'Allgemein',
    'Components': 'Komponenten',
    'Props': 'Eigenschaften',
    'Methods': 'Methoden',
    'Events': 'Ereignisse',
    'Source': 'Quellcode',
    'Example': 'Beispiel',
    'Template Structure': 'Template-Struktur',
    'Name': 'Name',
    'Type': 'Typ',
    'Default': 'Standard',
    'Required': 'Erforderlich',
    'Description': 'Beschreibung',
    'Parameters': 'Parameter',
    'Returns': 'Rückgabewert',
    'This section contains documentation for the following components': 'Dieser Abschnitt enthält Dokumentation für die folgenden Komponenten',
    'No components found in this category': 'Keine Komponenten in dieser Kategorie gefunden',
    'The ESB Online web application uses Vue.js components organized into the following categories': 'Die ESB Online Webanwendung verwendet Vue.js-Komponenten, die in die folgenden Kategorien unterteilt sind',
    'This category contains the following components': 'Diese Kategorie enthält die folgenden Komponenten',
    'No description available': 'Keine Beschreibung verfügbar',
    'Web Components': 'Web-Komponenten',
    'Yes': 'Ja',
    'No': 'Nein',
    'Usage': 'Verwendung',
    'Component Structure': 'Komponentenstruktur',
    'Script': 'Skript',
    'Style': 'Stil',
    'Computed Properties': 'Berechnete Eigenschaften',
    'Lifecycle Hooks': 'Lebenszyklus-Hooks',
    'Watch': 'Beobachter',
    'Data': 'Daten',
    'Slots': 'Slots',
    'Dependencies': 'Abhängigkeiten'
};

/**
 * Translate a text to German
 * @param {string} text - Text to translate
 * @returns {string} - Translated text
 */
function translate(text) {
    return translations[text] || text;
}

/**
 * Process a Vue component file using @vuedoc/parser and @vuedoc/md
 * @param {string} filePath - Path to Vue component file
 * @param {string} outputPath - Path to output markdown file
 * @param {string} componentName - Name of the component
 */
async function processComponentWithVueDoc(filePath, outputPath, componentName) {
    try {
        // Dynamically import @vuedoc/parser and @vuedoc/md
        const { parseComponent } = require('@vuedoc/parser');
        const { generateMarkdown } = require('@vuedoc/md');
        
        const component = await parseComponent({
            filename: filePath,
            encoding: 'utf8',
        });

        const markdown = await generateMarkdown(component, {
            filename: outputPath,
        });

        fs.writeFileSync(outputPath, markdown);
        console.log(`Generated VueDoc documentation for ${componentName}`);
        return true;
    } catch (err) {
        console.error(`Error processing component ${componentName} with VueDoc:`, err.message);
        return false;
    }
}

/**
 * Extract JSDoc comments from a string
 * @param {string} content - Content to extract JSDoc from
 * @returns {Object} - Object with extracted JSDoc information
 */
function extractJSDocComments(content) {
    const jsdocRegex = /\/\*\*\s*([\s\S]*?)\s*\*\//g;
    const comments = [];
    let match;
    
    while ((match = jsdocRegex.exec(content)) !== null) {
        const comment = match[1]
            .replace(/\s*\*\s*/g, ' ')
            .trim();
        comments.push(comment);
    }
    
    return comments;
}

/**
 * Extract computed properties from script content
 * @param {string} scriptContent - Script content
 * @returns {Array} - Array of computed property objects
 */
function extractComputedProperties(scriptContent) {
    const computedMatch = scriptContent.match(/computed\s*:\s*{([^}]*)}/);
    const computed = [];
    
    if (computedMatch && computedMatch[1]) {
        const computedContent = computedMatch[1];
        const propertyRegex = /(\w+)\s*\([^)]*\)\s*{([^}]*)}/g;
        let propMatch;
        
        while ((propMatch = propertyRegex.exec(computedContent)) !== null) {
            computed.push({
                name: propMatch[1],
                body: propMatch[2].trim()
            });
        }
    }
    
    return computed;
}

/**
 * Extract lifecycle hooks from script content
 * @param {string} scriptContent - Script content
 * @returns {Array} - Array of lifecycle hook objects
 */
function extractLifecycleHooks(scriptContent) {
    const lifecycleHooks = ['created', 'mounted', 'beforeMount', 'beforeCreate', 'beforeUpdate', 'updated', 'beforeUnmount', 'unmounted'];
    const hooks = [];
    
    lifecycleHooks.forEach(hook => {
        const hookRegex = new RegExp(`${hook}\\s*\\([^)]*\\)\\s*{([^}]*)}`, 'g');
        let hookMatch;
        
        while ((hookMatch = hookRegex.exec(scriptContent)) !== null) {
            hooks.push({
                name: hook,
                body: hookMatch[1].trim()
            });
        }
    });
    
    return hooks;
}

/**
 * Generate fallback documentation for a component
 * @param {string} filePath - Path to Vue component file
 * @param {string} outputPath - Path to output markdown file
 * @param {string} componentName - Name of the component
 */
function generateFallbackDocs(filePath, outputPath, componentName) {
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        
        // Extract component description from comments
        let description = translate('No description available');
        const jsdocComments = extractJSDocComments(content);
        if (jsdocComments.length > 0) {
            description = jsdocComments[0];
        }
        
        // Extract template
        let templateSection = '';
        const templateMatch = content.match(/<template>([\s\S]*?)<\/template>/);
        
        if (templateMatch && templateMatch[1]) {
            const template = templateMatch[1].trim();
            templateSection = `## ${translate('Template Structure')}\n\n`;
            templateSection += '```html\n' + template + '\n```\n\n';
        }
        
        // Extract script
        let scriptContent = '';
        let scriptSection = '';
        const scriptMatch = content.match(/<script.*?>([\s\S]*?)<\/script>/);
        
        if (scriptMatch && scriptMatch[1]) {
            scriptContent = scriptMatch[1].trim();
            scriptSection = `## ${translate('Script')}\n\n`;
            scriptSection += '```js\n' + scriptContent + '\n```\n\n';
        }
        
        // Extract props
        let propsSection = '';
        const propsMatch = content.match(/props\s*:\s*{([^}]*)}/);
        
        if (propsMatch && propsMatch[1]) {
            const propsContent = propsMatch[1];
            const propEntries = propsContent.split(',').filter(prop => prop.trim());
            
            if (propEntries.length > 0) {
                propsSection = `## ${translate('Props')}\n\n`;
                propsSection += `| ${translate('Name')} | ${translate('Type')} | ${translate('Default')} | ${translate('Required')} | ${translate('Description')} |\n`;
                propsSection += '|------|------|---------|----------|-------------|\n';
                
                propEntries.forEach(propEntry => {
                    const propMatch = propEntry.match(/(\w+)\s*:/);
                    if (propMatch && propMatch[1]) {
                        const propName = propMatch[1];
                        const typeMatch = propEntry.match(/type\s*:\s*(\w+)/);
                        const type = typeMatch ? typeMatch[1] : 'Any';
                        const defaultMatch = propEntry.match(/default\s*:\s*([^,]*)/);
                        const defaultValue = defaultMatch ? defaultMatch[1].trim() : '-';
                        const requiredMatch = propEntry.match(/required\s*:\s*(true|false)/);
                        const required = requiredMatch ? (requiredMatch[1] === 'true' ? translate('Yes') : translate('No')) : translate('No');
                        
                        // Look for JSDoc for this prop
                        let propDescription = '-';
                        const propJSDocRegex = new RegExp(`@prop\\s*{[^}]*}\\s*${propName}\\s*(.*)`, 'i');
                        jsdocComments.forEach(comment => {
                            const match = comment.match(propJSDocRegex);
                            if (match && match[1]) {
                                propDescription = match[1].trim();
                            }
                        });
                        
                        propsSection += `| \`${propName}\` | \`${type}\` | ${defaultValue} | ${required} | ${propDescription} |\n`;
                    }
                });
            }
        }
        
        // Extract computed properties
        const computed = extractComputedProperties(scriptContent);
        let computedSection = '';
        
        if (computed.length > 0) {
            computedSection = `## ${translate('Computed Properties')}\n\n`;
            
            computed.forEach(prop => {
                computedSection += `### \`${prop.name}\`\n\n`;
                
                // Look for JSDoc for this computed prop
                let propDescription = '-';
                const propJSDocRegex = new RegExp(`@computed\\s*${prop.name}\\s*(.*)`, 'i');
                jsdocComments.forEach(comment => {
                    const match = comment.match(propJSDocRegex);
                    if (match && match[1]) {
                        propDescription = match[1].trim();
                    }
                });
                
                computedSection += `${propDescription}\n\n`;
                computedSection += '```js\n' + prop.body + '\n```\n\n';
            });
        }
        
        // Extract lifecycle hooks
        const hooks = extractLifecycleHooks(scriptContent);
        let hooksSection = '';
        
        if (hooks.length > 0) {
            hooksSection = `## ${translate('Lifecycle Hooks')}\n\n`;
            
            hooks.forEach(hook => {
                hooksSection += `### \`${hook.name}()\`\n\n`;
                hooksSection += '```js\n' + hook.body + '\n```\n\n';
            });
        }
        
        // Extract methods
        let methodsSection = '';
        const methodsMatch = content.match(/methods\s*:\s*{([^}]*)}/);
        
        if (methodsMatch && methodsMatch[1]) {
            const methodsContent = methodsMatch[1];
            const methodRegex = /(\w+)\s*\([^)]*\)\s*{([^}]*)}/g;
            let methodMatch;
            
            methodsSection = `## ${translate('Methods')}\n\n`;
            
            while ((methodMatch = methodRegex.exec(methodsContent)) !== null) {
                const methodName = methodMatch[1];
                const methodBody = methodMatch[2].trim();
                
                methodsSection += `### \`${methodName}()\`\n\n`;
                
                // Look for JSDoc for this method
                let methodDescription = '-';
                let methodParams = [];
                let methodReturns = '-';
                
                const methodJSDocRegex = new RegExp(`@method\\s*${methodName}\\s*(.*)`, 'i');
                const paramRegex = /@param\\s*{([^}]*)}\\s*([^\s]*)\\s*(.*)/g;
                const returnsRegex = /@returns\\s*{([^}]*)}\\s*(.*)/;
                
                jsdocComments.forEach(comment => {
                    const match = comment.match(methodJSDocRegex);
                    if (match) {
                        methodDescription = match[1] ? match[1].trim() : '-';
                        
                        let paramMatch;
                        while ((paramMatch = paramRegex.exec(comment)) !== null) {
                            methodParams.push({
                                type: paramMatch[1],
                                name: paramMatch[2],
                                description: paramMatch[3] ? paramMatch[3].trim() : '-'
                            });
                        }
                        
                        const returnsMatch = comment.match(returnsRegex);
                        if (returnsMatch) {
                            methodReturns = `\`${returnsMatch[1]}\` ${returnsMatch[2] ? returnsMatch[2].trim() : ''}`;
                        }
                    }
                });
                
                methodsSection += `- ${translate('Description')}: ${methodDescription}\n`;
                
                if (methodParams.length > 0) {
                    methodsSection += `- ${translate('Parameters')}:\n`;
                    methodParams.forEach(param => {
                        methodsSection += `  - \`${param.name}\` (\`${param.type}\`): ${param.description}\n`;
                    });
                } else {
                    methodsSection += `- ${translate('Parameters')}: -\n`;
                }
                
                methodsSection += `- ${translate('Returns')}: ${methodReturns}\n\n`;
                methodsSection += '```js\n' + methodBody + '\n```\n\n';
            }
        }
        
        // Extract emits
        let emitsSection = '';
        const emitsMatch = content.match(/emits\s*:\s*\[(.*?)\]/);
        
        if (emitsMatch && emitsMatch[1]) {
            const emits = emitsMatch[1].split(',').map(emit => emit.trim().replace(/['"]/g, ''));
            
            if (emits.length > 0) {
                emitsSection = `## ${translate('Events')}\n\n`;
                emitsSection += `| ${translate('Name')} | ${translate('Description')} |\n`;
                emitsSection += '|------|-------------|\n';
                
                emits.forEach(emit => {
                    // Look for JSDoc for this emit
                    let emitDescription = '-';
                    const emitJSDocRegex = new RegExp(`@emit\\s*${emit}\\s*(.*)`, 'i');
                    jsdocComments.forEach(comment => {
                        const match = comment.match(emitJSDocRegex);
                        if (match && match[1]) {
                            emitDescription = match[1].trim();
                        }
                    });
                    
                    emitsSection += `| \`${emit}\` | ${emitDescription} |\n`;
                });
                
                emitsSection += '\n';
            }
        }
        
        // Extract style
        let styleSection = '';
        const styleMatch = content.match(/<style.*?>([\s\S]*?)<\/style>/);
        
        if (styleMatch && styleMatch[1]) {
            const style = styleMatch[1].trim();
            if (style.length > 0) {
                styleSection = `## ${translate('Style')}\n\n`;
                styleSection += '```css\n' + style + '\n```\n\n';
            }
        }
        
        // Create usage example
        let usageSection = `## ${translate('Usage')}\n\n`;
        usageSection += '```vue\n<template>\n';
        usageSection += `  <${componentName}`;
        
        // Add props to usage example
        if (propsMatch && propsMatch[1]) {
            const propsContent = propsMatch[1];
            const propEntries = propsContent.split(',').filter(prop => prop.trim());
            
            propEntries.forEach(propEntry => {
                const propMatch = propEntry.match(/(\w+)\s*:/);
                if (propMatch && propMatch[1]) {
                    const propName = propMatch[1];
                    const typeMatch = propEntry.match(/type\s*:\s*(\w+)/);
                    const type = typeMatch ? typeMatch[1] : 'Any';
                    
                    if (type === 'Boolean') {
                        usageSection += `\n    :${propName}="true"`;
                    } else if (type === 'Number') {
                        usageSection += `\n    :${propName}="1"`;
                    } else if (type === 'Array') {
                        usageSection += `\n    :${propName}="[]"`;
                    } else if (type === 'Object') {
                        usageSection += `\n    :${propName}="{}"`;
                    } else {
                        usageSection += `\n    ${propName}="value"`;
                    }
                }
            });
        }
        
        usageSection += ' />\n</template>\n```\n\n';
        
        // Create component structure overview
        let structureSection = `## ${translate('Component Structure')}\n\n`;
        structureSection += '```\n';
        structureSection += `${componentName}\n`;
        structureSection += '├── Props\n';
        
        if (propsMatch && propsMatch[1]) {
            const propsContent = propsMatch[1];
            const propEntries = propsContent.split(',').filter(prop => prop.trim());
            
            propEntries.forEach(propEntry => {
                const propMatch = propEntry.match(/(\w+)\s*:/);
                if (propMatch && propMatch[1]) {
                    structureSection += `│   └── ${propMatch[1]}\n`;
                }
            });
        }
        
        if (computed.length > 0) {
            structureSection += '├── Computed\n';
            computed.forEach(prop => {
                structureSection += `│   └── ${prop.name}\n`;
            });
        }
        
        if (methodsMatch && methodsMatch[1]) {
            structureSection += '├── Methods\n';
            const methodsContent = methodsMatch[1];
            const methodRegex = /(\w+)\s*\([^)]*\)/g;
            let methodMatch;
            
            while ((methodMatch = methodRegex.exec(methodsContent)) !== null) {
                structureSection += `│   └── ${methodMatch[1]}()\n`;
            }
        }
        
        if (emitsMatch && emitsMatch[1]) {
            structureSection += '└── Events\n';
            const emits = emitsMatch[1].split(',').map(emit => emit.trim().replace(/['"]/g, ''));
            
            emits.forEach(emit => {
                structureSection += `    └── ${emit}\n`;
            });
        }
        
        structureSection += '```\n\n';
        
        // Create markdown documentation
        let markdown = `---
id: ${componentName}
title: ${componentName}
---

# ${componentName}

${description}

${structureSection}

${usageSection}

${propsSection}

${computedSection}

${methodsSection}

${emitsSection}

${hooksSection}

${templateSection}

${styleSection}
`;
        
        fs.writeFileSync(outputPath, markdown);
        console.log(`Generated fallback documentation for ${componentName}`);
        return true;
    } catch (error) {
        console.error(`Error generating fallback documentation for ${componentName}:`, error);
        return false;
    }
}

/**
 * Create category index file
 * @param {string} categoryDir - Directory of the category
 * @param {string} categoryName - Name of the category
 * @param {Array<string>} components - List of components in the category
 */
function createCategoryIndex(categoryDir, categoryName, components) {
    const indexPath = path.join(categoryDir, 'index.md');
    const translatedCategoryName = translate(categoryName);
    
    let content = `---
id: index
title: ${translatedCategoryName} ${translate('Components')}
sidebar_position: 1
---

# ${translatedCategoryName} ${translate('Components')}

`;

    if (components.length > 0) {
        content += `${translate('This section contains documentation for the following components')}:\n\n`;
        
        components.forEach(component => {
            content += `- [${component}](./${component}.md)\n`;
        });
    } else {
        content += `${translate('No components found in this category')}.`;
    }
    
    fs.writeFileSync(indexPath, content);
    console.log(`Created index for ${categoryName} components`);
}

/**
 * Create main index file for web components
 * @param {Object} categories - Categories and their components
 */
function createMainIndex(categories) {
    const indexPath = path.join(webOutputDir, 'index.md');
    
    let content = `---
id: index
title: ${translate('Web Components')}
sidebar_position: 1
---

# ${translate('Web Components')}

${translate('The ESB Online web application uses Vue.js components organized into the following categories')}:

`;

    Object.keys(categories).forEach(category => {
        const translatedCategory = translate(category);
        content += `## ${translatedCategory}\n\n`;
        
        if (categories[category].length > 0) {
            content += `${translate('This category contains the following components')}:\n\n`;
            
            categories[category].forEach(component => {
                content += `- [${component}](./${category.toLowerCase()}/${component}.md)\n`;
            });
            
            content += '\n';
        } else {
            content += `${translate('No components found in this category')}.\n\n`;
        }
    });
    
    fs.writeFileSync(indexPath, content);
    console.log('Created main index for web components');
}

/**
 * Process components in a directory
 * @param {string} directory - Directory containing components
 * @param {string} categoryName - Name of the category
 */
async function processComponentCategory(directory, categoryName) {
    if (!fs.existsSync(directory)) {
        console.log(`Directory ${directory} does not exist. Skipping.`);
        return [];
    }
    
    const categoryDir = path.join(webOutputDir, categoryName.toLowerCase());
    if (!fs.existsSync(categoryDir)) {
        fs.mkdirSync(categoryDir, { recursive: true });
    }
    
    const components = [];
    
    try {
        const files = fs.readdirSync(directory);
        
        // Process Vue files
        for (const file of files.filter(f => f.endsWith('.vue'))) {
            const filePath = path.join(directory, file);
            const componentName = path.basename(file, '.vue');
            const outputPath = path.join(categoryDir, `${componentName}.md`);
            
            // Try to process with VueDoc.js first, fall back to custom parser if it fails
            const vueDocSuccess = await processComponentWithVueDoc(filePath, outputPath, componentName);
            
            if (!vueDocSuccess) {
                console.log(`Falling back to custom parser for ${componentName}`);
                generateFallbackDocs(filePath, outputPath, componentName);
            }
            
            components.push(componentName);
        }
        
        // Create category index
        createCategoryIndex(categoryDir, categoryName, components);
        
        return components;
    } catch (error) {
        console.error(`Error processing directory ${directory}:`, error);
        return [];
    }
}

// Main execution
async function main() {
    console.log('Generating component documentation with VueDoc.js (with fallback)...');

    const categories = {
        'BibleDisplay': [],
        'Navigation': [],
        'Search': [],
        'Common': []
    };

    // Process each category
    categories.BibleDisplay = await processComponentCategory(path.join(webComponentsDir, 'BibleDisplay'), 'BibleDisplay');
    categories.Navigation = await processComponentCategory(path.join(webComponentsDir, 'Navigation'), 'Navigation');
    categories.Search = await processComponentCategory(path.join(webComponentsDir, 'Search'), 'Search');
    categories.Common = await processComponentCategory(path.join(webComponentsDir, 'common'), 'Common');

    // Create main index
    createMainIndex(categories);

    console.log('Component documentation generation complete!');
}

main().catch(error => {
    console.error('Error generating component documentation:', error);
    process.exit(1);
});
