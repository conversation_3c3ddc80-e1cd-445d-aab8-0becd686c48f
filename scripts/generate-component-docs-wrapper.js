/**
 * Wrapper script for component documentation generation that bypasses the @vuedoc/parser dependency issue
 * 
 * This script creates a simplified version of the component documentation generation
 * that doesn't rely on @vuedoc/parser, which has dependency issues with @b613/utils.
 */
const fs = require('fs');
const path = require('path');

// Define directories
const webComponentsDir = path.resolve(__dirname, '../apps/web/resources/js/Components');
const webOutputDir = path.resolve(__dirname, '../docs/components/web');
const libsComponentsDir = path.resolve(__dirname, '../libs');
const libsOutputDir = path.resolve(__dirname, '../docs/components/libs');

// Create output directories if they don't exist
[webOutputDir, libsOutputDir].forEach(dir => {
    if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
    }
});

/**
 * Generate simple component documentation for a Vue file
 * @param {string} filePath - Path to the Vue file
 * @returns {string} - Markdown documentation
 */
function generateSimpleComponentDocs(filePath, componentName) {
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        
        // Extract component description from comments
        let description = 'No description available.';
        const descriptionMatch = content.match(/\/\*\*\s*([\s\S]*?)\s*\*\//);
        if (descriptionMatch && descriptionMatch[1]) {
            description = descriptionMatch[1]
                .replace(/\s*\*\s*/g, ' ')
                .trim();
        }
        
        // Extract props
        const propsMatch = content.match(/props\s*:\s*{([^}]*)}/);
        let propsSection = '';
        
        if (propsMatch && propsMatch[1]) {
            const propsContent = propsMatch[1];
            const propEntries = propsContent.split(',').filter(prop => prop.trim());
            
            if (propEntries.length > 0) {
                propsSection = '## Props\n\n';
                propsSection += '| Name | Type | Default | Description |\n';
                propsSection += '|------|------|---------|-------------|\n';
                
                propEntries.forEach(propEntry => {
                    const propMatch = propEntry.match(/(\w+)\s*:/);
                    if (propMatch && propMatch[1]) {
                        const propName = propMatch[1];
                        const typeMatch = propEntry.match(/type\s*:\s*(\w+)/);
                        const type = typeMatch ? typeMatch[1] : 'Any';
                        const defaultMatch = propEntry.match(/default\s*:\s*([^,]*)/);
                        const defaultValue = defaultMatch ? defaultMatch[1].trim() : '-';
                        
                        propsSection += `| ${propName} | ${type} | ${defaultValue} | - |\n`;
                    }
                });
            }
        }
        
        // Extract methods
        const methodsMatch = content.match(/methods\s*:\s*{([^}]*)}/);
        let methodsSection = '';
        
        if (methodsMatch && methodsMatch[1]) {
            const methodsContent = methodsMatch[1];
            const methodEntries = methodsContent.match(/(\w+)\s*\([^)]*\)\s*{/g);
            
            if (methodEntries && methodEntries.length > 0) {
                methodsSection = '## Methods\n\n';
                methodsSection += '| Name | Description |\n';
                methodsSection += '|------|-------------|\n';
                
                methodEntries.forEach(methodEntry => {
                    const methodMatch = methodEntry.match(/(\w+)\s*\(/);
                    if (methodMatch && methodMatch[1]) {
                        const methodName = methodMatch[1];
                        methodsSection += `| ${methodName} | - |\n`;
                    }
                });
            }
        }
        
        // Create markdown documentation
        return `---
id: ${componentName}
title: ${componentName}
---

# ${componentName}

${description}

${propsSection}

${methodsSection}

## Source

\`\`\`vue
${content}
\`\`\`
`;
    } catch (error) {
        console.error(`Error generating documentation for ${filePath}:`, error);
        return `---
id: ${componentName}
title: ${componentName}
---

# ${componentName}

Error generating documentation for this component.
`;
    }
}

/**
 * Process Vue components in a directory
 * @param {string} directory - Directory containing Vue components
 * @param {string} outputDir - Directory to output documentation
 */
function processComponents(directory, outputDir) {
    if (!fs.existsSync(directory)) {
        console.log(`Directory ${directory} does not exist. Skipping.`);
        return;
    }
    
    try {
        const files = fs.readdirSync(directory);
        
        // Process Vue files
        files.filter(file => file.endsWith('.vue')).forEach(file => {
            const filePath = path.join(directory, file);
            const componentName = path.basename(file, '.vue');
            const outputPath = path.join(outputDir, `${componentName}.md`);
            
            const markdown = generateSimpleComponentDocs(filePath, componentName);
            fs.writeFileSync(outputPath, markdown);
            console.log(`Generated documentation for ${componentName}`);
        });
        
        // Process subdirectories
        files.forEach(file => {
            const fullPath = path.join(directory, file);
            if (fs.statSync(fullPath).isDirectory()) {
                const subOutputDir = path.join(outputDir, file);
                if (!fs.existsSync(subOutputDir)) {
                    fs.mkdirSync(subOutputDir, { recursive: true });
                }
                processComponents(fullPath, subOutputDir);
            }
        });
    } catch (error) {
        console.error(`Error processing directory ${directory}:`, error);
    }
}

// Process web components
console.log('Processing web components...');
processComponents(webComponentsDir, webOutputDir);

// Process libs components recursively
console.log('Processing library components...');
if (fs.existsSync(libsComponentsDir)) {
    const libDirs = fs.readdirSync(libsComponentsDir);
    
    libDirs.forEach(libDir => {
        const libPath = path.join(libsComponentsDir, libDir);
        if (fs.statSync(libPath).isDirectory()) {
            // Look for components or src directory
            const componentsDir = path.join(libPath, 'components');
            const srcDir = path.join(libPath, 'src');
            
            if (fs.existsSync(componentsDir)) {
                const libOutputDir = path.join(libsOutputDir, libDir);
                if (!fs.existsSync(libOutputDir)) {
                    fs.mkdirSync(libOutputDir, { recursive: true });
                }
                processComponents(componentsDir, libOutputDir);
            }
            
            if (fs.existsSync(srcDir)) {
                const libOutputDir = path.join(libsOutputDir, libDir);
                if (!fs.existsSync(libOutputDir)) {
                    fs.mkdirSync(libOutputDir, { recursive: true });
                }
                processComponents(srcDir, libOutputDir);
            }
        }
    });
}

console.log('Component documentation generation complete!');
