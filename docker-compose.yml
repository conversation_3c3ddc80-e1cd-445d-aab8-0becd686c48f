services:
  web:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        APP_DIR: apps/web
    ports:
      - "8000:80"
    volumes:
      - ./apps/web:/var/www/html/apps/web
    env_file:
      - .env
      - ./apps/web/.env
    environment:
      - APP_ENV=local
      - DB_HOST=mysql
      - REDIS_HOST=redis
      - MEILISEARCH_HOST=http://meilisearch:7700
    depends_on:
      - mysql
      - redis
      - meilisearch

  # editor:
  #   build:
  #     context: .
  #     dockerfile: Dockerfile
  #     args:
  #       APP_DIR: apps/editor
  #   ports:
  #     - "8001:80"
  #   volumes:
  #     - ./apps/editor:/var/www/html/apps/editor
  #   env_file:
  #     - .env
  #     - ./apps/editor/.env
  #   environment:
  #     - APP_ENV=local
  #     - DB_HOST=mysql
  #     - REDIS_HOST=redis
  #     - MEILISEARCH_HOST=http://meilisearch:7700
  #   depends_on:
  #     - mysql
  #     - redis
  #     - meilisearch

  # mobile-builder:
  #   image: node:20
  #   working_dir: /app
  #   volumes:
  #     - ./apps/mobile:/app
  #   command: yarn install && yarn build

  # desktop-builder:
  #   image: node:20
  #   working_dir: /app
  #   volumes:
  #     - ./apps/desktop:/app
  #   command: yarn install && yarn build

  mysql:
    image: mysql:8.0
    ports:
      - "3306:3306"
    environment:
      MYSQL_DATABASE: ${DB_DATABASE}
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD}
    volumes:
      - mysql_data:/var/lib/mysql

  postgres:
    image: postgres:17
    ports:
      - "5432:5432"
    environment:
      POSTGRES_DB: ${DB_DATABASE}
      POSTGRES_USER: ${DB_USERNAME}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - pg_data:/var/lib/postgresql/data

  redis:
    image: redis:alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  meilisearch:
    image: getmeili/meilisearch:v1.14
    ports:
      - "7700:7700"
    environment:
      MEILI_MASTER_KEY: ${MEILISEARCH_KEY}
    volumes:
      - meilisearch_data:/meili_data

volumes:
  mysql_data:
  redis_data:
  meilisearch_data:
  pg_data:
