---
id: configuration
title: Konfiguration
sidebar_position: 3
---

# Konfiguration

Diese Anleitung beschreibt die Konfigurationsoptionen für die ESB Online-Plattform.

## Webanwendungskonfiguration

Die Konfiguration der Webanwendung wird in der `.env`-Datei im Verzeichnis `apps/web` gespeichert. Hier sind die wichtigsten Konfigurationsoptionen:

### Datenbankkonfiguration

```
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=esb_online
DB_USERNAME=root
DB_PASSWORD=
```

### Anwendungskonfiguration

```
APP_NAME="ESB Online"
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost
```

### Bibeldaten-Konfiguration

```
BIBLE_DATA_PATH=/path/to/bible/data
BIBLE_DEFAULT_TRANSLATION=ESB
```

## Frontend-Konfiguration

Die Frontend-Konfiguration wird über Umgebungsvariablen in der `.env`-Datei und über die Vue-Anwendungskonfiguration verwaltet.

### Vue-Anwendung

Die Konfiguration der Vue-Anwendung ist in `apps/web/resources/js/config.ts` definiert:

```typescript
export default {
  apiBaseUrl: '/api',
  defaultTranslation: 'ESB',
  defaultBook: 'GEN',
  defaultChapter: 1,
};
```

## TypeScript-Konfiguration

Die TypeScript-Konfiguration wird über die `tsconfig.json`-Dateien im Projekt verwaltet:

- Root-Konfiguration: `tsconfig.base.json`
- Webanwendungskonfiguration: `apps/web/tsconfig.json`
- Bibliothekskonfiguration: `libs/*/tsconfig.json`

## Dokumentationskonfiguration

Die Dokumentation wird über folgende Dateien konfiguriert:

- Docusaurus-Konfiguration: `docs/docusaurus.config.ts`
- TypeScript-Dokumentationsskript: `scripts/generate-types-docs.js`
- Scribe-Konfiguration: `apps/web/config/scribe.php`

## Nächste Schritte

Nach der Konfiguration der Plattform können Sie mit der Erkundung der [Webanwendungsdokumentation](/web/intro) oder der [API-Referenz](/web/api/intro) fortfahren.
