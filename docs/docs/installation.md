---
id: installation
title: Installation
sidebar_position: 2
---

# Installation

Diese Anleitung beschreibt den Installationsprozess für die ESB Online-Plattform.

## Voraussetzungen

Bevor Sie ESB Online installieren, stellen <PERSON>her, dass Sie folgende Voraussetzungen erfüllen:

- PHP 8.3 oder höher
- Node.js 18.0 oder höher
- Yarn 4.6 oder höher
- MySQL oder PostgreSQL Datenbank
- Composer

## Installationsschritte

### 1. Repository klonen

```bash
git clone https://gitlab.com/esra-bibel/esb-online.git
cd esb-online
```

### 2. Abhängigkeiten installieren

```bash
# Yarn-Abhängigkeiten installieren
yarn install

# Composer-Abhängigkeiten für die Web-App installieren
cd apps/web
composer install
```

### 3. Umgebung konfigurieren

```bash
# Beispiel-Umgebungsdatei kopieren
cp apps/web/.env.example apps/web/.env

# Anwendungsschlüssel generieren
cd apps/web
php artisan key:generate
```

Bearbeiten Sie die `.env`-Datei, um Ihre Datenbankverbindung und andere Einstellungen zu konfigurieren.

### 4. Migrationen ausführen

```bash
cd apps/web
php artisan migrate
```

### 5. Assets erstellen

```bash
# Vom Projekt-Root aus
yarn web:build
```

### 6. Entwicklungsserver starten

```bash
# Vom Projekt-Root aus
yarn web:dev
```

## Nächste Schritte

Nach der Installation können Sie mit der [Konfiguration](/docs/configuration) fortfahren, um die Plattform nach Ihren Bedürfnissen einzurichten.
