---
id: intro
title: ESB Online Dokumentation
sidebar_position: 1
---

# ESB Online Dokumentation

Willkommen zur umfassenden Dokumentation für die ESB Online-Plattform. Diese Dokumentation umfasst alle Aspekte der Plattform, einschließlich der Webanwendung, API, Komponenten und gemeinsam genutzten Bibliotheken.

## Dokumentationsstruktur

Die Dokumentation ist in mehrere Abschnitte gegliedert:

- **Anleitung**: Allgemeine Informationen über die Plattform
- **Web-App**: Dokumentation für die Laravel + Vue 3 Webanwendung
- **API**: API-Referenzdokumentation
- **Komponenten**: Vue-Komponentendokumentation
- **Bibliotheken**: Dokumentation für gemeinsam genutzte Bibliotheken

## Erste Schritte

Um mit der ESB Online-Plattform zu beginnen, beachten Sie bitte die Installations- und Konfigurationsanleitungen:

- [Installation](/docs/installation)
- [Konfiguration](/docs/configuration)

## Entwicklung

Für Entwicklungsinformationen beachten Sie bitte die folgenden Abschnitte:

- [Web-App-Entwicklung](/web/intro)
- [API-Entwicklung](/web/api/intro)
- [Komponentenentwicklung](/components/intro)
- [Bibliotheksentwicklung](/libs/intro)

## Mitwirken

Wir begrüßen Beiträge zur ESB Online-Plattform. Bitte beachten Sie die [Anleitung zum Mitwirken](/docs/intro#mitwirken) für weitere Informationen.
