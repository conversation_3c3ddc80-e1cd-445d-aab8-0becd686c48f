# ESB Online Dokumentation

Dieses Verzeichnis enthält die Dokumentation für die ESB Online-Plattform, erstellt mit [Docusaurus](https://docusaurus.io/).

## Struktur

Die Dokumentation ist in mehrere Abschnitte gegliedert:

- **Allgemeine Dokumentation**: Grundlegende Anleitungen und Informationen über die Plattform
- **Web App**: Dokumentation für die Laravel + Vue 3 Webanwendung
  - **API**: API-Referenzdokumentation für die Webanwendung
- **Komponenten**: Vue-Komponentendokumentation
- **Bibliotheken**: Dokumentation für gemeinsam genutzte Bibliotheken

## Entwicklung

Um den Dokumentations-Entwicklungsserver zu starten:

```bash
# Vom Root des Monorepos
yarn docs:dev

# Oder innerhalb des docs-Verzeichnisses
cd docs && yarn start
```

## Build-Prozess

Um die Dokumentation für die Produktion zu erstellen:

```bash
# Vom Root des Monorepos
yarn docs:full

# Dies wird:
# 1. Komponentendokumentation generieren
# 2. TypeScript-Dokumentation generieren
# 3. API-Dokumentation generieren
# 4. Die Docusaurus-Seite bauen
```

## Aktualisieren der API-Dokumentation

Wenn Änderungen an der API in der Webanwendung vorgenommen werden, müssen Sie die API-Dokumentation aktualisieren. Es gibt zwei Möglichkeiten, dies zu tun:

### Option 1: Vom Verzeichnis der Webanwendung aus

```bash
cd apps/web
yarn docs:api
```

### Option 2: Vom Root des Monorepos aus

```bash
yarn docs:api
```

Dies wird:
1. API-Dokumentation mit Scribe generieren
2. Die generierte Dokumentation in das Docusaurus-Static-Verzeichnis kopieren
3. Sicherstellen, dass das Verzeichnis web/api für Markdown-Dateien existiert

## Hinzufügen neuer Dokumentation

### Hinzufügen neuer Markdown-Dateien

1. Erstellen Sie eine neue `.md`-Datei im entsprechenden Verzeichnis:
   - `/docs/docs/` für allgemeine Dokumentation
   - `/docs/web/` für Webanwendungsdokumentation
   - `/docs/web/api/` für API-Dokumentation der Webanwendung
   - `/docs/components/` für Komponentendokumentation
   - `/docs/libs/` für Bibliotheksdokumentation

2. Fügen Sie Frontmatter am Anfang der Datei hinzu:

```md
---
id: unique-id
title: Seitentitel
sidebar_position: 2
---

# Seitentitel

Inhalt kommt hier...
```

3. Aktualisieren Sie bei Bedarf die entsprechende Sidebar-Datei:
   - `sidebars.ts` für allgemeine Dokumentation
   - `sidebars-web.ts` für Webanwendungsdokumentation
   - `sidebars-components.ts` für Komponentendokumentation
   - `sidebars-libs.ts` für Bibliotheksdokumentation

## Barrierefreiheit

Die gesamte Dokumentation sollte den Best Practices für Barrierefreiheit folgen:

- Verwenden Sie eine korrekte Überschriftenhierarchie (h1, h2, h3 usw.)
- Fügen Sie Alternativtext zu Bildern hinzu
- Verwenden Sie semantisches HTML
- Stellen Sie ausreichenden Farbkontrast sicher

## Testen

Vor der Bereitstellung sollten Sie die Dokumentationsseite testen:

1. Dokumentation erstellen: `yarn docs:full`
2. Lokalen Server starten: `cd docs && yarn serve`
3. Auf fehlerhafte Links und Bilder prüfen
4. Überprüfen, ob alle Abschnitte zugänglich und korrekt formatiert sind
