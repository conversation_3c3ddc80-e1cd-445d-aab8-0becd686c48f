import React from 'react';
import ComponentCreator from '@docusaurus/ComponentCreator';

export default [
  {
    path: '/en/components',
    component: ComponentCreator('/en/components', '359'),
    routes: [
      {
        path: '/en/components',
        component: ComponentCreator('/en/components', 'e2c'),
        routes: [
          {
            path: '/en/components',
            component: ComponentCreator('/en/components', 'e6b'),
            routes: [
              {
                path: '/en/components/intro',
                component: ComponentCreator('/en/components/intro', '86b'),
                exact: true,
                sidebar: "componentsSidebar"
              },
              {
                path: '/en/components/web/BibleDisplay/ChapterContent',
                component: ComponentCreator('/en/components/web/BibleDisplay/ChapterContent', '24b'),
                exact: true
              },
              {
                path: '/en/components/web/BibleDisplay/ChapterNumber',
                component: ComponentCreator('/en/components/web/BibleDisplay/ChapterNumber', '954'),
                exact: true
              },
              {
                path: '/en/components/web/BibleDisplay/ChapterWrapper',
                component: ComponentCreator('/en/components/web/BibleDisplay/ChapterWrapper', '5b1'),
                exact: true
              },
              {
                path: '/en/components/web/BibleDisplay/FootnoteContent',
                component: ComponentCreator('/en/components/web/BibleDisplay/FootnoteContent', 'a4d'),
                exact: true
              },
              {
                path: '/en/components/web/BibleDisplay/FootnoteTooltip',
                component: ComponentCreator('/en/components/web/BibleDisplay/FootnoteTooltip', '9d4'),
                exact: true
              },
              {
                path: '/en/components/web/BibleDisplay/FrontMatter',
                component: ComponentCreator('/en/components/web/BibleDisplay/FrontMatter', '362'),
                exact: true
              },
              {
                path: '/en/components/web/BibleDisplay/InfoItem',
                component: ComponentCreator('/en/components/web/BibleDisplay/InfoItem', 'd90'),
                exact: true
              },
              {
                path: '/en/components/web/BibleDisplay/InfoSection',
                component: ComponentCreator('/en/components/web/BibleDisplay/InfoSection', '54a'),
                exact: true
              },
              {
                path: '/en/components/web/BibleDisplay/NumberSelector',
                component: ComponentCreator('/en/components/web/BibleDisplay/NumberSelector', 'a8c'),
                exact: true
              },
              {
                path: '/en/components/web/BibleDisplay/ReferenceSelector',
                component: ComponentCreator('/en/components/web/BibleDisplay/ReferenceSelector', 'c88'),
                exact: true
              },
              {
                path: '/en/components/web/BibleDisplay/TextFormatDropdown',
                component: ComponentCreator('/en/components/web/BibleDisplay/TextFormatDropdown', '670'),
                exact: true
              },
              {
                path: '/en/components/web/BibleDisplay/UnavailableBookNotice',
                component: ComponentCreator('/en/components/web/BibleDisplay/UnavailableBookNotice', '559'),
                exact: true
              },
              {
                path: '/en/components/web/BibleDisplay/VerseContent',
                component: ComponentCreator('/en/components/web/BibleDisplay/VerseContent', 'a0b'),
                exact: true
              },
              {
                path: '/en/components/web/BibleDisplay/VerseNumber',
                component: ComponentCreator('/en/components/web/BibleDisplay/VerseNumber', '672'),
                exact: true
              },
              {
                path: '/en/components/web/BibleDisplay/WordGroupContainer',
                component: ComponentCreator('/en/components/web/BibleDisplay/WordGroupContainer', 'ee1'),
                exact: true
              },
              {
                path: '/en/components/web/common/ApplicationLogo',
                component: ComponentCreator('/en/components/web/common/ApplicationLogo', 'ac7'),
                exact: true
              },
              {
                path: '/en/components/web/common/Checkbox',
                component: ComponentCreator('/en/components/web/common/Checkbox', '3c4'),
                exact: true
              },
              {
                path: '/en/components/web/common/DangerButton',
                component: ComponentCreator('/en/components/web/common/DangerButton', 'd8f'),
                exact: true
              },
              {
                path: '/en/components/web/common/Dropdown',
                component: ComponentCreator('/en/components/web/common/Dropdown', 'a6d'),
                exact: true
              },
              {
                path: '/en/components/web/common/DropdownLink',
                component: ComponentCreator('/en/components/web/common/DropdownLink', '608'),
                exact: true
              },
              {
                path: '/en/components/web/common/ErrorBoundary',
                component: ComponentCreator('/en/components/web/common/ErrorBoundary', '984'),
                exact: true
              },
              {
                path: '/en/components/web/common/InputError',
                component: ComponentCreator('/en/components/web/common/InputError', 'c8c'),
                exact: true
              },
              {
                path: '/en/components/web/common/InputLabel',
                component: ComponentCreator('/en/components/web/common/InputLabel', 'be4'),
                exact: true
              },
              {
                path: '/en/components/web/common/LoadingSpinner',
                component: ComponentCreator('/en/components/web/common/LoadingSpinner', '708'),
                exact: true
              },
              {
                path: '/en/components/web/common/Modal',
                component: ComponentCreator('/en/components/web/common/Modal', '054'),
                exact: true
              },
              {
                path: '/en/components/web/common/Overlay',
                component: ComponentCreator('/en/components/web/common/Overlay', '617'),
                exact: true
              },
              {
                path: '/en/components/web/common/PrimaryButton',
                component: ComponentCreator('/en/components/web/common/PrimaryButton', '059'),
                exact: true
              },
              {
                path: '/en/components/web/common/SecondaryButton',
                component: ComponentCreator('/en/components/web/common/SecondaryButton', '052'),
                exact: true
              },
              {
                path: '/en/components/web/common/TextInput',
                component: ComponentCreator('/en/components/web/common/TextInput', 'a9e'),
                exact: true
              },
              {
                path: '/en/components/web/Icons/Icon',
                component: ComponentCreator('/en/components/web/Icons/Icon', 'e08'),
                exact: true
              },
              {
                path: '/en/components/web/Icons/TextSettings',
                component: ComponentCreator('/en/components/web/Icons/TextSettings', '12a'),
                exact: true
              },
              {
                path: '/en/components/web/Navigation/BibleBookDropdown',
                component: ComponentCreator('/en/components/web/Navigation/BibleBookDropdown', '5d1'),
                exact: true
              },
              {
                path: '/en/components/web/Navigation/BibleBookOffcanvas',
                component: ComponentCreator('/en/components/web/Navigation/BibleBookOffcanvas', 'bb9'),
                exact: true
              },
              {
                path: '/en/components/web/Navigation/BibleBookSelector',
                component: ComponentCreator('/en/components/web/Navigation/BibleBookSelector', '1ee'),
                exact: true
              },
              {
                path: '/en/components/web/Navigation/MobileSearchOverlay',
                component: ComponentCreator('/en/components/web/Navigation/MobileSearchOverlay', '965'),
                exact: true
              },
              {
                path: '/en/components/web/Navigation/NavigationBar',
                component: ComponentCreator('/en/components/web/Navigation/NavigationBar', '8fa'),
                exact: true
              },
              {
                path: '/en/components/web/Navigation/NavLink',
                component: ComponentCreator('/en/components/web/Navigation/NavLink', '952'),
                exact: true
              },
              {
                path: '/en/components/web/Navigation/OffcanvasSidebar',
                component: ComponentCreator('/en/components/web/Navigation/OffcanvasSidebar', 'bf9'),
                exact: true
              },
              {
                path: '/en/components/web/Navigation/ReferenceSelector',
                component: ComponentCreator('/en/components/web/Navigation/ReferenceSelector', '765'),
                exact: true
              },
              {
                path: '/en/components/web/Navigation/ResponsiveNavLink',
                component: ComponentCreator('/en/components/web/Navigation/ResponsiveNavLink', '494'),
                exact: true
              },
              {
                path: '/en/components/web/Navigation/SubNavigationBar',
                component: ComponentCreator('/en/components/web/Navigation/SubNavigationBar', '7ed'),
                exact: true
              },
              {
                path: '/en/components/web/Search/BibleSearch',
                component: ComponentCreator('/en/components/web/Search/BibleSearch', '3fd'),
                exact: true
              },
              {
                path: '/en/components/web/Search/SearchCategoryToggle',
                component: ComponentCreator('/en/components/web/Search/SearchCategoryToggle', 'e10'),
                exact: true
              },
              {
                path: '/en/components/web/Search/SearchResultItem',
                component: ComponentCreator('/en/components/web/Search/SearchResultItem', '4ae'),
                exact: true
              },
              {
                path: '/en/components/web/Search/SearchTypeDropdown',
                component: ComponentCreator('/en/components/web/Search/SearchTypeDropdown', 'ab0'),
                exact: true
              },
              {
                path: '/en/components/web/Settings/SettingsAside',
                component: ComponentCreator('/en/components/web/Settings/SettingsAside', 'e7e'),
                exact: true
              },
              {
                path: '/en/components/web/Settings/TextDisplaySettings',
                component: ComponentCreator('/en/components/web/Settings/TextDisplaySettings', '743'),
                exact: true
              },
              {
                path: '/en/components/web/Settings/ThemeSettings',
                component: ComponentCreator('/en/components/web/Settings/ThemeSettings', '55e'),
                exact: true
              },
              {
                path: '/en/components/web/Settings/VisibilitySettings',
                component: ComponentCreator('/en/components/web/Settings/VisibilitySettings', 'ded'),
                exact: true
              }
            ]
          }
        ]
      }
    ]
  },
  {
    path: '/en/docs',
    component: ComponentCreator('/en/docs', 'ff8'),
    routes: [
      {
        path: '/en/docs',
        component: ComponentCreator('/en/docs', '9e8'),
        routes: [
          {
            path: '/en/docs',
            component: ComponentCreator('/en/docs', '70a'),
            routes: [
              {
                path: '/en/docs/configuration',
                component: ComponentCreator('/en/docs/configuration', '857'),
                exact: true,
                sidebar: "tutorialSidebar"
              },
              {
                path: '/en/docs/installation',
                component: ComponentCreator('/en/docs/installation', '7a4'),
                exact: true,
                sidebar: "tutorialSidebar"
              },
              {
                path: '/en/docs/intro',
                component: ComponentCreator('/en/docs/intro', '6d2'),
                exact: true,
                sidebar: "tutorialSidebar"
              }
            ]
          }
        ]
      }
    ]
  },
  {
    path: '/en/libs',
    component: ComponentCreator('/en/libs', 'db5'),
    routes: [
      {
        path: '/en/libs',
        component: ComponentCreator('/en/libs', '882'),
        routes: [
          {
            path: '/en/libs',
            component: ComponentCreator('/en/libs', '213'),
            routes: [
              {
                path: '/en/libs/',
                component: ComponentCreator('/en/libs/', '07c'),
                exact: true,
                sidebar: "libsSidebar"
              },
              {
                path: '/en/libs/bible-types',
                component: ComponentCreator('/en/libs/bible-types', '35a'),
                exact: true,
                sidebar: "libsSidebar"
              },
              {
                path: '/en/libs/common-types',
                component: ComponentCreator('/en/libs/common-types', 'a56'),
                exact: true,
                sidebar: "libsSidebar"
              },
              {
                path: '/en/libs/display-types',
                component: ComponentCreator('/en/libs/display-types', '0ee'),
                exact: true,
                sidebar: "libsSidebar"
              },
              {
                path: '/en/libs/enums',
                component: ComponentCreator('/en/libs/enums', '883'),
                exact: true,
                sidebar: "libsSidebar"
              },
              {
                path: '/en/libs/intro',
                component: ComponentCreator('/en/libs/intro', '1d5'),
                exact: true,
                sidebar: "libsSidebar"
              },
              {
                path: '/en/libs/search-types',
                component: ComponentCreator('/en/libs/search-types', '9c5'),
                exact: true,
                sidebar: "libsSidebar"
              },
              {
                path: '/en/libs/sidebar',
                component: ComponentCreator('/en/libs/sidebar', '3af'),
                exact: true
              },
              {
                path: '/en/libs/text-types',
                component: ComponentCreator('/en/libs/text-types', '4f8'),
                exact: true,
                sidebar: "libsSidebar"
              }
            ]
          }
        ]
      }
    ]
  },
  {
    path: '/en/web',
    component: ComponentCreator('/en/web', '588'),
    routes: [
      {
        path: '/en/web',
        component: ComponentCreator('/en/web', 'f62'),
        routes: [
          {
            path: '/en/web',
            component: ComponentCreator('/en/web', '0cf'),
            routes: [
              {
                path: '/en/web/api/authentication',
                component: ComponentCreator('/en/web/api/authentication', '27e'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/api/endpoints',
                component: ComponentCreator('/en/web/api/endpoints', 'aeb'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/api/full-documentation',
                component: ComponentCreator('/en/web/api/full-documentation', 'ccd'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/api/intro',
                component: ComponentCreator('/en/web/api/intro', '197'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/bible-navigation-flow',
                component: ComponentCreator('/en/web/bible-navigation-flow', '8e6'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/components/',
                component: ComponentCreator('/en/web/components/', '9b9'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/components/bibledisplay/',
                component: ComponentCreator('/en/web/components/bibledisplay/', 'fe9'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/components/bibledisplay/ChapterContent',
                component: ComponentCreator('/en/web/components/bibledisplay/ChapterContent', '8c1'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/components/bibledisplay/ChapterNumber',
                component: ComponentCreator('/en/web/components/bibledisplay/ChapterNumber', '452'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/components/bibledisplay/ChapterWrapper',
                component: ComponentCreator('/en/web/components/bibledisplay/ChapterWrapper', '3a8'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/components/bibledisplay/FootnoteContent',
                component: ComponentCreator('/en/web/components/bibledisplay/FootnoteContent', '0f6'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/components/bibledisplay/FootnoteTooltip',
                component: ComponentCreator('/en/web/components/bibledisplay/FootnoteTooltip', '5b4'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/components/bibledisplay/FrontMatter',
                component: ComponentCreator('/en/web/components/bibledisplay/FrontMatter', 'eba'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/components/bibledisplay/InfoItem',
                component: ComponentCreator('/en/web/components/bibledisplay/InfoItem', '3ba'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/components/bibledisplay/InfoSection',
                component: ComponentCreator('/en/web/components/bibledisplay/InfoSection', '885'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/components/bibledisplay/NumberSelector',
                component: ComponentCreator('/en/web/components/bibledisplay/NumberSelector', 'df9'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/components/bibledisplay/ReferenceSelector',
                component: ComponentCreator('/en/web/components/bibledisplay/ReferenceSelector', '976'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/components/bibledisplay/TextFormatDropdown',
                component: ComponentCreator('/en/web/components/bibledisplay/TextFormatDropdown', '548'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/components/bibledisplay/UnavailableBookNotice',
                component: ComponentCreator('/en/web/components/bibledisplay/UnavailableBookNotice', 'db5'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/components/bibledisplay/VerseContent',
                component: ComponentCreator('/en/web/components/bibledisplay/VerseContent', 'd39'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/components/bibledisplay/VerseNumber',
                component: ComponentCreator('/en/web/components/bibledisplay/VerseNumber', 'b2a'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/components/bibledisplay/WordGroupContainer',
                component: ComponentCreator('/en/web/components/bibledisplay/WordGroupContainer', 'a2f'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/components/common/',
                component: ComponentCreator('/en/web/components/common/', '4ce'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/components/common/ApplicationLogo',
                component: ComponentCreator('/en/web/components/common/ApplicationLogo', 'f24'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/components/common/Checkbox',
                component: ComponentCreator('/en/web/components/common/Checkbox', '272'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/components/common/DangerButton',
                component: ComponentCreator('/en/web/components/common/DangerButton', 'fc6'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/components/common/Dropdown',
                component: ComponentCreator('/en/web/components/common/Dropdown', '50e'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/components/common/DropdownLink',
                component: ComponentCreator('/en/web/components/common/DropdownLink', 'baa'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/components/common/ErrorBoundary',
                component: ComponentCreator('/en/web/components/common/ErrorBoundary', 'fe1'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/components/common/InputError',
                component: ComponentCreator('/en/web/components/common/InputError', '419'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/components/common/InputLabel',
                component: ComponentCreator('/en/web/components/common/InputLabel', '9ef'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/components/common/LoadingSpinner',
                component: ComponentCreator('/en/web/components/common/LoadingSpinner', '0bc'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/components/common/Modal',
                component: ComponentCreator('/en/web/components/common/Modal', 'caa'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/components/common/Overlay',
                component: ComponentCreator('/en/web/components/common/Overlay', '1be'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/components/common/PrimaryButton',
                component: ComponentCreator('/en/web/components/common/PrimaryButton', '58d'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/components/common/SecondaryButton',
                component: ComponentCreator('/en/web/components/common/SecondaryButton', '644'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/components/common/TextInput',
                component: ComponentCreator('/en/web/components/common/TextInput', 'fba'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/components/intro',
                component: ComponentCreator('/en/web/components/intro', 'c44'),
                exact: true
              },
              {
                path: '/en/web/components/navigation/',
                component: ComponentCreator('/en/web/components/navigation/', '086'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/components/navigation/BibleBookDropdown',
                component: ComponentCreator('/en/web/components/navigation/BibleBookDropdown', 'bbd'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/components/navigation/BibleBookOffcanvas',
                component: ComponentCreator('/en/web/components/navigation/BibleBookOffcanvas', '8e4'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/components/navigation/BibleBookSelector',
                component: ComponentCreator('/en/web/components/navigation/BibleBookSelector', '7f5'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/components/navigation/MobileSearchOverlay',
                component: ComponentCreator('/en/web/components/navigation/MobileSearchOverlay', '683'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/components/navigation/NavigationBar',
                component: ComponentCreator('/en/web/components/navigation/NavigationBar', '036'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/components/navigation/NavLink',
                component: ComponentCreator('/en/web/components/navigation/NavLink', '773'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/components/navigation/OffcanvasSidebar',
                component: ComponentCreator('/en/web/components/navigation/OffcanvasSidebar', '1fe'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/components/navigation/ReferenceSelector',
                component: ComponentCreator('/en/web/components/navigation/ReferenceSelector', 'b9a'),
                exact: true
              },
              {
                path: '/en/web/components/navigation/ResponsiveNavLink',
                component: ComponentCreator('/en/web/components/navigation/ResponsiveNavLink', '8ba'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/components/navigation/SubNavigationBar',
                component: ComponentCreator('/en/web/components/navigation/SubNavigationBar', 'cea'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/components/search/',
                component: ComponentCreator('/en/web/components/search/', 'c30'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/components/search/BibleSearch',
                component: ComponentCreator('/en/web/components/search/BibleSearch', '1b4'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/components/search/SearchCategoryToggle',
                component: ComponentCreator('/en/web/components/search/SearchCategoryToggle', '160'),
                exact: true
              },
              {
                path: '/en/web/components/search/SearchResultItem',
                component: ComponentCreator('/en/web/components/search/SearchResultItem', 'ce7'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/components/search/SearchTypeDropdown',
                component: ComponentCreator('/en/web/components/search/SearchTypeDropdown', '65b'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/composables/',
                component: ComponentCreator('/en/web/composables/', 'ab7'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/composables/intro',
                component: ComponentCreator('/en/web/composables/intro', '365'),
                exact: true
              },
              {
                path: '/en/web/composables/useDebounce',
                component: ComponentCreator('/en/web/composables/useDebounce', '9d5'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/composables/useDropdown',
                component: ComponentCreator('/en/web/composables/useDropdown', '489'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/composables/useLocalStorage',
                component: ComponentCreator('/en/web/composables/useLocalStorage', 'f3a'),
                exact: true
              },
              {
                path: '/en/web/composables/useScrollManager',
                component: ComponentCreator('/en/web/composables/useScrollManager', '5e1'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/composables/useSearchResults',
                component: ComponentCreator('/en/web/composables/useSearchResults', '8b0'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/composables/useSettingsAside',
                component: ComponentCreator('/en/web/composables/useSettingsAside', '457'),
                exact: true
              },
              {
                path: '/en/web/composables/useTextSettings',
                component: ComponentCreator('/en/web/composables/useTextSettings', '9ac'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/composables/useThrottle',
                component: ComponentCreator('/en/web/composables/useThrottle', '951'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/composables/useVerseReference',
                component: ComponentCreator('/en/web/composables/useVerseReference', 'e03'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/intro',
                component: ComponentCreator('/en/web/intro', '11d'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/stores/',
                component: ComponentCreator('/en/web/stores/', '092'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/stores/bibleDataStore',
                component: ComponentCreator('/en/web/stores/bibleDataStore', '820'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/stores/bibleHighlightStore',
                component: ComponentCreator('/en/web/stores/bibleHighlightStore', '8c6'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/stores/bibleMemoryStore',
                component: ComponentCreator('/en/web/stores/bibleMemoryStore', '9aa'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/stores/bibleSectionStore',
                component: ComponentCreator('/en/web/stores/bibleSectionStore', '75d'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/stores/intro',
                component: ComponentCreator('/en/web/stores/intro', '3d7'),
                exact: true
              },
              {
                path: '/en/web/stores/searchSettingsStore',
                component: ComponentCreator('/en/web/stores/searchSettingsStore', 'd4d'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/stores/searchStore',
                component: ComponentCreator('/en/web/stores/searchStore', '112'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/stores/textSettingsStore',
                component: ComponentCreator('/en/web/stores/textSettingsStore', 'd03'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/types/',
                component: ComponentCreator('/en/web/types/', 'f63'),
                exact: true,
                sidebar: "webSidebar"
              },
              {
                path: '/en/web/types/bible-navigation-store',
                component: ComponentCreator('/en/web/types/bible-navigation-store', '90a'),
                exact: true
              },
              {
                path: '/en/web/types/intro',
                component: ComponentCreator('/en/web/types/intro', '7df'),
                exact: true
              },
              {
                path: '/en/web/types/sidebar',
                component: ComponentCreator('/en/web/types/sidebar', 'de3'),
                exact: true
              },
              {
                path: '/en/web/types/store-types',
                component: ComponentCreator('/en/web/types/store-types', 'a0f'),
                exact: true,
                sidebar: "webSidebar"
              }
            ]
          }
        ]
      }
    ]
  },
  {
    path: '/en/',
    component: ComponentCreator('/en/', '509'),
    exact: true
  },
  {
    path: '*',
    component: ComponentCreator('*'),
  },
];
