{"id": "intro", "title": "Gemeinsame Bibliotheken", "description": "Diese Dokumentation beschreibt die gemeinsam genutzten Bibliotheken (libs) der ESB Online Plattform. Diese Bibliotheken stellen wiederverwendbare Funktionen, Typen und Konstanten bereit, die von verschiedenen Anwendungen innerhalb des Monorepos genutzt werden.", "source": "@site/libs/intro.md", "sourceDirName": ".", "slug": "/intro", "permalink": "/libs/intro", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 1, "frontMatter": {"id": "intro", "title": "Gemeinsame Bibliotheken", "sidebar_position": 1}, "sidebar": "libsSidebar", "next": {"title": "ESB Online Library Types", "permalink": "/libs/"}}