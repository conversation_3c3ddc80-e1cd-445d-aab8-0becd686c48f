{"id": "intro", "title": "Shared Libraries", "description": "This documentation describes the shared libraries (libs) of the ESB Online platform. These libraries provide reusable functions, types, and constants that are used by various applications within the monorepo.", "source": "@site/i18n/en/docusaurus-plugin-content-docs-libs/current/intro.md", "sourceDirName": ".", "slug": "/intro", "permalink": "/en/libs/intro", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 1, "frontMatter": {"id": "intro", "title": "Shared Libraries", "sidebar_position": 1}, "sidebar": "libsSidebar", "next": {"title": "ESB Online Library Types", "permalink": "/en/libs/"}}