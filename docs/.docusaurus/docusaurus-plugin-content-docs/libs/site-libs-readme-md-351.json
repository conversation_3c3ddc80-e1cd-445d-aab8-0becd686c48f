{"id": "README", "title": "ESB Online Library Types", "description": "This section contains comprehensive documentation for all TypeScript types and enums used in the ESB Online libraries.", "source": "@site/libs/README.md", "sourceDirName": ".", "slug": "/", "permalink": "/en/libs/", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {}, "sidebar": "libsSidebar", "previous": {"title": "Shared Libraries", "permalink": "/en/libs/intro"}, "next": {"title": "Bible Types", "permalink": "/en/libs/bible-types"}}