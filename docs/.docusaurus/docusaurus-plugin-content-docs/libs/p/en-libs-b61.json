{"version": {"pluginId": "libs", "version": "current", "label": "Next", "banner": null, "badge": false, "noIndex": false, "className": "docs-version-current", "isLast": true, "docsSidebars": {"libsSidebar": [{"type": "category", "label": "Bibliotheken", "items": [{"type": "link", "label": "Shared Libraries", "href": "/en/libs/intro", "docId": "intro", "unlisted": false}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "Typen", "items": [{"type": "link", "label": "ESB Online Library Types", "href": "/en/libs/", "docId": "README", "unlisted": false}, {"type": "category", "label": "Bibel-Typen", "items": [{"type": "link", "label": "Bible Types", "href": "/en/libs/bible-types", "docId": "bible-types", "unlisted": false}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "Allgemeine Typen", "items": [{"type": "link", "label": "Common Types", "href": "/en/libs/common-types", "docId": "common-types", "unlisted": false}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "Anzeige-Typen", "items": [{"type": "link", "label": "Display Types", "href": "/en/libs/display-types", "docId": "display-types", "unlisted": false}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "Such-Typen", "items": [{"type": "link", "label": "Search Types", "href": "/en/libs/search-types", "docId": "search-types", "unlisted": false}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "Text-Typen", "items": [{"type": "link", "label": "Text Types", "href": "/en/libs/text-types", "docId": "text-types", "unlisted": false}], "collapsed": true, "collapsible": true}, {"type": "link", "label": "Enums", "href": "/en/libs/enums", "docId": "enums", "unlisted": false}], "collapsed": true, "collapsible": true}]}, "docs": {"bible-types": {"id": "bible-types", "title": "Bible Types", "description": "This module contains types related to bible functionality.", "sidebar": "libsSidebar"}, "common-types": {"id": "common-types", "title": "Common Types", "description": "This module contains types related to common functionality.", "sidebar": "libsSidebar"}, "display-types": {"id": "display-types", "title": "Display Types", "description": "This module contains types related to display functionality.", "sidebar": "libsSidebar"}, "enums": {"id": "enums", "title": "Enums", "description": "This module contains all enumerations used throughout the ESB Online application.", "sidebar": "libsSidebar"}, "intro": {"id": "intro", "title": "Shared Libraries", "description": "This documentation describes the shared libraries (libs) of the ESB Online platform. These libraries provide reusable functions, types, and constants that are used by various applications within the monorepo.", "sidebar": "libsSidebar"}, "README": {"id": "README", "title": "ESB Online Library Types", "description": "This section contains comprehensive documentation for all TypeScript types and enums used in the ESB Online libraries.", "sidebar": "libsSidebar"}, "search-types": {"id": "search-types", "title": "Search Types", "description": "This module contains types related to search functionality.", "sidebar": "libsSidebar"}, "sidebar": {"id": "sidebar", "title": "Library Types Sidebar", "description": "- Overview"}, "text-types": {"id": "text-types", "title": "Text Types", "description": "This module contains types related to text functionality.", "sidebar": "libsSidebar"}}}}