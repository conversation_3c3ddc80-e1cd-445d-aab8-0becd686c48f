{"id": "intro", "title": "<PERSON><PERSON>", "description": "Diese Dokumentation beschreibt die Vue 3 Komponenten, die in der ESB Online Plattform verwendet werden. Die Komponenten sind in TypeScript geschrieben und nutzen die Composition API.", "source": "@site/components/intro.md", "sourceDirName": ".", "slug": "/intro", "permalink": "/components/intro", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 1, "frontMatter": {"id": "intro", "title": "<PERSON><PERSON>", "sidebar_position": 1}, "sidebar": "componentsSidebar"}