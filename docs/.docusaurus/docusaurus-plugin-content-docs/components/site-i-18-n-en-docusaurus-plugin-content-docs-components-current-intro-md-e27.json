{"id": "intro", "title": "Vue Components", "description": "This documentation describes the Vue 3 components used in the ESB Online platform. The components are written in TypeScript and use the Composition API.", "source": "@site/i18n/en/docusaurus-plugin-content-docs-components/current/intro.md", "sourceDirName": ".", "slug": "/intro", "permalink": "/en/components/intro", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 1, "frontMatter": {"id": "intro", "title": "Vue Components", "sidebar_position": 1}, "sidebar": "componentsSidebar"}