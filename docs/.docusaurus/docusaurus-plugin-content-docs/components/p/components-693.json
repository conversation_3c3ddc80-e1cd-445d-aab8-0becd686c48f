{"version": {"pluginId": "components", "version": "current", "label": "Next", "banner": null, "badge": false, "noIndex": false, "className": "docs-version-current", "isLast": true, "docsSidebars": {"componentsSidebar": [{"type": "category", "label": "Vue Components", "items": [{"type": "link", "label": "<PERSON><PERSON>", "href": "/components/intro", "docId": "intro", "unlisted": false}], "collapsed": true, "collapsible": true}]}, "docs": {"intro": {"id": "intro", "title": "<PERSON><PERSON>", "description": "Diese Dokumentation beschreibt die Vue 3 Komponenten, die in der ESB Online Plattform verwendet werden. Die Komponenten sind in TypeScript geschrieben und nutzen die Composition API.", "sidebar": "componentsSidebar"}, "web/BibleDisplay/ChapterContent": {"id": "web/BibleDisplay/ChapterContent", "title": "ChapterContent", "description": "No description available."}, "web/BibleDisplay/ChapterNumber": {"id": "web/BibleDisplay/ChapterNumber", "title": "ChapterNumber", "description": "No description available."}, "web/BibleDisplay/ChapterWrapper": {"id": "web/BibleDisplay/ChapterWrapper", "title": "ChapterWrapper", "description": "No description available."}, "web/BibleDisplay/FootnoteContent": {"id": "web/BibleDisplay/FootnoteContent", "title": "FootnoteContent", "description": "No description available."}, "web/BibleDisplay/FootnoteTooltip": {"id": "web/BibleDisplay/FootnoteTooltip", "title": "FootnoteTooltip", "description": "No description available."}, "web/BibleDisplay/FrontMatter": {"id": "web/BibleDisplay/FrontMatter", "title": "FrontMatter", "description": "No description available."}, "web/BibleDisplay/InfoItem": {"id": "web/BibleDisplay/InfoItem", "title": "InfoItem", "description": "No description available."}, "web/BibleDisplay/InfoSection": {"id": "web/BibleDisplay/InfoSection", "title": "InfoSection", "description": "No description available."}, "web/BibleDisplay/NumberSelector": {"id": "web/BibleDisplay/NumberSelector", "title": "NumberSelector", "description": "No description available."}, "web/BibleDisplay/ReferenceSelector": {"id": "web/BibleDisplay/ReferenceSelector", "title": "ReferenceSelector", "description": "No description available."}, "web/BibleDisplay/TextFormatDropdown": {"id": "web/BibleDisplay/TextFormatDropdown", "title": "TextFormatDropdown", "description": "No description available."}, "web/BibleDisplay/UnavailableBookNotice": {"id": "web/BibleDisplay/UnavailableBookNotice", "title": "UnavailableBookNotice", "description": "No description available."}, "web/BibleDisplay/VerseContent": {"id": "web/BibleDisplay/VerseContent", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "No description available."}, "web/BibleDisplay/VerseNumber": {"id": "web/BibleDisplay/VerseNumber", "title": "VerseNumber", "description": "No description available."}, "web/BibleDisplay/WordGroupContainer": {"id": "web/BibleDisplay/WordGroupContainer", "title": "WordGroupContainer", "description": "Returns the word type for data attribute"}, "web/common/ApplicationLogo": {"id": "web/common/ApplicationLogo", "title": "ApplicationLogo", "description": "No description available."}, "web/common/Checkbox": {"id": "web/common/Checkbox", "title": "Checkbox", "description": "No description available."}, "web/common/DangerButton": {"id": "web/common/DangerButton", "title": "DangerButton", "description": "No description available."}, "web/common/Dropdown": {"id": "web/common/Dropdown", "title": "Dropdown", "description": "No description available."}, "web/common/DropdownLink": {"id": "web/common/DropdownLink", "title": "DropdownLink", "description": "No description available."}, "web/common/ErrorBoundary": {"id": "web/common/ErrorBoundary", "title": "Error<PERSON>ou<PERSON><PERSON>", "description": "No description available."}, "web/common/InputError": {"id": "web/common/InputError", "title": "InputError", "description": "No description available."}, "web/common/InputLabel": {"id": "web/common/InputLabel", "title": "InputLabel", "description": "No description available."}, "web/common/LoadingSpinner": {"id": "web/common/LoadingSpinner", "title": "LoadingSpinner", "description": "No description available."}, "web/common/Modal": {"id": "web/common/Modal", "title": "Modal", "description": "No description available."}, "web/common/Overlay": {"id": "web/common/Overlay", "title": "Overlay", "description": "No description available."}, "web/common/PrimaryButton": {"id": "web/common/PrimaryButton", "title": "PrimaryButton", "description": "No description available."}, "web/common/SecondaryButton": {"id": "web/common/SecondaryButton", "title": "SecondaryButton", "description": "No description available."}, "web/common/TextInput": {"id": "web/common/TextInput", "title": "TextInput", "description": "No description available."}, "web/Icons/Icon": {"id": "web/Icons/Icon", "title": "Icon", "description": "No description available."}, "web/Icons/TextSettings": {"id": "web/Icons/TextSettings", "title": "TextSettings", "description": "No description available."}, "web/Navigation/BibleBookDropdown": {"id": "web/Navigation/BibleBookDropdown", "title": "BibleBookDropdown", "description": "No description available."}, "web/Navigation/BibleBookOffcanvas": {"id": "web/Navigation/BibleBookOffcanvas", "title": "BibleBookOffcanvas", "description": "No description available."}, "web/Navigation/BibleBookSelector": {"id": "web/Navigation/BibleBookSelector", "title": "BibleBookSelector", "description": "No description available."}, "web/Navigation/MobileSearchOverlay": {"id": "web/Navigation/MobileSearchOverlay", "title": "MobileSearchOverlay", "description": "MobileSearchOverlay component  Displays a search overlay for mobile devices with a search input and close button. This component is shown when the search is expanded in the NavigationBar.  @emits close - Emitted when the close button is clicked"}, "web/Navigation/NavigationBar": {"id": "web/Navigation/NavigationBar", "title": "NavigationBar", "description": "Interface for scroll handling configuration"}, "web/Navigation/NavLink": {"id": "web/Navigation/NavLink", "title": "NavLink", "description": "No description available."}, "web/Navigation/OffcanvasSidebar": {"id": "web/Navigation/OffcanvasSidebar", "title": "OffcanvasSidebar", "description": "No description available."}, "web/Navigation/ReferenceSelector": {"id": "web/Navigation/ReferenceSelector", "title": "ReferenceSelector", "description": "No description available."}, "web/Navigation/ResponsiveNavLink": {"id": "web/Navigation/ResponsiveNavLink", "title": "ResponsiveNavLink", "description": "No description available."}, "web/Navigation/SubNavigationBar": {"id": "web/Navigation/SubNavigationBar", "title": "SubNavigationBar", "description": "Toggles the bookmark state TODO: Implement bookmark functionality"}, "web/Search/BibleSearch": {"id": "web/Search/BibleSearch", "title": "BibleSearch", "description": "No description available."}, "web/Search/SearchCategoryToggle": {"id": "web/Search/SearchCategoryToggle", "title": "SearchCategoryToggle", "description": "No description available."}, "web/Search/SearchResultItem": {"id": "web/Search/SearchResultItem", "title": "SearchResultItem", "description": "No description available."}, "web/Search/SearchTypeDropdown": {"id": "web/Search/SearchTypeDropdown", "title": "SearchTypeDropdown", "description": "No description available."}, "web/Settings/SettingsAside": {"id": "web/Settings/SettingsAside", "title": "SettingsAside", "description": "No description available."}, "web/Settings/TextDisplaySettings": {"id": "web/Settings/TextDisplaySettings", "title": "TextDisplaySettings", "description": "No description available."}, "web/Settings/ThemeSettings": {"id": "web/Settings/ThemeSettings", "title": "ThemeSettings", "description": "No description available."}, "web/Settings/VisibilitySettings": {"id": "web/Settings/VisibilitySettings", "title": "VisibilitySettings", "description": "No description available."}}}}