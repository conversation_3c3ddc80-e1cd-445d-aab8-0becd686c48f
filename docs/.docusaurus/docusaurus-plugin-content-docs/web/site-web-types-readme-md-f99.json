{"id": "types/readme", "title": "ESB Online Web App Types", "description": "This section contains documentation for the TypeScript types used in the ESB Online web application.", "source": "@site/web/types/readme.md", "sourceDirName": "types", "slug": "/types/", "permalink": "/web/types/", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {}, "sidebar": "webSidebar", "previous": {"title": "Full API Documentation", "permalink": "/web/api/full-documentation"}, "next": {"title": "ESB Online Web App Types", "permalink": "/web/types/"}}