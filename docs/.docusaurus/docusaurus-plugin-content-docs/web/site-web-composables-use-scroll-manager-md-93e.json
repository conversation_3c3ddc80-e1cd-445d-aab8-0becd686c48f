{"id": "composables/useScrollManager", "title": "useScrollManager", "description": "Handles scroll events and position management", "source": "@site/web/composables/useScrollManager.md", "sourceDirName": "composables", "slug": "/composables/useScrollManager", "permalink": "/en/web/composables/useScrollManager", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 2, "frontMatter": {"id": "useScrollManager", "title": "useScrollManager", "sidebar_position": 2}, "sidebar": "webSidebar", "previous": {"title": "useDropdown", "permalink": "/en/web/composables/useDropdown"}, "next": {"title": "useSearchResults", "permalink": "/en/web/composables/useSearchResults"}}