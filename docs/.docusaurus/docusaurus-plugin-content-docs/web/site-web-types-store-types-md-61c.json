{"id": "types/store-types", "title": "Store Types", "description": "This module contains types for Pinia stores in the web application.", "source": "@site/web/types/store-types.md", "sourceDirName": "types", "slug": "/types/store-types", "permalink": "/web/types/store-types", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {}, "sidebar": "webSidebar", "previous": {"title": "ESB Online Web App Types", "permalink": "/web/types/"}, "next": {"title": "Web-Komponenten", "permalink": "/web/components/"}}