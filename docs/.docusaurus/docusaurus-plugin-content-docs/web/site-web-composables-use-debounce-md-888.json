{"id": "composables/useDebounce", "title": "useDebounce", "description": "Provides debounce functionality to delay function execution", "source": "@site/web/composables/useDebounce.md", "sourceDirName": "composables", "slug": "/composables/useDebounce", "permalink": "/en/web/composables/useDebounce", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 2, "frontMatter": {"id": "useDebounce", "title": "useDebounce", "sidebar_position": 2}, "sidebar": "webSidebar", "previous": {"title": "Composables Overview", "permalink": "/en/web/composables/"}, "next": {"title": "useDropdown", "permalink": "/en/web/composables/useDropdown"}}