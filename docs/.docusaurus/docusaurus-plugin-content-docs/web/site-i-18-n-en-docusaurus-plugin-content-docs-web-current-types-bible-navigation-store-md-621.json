{"id": "types/bible-navigation-store", "title": "Bible Navigation Store", "description": "The Bible Navigation Store is a Pinia store that manages the navigation state for the Bible application.", "source": "@site/i18n/en/docusaurus-plugin-content-docs-web/current/types/bible-navigation-store.md", "sourceDirName": "types", "slug": "/types/bible-navigation-store", "permalink": "/en/web/types/bible-navigation-store", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 2, "frontMatter": {"id": "bible-navigation-store", "title": "Bible Navigation Store", "sidebar_position": 2}}