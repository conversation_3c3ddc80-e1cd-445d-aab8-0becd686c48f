{"options": {"id": "web", "path": "web", "routeBasePath": "/web", "sidebarPath": "/Applications/MAMP/htdocs/ebtc/esra-bibel/docs/sidebars-web.ts", "editCurrentVersion": false, "editLocalizedFiles": false, "tagsBasePath": "tags", "include": ["**/*.{md,mdx}"], "exclude": ["**/_*.{js,jsx,ts,tsx,md,mdx}", "**/_*/**", "**/*.test.{js,jsx,ts,tsx}", "**/__tests__/**"], "sidebarCollapsible": true, "sidebarCollapsed": true, "docsRootComponent": "@theme/DocsRoot", "docVersionRootComponent": "@theme/DocVersionRoot", "docRootComponent": "@theme/DocRoot", "docItemComponent": "@theme/DocItem", "docTagsListComponent": "@theme/DocTagsListPage", "docTagDocListComponent": "@theme/DocTagDocListPage", "docCategoryGeneratedIndexComponent": "@theme/DocCategoryGeneratedIndexPage", "remarkPlugins": [], "rehypePlugins": [], "recmaPlugins": [], "beforeDefaultRemarkPlugins": [], "beforeDefaultRehypePlugins": [], "admonitions": true, "showLastUpdateTime": false, "showLastUpdateAuthor": false, "includeCurrentVersion": true, "disableVersioning": false, "versions": {}, "breadcrumbs": true, "onInlineTags": "warn"}, "versionsMetadata": [{"versionName": "current", "label": "Next", "banner": null, "badge": false, "noIndex": false, "className": "docs-version-current", "path": "/en/web", "tagsPath": "/en/web/tags", "isLast": true, "routePriority": -1, "sidebarFilePath": "/Applications/MAMP/htdocs/ebtc/esra-bibel/docs/sidebars-web.ts", "contentPath": "/Applications/MAMP/htdocs/ebtc/esra-bibel/docs/web", "contentPathLocalized": "/Applications/MAMP/htdocs/ebtc/esra-bibel/docs/i18n/en/docusaurus-plugin-content-docs-web/current"}]}