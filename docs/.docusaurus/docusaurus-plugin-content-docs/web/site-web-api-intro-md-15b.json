{"id": "api/intro", "title": "Web App API", "description": "Willkommen zur API-Dokumentation für die ESB Online Web-Anwendung. Diese Dokumentation wird automatisch aus dem Laravel-Backend generiert und bietet eine umfassende Referenz für alle verfügbaren API-Endpunkte.", "source": "@site/web/api/intro.md", "sourceDirName": "api", "slug": "/api/intro", "permalink": "/en/web/api/intro", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 1, "frontMatter": {"id": "intro", "title": "Web App API", "sidebar_position": 1}, "sidebar": "webSidebar", "previous": {"title": "useVerseReference", "permalink": "/en/web/composables/useVerseReference"}, "next": {"title": "Web App API", "permalink": "/en/web/api/intro"}}