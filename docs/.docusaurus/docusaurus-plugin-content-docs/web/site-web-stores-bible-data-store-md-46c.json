{"id": "stores/bibleDataStore", "title": "bible-data Store", "description": "/ Store for managing Bible data (books, chapters) /", "source": "@site/web/stores/bibleDataStore.md", "sourceDirName": "stores", "slug": "/stores/bibleDataStore", "permalink": "/en/web/stores/bibleDataStore", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 2, "frontMatter": {"id": "bibleDataStore", "title": "bible-data Store", "sidebar_position": 2}, "sidebar": "webSidebar", "previous": {"title": "Stores Overview", "permalink": "/en/web/stores/"}, "next": {"title": "bible-section Store", "permalink": "/en/web/stores/bibleSectionStore"}}