{"id": "api/authentication", "title": "Authentication", "description": "The ESB Online API uses Laravel Sanctum for authentication, which provides a simple token-based API authentication system.", "source": "@site/web/api/authentication.md", "sourceDirName": "api", "slug": "/api/authentication", "permalink": "/en/web/api/authentication", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 3, "frontMatter": {"id": "authentication", "title": "Authentication", "sidebar_position": 3}, "sidebar": "webSidebar", "previous": {"title": "Web App API", "permalink": "/en/web/api/intro"}, "next": {"title": "API Endpoints", "permalink": "/en/web/api/endpoints"}}