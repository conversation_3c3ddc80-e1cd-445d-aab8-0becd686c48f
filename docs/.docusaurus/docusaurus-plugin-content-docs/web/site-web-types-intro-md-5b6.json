{"id": "types/intro", "title": "Web-App-Typen", "description": "TypeScript-Typdokumentation für die ESB Online Webanwendung.", "source": "@site/web/types/intro.md", "sourceDirName": "types", "slug": "/types/intro", "permalink": "/web/types/intro", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 1, "frontMatter": {"id": "intro", "title": "Web-App-Typen", "sidebar_position": 1}}