{"id": "composables/index", "title": "Composables Overview", "description": "This section contains documentation for all the Vue composables used in the ESB Online web application.", "source": "@site/web/composables/index.md", "sourceDirName": "composables", "slug": "/composables/", "permalink": "/en/web/composables/", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 1, "frontMatter": {"id": "index", "title": "Composables Overview", "sidebar_position": 1}, "sidebar": "webSidebar", "previous": {"title": "textSettings Store", "permalink": "/en/web/stores/textSettingsStore"}, "next": {"title": "Composables Overview", "permalink": "/en/web/composables/"}}