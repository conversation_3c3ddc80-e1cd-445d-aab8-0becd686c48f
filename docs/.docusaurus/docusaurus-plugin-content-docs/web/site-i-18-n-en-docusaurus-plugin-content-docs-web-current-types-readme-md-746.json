{"id": "types/readme", "title": "Overview", "description": "This section contains documentation for the TypeScript types used in the ESB Online web application.", "source": "@site/i18n/en/docusaurus-plugin-content-docs-web/current/types/readme.md", "sourceDirName": "types", "slug": "/types/", "permalink": "/en/web/types/", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 1, "frontMatter": {"id": "readme", "title": "Overview", "sidebar_position": 1}, "sidebar": "webSidebar", "previous": {"title": "Full API Documentation", "permalink": "/en/web/api/full-documentation"}, "next": {"title": "Overview", "permalink": "/en/web/types/"}}