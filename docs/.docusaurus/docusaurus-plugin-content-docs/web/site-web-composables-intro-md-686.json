{"id": "composables/intro", "title": "Web App Composables", "description": "Documentation for the Vue composables used in the ESB Online web application.", "source": "@site/web/composables/intro.md", "sourceDirName": "composables", "slug": "/composables/intro", "permalink": "/en/web/composables/intro", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 1, "frontMatter": {"id": "intro", "title": "Web App Composables", "sidebar_position": 1}}