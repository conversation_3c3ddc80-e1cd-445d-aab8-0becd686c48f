{"id": "components/index", "title": "Web-Komponenten", "description": "Die ESB Online Webanwendung verwendet Vue.js-Komponenten, die in die folgenden Kategorien unterteilt sind:", "source": "@site/web/components/index.md", "sourceDirName": "components", "slug": "/components/", "permalink": "/en/web/components/", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 1, "frontMatter": {"id": "index", "title": "Web-Komponenten", "sidebar_position": 1}, "sidebar": "webSidebar", "previous": {"title": "Store Types", "permalink": "/en/web/types/store-types"}, "next": {"title": "Web-Komponenten", "permalink": "/en/web/components/"}}