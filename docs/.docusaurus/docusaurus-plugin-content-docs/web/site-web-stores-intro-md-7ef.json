{"id": "stores/intro", "title": "Web App Stores", "description": "Documentation for the Pinia stores used in the ESB Online web application.", "source": "@site/web/stores/intro.md", "sourceDirName": "stores", "slug": "/stores/intro", "permalink": "/en/web/stores/intro", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 1, "frontMatter": {"id": "intro", "title": "Web App Stores", "sidebar_position": 1}}