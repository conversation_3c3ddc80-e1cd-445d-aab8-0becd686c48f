{"id": "composables/useThrottle", "title": "useThrottle", "description": "Provides throttle functionality to limit function execution frequency", "source": "@site/web/composables/useThrottle.md", "sourceDirName": "composables", "slug": "/composables/useThrottle", "permalink": "/en/web/composables/useThrottle", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 2, "frontMatter": {"id": "useThrottle", "title": "useThrottle", "sidebar_position": 2}, "sidebar": "webSidebar", "previous": {"title": "useTextSettings", "permalink": "/en/web/composables/useTextSettings"}, "next": {"title": "useVerseReference", "permalink": "/en/web/composables/useVerseReference"}}