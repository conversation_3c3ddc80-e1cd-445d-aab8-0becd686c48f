{"id": "intro", "title": "Web Application", "description": "This documentation describes the ESB Online web application, developed with Laravel 11 and Vue 3.", "source": "@site/i18n/en/docusaurus-plugin-content-docs-web/current/intro.md", "sourceDirName": ".", "slug": "/intro", "permalink": "/en/web/intro", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 1, "frontMatter": {"id": "intro", "title": "Web Application", "sidebar_position": 1}, "sidebar": "webSidebar", "next": {"title": "Bibelnavigationsablauf", "permalink": "/en/web/bible-navigation-flow"}}