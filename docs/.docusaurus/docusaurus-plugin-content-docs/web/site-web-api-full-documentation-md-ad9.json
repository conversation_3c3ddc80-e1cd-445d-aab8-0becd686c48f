{"id": "api/full-documentation", "title": "Full API Documentation", "description": "The complete API documentation for the ESB Online web application is generated using Scribe and provides detailed information about all endpoints, including:", "source": "@site/web/api/full-documentation.md", "sourceDirName": "api", "slug": "/api/full-documentation", "permalink": "/en/web/api/full-documentation", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 4, "frontMatter": {"id": "full-documentation", "title": "Full API Documentation", "sidebar_position": 4}, "sidebar": "webSidebar", "previous": {"title": "API Endpoints", "permalink": "/en/web/api/endpoints"}, "next": {"title": "Overview", "permalink": "/en/web/types/"}}