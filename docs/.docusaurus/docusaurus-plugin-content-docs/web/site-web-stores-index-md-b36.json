{"id": "stores/index", "title": "Stores Overview", "description": "This section contains documentation for all the Pinia stores used in the ESB Online web application.", "source": "@site/web/stores/index.md", "sourceDirName": "stores", "slug": "/stores/", "permalink": "/en/web/stores/", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 1, "frontMatter": {"id": "index", "title": "Stores Overview", "sidebar_position": 1}, "sidebar": "webSidebar", "previous": {"title": "Bibelnavigationsablauf", "permalink": "/en/web/bible-navigation-flow"}, "next": {"title": "Stores Overview", "permalink": "/en/web/stores/"}}