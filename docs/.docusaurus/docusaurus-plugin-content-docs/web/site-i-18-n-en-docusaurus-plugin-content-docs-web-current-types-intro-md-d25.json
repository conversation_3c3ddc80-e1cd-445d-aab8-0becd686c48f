{"id": "types/intro", "title": "Web App Types", "description": "TypeScript type documentation for the ESB Online web application.", "source": "@site/i18n/en/docusaurus-plugin-content-docs-web/current/types/intro.md", "sourceDirName": "types", "slug": "/types/intro", "permalink": "/en/web/types/intro", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 1, "frontMatter": {"id": "intro", "title": "Web App Types", "sidebar_position": 1}}