{"id": "types/store-types", "title": "Store Types", "description": "This module contains types for Pinia stores in the web application.", "source": "@site/i18n/en/docusaurus-plugin-content-docs-web/current/types/store-types.md", "sourceDirName": "types", "slug": "/types/store-types", "permalink": "/en/web/types/store-types", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 3, "frontMatter": {"id": "store-types", "title": "Store Types", "sidebar_position": 3}, "sidebar": "webSidebar", "previous": {"title": "Overview", "permalink": "/en/web/types/"}, "next": {"title": "Web-Komponenten", "permalink": "/en/web/components/"}}