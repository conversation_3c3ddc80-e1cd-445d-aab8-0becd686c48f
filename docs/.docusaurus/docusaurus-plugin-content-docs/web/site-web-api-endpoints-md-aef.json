{"id": "api/endpoints", "title": "API Endpoints", "description": "The ESB Online platform provides several API endpoints for accessing Bible data and user information. Below is a summary of the available endpoints.", "source": "@site/web/api/endpoints.md", "sourceDirName": "api", "slug": "/api/endpoints", "permalink": "/en/web/api/endpoints", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 2, "frontMatter": {"id": "endpoints", "title": "API Endpoints", "sidebar_position": 2}, "sidebar": "webSidebar", "previous": {"title": "Authentication", "permalink": "/en/web/api/authentication"}, "next": {"title": "Full API Documentation", "permalink": "/en/web/api/full-documentation"}}