{"version": {"pluginId": "web", "version": "current", "label": "Next", "banner": null, "badge": false, "noIndex": false, "className": "docs-version-current", "isLast": true, "docsSidebars": {"webSidebar": [{"type": "link", "label": "Web Application", "href": "/en/web/intro", "docId": "intro", "unlisted": false}, {"type": "link", "label": "Bibelnavigationsablauf", "href": "/en/web/bible-navigation-flow", "docId": "bible-navigation-flow", "unlisted": false}, {"type": "category", "label": "<PERSON><PERSON><PERSON><PERSON>", "items": [{"type": "link", "label": "Stores Overview", "href": "/en/web/stores/", "docId": "stores/index", "unlisted": false}, {"type": "category", "label": "Bibel-Speicher", "items": [{"type": "link", "label": "bible-data Store", "href": "/en/web/stores/bibleDataStore", "docId": "stores/bibleDataStore", "unlisted": false}, {"type": "link", "label": "bible-section Store", "href": "/en/web/stores/bibleSectionStore", "docId": "stores/bibleSectionStore", "unlisted": false}, {"type": "link", "label": "bible-highlight Store", "href": "/en/web/stores/bibleHighlightStore", "docId": "stores/bibleHighlightStore", "unlisted": false}, {"type": "link", "label": "bible-memory Store", "href": "/en/web/stores/bibleMemoryStore", "docId": "stores/bibleMemoryStore", "unlisted": false}], "collapsed": true, "collapsible": true}, {"type": "link", "label": "search Store", "href": "/en/web/stores/searchStore", "docId": "stores/searchStore", "unlisted": false}, {"type": "link", "label": "search-settings Store", "href": "/en/web/stores/searchSettingsStore", "docId": "stores/searchSettingsStore", "unlisted": false}, {"type": "link", "label": "textSettings Store", "href": "/en/web/stores/textSettingsStore", "docId": "stores/textSettingsStore", "unlisted": false}], "collapsed": true, "collapsible": true, "href": "/en/web/stores/"}, {"type": "category", "label": "Composables", "items": [{"type": "link", "label": "Composables Overview", "href": "/en/web/composables/", "docId": "composables/index", "unlisted": false}, {"type": "link", "label": "useDebounce", "href": "/en/web/composables/useDebounce", "docId": "composables/useDebounce", "unlisted": false}, {"type": "link", "label": "useDropdown", "href": "/en/web/composables/useDropdown", "docId": "composables/useDropdown", "unlisted": false}, {"type": "link", "label": "useScrollManager", "href": "/en/web/composables/useScrollManager", "docId": "composables/useScrollManager", "unlisted": false}, {"type": "link", "label": "useSearchResults", "href": "/en/web/composables/useSearchResults", "docId": "composables/useSearchResults", "unlisted": false}, {"type": "link", "label": "useTextSettings", "href": "/en/web/composables/useTextSettings", "docId": "composables/useTextSettings", "unlisted": false}, {"type": "link", "label": "useThrottle", "href": "/en/web/composables/useThrottle", "docId": "composables/useThrottle", "unlisted": false}, {"type": "link", "label": "useVerseReference", "href": "/en/web/composables/useVerseReference", "docId": "composables/useVerseReference", "unlisted": false}], "collapsed": true, "collapsible": true, "href": "/en/web/composables/"}, {"type": "category", "label": "API", "items": [{"type": "link", "label": "Web App API", "href": "/en/web/api/intro", "docId": "api/intro", "unlisted": false}, {"type": "link", "label": "Authentication", "href": "/en/web/api/authentication", "docId": "api/authentication", "unlisted": false}, {"type": "link", "label": "API Endpoints", "href": "/en/web/api/endpoints", "docId": "api/endpoints", "unlisted": false}, {"type": "link", "label": "Full API Documentation", "href": "/en/web/api/full-documentation", "docId": "api/full-documentation", "unlisted": false}], "collapsed": true, "collapsible": true, "href": "/en/web/api/intro"}, {"type": "category", "label": "Typen", "items": [{"type": "link", "label": "Overview", "href": "/en/web/types/", "docId": "types/readme", "unlisted": false}, {"type": "link", "label": "Store Types", "href": "/en/web/types/store-types", "docId": "types/store-types", "unlisted": false}], "collapsed": true, "collapsible": true, "href": "/en/web/types/"}, {"type": "category", "label": "Komponenten", "items": [{"type": "link", "label": "Web-Komponenten", "href": "/en/web/components/", "docId": "components/index", "unlisted": false}, {"type": "category", "label": "Bibelanzeige", "items": [{"type": "link", "label": "Bibelanzeige Komponenten", "href": "/en/web/components/bibledisplay/", "docId": "components/bibledisplay/index", "unlisted": false}, {"type": "link", "label": "ChapterContent", "href": "/en/web/components/bibledisplay/ChapterContent", "docId": "components/bibledisplay/ChapterContent", "unlisted": false}, {"type": "link", "label": "ChapterNumber", "href": "/en/web/components/bibledisplay/ChapterNumber", "docId": "components/bibledisplay/ChapterNumber", "unlisted": false}, {"type": "link", "label": "ChapterWrapper", "href": "/en/web/components/bibledisplay/ChapterWrapper", "docId": "components/bibledisplay/ChapterWrapper", "unlisted": false}, {"type": "link", "label": "FootnoteContent", "href": "/en/web/components/bibledisplay/FootnoteContent", "docId": "components/bibledisplay/FootnoteContent", "unlisted": false}, {"type": "link", "label": "FootnoteTooltip", "href": "/en/web/components/bibledisplay/FootnoteTooltip", "docId": "components/bibledisplay/FootnoteTooltip", "unlisted": false}, {"type": "link", "label": "FrontMatter", "href": "/en/web/components/bibledisplay/FrontMatter", "docId": "components/bibledisplay/FrontMatter", "unlisted": false}, {"type": "link", "label": "InfoItem", "href": "/en/web/components/bibledisplay/InfoItem", "docId": "components/bibledisplay/InfoItem", "unlisted": false}, {"type": "link", "label": "InfoSection", "href": "/en/web/components/bibledisplay/InfoSection", "docId": "components/bibledisplay/InfoSection", "unlisted": false}, {"type": "link", "label": "NumberSelector", "href": "/en/web/components/bibledisplay/NumberSelector", "docId": "components/bibledisplay/NumberSelector", "unlisted": false}, {"type": "link", "label": "ReferenceSelector", "href": "/en/web/components/bibledisplay/ReferenceSelector", "docId": "components/bibledisplay/ReferenceSelector", "unlisted": false}, {"type": "link", "label": "TextFormatDropdown", "href": "/en/web/components/bibledisplay/TextFormatDropdown", "docId": "components/bibledisplay/TextFormatDropdown", "unlisted": false}, {"type": "link", "label": "UnavailableBookNotice", "href": "/en/web/components/bibledisplay/UnavailableBookNotice", "docId": "components/bibledisplay/UnavailableBookNotice", "unlisted": false}, {"type": "link", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "href": "/en/web/components/bibledisplay/VerseContent", "docId": "components/bibledisplay/VerseContent", "unlisted": false}, {"type": "link", "label": "VerseNumber", "href": "/en/web/components/bibledisplay/VerseNumber", "docId": "components/bibledisplay/VerseNumber", "unlisted": false}, {"type": "link", "label": "WordGroupContainer", "href": "/en/web/components/bibledisplay/WordGroupContainer", "docId": "components/bibledisplay/WordGroupContainer", "unlisted": false}], "collapsed": true, "collapsible": true, "href": "/en/web/components/bibledisplay/"}, {"type": "category", "label": "Navigationsleiste", "items": [{"type": "link", "label": "Navigation Komponenten", "href": "/en/web/components/navigation/", "docId": "components/navigation/index", "unlisted": false}, {"type": "link", "label": "BibleBookDropdown", "href": "/en/web/components/navigation/BibleBookDropdown", "docId": "components/navigation/BibleBookDropdown", "unlisted": false}, {"type": "link", "label": "BibleBookOffcanvas", "href": "/en/web/components/navigation/BibleBookOffcanvas", "docId": "components/navigation/BibleBookOffcanvas", "unlisted": false}, {"type": "link", "label": "BibleBookSelector", "href": "/en/web/components/navigation/BibleBookSelector", "docId": "components/navigation/BibleBookSelector", "unlisted": false}, {"type": "link", "label": "MobileSearchOverlay", "href": "/en/web/components/navigation/MobileSearchOverlay", "docId": "components/navigation/MobileSearchOverlay", "unlisted": false}, {"type": "link", "label": "NavLink", "href": "/en/web/components/navigation/NavLink", "docId": "components/navigation/NavLink", "unlisted": false}, {"type": "link", "label": "NavigationBar", "href": "/en/web/components/navigation/NavigationBar", "docId": "components/navigation/NavigationBar", "unlisted": false}, {"type": "link", "label": "OffcanvasSidebar", "href": "/en/web/components/navigation/OffcanvasSidebar", "docId": "components/navigation/OffcanvasSidebar", "unlisted": false}, {"type": "link", "label": "ResponsiveNavLink", "href": "/en/web/components/navigation/ResponsiveNavLink", "docId": "components/navigation/ResponsiveNavLink", "unlisted": false}, {"type": "link", "label": "SubNavigationBar", "href": "/en/web/components/navigation/SubNavigationBar", "docId": "components/navigation/SubNavigationBar", "unlisted": false}], "collapsed": true, "collapsible": true, "href": "/en/web/components/navigation/"}, {"type": "category", "label": "<PERSON><PERSON>", "items": [{"type": "link", "label": "<PERSON><PERSON> Kompo<PERSON>en", "href": "/en/web/components/search/", "docId": "components/search/index", "unlisted": false}, {"type": "link", "label": "BibleSearch", "href": "/en/web/components/search/BibleSearch", "docId": "components/search/BibleSearch", "unlisted": false}, {"type": "link", "label": "SearchResultItem", "href": "/en/web/components/search/SearchResultItem", "docId": "components/search/SearchResultItem", "unlisted": false}, {"type": "link", "label": "SearchTypeDropdown", "href": "/en/web/components/search/SearchTypeDropdown", "docId": "components/search/SearchTypeDropdown", "unlisted": false}], "collapsed": true, "collapsible": true, "href": "/en/web/components/search/"}, {"type": "category", "label": "Allgemein", "items": [{"type": "link", "label": "Allgemein Komponenten", "href": "/en/web/components/common/", "docId": "components/common/index", "unlisted": false}, {"type": "link", "label": "ApplicationLogo", "href": "/en/web/components/common/ApplicationLogo", "docId": "components/common/ApplicationLogo", "unlisted": false}, {"type": "link", "label": "Checkbox", "href": "/en/web/components/common/Checkbox", "docId": "components/common/Checkbox", "unlisted": false}, {"type": "link", "label": "DangerButton", "href": "/en/web/components/common/DangerButton", "docId": "components/common/DangerButton", "unlisted": false}, {"type": "link", "label": "Dropdown", "href": "/en/web/components/common/Dropdown", "docId": "components/common/Dropdown", "unlisted": false}, {"type": "link", "label": "DropdownLink", "href": "/en/web/components/common/DropdownLink", "docId": "components/common/DropdownLink", "unlisted": false}, {"type": "link", "label": "Error<PERSON>ou<PERSON><PERSON>", "href": "/en/web/components/common/ErrorBoundary", "docId": "components/common/ErrorBoundary", "unlisted": false}, {"type": "link", "label": "InputError", "href": "/en/web/components/common/InputError", "docId": "components/common/InputError", "unlisted": false}, {"type": "link", "label": "InputLabel", "href": "/en/web/components/common/InputLabel", "docId": "components/common/InputLabel", "unlisted": false}, {"type": "link", "label": "LoadingSpinner", "href": "/en/web/components/common/LoadingSpinner", "docId": "components/common/LoadingSpinner", "unlisted": false}, {"type": "link", "label": "Modal", "href": "/en/web/components/common/Modal", "docId": "components/common/Modal", "unlisted": false}, {"type": "link", "label": "Overlay", "href": "/en/web/components/common/Overlay", "docId": "components/common/Overlay", "unlisted": false}, {"type": "link", "label": "PrimaryButton", "href": "/en/web/components/common/PrimaryButton", "docId": "components/common/PrimaryButton", "unlisted": false}, {"type": "link", "label": "SecondaryButton", "href": "/en/web/components/common/SecondaryButton", "docId": "components/common/SecondaryButton", "unlisted": false}, {"type": "link", "label": "TextInput", "href": "/en/web/components/common/TextInput", "docId": "components/common/TextInput", "unlisted": false}], "collapsed": true, "collapsible": true, "href": "/en/web/components/common/"}], "collapsed": true, "collapsible": true, "href": "/en/web/components/"}]}, "docs": {"api/authentication": {"id": "api/authentication", "title": "Authentication", "description": "The ESB Online API uses Laravel Sanctum for authentication, which provides a simple token-based API authentication system.", "sidebar": "webSidebar"}, "api/endpoints": {"id": "api/endpoints", "title": "API Endpoints", "description": "The ESB Online platform provides several API endpoints for accessing Bible data and user information. Below is a summary of the available endpoints.", "sidebar": "webSidebar"}, "api/full-documentation": {"id": "api/full-documentation", "title": "Full API Documentation", "description": "The complete API documentation for the ESB Online web application is generated using Scribe and provides detailed information about all endpoints, including:", "sidebar": "webSidebar"}, "api/intro": {"id": "api/intro", "title": "Web App API", "description": "Willkommen zur API-Dokumentation für die ESB Online Web-Anwendung. Diese Dokumentation wird automatisch aus dem Laravel-Backend generiert und bietet eine umfassende Referenz für alle verfügbaren API-Endpunkte.", "sidebar": "webSidebar"}, "bible-navigation-flow": {"id": "bible-navigation-flow", "title": "Bibelnavigationsablauf", "description": "Dieses Dokument beschreibt den Ausführungsablauf bei der Navigation zu einer Bibelkapitel-URL (z.B. /Johannes1) und beim Scrollen durch Kapitel.", "sidebar": "webSidebar"}, "components/bibledisplay/ChapterContent": {"id": "components/bibledisplay/ChapterContent", "title": "ChapterContent", "description": "Keine Beschreibung verfügbar", "sidebar": "webSidebar"}, "components/bibledisplay/ChapterNumber": {"id": "components/bibledisplay/ChapterNumber", "title": "ChapterNumber", "description": "Keine Beschreibung verfügbar", "sidebar": "webSidebar"}, "components/bibledisplay/ChapterWrapper": {"id": "components/bibledisplay/ChapterWrapper", "title": "ChapterWrapper", "description": "Keine Beschreibung verfügbar", "sidebar": "webSidebar"}, "components/bibledisplay/FootnoteContent": {"id": "components/bibledisplay/FootnoteContent", "title": "FootnoteContent", "description": "Keine Beschreibung verfügbar", "sidebar": "webSidebar"}, "components/bibledisplay/FootnoteTooltip": {"id": "components/bibledisplay/FootnoteTooltip", "title": "FootnoteTooltip", "description": "Keine Beschreibung verfügbar", "sidebar": "webSidebar"}, "components/bibledisplay/FrontMatter": {"id": "components/bibledisplay/FrontMatter", "title": "FrontMatter", "description": "Keine Beschreibung verfügbar", "sidebar": "webSidebar"}, "components/bibledisplay/index": {"id": "components/bibledisplay/index", "title": "Bibelanzeige Komponenten", "description": "Dieser Abschnitt enthält Dokumentation für die folgenden Komponenten:", "sidebar": "webSidebar"}, "components/bibledisplay/InfoItem": {"id": "components/bibledisplay/InfoItem", "title": "InfoItem", "description": "Keine Beschreibung verfügbar", "sidebar": "webSidebar"}, "components/bibledisplay/InfoSection": {"id": "components/bibledisplay/InfoSection", "title": "InfoSection", "description": "Keine Beschreibung verfügbar", "sidebar": "webSidebar"}, "components/bibledisplay/NumberSelector": {"id": "components/bibledisplay/NumberSelector", "title": "NumberSelector", "description": "Keine Beschreibung verfügbar", "sidebar": "webSidebar"}, "components/bibledisplay/ReferenceSelector": {"id": "components/bibledisplay/ReferenceSelector", "title": "ReferenceSelector", "description": "Keine Beschreibung verfügbar", "sidebar": "webSidebar"}, "components/bibledisplay/TextFormatDropdown": {"id": "components/bibledisplay/TextFormatDropdown", "title": "TextFormatDropdown", "description": "Keine Beschreibung verfügbar", "sidebar": "webSidebar"}, "components/bibledisplay/UnavailableBookNotice": {"id": "components/bibledisplay/UnavailableBookNotice", "title": "UnavailableBookNotice", "description": "Keine Beschreibung verfügbar", "sidebar": "webSidebar"}, "components/bibledisplay/VerseContent": {"id": "components/bibledisplay/VerseContent", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Keine Beschreibung verfügbar", "sidebar": "webSidebar"}, "components/bibledisplay/VerseNumber": {"id": "components/bibledisplay/VerseNumber", "title": "VerseNumber", "description": "Keine Beschreibung verfügbar", "sidebar": "webSidebar"}, "components/bibledisplay/WordGroupContainer": {"id": "components/bibledisplay/WordGroupContainer", "title": "WordGroupContainer", "description": "Returns the word type for data attribute", "sidebar": "webSidebar"}, "components/common/ApplicationLogo": {"id": "components/common/ApplicationLogo", "title": "ApplicationLogo", "description": "Keine Beschreibung verfügbar", "sidebar": "webSidebar"}, "components/common/Checkbox": {"id": "components/common/Checkbox", "title": "Checkbox", "description": "Keine Beschreibung verfügbar", "sidebar": "webSidebar"}, "components/common/DangerButton": {"id": "components/common/DangerButton", "title": "DangerButton", "description": "Keine Beschreibung verfügbar", "sidebar": "webSidebar"}, "components/common/Dropdown": {"id": "components/common/Dropdown", "title": "Dropdown", "description": "Keine Beschreibung verfügbar", "sidebar": "webSidebar"}, "components/common/DropdownLink": {"id": "components/common/DropdownLink", "title": "DropdownLink", "description": "Keine Beschreibung verfügbar", "sidebar": "webSidebar"}, "components/common/ErrorBoundary": {"id": "components/common/ErrorBoundary", "title": "Error<PERSON>ou<PERSON><PERSON>", "description": "Keine Beschreibung verfügbar", "sidebar": "webSidebar"}, "components/common/index": {"id": "components/common/index", "title": "Allgemein Komponenten", "description": "Dieser Abschnitt enthält Dokumentation für die folgenden Komponenten:", "sidebar": "webSidebar"}, "components/common/InputError": {"id": "components/common/InputError", "title": "InputError", "description": "Keine Beschreibung verfügbar", "sidebar": "webSidebar"}, "components/common/InputLabel": {"id": "components/common/InputLabel", "title": "InputLabel", "description": "Keine Beschreibung verfügbar", "sidebar": "webSidebar"}, "components/common/LoadingSpinner": {"id": "components/common/LoadingSpinner", "title": "LoadingSpinner", "description": "Keine Beschreibung verfügbar", "sidebar": "webSidebar"}, "components/common/Modal": {"id": "components/common/Modal", "title": "Modal", "description": "Keine Beschreibung verfügbar", "sidebar": "webSidebar"}, "components/common/Overlay": {"id": "components/common/Overlay", "title": "Overlay", "description": "Keine Beschreibung verfügbar", "sidebar": "webSidebar"}, "components/common/PrimaryButton": {"id": "components/common/PrimaryButton", "title": "PrimaryButton", "description": "Keine Beschreibung verfügbar", "sidebar": "webSidebar"}, "components/common/SecondaryButton": {"id": "components/common/SecondaryButton", "title": "SecondaryButton", "description": "Keine Beschreibung verfügbar", "sidebar": "webSidebar"}, "components/common/TextInput": {"id": "components/common/TextInput", "title": "TextInput", "description": "Keine Beschreibung verfügbar", "sidebar": "webSidebar"}, "components/index": {"id": "components/index", "title": "Web-Komponenten", "description": "Die ESB Online Webanwendung verwendet Vue.js-Komponenten, die in die folgenden Kategorien unterteilt sind:", "sidebar": "webSidebar"}, "components/intro": {"id": "components/intro", "title": "Web Components", "description": "Vue component documentation for the ESB Online web application."}, "components/navigation/BibleBookDropdown": {"id": "components/navigation/BibleBookDropdown", "title": "BibleBookDropdown", "description": "Keine Beschreibung verfügbar", "sidebar": "webSidebar"}, "components/navigation/BibleBookOffcanvas": {"id": "components/navigation/BibleBookOffcanvas", "title": "BibleBookOffcanvas", "description": "Keine Beschreibung verfügbar", "sidebar": "webSidebar"}, "components/navigation/BibleBookSelector": {"id": "components/navigation/BibleBookSelector", "title": "BibleBookSelector", "description": "Keine Beschreibung verfügbar", "sidebar": "webSidebar"}, "components/navigation/index": {"id": "components/navigation/index", "title": "Navigation Komponenten", "description": "Dieser Abschnitt enthält Dokumentation für die folgenden Komponenten:", "sidebar": "webSidebar"}, "components/navigation/MobileSearchOverlay": {"id": "components/navigation/MobileSearchOverlay", "title": "MobileSearchOverlay", "description": "MobileSearchOverlay component  Displays a search overlay for mobile devices with a search input and close button. This component is shown when the search is expanded in the NavigationBar.  @emits close - Emitted when the close button is clicked", "sidebar": "webSidebar"}, "components/navigation/NavigationBar": {"id": "components/navigation/NavigationBar", "title": "NavigationBar", "description": "Interface for scroll handling configuration", "sidebar": "webSidebar"}, "components/navigation/NavLink": {"id": "components/navigation/NavLink", "title": "NavLink", "description": "Keine Beschreibung verfügbar", "sidebar": "webSidebar"}, "components/navigation/OffcanvasSidebar": {"id": "components/navigation/OffcanvasSidebar", "title": "OffcanvasSidebar", "description": "Keine Beschreibung verfügbar", "sidebar": "webSidebar"}, "components/navigation/ReferenceSelector": {"id": "components/navigation/ReferenceSelector", "title": "ReferenceSelector", "description": "Keine Beschreibung verfügbar"}, "components/navigation/ResponsiveNavLink": {"id": "components/navigation/ResponsiveNavLink", "title": "ResponsiveNavLink", "description": "Keine Beschreibung verfügbar", "sidebar": "webSidebar"}, "components/navigation/SubNavigationBar": {"id": "components/navigation/SubNavigationBar", "title": "SubNavigationBar", "description": "Toggles the bookmark state TODO: Implement bookmark functionality", "sidebar": "webSidebar"}, "components/search/BibleSearch": {"id": "components/search/BibleSearch", "title": "BibleSearch", "description": "Keine Beschreibung verfügbar", "sidebar": "webSidebar"}, "components/search/index": {"id": "components/search/index", "title": "<PERSON><PERSON> Kompo<PERSON>en", "description": "Dieser Abschnitt enthält Dokumentation für die folgenden Komponenten:", "sidebar": "webSidebar"}, "components/search/SearchCategoryToggle": {"id": "components/search/SearchCategoryToggle", "title": "SearchCategoryToggle", "description": "Keine Beschreibung verfügbar"}, "components/search/SearchResultItem": {"id": "components/search/SearchResultItem", "title": "SearchResultItem", "description": "Keine Beschreibung verfügbar", "sidebar": "webSidebar"}, "components/search/SearchTypeDropdown": {"id": "components/search/SearchTypeDropdown", "title": "SearchTypeDropdown", "description": "Keine Beschreibung verfügbar", "sidebar": "webSidebar"}, "composables/index": {"id": "composables/index", "title": "Composables Overview", "description": "This section contains documentation for all the Vue composables used in the ESB Online web application.", "sidebar": "webSidebar"}, "composables/intro": {"id": "composables/intro", "title": "Web App Composables", "description": "Documentation for the Vue composables used in the ESB Online web application."}, "composables/useDebounce": {"id": "composables/useDebounce", "title": "useDebounce", "description": "Provides debounce functionality to delay function execution", "sidebar": "webSidebar"}, "composables/useDropdown": {"id": "composables/useDropdown", "title": "useDropdown", "description": "Manages dropdown UI component state and interactions", "sidebar": "webSidebar"}, "composables/useLocalStorage": {"id": "composables/useLocalStorage", "title": "isLocalStorageAvailable", "description": "/ /"}, "composables/useScrollManager": {"id": "composables/useScrollManager", "title": "useScrollManager", "description": "Handles scroll events and position management", "sidebar": "webSidebar"}, "composables/useSearchResults": {"id": "composables/useSearchResults", "title": "useSearchResults", "description": "Manages search results and search-related functionality", "sidebar": "webSidebar"}, "composables/useSettingsAside": {"id": "composables/useSettingsAside", "title": "useSettingsAside", "description": "Composable for SettingsAside"}, "composables/useTextSettings": {"id": "composables/useTextSettings", "title": "useTextSettings", "description": "Manages text display settings and preferences", "sidebar": "webSidebar"}, "composables/useThrottle": {"id": "composables/useThrottle", "title": "useThrottle", "description": "Provides throttle functionality to limit function execution frequency", "sidebar": "webSidebar"}, "composables/useVerseReference": {"id": "composables/useVerseReference", "title": "useVerseReference", "description": "Handles Bible verse references and navigation", "sidebar": "webSidebar"}, "intro": {"id": "intro", "title": "Web Application", "description": "This documentation describes the ESB Online web application, developed with Laravel 11 and Vue 3.", "sidebar": "webSidebar"}, "stores/bibleDataStore": {"id": "stores/bibleDataStore", "title": "bible-data Store", "description": "/ Store for managing Bible data (books, chapters) /", "sidebar": "webSidebar"}, "stores/bibleHighlightStore": {"id": "stores/bibleHighlightStore", "title": "bible-highlight Store", "description": "/ Store for managing word highlighting functionality /", "sidebar": "webSidebar"}, "stores/bibleMemoryStore": {"id": "stores/bibleMemoryStore", "title": "bible-memory Store", "description": "/ Store for managing memory usage and chapter cleanup /", "sidebar": "webSidebar"}, "stores/bibleSectionStore": {"id": "stores/bibleSectionStore", "title": "bible-section Store", "description": "Bible sections and navigation", "sidebar": "webSidebar"}, "stores/index": {"id": "stores/index", "title": "Stores Overview", "description": "This section contains documentation for all the Pinia stores used in the ESB Online web application.", "sidebar": "webSidebar"}, "stores/intro": {"id": "stores/intro", "title": "Web App Stores", "description": "Documentation for the Pinia stores used in the ESB Online web application."}, "stores/searchSettingsStore": {"id": "stores/searchSettingsStore", "title": "search-settings Store", "description": "Search configuration and settings", "sidebar": "webSidebar"}, "stores/searchStore": {"id": "stores/searchStore", "title": "search Store", "description": "Bible search functionality and results", "sidebar": "webSidebar"}, "stores/textSettingsStore": {"id": "stores/textSettingsStore", "title": "textSettings Store", "description": "Text display settings and preferences", "sidebar": "webSidebar"}, "types/bible-navigation-store": {"id": "types/bible-navigation-store", "title": "Bible Navigation Store", "description": "The Bible Navigation Store is a Pinia store that manages the navigation state for the Bible application."}, "types/intro": {"id": "types/intro", "title": "Web App Types", "description": "TypeScript type documentation for the ESB Online web application."}, "types/readme": {"id": "types/readme", "title": "Overview", "description": "This section contains documentation for the TypeScript types used in the ESB Online web application.", "sidebar": "webSidebar"}, "types/sidebar": {"id": "types/sidebar", "title": "Web App Types Sidebar", "description": "- Overview"}, "types/store-types": {"id": "types/store-types", "title": "Store Types", "description": "This module contains types for Pinia stores in the web application.", "sidebar": "webSidebar"}}}}