{"id": "composables/useDropdown", "title": "useDropdown", "description": "Manages dropdown UI component state and interactions", "source": "@site/web/composables/useDropdown.md", "sourceDirName": "composables", "slug": "/composables/useDropdown", "permalink": "/en/web/composables/useDropdown", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 2, "frontMatter": {"id": "useDropdown", "title": "useDropdown", "sidebar_position": 2}, "sidebar": "webSidebar", "previous": {"title": "useDebounce", "permalink": "/en/web/composables/useDebounce"}, "next": {"title": "useScrollManager", "permalink": "/en/web/composables/useScrollManager"}}