{"allContent": {"docusaurus-plugin-content-docs": {"default": {"loadedVersions": [{"versionName": "current", "label": "Next", "banner": null, "badge": false, "noIndex": false, "className": "docs-version-current", "path": "/docs", "tagsPath": "/docs/tags", "editUrl": "https://gitlab.com/esra-bibel/esb-online/-/tree/main/docs/docs", "editUrlLocalized": "https://gitlab.com/esra-bibel/esb-online/-/tree/main/docs/i18n/de/docusaurus-plugin-content-docs/current", "isLast": true, "routePriority": -1, "sidebarFilePath": "/Applications/MAMP/htdocs/ebtc/esra-bibel/docs/sidebars.ts", "contentPath": "/Applications/MAMP/htdocs/ebtc/esra-bibel/docs/docs", "contentPathLocalized": "/Applications/MAMP/htdocs/ebtc/esra-bibel/docs/i18n/de/docusaurus-plugin-content-docs/current", "docs": [{"id": "configuration", "title": "Konfiguration", "description": "Diese Anleitung beschreibt die Konfigurationsoptionen für die ESB Online-Plattform.", "source": "@site/docs/configuration.md", "sourceDirName": ".", "slug": "/configuration", "permalink": "/docs/configuration", "draft": false, "unlisted": false, "editUrl": "https://gitlab.com/esra-bibel/esb-online/-/tree/main/docs/docs/configuration.md", "tags": [], "version": "current", "sidebarPosition": 3, "frontMatter": {"id": "configuration", "title": "Konfiguration", "sidebar_position": 3}, "sidebar": "tutorialSidebar", "previous": {"title": "Installation", "permalink": "/docs/installation"}}, {"id": "installation", "title": "Installation", "description": "Diese Anleitung beschreibt den Installationsprozess für die ESB Online-Plattform.", "source": "@site/docs/installation.md", "sourceDirName": ".", "slug": "/installation", "permalink": "/docs/installation", "draft": false, "unlisted": false, "editUrl": "https://gitlab.com/esra-bibel/esb-online/-/tree/main/docs/docs/installation.md", "tags": [], "version": "current", "sidebarPosition": 2, "frontMatter": {"id": "installation", "title": "Installation", "sidebar_position": 2}, "sidebar": "tutorialSidebar", "previous": {"title": "ESB Online Dokumentation", "permalink": "/docs/intro"}, "next": {"title": "Konfiguration", "permalink": "/docs/configuration"}}, {"id": "intro", "title": "ESB Online Dokumentation", "description": "Willkommen zur umfassenden Dokumentation für die ESB Online-Plattform. Diese Dokumentation umfasst alle Aspekte der Plattform, einschließlich der Webanwendung, API, Komponenten und gemeinsam genutzten Bibliotheken.", "source": "@site/docs/intro.md", "sourceDirName": ".", "slug": "/intro", "permalink": "/docs/intro", "draft": false, "unlisted": false, "editUrl": "https://gitlab.com/esra-bibel/esb-online/-/tree/main/docs/docs/intro.md", "tags": [], "version": "current", "sidebarPosition": 1, "frontMatter": {"id": "intro", "title": "ESB Online Dokumentation", "sidebar_position": 1}, "sidebar": "tutorialSidebar", "next": {"title": "Installation", "permalink": "/docs/installation"}}], "drafts": [], "sidebars": {"tutorialSidebar": [{"type": "category", "label": "Introduction", "items": [{"type": "doc", "id": "intro"}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "Getting Started", "items": [{"type": "doc", "id": "installation"}, {"type": "doc", "id": "configuration"}], "collapsed": true, "collapsible": true}]}}]}, "web": {"loadedVersions": [{"versionName": "current", "label": "Next", "banner": null, "badge": false, "noIndex": false, "className": "docs-version-current", "path": "/web", "tagsPath": "/web/tags", "isLast": true, "routePriority": -1, "sidebarFilePath": "/Applications/MAMP/htdocs/ebtc/esra-bibel/docs/sidebars-web.ts", "contentPath": "/Applications/MAMP/htdocs/ebtc/esra-bibel/docs/web", "contentPathLocalized": "/Applications/MAMP/htdocs/ebtc/esra-bibel/docs/i18n/de/docusaurus-plugin-content-docs-web/current", "docs": [{"id": "api/authentication", "title": "Authentication", "description": "The ESB Online API uses Laravel Sanctum for authentication, which provides a simple token-based API authentication system.", "source": "@site/web/api/authentication.md", "sourceDirName": "api", "slug": "/api/authentication", "permalink": "/web/api/authentication", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 3, "frontMatter": {"id": "authentication", "title": "Authentication", "sidebar_position": 3}, "sidebar": "webSidebar", "previous": {"title": "Web App API", "permalink": "/web/api/intro"}, "next": {"title": "API Endpoints", "permalink": "/web/api/endpoints"}}, {"id": "api/endpoints", "title": "API Endpoints", "description": "The ESB Online platform provides several API endpoints for accessing Bible data and user information. Below is a summary of the available endpoints.", "source": "@site/web/api/endpoints.md", "sourceDirName": "api", "slug": "/api/endpoints", "permalink": "/web/api/endpoints", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 2, "frontMatter": {"id": "endpoints", "title": "API Endpoints", "sidebar_position": 2}, "sidebar": "webSidebar", "previous": {"title": "Authentication", "permalink": "/web/api/authentication"}, "next": {"title": "Full API Documentation", "permalink": "/web/api/full-documentation"}}, {"id": "api/full-documentation", "title": "Full API Documentation", "description": "The complete API documentation for the ESB Online web application is generated using Scribe and provides detailed information about all endpoints, including:", "source": "@site/web/api/full-documentation.md", "sourceDirName": "api", "slug": "/api/full-documentation", "permalink": "/web/api/full-documentation", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 4, "frontMatter": {"id": "full-documentation", "title": "Full API Documentation", "sidebar_position": 4}, "sidebar": "webSidebar", "previous": {"title": "API Endpoints", "permalink": "/web/api/endpoints"}, "next": {"title": "ESB Online Web App Types", "permalink": "/web/types/"}}, {"id": "api/intro", "title": "Web App API", "description": "Willkommen zur API-Dokumentation für die ESB Online Web-Anwendung. Diese Dokumentation wird automatisch aus dem Laravel-Backend generiert und bietet eine umfassende Referenz für alle verfügbaren API-Endpunkte.", "source": "@site/web/api/intro.md", "sourceDirName": "api", "slug": "/api/intro", "permalink": "/web/api/intro", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 1, "frontMatter": {"id": "intro", "title": "Web App API", "sidebar_position": 1}, "sidebar": "webSidebar", "previous": {"title": "useVerseReference", "permalink": "/web/composables/useVerseReference"}, "next": {"title": "Web App API", "permalink": "/web/api/intro"}}, {"id": "bible-navigation-flow", "title": "Bibelnavigationsablauf", "description": "Dieses Dokument beschreibt den Ausführungsablauf bei der Navigation zu einer Bibelkapitel-URL (z.B. /Johannes1) und beim Scrollen durch Kapitel.", "source": "@site/web/bible-navigation-flow.md", "sourceDirName": ".", "slug": "/bible-navigation-flow", "permalink": "/web/bible-navigation-flow", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 2, "frontMatter": {"sidebar_position": 2, "title": "Bibelnavigationsablauf"}, "sidebar": "webSidebar", "previous": {"title": "Web-Anwendung", "permalink": "/web/intro"}, "next": {"title": "Stores Overview", "permalink": "/web/stores/"}}, {"id": "components/bibledisplay/ChapterContent", "title": "ChapterContent", "description": "Keine Beschreibung verfügbar", "source": "@site/web/components/bibledisplay/ChapterContent.md", "sourceDirName": "components/bibledisplay", "slug": "/components/bibledisplay/ChapterContent", "permalink": "/web/components/bibledisplay/ChapterContent", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "ChapterContent", "title": "ChapterContent"}, "sidebar": "webSidebar", "previous": {"title": "Bibelanzeige Komponenten", "permalink": "/web/components/bibledisplay/"}, "next": {"title": "ChapterNumber", "permalink": "/web/components/bibledisplay/ChapterNumber"}}, {"id": "components/bibledisplay/ChapterNumber", "title": "ChapterNumber", "description": "Keine Beschreibung verfügbar", "source": "@site/web/components/bibledisplay/ChapterNumber.md", "sourceDirName": "components/bibledisplay", "slug": "/components/bibledisplay/ChapterNumber", "permalink": "/web/components/bibledisplay/ChapterNumber", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "ChapterNumber", "title": "ChapterNumber"}, "sidebar": "webSidebar", "previous": {"title": "ChapterContent", "permalink": "/web/components/bibledisplay/ChapterContent"}, "next": {"title": "ChapterWrapper", "permalink": "/web/components/bibledisplay/ChapterWrapper"}}, {"id": "components/bibledisplay/ChapterWrapper", "title": "ChapterWrapper", "description": "Keine Beschreibung verfügbar", "source": "@site/web/components/bibledisplay/ChapterWrapper.md", "sourceDirName": "components/bibledisplay", "slug": "/components/bibledisplay/ChapterWrapper", "permalink": "/web/components/bibledisplay/ChapterWrapper", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "ChapterWrapper", "title": "ChapterWrapper"}, "sidebar": "webSidebar", "previous": {"title": "ChapterNumber", "permalink": "/web/components/bibledisplay/ChapterNumber"}, "next": {"title": "FootnoteContent", "permalink": "/web/components/bibledisplay/FootnoteContent"}}, {"id": "components/bibledisplay/FootnoteContent", "title": "FootnoteContent", "description": "Keine Beschreibung verfügbar", "source": "@site/web/components/bibledisplay/FootnoteContent.md", "sourceDirName": "components/bibledisplay", "slug": "/components/bibledisplay/FootnoteContent", "permalink": "/web/components/bibledisplay/FootnoteContent", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "FootnoteContent", "title": "FootnoteContent"}, "sidebar": "webSidebar", "previous": {"title": "ChapterWrapper", "permalink": "/web/components/bibledisplay/ChapterWrapper"}, "next": {"title": "FootnoteTooltip", "permalink": "/web/components/bibledisplay/FootnoteTooltip"}}, {"id": "components/bibledisplay/FootnoteTooltip", "title": "FootnoteTooltip", "description": "Keine Beschreibung verfügbar", "source": "@site/web/components/bibledisplay/FootnoteTooltip.md", "sourceDirName": "components/bibledisplay", "slug": "/components/bibledisplay/FootnoteTooltip", "permalink": "/web/components/bibledisplay/FootnoteTooltip", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "FootnoteTooltip", "title": "FootnoteTooltip"}, "sidebar": "webSidebar", "previous": {"title": "FootnoteContent", "permalink": "/web/components/bibledisplay/FootnoteContent"}, "next": {"title": "FrontMatter", "permalink": "/web/components/bibledisplay/FrontMatter"}}, {"id": "components/bibledisplay/FrontMatter", "title": "FrontMatter", "description": "Keine Beschreibung verfügbar", "source": "@site/web/components/bibledisplay/FrontMatter.md", "sourceDirName": "components/bibledisplay", "slug": "/components/bibledisplay/FrontMatter", "permalink": "/web/components/bibledisplay/FrontMatter", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "FrontMatter", "title": "FrontMatter"}, "sidebar": "webSidebar", "previous": {"title": "FootnoteTooltip", "permalink": "/web/components/bibledisplay/FootnoteTooltip"}, "next": {"title": "InfoItem", "permalink": "/web/components/bibledisplay/InfoItem"}}, {"id": "components/bibledisplay/index", "title": "Bibelanzeige Komponenten", "description": "Dieser Abschnitt enthält Dokumentation für die folgenden Komponenten:", "source": "@site/web/components/bibledisplay/index.md", "sourceDirName": "components/bibledisplay", "slug": "/components/bibledisplay/", "permalink": "/web/components/bibledisplay/", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 1, "frontMatter": {"id": "index", "title": "Bibelanzeige Komponenten", "sidebar_position": 1}, "sidebar": "webSidebar", "previous": {"title": "Web-Komponenten", "permalink": "/web/components/"}, "next": {"title": "Bibelanzeige Komponenten", "permalink": "/web/components/bibledisplay/"}}, {"id": "components/bibledisplay/InfoItem", "title": "InfoItem", "description": "Keine Beschreibung verfügbar", "source": "@site/web/components/bibledisplay/InfoItem.md", "sourceDirName": "components/bibledisplay", "slug": "/components/bibledisplay/InfoItem", "permalink": "/web/components/bibledisplay/InfoItem", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "InfoItem", "title": "InfoItem"}, "sidebar": "webSidebar", "previous": {"title": "FrontMatter", "permalink": "/web/components/bibledisplay/FrontMatter"}, "next": {"title": "InfoSection", "permalink": "/web/components/bibledisplay/InfoSection"}}, {"id": "components/bibledisplay/InfoSection", "title": "InfoSection", "description": "Keine Beschreibung verfügbar", "source": "@site/web/components/bibledisplay/InfoSection.md", "sourceDirName": "components/bibledisplay", "slug": "/components/bibledisplay/InfoSection", "permalink": "/web/components/bibledisplay/InfoSection", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "InfoSection", "title": "InfoSection"}, "sidebar": "webSidebar", "previous": {"title": "InfoItem", "permalink": "/web/components/bibledisplay/InfoItem"}, "next": {"title": "NumberSelector", "permalink": "/web/components/bibledisplay/NumberSelector"}}, {"id": "components/bibledisplay/NumberSelector", "title": "NumberSelector", "description": "Keine Beschreibung verfügbar", "source": "@site/web/components/bibledisplay/NumberSelector.md", "sourceDirName": "components/bibledisplay", "slug": "/components/bibledisplay/NumberSelector", "permalink": "/web/components/bibledisplay/NumberSelector", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "NumberSelector", "title": "NumberSelector"}, "sidebar": "webSidebar", "previous": {"title": "InfoSection", "permalink": "/web/components/bibledisplay/InfoSection"}, "next": {"title": "ReferenceSelector", "permalink": "/web/components/bibledisplay/ReferenceSelector"}}, {"id": "components/bibledisplay/ReferenceSelector", "title": "ReferenceSelector", "description": "Keine Beschreibung verfügbar", "source": "@site/web/components/bibledisplay/ReferenceSelector.md", "sourceDirName": "components/bibledisplay", "slug": "/components/bibledisplay/ReferenceSelector", "permalink": "/web/components/bibledisplay/ReferenceSelector", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "ReferenceSelector", "title": "ReferenceSelector"}, "sidebar": "webSidebar", "previous": {"title": "NumberSelector", "permalink": "/web/components/bibledisplay/NumberSelector"}, "next": {"title": "TextFormatDropdown", "permalink": "/web/components/bibledisplay/TextFormatDropdown"}}, {"id": "components/bibledisplay/TextFormatDropdown", "title": "TextFormatDropdown", "description": "Keine Beschreibung verfügbar", "source": "@site/web/components/bibledisplay/TextFormatDropdown.md", "sourceDirName": "components/bibledisplay", "slug": "/components/bibledisplay/TextFormatDropdown", "permalink": "/web/components/bibledisplay/TextFormatDropdown", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "TextFormatDropdown", "title": "TextFormatDropdown"}, "sidebar": "webSidebar", "previous": {"title": "ReferenceSelector", "permalink": "/web/components/bibledisplay/ReferenceSelector"}, "next": {"title": "UnavailableBookNotice", "permalink": "/web/components/bibledisplay/UnavailableBookNotice"}}, {"id": "components/bibledisplay/UnavailableBookNotice", "title": "UnavailableBookNotice", "description": "Keine Beschreibung verfügbar", "source": "@site/web/components/bibledisplay/UnavailableBookNotice.md", "sourceDirName": "components/bibledisplay", "slug": "/components/bibledisplay/UnavailableBookNotice", "permalink": "/web/components/bibledisplay/UnavailableBookNotice", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "UnavailableBookNotice", "title": "UnavailableBookNotice"}, "sidebar": "webSidebar", "previous": {"title": "TextFormatDropdown", "permalink": "/web/components/bibledisplay/TextFormatDropdown"}, "next": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "permalink": "/web/components/bibledisplay/VerseContent"}}, {"id": "components/bibledisplay/VerseContent", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Keine Beschreibung verfügbar", "source": "@site/web/components/bibledisplay/VerseContent.md", "sourceDirName": "components/bibledisplay", "slug": "/components/bibledisplay/VerseContent", "permalink": "/web/components/bibledisplay/VerseContent", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sidebar": "webSidebar", "previous": {"title": "UnavailableBookNotice", "permalink": "/web/components/bibledisplay/UnavailableBookNotice"}, "next": {"title": "VerseNumber", "permalink": "/web/components/bibledisplay/VerseNumber"}}, {"id": "components/bibledisplay/VerseNumber", "title": "VerseNumber", "description": "Keine Beschreibung verfügbar", "source": "@site/web/components/bibledisplay/VerseNumber.md", "sourceDirName": "components/bibledisplay", "slug": "/components/bibledisplay/VerseNumber", "permalink": "/web/components/bibledisplay/VerseNumber", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "VerseNumber", "title": "VerseNumber"}, "sidebar": "webSidebar", "previous": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "permalink": "/web/components/bibledisplay/VerseContent"}, "next": {"title": "WordGroupContainer", "permalink": "/web/components/bibledisplay/WordGroupContainer"}}, {"id": "components/bibledisplay/WordGroupContainer", "title": "WordGroupContainer", "description": "Returns the word type for data attribute", "source": "@site/web/components/bibledisplay/WordGroupContainer.md", "sourceDirName": "components/bibledisplay", "slug": "/components/bibledisplay/WordGroupContainer", "permalink": "/web/components/bibledisplay/WordGroupContainer", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "WordGroupContainer", "title": "WordGroupContainer"}, "sidebar": "webSidebar", "previous": {"title": "VerseNumber", "permalink": "/web/components/bibledisplay/VerseNumber"}, "next": {"title": "Navigation Komponenten", "permalink": "/web/components/navigation/"}}, {"id": "components/common/ApplicationLogo", "title": "ApplicationLogo", "description": "Keine Beschreibung verfügbar", "source": "@site/web/components/common/ApplicationLogo.md", "sourceDirName": "components/common", "slug": "/components/common/ApplicationLogo", "permalink": "/web/components/common/ApplicationLogo", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "ApplicationLogo", "title": "ApplicationLogo"}, "sidebar": "webSidebar", "previous": {"title": "Allgemein Komponenten", "permalink": "/web/components/common/"}, "next": {"title": "Checkbox", "permalink": "/web/components/common/Checkbox"}}, {"id": "components/common/Checkbox", "title": "Checkbox", "description": "Keine Beschreibung verfügbar", "source": "@site/web/components/common/Checkbox.md", "sourceDirName": "components/common", "slug": "/components/common/Checkbox", "permalink": "/web/components/common/Checkbox", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "Checkbox", "title": "Checkbox"}, "sidebar": "webSidebar", "previous": {"title": "ApplicationLogo", "permalink": "/web/components/common/ApplicationLogo"}, "next": {"title": "DangerButton", "permalink": "/web/components/common/DangerButton"}}, {"id": "components/common/DangerButton", "title": "DangerButton", "description": "Keine Beschreibung verfügbar", "source": "@site/web/components/common/DangerButton.md", "sourceDirName": "components/common", "slug": "/components/common/DangerButton", "permalink": "/web/components/common/DangerButton", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "DangerButton", "title": "DangerButton"}, "sidebar": "webSidebar", "previous": {"title": "Checkbox", "permalink": "/web/components/common/Checkbox"}, "next": {"title": "Dropdown", "permalink": "/web/components/common/Dropdown"}}, {"id": "components/common/Dropdown", "title": "Dropdown", "description": "Keine Beschreibung verfügbar", "source": "@site/web/components/common/Dropdown.md", "sourceDirName": "components/common", "slug": "/components/common/Dropdown", "permalink": "/web/components/common/Dropdown", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "Dropdown", "title": "Dropdown"}, "sidebar": "webSidebar", "previous": {"title": "DangerButton", "permalink": "/web/components/common/DangerButton"}, "next": {"title": "DropdownLink", "permalink": "/web/components/common/DropdownLink"}}, {"id": "components/common/DropdownLink", "title": "DropdownLink", "description": "Keine Beschreibung verfügbar", "source": "@site/web/components/common/DropdownLink.md", "sourceDirName": "components/common", "slug": "/components/common/DropdownLink", "permalink": "/web/components/common/DropdownLink", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "DropdownLink", "title": "DropdownLink"}, "sidebar": "webSidebar", "previous": {"title": "Dropdown", "permalink": "/web/components/common/Dropdown"}, "next": {"title": "Error<PERSON>ou<PERSON><PERSON>", "permalink": "/web/components/common/ErrorBoundary"}}, {"id": "components/common/ErrorBoundary", "title": "Error<PERSON>ou<PERSON><PERSON>", "description": "Keine Beschreibung verfügbar", "source": "@site/web/components/common/ErrorBoundary.md", "sourceDirName": "components/common", "slug": "/components/common/ErrorBoundary", "permalink": "/web/components/common/ErrorBoundary", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "Error<PERSON>ou<PERSON><PERSON>", "title": "Error<PERSON>ou<PERSON><PERSON>"}, "sidebar": "webSidebar", "previous": {"title": "DropdownLink", "permalink": "/web/components/common/DropdownLink"}, "next": {"title": "InputError", "permalink": "/web/components/common/InputError"}}, {"id": "components/common/index", "title": "Allgemein Komponenten", "description": "Dieser Abschnitt enthält Dokumentation für die folgenden Komponenten:", "source": "@site/web/components/common/index.md", "sourceDirName": "components/common", "slug": "/components/common/", "permalink": "/web/components/common/", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 1, "frontMatter": {"id": "index", "title": "Allgemein Komponenten", "sidebar_position": 1}, "sidebar": "webSidebar", "previous": {"title": "SearchTypeDropdown", "permalink": "/web/components/search/SearchTypeDropdown"}, "next": {"title": "Allgemein Komponenten", "permalink": "/web/components/common/"}}, {"id": "components/common/InputError", "title": "InputError", "description": "Keine Beschreibung verfügbar", "source": "@site/web/components/common/InputError.md", "sourceDirName": "components/common", "slug": "/components/common/InputError", "permalink": "/web/components/common/InputError", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "InputError", "title": "InputError"}, "sidebar": "webSidebar", "previous": {"title": "Error<PERSON>ou<PERSON><PERSON>", "permalink": "/web/components/common/ErrorBoundary"}, "next": {"title": "InputLabel", "permalink": "/web/components/common/InputLabel"}}, {"id": "components/common/InputLabel", "title": "InputLabel", "description": "Keine Beschreibung verfügbar", "source": "@site/web/components/common/InputLabel.md", "sourceDirName": "components/common", "slug": "/components/common/InputLabel", "permalink": "/web/components/common/InputLabel", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "InputLabel", "title": "InputLabel"}, "sidebar": "webSidebar", "previous": {"title": "InputError", "permalink": "/web/components/common/InputError"}, "next": {"title": "LoadingSpinner", "permalink": "/web/components/common/LoadingSpinner"}}, {"id": "components/common/LoadingSpinner", "title": "LoadingSpinner", "description": "Keine Beschreibung verfügbar", "source": "@site/web/components/common/LoadingSpinner.md", "sourceDirName": "components/common", "slug": "/components/common/LoadingSpinner", "permalink": "/web/components/common/LoadingSpinner", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "LoadingSpinner", "title": "LoadingSpinner"}, "sidebar": "webSidebar", "previous": {"title": "InputLabel", "permalink": "/web/components/common/InputLabel"}, "next": {"title": "Modal", "permalink": "/web/components/common/Modal"}}, {"id": "components/common/Modal", "title": "Modal", "description": "Keine Beschreibung verfügbar", "source": "@site/web/components/common/Modal.md", "sourceDirName": "components/common", "slug": "/components/common/Modal", "permalink": "/web/components/common/Modal", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "Modal", "title": "Modal"}, "sidebar": "webSidebar", "previous": {"title": "LoadingSpinner", "permalink": "/web/components/common/LoadingSpinner"}, "next": {"title": "Overlay", "permalink": "/web/components/common/Overlay"}}, {"id": "components/common/Overlay", "title": "Overlay", "description": "Keine Beschreibung verfügbar", "source": "@site/web/components/common/Overlay.md", "sourceDirName": "components/common", "slug": "/components/common/Overlay", "permalink": "/web/components/common/Overlay", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "Overlay", "title": "Overlay"}, "sidebar": "webSidebar", "previous": {"title": "Modal", "permalink": "/web/components/common/Modal"}, "next": {"title": "PrimaryButton", "permalink": "/web/components/common/PrimaryButton"}}, {"id": "components/common/PrimaryButton", "title": "PrimaryButton", "description": "Keine Beschreibung verfügbar", "source": "@site/web/components/common/PrimaryButton.md", "sourceDirName": "components/common", "slug": "/components/common/PrimaryButton", "permalink": "/web/components/common/PrimaryButton", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "PrimaryButton", "title": "PrimaryButton"}, "sidebar": "webSidebar", "previous": {"title": "Overlay", "permalink": "/web/components/common/Overlay"}, "next": {"title": "SecondaryButton", "permalink": "/web/components/common/SecondaryButton"}}, {"id": "components/common/SecondaryButton", "title": "SecondaryButton", "description": "Keine Beschreibung verfügbar", "source": "@site/web/components/common/SecondaryButton.md", "sourceDirName": "components/common", "slug": "/components/common/SecondaryButton", "permalink": "/web/components/common/SecondaryButton", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "SecondaryButton", "title": "SecondaryButton"}, "sidebar": "webSidebar", "previous": {"title": "PrimaryButton", "permalink": "/web/components/common/PrimaryButton"}, "next": {"title": "TextInput", "permalink": "/web/components/common/TextInput"}}, {"id": "components/common/TextInput", "title": "TextInput", "description": "Keine Beschreibung verfügbar", "source": "@site/web/components/common/TextInput.md", "sourceDirName": "components/common", "slug": "/components/common/TextInput", "permalink": "/web/components/common/TextInput", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "TextInput", "title": "TextInput"}, "sidebar": "webSidebar", "previous": {"title": "SecondaryButton", "permalink": "/web/components/common/SecondaryButton"}}, {"id": "components/index", "title": "Web-Komponenten", "description": "Die ESB Online Webanwendung verwendet Vue.js-Komponenten, die in die folgenden Kategorien unterteilt sind:", "source": "@site/web/components/index.md", "sourceDirName": "components", "slug": "/components/", "permalink": "/web/components/", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 1, "frontMatter": {"id": "index", "title": "Web-Komponenten", "sidebar_position": 1}, "sidebar": "webSidebar", "previous": {"title": "Store Types", "permalink": "/web/types/store-types"}, "next": {"title": "Web-Komponenten", "permalink": "/web/components/"}}, {"id": "components/intro", "title": "Web Components", "description": "Vue component documentation for the ESB Online web application.", "source": "@site/web/components/intro.md", "sourceDirName": "components", "slug": "/components/intro", "permalink": "/web/components/intro", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 1, "frontMatter": {"id": "intro", "title": "Web Components", "sidebar_position": 1}}, {"id": "components/navigation/BibleBookDropdown", "title": "BibleBookDropdown", "description": "Keine Beschreibung verfügbar", "source": "@site/web/components/navigation/BibleBookDropdown.md", "sourceDirName": "components/navigation", "slug": "/components/navigation/BibleBookDropdown", "permalink": "/web/components/navigation/BibleBookDropdown", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "BibleBookDropdown", "title": "BibleBookDropdown"}, "sidebar": "webSidebar", "previous": {"title": "Navigation Komponenten", "permalink": "/web/components/navigation/"}, "next": {"title": "BibleBookOffcanvas", "permalink": "/web/components/navigation/BibleBookOffcanvas"}}, {"id": "components/navigation/BibleBookOffcanvas", "title": "BibleBookOffcanvas", "description": "Keine Beschreibung verfügbar", "source": "@site/web/components/navigation/BibleBookOffcanvas.md", "sourceDirName": "components/navigation", "slug": "/components/navigation/BibleBookOffcanvas", "permalink": "/web/components/navigation/BibleBookOffcanvas", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "BibleBookOffcanvas", "title": "BibleBookOffcanvas"}, "sidebar": "webSidebar", "previous": {"title": "BibleBookDropdown", "permalink": "/web/components/navigation/BibleBookDropdown"}, "next": {"title": "BibleBookSelector", "permalink": "/web/components/navigation/BibleBookSelector"}}, {"id": "components/navigation/BibleBookSelector", "title": "BibleBookSelector", "description": "Keine Beschreibung verfügbar", "source": "@site/web/components/navigation/BibleBookSelector.md", "sourceDirName": "components/navigation", "slug": "/components/navigation/BibleBookSelector", "permalink": "/web/components/navigation/BibleBookSelector", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "BibleBookSelector", "title": "BibleBookSelector"}, "sidebar": "webSidebar", "previous": {"title": "BibleBookOffcanvas", "permalink": "/web/components/navigation/BibleBookOffcanvas"}, "next": {"title": "MobileSearchOverlay", "permalink": "/web/components/navigation/MobileSearchOverlay"}}, {"id": "components/navigation/index", "title": "Navigation Komponenten", "description": "Dieser Abschnitt enthält Dokumentation für die folgenden Komponenten:", "source": "@site/web/components/navigation/index.md", "sourceDirName": "components/navigation", "slug": "/components/navigation/", "permalink": "/web/components/navigation/", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 1, "frontMatter": {"id": "index", "title": "Navigation Komponenten", "sidebar_position": 1}, "sidebar": "webSidebar", "previous": {"title": "WordGroupContainer", "permalink": "/web/components/bibledisplay/WordGroupContainer"}, "next": {"title": "Navigation Komponenten", "permalink": "/web/components/navigation/"}}, {"id": "components/navigation/MobileSearchOverlay", "title": "MobileSearchOverlay", "description": "MobileSearchOverlay component  Displays a search overlay for mobile devices with a search input and close button. This component is shown when the search is expanded in the NavigationBar.  @emits close - Emitted when the close button is clicked", "source": "@site/web/components/navigation/MobileSearchOverlay.md", "sourceDirName": "components/navigation", "slug": "/components/navigation/MobileSearchOverlay", "permalink": "/web/components/navigation/MobileSearchOverlay", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "MobileSearchOverlay", "title": "MobileSearchOverlay"}, "sidebar": "webSidebar", "previous": {"title": "BibleBookSelector", "permalink": "/web/components/navigation/BibleBookSelector"}, "next": {"title": "NavLink", "permalink": "/web/components/navigation/NavLink"}}, {"id": "components/navigation/NavigationBar", "title": "NavigationBar", "description": "Interface for scroll handling configuration", "source": "@site/web/components/navigation/NavigationBar.md", "sourceDirName": "components/navigation", "slug": "/components/navigation/NavigationBar", "permalink": "/web/components/navigation/NavigationBar", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "NavigationBar", "title": "NavigationBar"}, "sidebar": "webSidebar", "previous": {"title": "NavLink", "permalink": "/web/components/navigation/NavLink"}, "next": {"title": "OffcanvasSidebar", "permalink": "/web/components/navigation/OffcanvasSidebar"}}, {"id": "components/navigation/NavLink", "title": "NavLink", "description": "Keine Beschreibung verfügbar", "source": "@site/web/components/navigation/NavLink.md", "sourceDirName": "components/navigation", "slug": "/components/navigation/NavLink", "permalink": "/web/components/navigation/NavLink", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "NavLink", "title": "NavLink"}, "sidebar": "webSidebar", "previous": {"title": "MobileSearchOverlay", "permalink": "/web/components/navigation/MobileSearchOverlay"}, "next": {"title": "NavigationBar", "permalink": "/web/components/navigation/NavigationBar"}}, {"id": "components/navigation/OffcanvasSidebar", "title": "OffcanvasSidebar", "description": "Keine Beschreibung verfügbar", "source": "@site/web/components/navigation/OffcanvasSidebar.md", "sourceDirName": "components/navigation", "slug": "/components/navigation/OffcanvasSidebar", "permalink": "/web/components/navigation/OffcanvasSidebar", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "OffcanvasSidebar", "title": "OffcanvasSidebar"}, "sidebar": "webSidebar", "previous": {"title": "NavigationBar", "permalink": "/web/components/navigation/NavigationBar"}, "next": {"title": "ResponsiveNavLink", "permalink": "/web/components/navigation/ResponsiveNavLink"}}, {"id": "components/navigation/ReferenceSelector", "title": "ReferenceSelector", "description": "Keine Beschreibung verfügbar", "source": "@site/web/components/navigation/ReferenceSelector.md", "sourceDirName": "components/navigation", "slug": "/components/navigation/ReferenceSelector", "permalink": "/web/components/navigation/ReferenceSelector", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "ReferenceSelector", "title": "ReferenceSelector"}}, {"id": "components/navigation/ResponsiveNavLink", "title": "ResponsiveNavLink", "description": "Keine Beschreibung verfügbar", "source": "@site/web/components/navigation/ResponsiveNavLink.md", "sourceDirName": "components/navigation", "slug": "/components/navigation/ResponsiveNavLink", "permalink": "/web/components/navigation/ResponsiveNavLink", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "ResponsiveNavLink", "title": "ResponsiveNavLink"}, "sidebar": "webSidebar", "previous": {"title": "OffcanvasSidebar", "permalink": "/web/components/navigation/OffcanvasSidebar"}, "next": {"title": "SubNavigationBar", "permalink": "/web/components/navigation/SubNavigationBar"}}, {"id": "components/navigation/SubNavigationBar", "title": "SubNavigationBar", "description": "Toggles the bookmark state TODO: Implement bookmark functionality", "source": "@site/web/components/navigation/SubNavigationBar.md", "sourceDirName": "components/navigation", "slug": "/components/navigation/SubNavigationBar", "permalink": "/web/components/navigation/SubNavigationBar", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "SubNavigationBar", "title": "SubNavigationBar"}, "sidebar": "webSidebar", "previous": {"title": "ResponsiveNavLink", "permalink": "/web/components/navigation/ResponsiveNavLink"}, "next": {"title": "<PERSON><PERSON> Kompo<PERSON>en", "permalink": "/web/components/search/"}}, {"id": "components/search/BibleSearch", "title": "BibleSearch", "description": "Keine Beschreibung verfügbar", "source": "@site/web/components/search/BibleSearch.md", "sourceDirName": "components/search", "slug": "/components/search/BibleSearch", "permalink": "/web/components/search/BibleSearch", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "BibleSearch", "title": "BibleSearch"}, "sidebar": "webSidebar", "previous": {"title": "<PERSON><PERSON> Kompo<PERSON>en", "permalink": "/web/components/search/"}, "next": {"title": "SearchResultItem", "permalink": "/web/components/search/SearchResultItem"}}, {"id": "components/search/index", "title": "<PERSON><PERSON> Kompo<PERSON>en", "description": "Dieser Abschnitt enthält Dokumentation für die folgenden Komponenten:", "source": "@site/web/components/search/index.md", "sourceDirName": "components/search", "slug": "/components/search/", "permalink": "/web/components/search/", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 1, "frontMatter": {"id": "index", "title": "<PERSON><PERSON> Kompo<PERSON>en", "sidebar_position": 1}, "sidebar": "webSidebar", "previous": {"title": "SubNavigationBar", "permalink": "/web/components/navigation/SubNavigationBar"}, "next": {"title": "<PERSON><PERSON> Kompo<PERSON>en", "permalink": "/web/components/search/"}}, {"id": "components/search/SearchCategoryToggle", "title": "SearchCategoryToggle", "description": "Keine Beschreibung verfügbar", "source": "@site/web/components/search/SearchCategoryToggle.md", "sourceDirName": "components/search", "slug": "/components/search/SearchCategoryToggle", "permalink": "/web/components/search/SearchCategoryToggle", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "SearchCategoryToggle", "title": "SearchCategoryToggle"}}, {"id": "components/search/SearchResultItem", "title": "SearchResultItem", "description": "Keine Beschreibung verfügbar", "source": "@site/web/components/search/SearchResultItem.md", "sourceDirName": "components/search", "slug": "/components/search/SearchResultItem", "permalink": "/web/components/search/SearchResultItem", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "SearchResultItem", "title": "SearchResultItem"}, "sidebar": "webSidebar", "previous": {"title": "BibleSearch", "permalink": "/web/components/search/BibleSearch"}, "next": {"title": "SearchTypeDropdown", "permalink": "/web/components/search/SearchTypeDropdown"}}, {"id": "components/search/SearchTypeDropdown", "title": "SearchTypeDropdown", "description": "Keine Beschreibung verfügbar", "source": "@site/web/components/search/SearchTypeDropdown.md", "sourceDirName": "components/search", "slug": "/components/search/SearchTypeDropdown", "permalink": "/web/components/search/SearchTypeDropdown", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "SearchTypeDropdown", "title": "SearchTypeDropdown"}, "sidebar": "webSidebar", "previous": {"title": "SearchResultItem", "permalink": "/web/components/search/SearchResultItem"}, "next": {"title": "Allgemein Komponenten", "permalink": "/web/components/common/"}}, {"id": "composables/index", "title": "Composables Overview", "description": "This section contains documentation for all the Vue composables used in the ESB Online web application.", "source": "@site/web/composables/index.md", "sourceDirName": "composables", "slug": "/composables/", "permalink": "/web/composables/", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 1, "frontMatter": {"id": "index", "title": "Composables Overview", "sidebar_position": 1}, "sidebar": "webSidebar", "previous": {"title": "textSettings Store", "permalink": "/web/stores/textSettingsStore"}, "next": {"title": "Composables Overview", "permalink": "/web/composables/"}}, {"id": "composables/intro", "title": "Web App Composables", "description": "Documentation for the Vue composables used in the ESB Online web application.", "source": "@site/web/composables/intro.md", "sourceDirName": "composables", "slug": "/composables/intro", "permalink": "/web/composables/intro", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 1, "frontMatter": {"id": "intro", "title": "Web App Composables", "sidebar_position": 1}}, {"id": "composables/useDebounce", "title": "useDebounce", "description": "Provides debounce functionality to delay function execution", "source": "@site/web/composables/useDebounce.md", "sourceDirName": "composables", "slug": "/composables/useDebounce", "permalink": "/web/composables/useDebounce", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 2, "frontMatter": {"id": "useDebounce", "title": "useDebounce", "sidebar_position": 2}, "sidebar": "webSidebar", "previous": {"title": "Composables Overview", "permalink": "/web/composables/"}, "next": {"title": "useDropdown", "permalink": "/web/composables/useDropdown"}}, {"id": "composables/useDropdown", "title": "useDropdown", "description": "Manages dropdown UI component state and interactions", "source": "@site/web/composables/useDropdown.md", "sourceDirName": "composables", "slug": "/composables/useDropdown", "permalink": "/web/composables/useDropdown", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 2, "frontMatter": {"id": "useDropdown", "title": "useDropdown", "sidebar_position": 2}, "sidebar": "webSidebar", "previous": {"title": "useDebounce", "permalink": "/web/composables/useDebounce"}, "next": {"title": "useScrollManager", "permalink": "/web/composables/useScrollManager"}}, {"id": "composables/useLocalStorage", "title": "isLocalStorageAvailable", "description": "/ /", "source": "@site/web/composables/useLocalStorage.md", "sourceDirName": "composables", "slug": "/composables/useLocalStorage", "permalink": "/web/composables/useLocalStorage", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 2, "frontMatter": {"id": "useLocalStorage", "title": "isLocalStorageAvailable", "sidebar_position": 2}}, {"id": "composables/useScrollManager", "title": "useScrollManager", "description": "Handles scroll events and position management", "source": "@site/web/composables/useScrollManager.md", "sourceDirName": "composables", "slug": "/composables/useScrollManager", "permalink": "/web/composables/useScrollManager", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 2, "frontMatter": {"id": "useScrollManager", "title": "useScrollManager", "sidebar_position": 2}, "sidebar": "webSidebar", "previous": {"title": "useDropdown", "permalink": "/web/composables/useDropdown"}, "next": {"title": "useSearchResults", "permalink": "/web/composables/useSearchResults"}}, {"id": "composables/useSearchResults", "title": "useSearchResults", "description": "Manages search results and search-related functionality", "source": "@site/web/composables/useSearchResults.md", "sourceDirName": "composables", "slug": "/composables/useSearchResults", "permalink": "/web/composables/useSearchResults", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 2, "frontMatter": {"id": "useSearchResults", "title": "useSearchResults", "sidebar_position": 2}, "sidebar": "webSidebar", "previous": {"title": "useScrollManager", "permalink": "/web/composables/useScrollManager"}, "next": {"title": "useTextSettings", "permalink": "/web/composables/useTextSettings"}}, {"id": "composables/useSettingsAside", "title": "useSettingsAside", "description": "Composable for SettingsAside", "source": "@site/web/composables/useSettingsAside.md", "sourceDirName": "composables", "slug": "/composables/useSettingsAside", "permalink": "/web/composables/useSettingsAside", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 2, "frontMatter": {"id": "useSettingsAside", "title": "useSettingsAside", "sidebar_position": 2}}, {"id": "composables/useTextSettings", "title": "useTextSettings", "description": "Manages text display settings and preferences", "source": "@site/web/composables/useTextSettings.md", "sourceDirName": "composables", "slug": "/composables/useTextSettings", "permalink": "/web/composables/useTextSettings", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 2, "frontMatter": {"id": "useTextSettings", "title": "useTextSettings", "sidebar_position": 2}, "sidebar": "webSidebar", "previous": {"title": "useSearchResults", "permalink": "/web/composables/useSearchResults"}, "next": {"title": "useThrottle", "permalink": "/web/composables/useThrottle"}}, {"id": "composables/useThrottle", "title": "useThrottle", "description": "Provides throttle functionality to limit function execution frequency", "source": "@site/web/composables/useThrottle.md", "sourceDirName": "composables", "slug": "/composables/useThrottle", "permalink": "/web/composables/useThrottle", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 2, "frontMatter": {"id": "useThrottle", "title": "useThrottle", "sidebar_position": 2}, "sidebar": "webSidebar", "previous": {"title": "useTextSettings", "permalink": "/web/composables/useTextSettings"}, "next": {"title": "useVerseReference", "permalink": "/web/composables/useVerseReference"}}, {"id": "composables/useVerseReference", "title": "useVerseReference", "description": "Handles Bible verse references and navigation", "source": "@site/web/composables/useVerseReference.md", "sourceDirName": "composables", "slug": "/composables/useVerseReference", "permalink": "/web/composables/useVerseReference", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 2, "frontMatter": {"id": "useVerseReference", "title": "useVerseReference", "sidebar_position": 2}, "sidebar": "webSidebar", "previous": {"title": "useThrottle", "permalink": "/web/composables/useThrottle"}, "next": {"title": "Web App API", "permalink": "/web/api/intro"}}, {"id": "intro", "title": "Web-Anwendung", "description": "Diese Dokumentation beschreibt die ESB Online Web-Anwendung, die mit Laravel 11 und Vue 3 entwickelt wurde.", "source": "@site/web/intro.md", "sourceDirName": ".", "slug": "/intro", "permalink": "/web/intro", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 1, "frontMatter": {"id": "intro", "title": "Web-Anwendung", "sidebar_position": 1}, "sidebar": "webSidebar", "next": {"title": "Bibelnavigationsablauf", "permalink": "/web/bible-navigation-flow"}}, {"id": "stores/bibleDataStore", "title": "bible-data Store", "description": "/ Store for managing Bible data (books, chapters) /", "source": "@site/web/stores/bibleDataStore.md", "sourceDirName": "stores", "slug": "/stores/bibleDataStore", "permalink": "/web/stores/bibleDataStore", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 2, "frontMatter": {"id": "bibleDataStore", "title": "bible-data Store", "sidebar_position": 2}, "sidebar": "webSidebar", "previous": {"title": "Stores Overview", "permalink": "/web/stores/"}, "next": {"title": "bible-section Store", "permalink": "/web/stores/bibleSectionStore"}}, {"id": "stores/bibleHighlightStore", "title": "bible-highlight Store", "description": "/ Store for managing word highlighting functionality /", "source": "@site/web/stores/bibleHighlightStore.md", "sourceDirName": "stores", "slug": "/stores/bibleHighlightStore", "permalink": "/web/stores/bibleHighlightStore", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 2, "frontMatter": {"id": "bibleHighlightStore", "title": "bible-highlight Store", "sidebar_position": 2}, "sidebar": "webSidebar", "previous": {"title": "bible-section Store", "permalink": "/web/stores/bibleSectionStore"}, "next": {"title": "bible-memory Store", "permalink": "/web/stores/bibleMemoryStore"}}, {"id": "stores/bibleMemoryStore", "title": "bible-memory Store", "description": "/ Store for managing memory usage and chapter cleanup /", "source": "@site/web/stores/bibleMemoryStore.md", "sourceDirName": "stores", "slug": "/stores/bibleMemoryStore", "permalink": "/web/stores/bibleMemoryStore", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 2, "frontMatter": {"id": "bibleMemoryStore", "title": "bible-memory Store", "sidebar_position": 2}, "sidebar": "webSidebar", "previous": {"title": "bible-highlight Store", "permalink": "/web/stores/bibleHighlightStore"}, "next": {"title": "search Store", "permalink": "/web/stores/searchStore"}}, {"id": "stores/bibleSectionStore", "title": "bible-section Store", "description": "Bible sections and navigation", "source": "@site/web/stores/bibleSectionStore.md", "sourceDirName": "stores", "slug": "/stores/bibleSectionStore", "permalink": "/web/stores/bibleSectionStore", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 2, "frontMatter": {"id": "bibleSectionStore", "title": "bible-section Store", "sidebar_position": 2}, "sidebar": "webSidebar", "previous": {"title": "bible-data Store", "permalink": "/web/stores/bibleDataStore"}, "next": {"title": "bible-highlight Store", "permalink": "/web/stores/bibleHighlightStore"}}, {"id": "stores/index", "title": "Stores Overview", "description": "This section contains documentation for all the Pinia stores used in the ESB Online web application.", "source": "@site/web/stores/index.md", "sourceDirName": "stores", "slug": "/stores/", "permalink": "/web/stores/", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 1, "frontMatter": {"id": "index", "title": "Stores Overview", "sidebar_position": 1}, "sidebar": "webSidebar", "previous": {"title": "Bibelnavigationsablauf", "permalink": "/web/bible-navigation-flow"}, "next": {"title": "Stores Overview", "permalink": "/web/stores/"}}, {"id": "stores/intro", "title": "Web App Stores", "description": "Documentation for the Pinia stores used in the ESB Online web application.", "source": "@site/web/stores/intro.md", "sourceDirName": "stores", "slug": "/stores/intro", "permalink": "/web/stores/intro", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 1, "frontMatter": {"id": "intro", "title": "Web App Stores", "sidebar_position": 1}}, {"id": "stores/searchSettingsStore", "title": "search-settings Store", "description": "Search configuration and settings", "source": "@site/web/stores/searchSettingsStore.md", "sourceDirName": "stores", "slug": "/stores/searchSettingsStore", "permalink": "/web/stores/searchSettingsStore", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 2, "frontMatter": {"id": "searchSettingsStore", "title": "search-settings Store", "sidebar_position": 2}, "sidebar": "webSidebar", "previous": {"title": "search Store", "permalink": "/web/stores/searchStore"}, "next": {"title": "textSettings Store", "permalink": "/web/stores/textSettingsStore"}}, {"id": "stores/searchStore", "title": "search Store", "description": "Bible search functionality and results", "source": "@site/web/stores/searchStore.md", "sourceDirName": "stores", "slug": "/stores/searchStore", "permalink": "/web/stores/searchStore", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 2, "frontMatter": {"id": "searchStore", "title": "search Store", "sidebar_position": 2}, "sidebar": "webSidebar", "previous": {"title": "bible-memory Store", "permalink": "/web/stores/bibleMemoryStore"}, "next": {"title": "search-settings Store", "permalink": "/web/stores/searchSettingsStore"}}, {"id": "stores/textSettingsStore", "title": "textSettings Store", "description": "Text display settings and preferences", "source": "@site/web/stores/textSettingsStore.md", "sourceDirName": "stores", "slug": "/stores/textSettingsStore", "permalink": "/web/stores/textSettingsStore", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 2, "frontMatter": {"id": "textSettingsStore", "title": "textSettings Store", "sidebar_position": 2}, "sidebar": "webSidebar", "previous": {"title": "search-settings Store", "permalink": "/web/stores/searchSettingsStore"}, "next": {"title": "Composables Overview", "permalink": "/web/composables/"}}, {"id": "types/bible-navigation-store", "title": "Bibel-Navigations-Store", "description": "Der Bibel-Navigations-Store ist ein Pinia-Store, der den Navigationszustand für die Bibelanwendung verwaltet.", "source": "@site/web/types/bible-navigation-store.md", "sourceDirName": "types", "slug": "/types/bible-navigation-store", "permalink": "/web/types/bible-navigation-store", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {}}, {"id": "types/intro", "title": "Web-App-Typen", "description": "TypeScript-Typdokumentation für die ESB Online Webanwendung.", "source": "@site/web/types/intro.md", "sourceDirName": "types", "slug": "/types/intro", "permalink": "/web/types/intro", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 1, "frontMatter": {"id": "intro", "title": "Web-App-Typen", "sidebar_position": 1}}, {"id": "types/readme", "title": "ESB Online Web App Types", "description": "This section contains documentation for the TypeScript types used in the ESB Online web application.", "source": "@site/web/types/readme.md", "sourceDirName": "types", "slug": "/types/", "permalink": "/web/types/", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {}, "sidebar": "webSidebar", "previous": {"title": "Full API Documentation", "permalink": "/web/api/full-documentation"}, "next": {"title": "ESB Online Web App Types", "permalink": "/web/types/"}}, {"id": "types/sidebar", "title": "Web App Types Sidebar", "description": "- Overview", "source": "@site/web/types/sidebar.md", "sourceDirName": "types", "slug": "/types/sidebar", "permalink": "/web/types/sidebar", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {}}, {"id": "types/store-types", "title": "Store Types", "description": "This module contains types for Pinia stores in the web application.", "source": "@site/web/types/store-types.md", "sourceDirName": "types", "slug": "/types/store-types", "permalink": "/web/types/store-types", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {}, "sidebar": "webSidebar", "previous": {"title": "ESB Online Web App Types", "permalink": "/web/types/"}, "next": {"title": "Web-Komponenten", "permalink": "/web/components/"}}], "drafts": [], "sidebars": {"webSidebar": [{"type": "doc", "id": "intro"}, {"type": "doc", "id": "bible-navigation-flow"}, {"type": "category", "label": "<PERSON><PERSON><PERSON><PERSON>", "link": {"type": "doc", "id": "stores/index"}, "items": [{"type": "doc", "id": "stores/index"}, {"type": "category", "label": "Bibel-Speicher", "items": [{"type": "doc", "id": "stores/bibleDataStore"}, {"type": "doc", "id": "stores/bibleSectionStore"}, {"type": "doc", "id": "stores/bibleHighlightStore"}, {"type": "doc", "id": "stores/bibleMemoryStore"}], "collapsed": true, "collapsible": true}, {"type": "doc", "id": "stores/searchStore"}, {"type": "doc", "id": "stores/searchSettingsStore"}, {"type": "doc", "id": "stores/textSettingsStore"}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "Composables", "link": {"type": "doc", "id": "composables/index"}, "items": [{"type": "doc", "id": "composables/index"}, {"type": "doc", "id": "composables/useDebounce"}, {"type": "doc", "id": "composables/useDropdown"}, {"type": "doc", "id": "composables/useScrollManager"}, {"type": "doc", "id": "composables/useSearchResults"}, {"type": "doc", "id": "composables/useTextSettings"}, {"type": "doc", "id": "composables/useThrottle"}, {"type": "doc", "id": "composables/useVerseReference"}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "API", "link": {"type": "doc", "id": "api/intro"}, "items": [{"type": "doc", "id": "api/intro"}, {"type": "doc", "id": "api/authentication"}, {"type": "doc", "id": "api/endpoints"}, {"type": "doc", "id": "api/full-documentation"}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "Typen", "link": {"type": "doc", "id": "types/readme"}, "items": [{"type": "doc", "id": "types/readme"}, {"type": "doc", "id": "types/store-types"}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "Komponenten", "link": {"type": "doc", "id": "components/index"}, "items": [{"type": "doc", "id": "components/index"}, {"type": "category", "label": "Bibelanzeige", "link": {"type": "doc", "id": "components/bibledisplay/index"}, "items": [{"type": "doc", "id": "components/bibledisplay/index"}, {"type": "doc", "id": "components/bibledisplay/ChapterContent"}, {"type": "doc", "id": "components/bibledisplay/ChapterNumber"}, {"type": "doc", "id": "components/bibledisplay/ChapterWrapper"}, {"type": "doc", "id": "components/bibledisplay/FootnoteContent"}, {"type": "doc", "id": "components/bibledisplay/FootnoteTooltip"}, {"type": "doc", "id": "components/bibledisplay/FrontMatter"}, {"type": "doc", "id": "components/bibledisplay/InfoItem"}, {"type": "doc", "id": "components/bibledisplay/InfoSection"}, {"type": "doc", "id": "components/bibledisplay/NumberSelector"}, {"type": "doc", "id": "components/bibledisplay/ReferenceSelector"}, {"type": "doc", "id": "components/bibledisplay/TextFormatDropdown"}, {"type": "doc", "id": "components/bibledisplay/UnavailableBookNotice"}, {"type": "doc", "id": "components/bibledisplay/VerseContent"}, {"type": "doc", "id": "components/bibledisplay/VerseNumber"}, {"type": "doc", "id": "components/bibledisplay/WordGroupContainer"}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "Navigationsleiste", "link": {"type": "doc", "id": "components/navigation/index"}, "items": [{"type": "doc", "id": "components/navigation/index"}, {"type": "doc", "id": "components/navigation/BibleBookDropdown"}, {"type": "doc", "id": "components/navigation/BibleBookOffcanvas"}, {"type": "doc", "id": "components/navigation/BibleBookSelector"}, {"type": "doc", "id": "components/navigation/MobileSearchOverlay"}, {"type": "doc", "id": "components/navigation/NavLink"}, {"type": "doc", "id": "components/navigation/NavigationBar"}, {"type": "doc", "id": "components/navigation/OffcanvasSidebar"}, {"type": "doc", "id": "components/navigation/ResponsiveNavLink"}, {"type": "doc", "id": "components/navigation/SubNavigationBar"}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "<PERSON><PERSON>", "link": {"type": "doc", "id": "components/search/index"}, "items": [{"type": "doc", "id": "components/search/index"}, {"type": "doc", "id": "components/search/BibleSearch"}, {"type": "doc", "id": "components/search/SearchResultItem"}, {"type": "doc", "id": "components/search/SearchTypeDropdown"}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "Allgemein", "link": {"type": "doc", "id": "components/common/index"}, "items": [{"type": "doc", "id": "components/common/index"}, {"type": "doc", "id": "components/common/ApplicationLogo"}, {"type": "doc", "id": "components/common/Checkbox"}, {"type": "doc", "id": "components/common/DangerButton"}, {"type": "doc", "id": "components/common/Dropdown"}, {"type": "doc", "id": "components/common/DropdownLink"}, {"type": "doc", "id": "components/common/ErrorBoundary"}, {"type": "doc", "id": "components/common/InputError"}, {"type": "doc", "id": "components/common/InputLabel"}, {"type": "doc", "id": "components/common/LoadingSpinner"}, {"type": "doc", "id": "components/common/Modal"}, {"type": "doc", "id": "components/common/Overlay"}, {"type": "doc", "id": "components/common/PrimaryButton"}, {"type": "doc", "id": "components/common/SecondaryButton"}, {"type": "doc", "id": "components/common/TextInput"}], "collapsed": true, "collapsible": true}], "collapsed": true, "collapsible": true}]}}]}, "components": {"loadedVersions": [{"versionName": "current", "label": "Next", "banner": null, "badge": false, "noIndex": false, "className": "docs-version-current", "path": "/components", "tagsPath": "/components/tags", "isLast": true, "routePriority": -1, "sidebarFilePath": "/Applications/MAMP/htdocs/ebtc/esra-bibel/docs/sidebars-components.ts", "contentPath": "/Applications/MAMP/htdocs/ebtc/esra-bibel/docs/components", "contentPathLocalized": "/Applications/MAMP/htdocs/ebtc/esra-bibel/docs/i18n/de/docusaurus-plugin-content-docs-components/current", "docs": [{"id": "intro", "title": "<PERSON><PERSON>", "description": "Diese Dokumentation beschreibt die Vue 3 Komponenten, die in der ESB Online Plattform verwendet werden. Die Komponenten sind in TypeScript geschrieben und nutzen die Composition API.", "source": "@site/components/intro.md", "sourceDirName": ".", "slug": "/intro", "permalink": "/components/intro", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 1, "frontMatter": {"id": "intro", "title": "<PERSON><PERSON>", "sidebar_position": 1}, "sidebar": "componentsSidebar"}, {"id": "web/BibleDisplay/ChapterContent", "title": "ChapterContent", "description": "No description available.", "source": "@site/components/web/BibleDisplay/ChapterContent.md", "sourceDirName": "web/BibleDisplay", "slug": "/web/BibleDisplay/ChapterContent", "permalink": "/components/web/BibleDisplay/ChapterContent", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "ChapterContent", "title": "ChapterContent"}}, {"id": "web/BibleDisplay/ChapterNumber", "title": "ChapterNumber", "description": "No description available.", "source": "@site/components/web/BibleDisplay/ChapterNumber.md", "sourceDirName": "web/BibleDisplay", "slug": "/web/BibleDisplay/ChapterNumber", "permalink": "/components/web/BibleDisplay/ChapterNumber", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "ChapterNumber", "title": "ChapterNumber"}}, {"id": "web/BibleDisplay/ChapterWrapper", "title": "ChapterWrapper", "description": "No description available.", "source": "@site/components/web/BibleDisplay/ChapterWrapper.md", "sourceDirName": "web/BibleDisplay", "slug": "/web/BibleDisplay/ChapterWrapper", "permalink": "/components/web/BibleDisplay/ChapterWrapper", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "ChapterWrapper", "title": "ChapterWrapper"}}, {"id": "web/BibleDisplay/FootnoteContent", "title": "FootnoteContent", "description": "No description available.", "source": "@site/components/web/BibleDisplay/FootnoteContent.md", "sourceDirName": "web/BibleDisplay", "slug": "/web/BibleDisplay/FootnoteContent", "permalink": "/components/web/BibleDisplay/FootnoteContent", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "FootnoteContent", "title": "FootnoteContent"}}, {"id": "web/BibleDisplay/FootnoteTooltip", "title": "FootnoteTooltip", "description": "No description available.", "source": "@site/components/web/BibleDisplay/FootnoteTooltip.md", "sourceDirName": "web/BibleDisplay", "slug": "/web/BibleDisplay/FootnoteTooltip", "permalink": "/components/web/BibleDisplay/FootnoteTooltip", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "FootnoteTooltip", "title": "FootnoteTooltip"}}, {"id": "web/BibleDisplay/FrontMatter", "title": "FrontMatter", "description": "No description available.", "source": "@site/components/web/BibleDisplay/FrontMatter.md", "sourceDirName": "web/BibleDisplay", "slug": "/web/BibleDisplay/FrontMatter", "permalink": "/components/web/BibleDisplay/FrontMatter", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "FrontMatter", "title": "FrontMatter"}}, {"id": "web/BibleDisplay/InfoItem", "title": "InfoItem", "description": "No description available.", "source": "@site/components/web/BibleDisplay/InfoItem.md", "sourceDirName": "web/BibleDisplay", "slug": "/web/BibleDisplay/InfoItem", "permalink": "/components/web/BibleDisplay/InfoItem", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "InfoItem", "title": "InfoItem"}}, {"id": "web/BibleDisplay/InfoSection", "title": "InfoSection", "description": "No description available.", "source": "@site/components/web/BibleDisplay/InfoSection.md", "sourceDirName": "web/BibleDisplay", "slug": "/web/BibleDisplay/InfoSection", "permalink": "/components/web/BibleDisplay/InfoSection", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "InfoSection", "title": "InfoSection"}}, {"id": "web/BibleDisplay/NumberSelector", "title": "NumberSelector", "description": "No description available.", "source": "@site/components/web/BibleDisplay/NumberSelector.md", "sourceDirName": "web/BibleDisplay", "slug": "/web/BibleDisplay/NumberSelector", "permalink": "/components/web/BibleDisplay/NumberSelector", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "NumberSelector", "title": "NumberSelector"}}, {"id": "web/BibleDisplay/ReferenceSelector", "title": "ReferenceSelector", "description": "No description available.", "source": "@site/components/web/BibleDisplay/ReferenceSelector.md", "sourceDirName": "web/BibleDisplay", "slug": "/web/BibleDisplay/ReferenceSelector", "permalink": "/components/web/BibleDisplay/ReferenceSelector", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "ReferenceSelector", "title": "ReferenceSelector"}}, {"id": "web/BibleDisplay/TextFormatDropdown", "title": "TextFormatDropdown", "description": "No description available.", "source": "@site/components/web/BibleDisplay/TextFormatDropdown.md", "sourceDirName": "web/BibleDisplay", "slug": "/web/BibleDisplay/TextFormatDropdown", "permalink": "/components/web/BibleDisplay/TextFormatDropdown", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "TextFormatDropdown", "title": "TextFormatDropdown"}}, {"id": "web/BibleDisplay/UnavailableBookNotice", "title": "UnavailableBookNotice", "description": "No description available.", "source": "@site/components/web/BibleDisplay/UnavailableBookNotice.md", "sourceDirName": "web/BibleDisplay", "slug": "/web/BibleDisplay/UnavailableBookNotice", "permalink": "/components/web/BibleDisplay/UnavailableBookNotice", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "UnavailableBookNotice", "title": "UnavailableBookNotice"}}, {"id": "web/BibleDisplay/VerseContent", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "No description available.", "source": "@site/components/web/BibleDisplay/VerseContent.md", "sourceDirName": "web/BibleDisplay", "slug": "/web/BibleDisplay/VerseContent", "permalink": "/components/web/BibleDisplay/VerseContent", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"id": "web/BibleDisplay/VerseNumber", "title": "VerseNumber", "description": "No description available.", "source": "@site/components/web/BibleDisplay/VerseNumber.md", "sourceDirName": "web/BibleDisplay", "slug": "/web/BibleDisplay/VerseNumber", "permalink": "/components/web/BibleDisplay/VerseNumber", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "VerseNumber", "title": "VerseNumber"}}, {"id": "web/BibleDisplay/WordGroupContainer", "title": "WordGroupContainer", "description": "Returns the word type for data attribute", "source": "@site/components/web/BibleDisplay/WordGroupContainer.md", "sourceDirName": "web/BibleDisplay", "slug": "/web/BibleDisplay/WordGroupContainer", "permalink": "/components/web/BibleDisplay/WordGroupContainer", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "WordGroupContainer", "title": "WordGroupContainer"}}, {"id": "web/common/ApplicationLogo", "title": "ApplicationLogo", "description": "No description available.", "source": "@site/components/web/common/ApplicationLogo.md", "sourceDirName": "web/common", "slug": "/web/common/ApplicationLogo", "permalink": "/components/web/common/ApplicationLogo", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "ApplicationLogo", "title": "ApplicationLogo"}}, {"id": "web/common/Checkbox", "title": "Checkbox", "description": "No description available.", "source": "@site/components/web/common/Checkbox.md", "sourceDirName": "web/common", "slug": "/web/common/Checkbox", "permalink": "/components/web/common/Checkbox", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "Checkbox", "title": "Checkbox"}}, {"id": "web/common/DangerButton", "title": "DangerButton", "description": "No description available.", "source": "@site/components/web/common/DangerButton.md", "sourceDirName": "web/common", "slug": "/web/common/DangerButton", "permalink": "/components/web/common/DangerButton", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "DangerButton", "title": "DangerButton"}}, {"id": "web/common/Dropdown", "title": "Dropdown", "description": "No description available.", "source": "@site/components/web/common/Dropdown.md", "sourceDirName": "web/common", "slug": "/web/common/Dropdown", "permalink": "/components/web/common/Dropdown", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "Dropdown", "title": "Dropdown"}}, {"id": "web/common/DropdownLink", "title": "DropdownLink", "description": "No description available.", "source": "@site/components/web/common/DropdownLink.md", "sourceDirName": "web/common", "slug": "/web/common/DropdownLink", "permalink": "/components/web/common/DropdownLink", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "DropdownLink", "title": "DropdownLink"}}, {"id": "web/common/ErrorBoundary", "title": "Error<PERSON>ou<PERSON><PERSON>", "description": "No description available.", "source": "@site/components/web/common/ErrorBoundary.md", "sourceDirName": "web/common", "slug": "/web/common/ErrorBoundary", "permalink": "/components/web/common/ErrorBoundary", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "Error<PERSON>ou<PERSON><PERSON>", "title": "Error<PERSON>ou<PERSON><PERSON>"}}, {"id": "web/common/InputError", "title": "InputError", "description": "No description available.", "source": "@site/components/web/common/InputError.md", "sourceDirName": "web/common", "slug": "/web/common/InputError", "permalink": "/components/web/common/InputError", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "InputError", "title": "InputError"}}, {"id": "web/common/InputLabel", "title": "InputLabel", "description": "No description available.", "source": "@site/components/web/common/InputLabel.md", "sourceDirName": "web/common", "slug": "/web/common/InputLabel", "permalink": "/components/web/common/InputLabel", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "InputLabel", "title": "InputLabel"}}, {"id": "web/common/LoadingSpinner", "title": "LoadingSpinner", "description": "No description available.", "source": "@site/components/web/common/LoadingSpinner.md", "sourceDirName": "web/common", "slug": "/web/common/LoadingSpinner", "permalink": "/components/web/common/LoadingSpinner", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "LoadingSpinner", "title": "LoadingSpinner"}}, {"id": "web/common/Modal", "title": "Modal", "description": "No description available.", "source": "@site/components/web/common/Modal.md", "sourceDirName": "web/common", "slug": "/web/common/Modal", "permalink": "/components/web/common/Modal", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "Modal", "title": "Modal"}}, {"id": "web/common/Overlay", "title": "Overlay", "description": "No description available.", "source": "@site/components/web/common/Overlay.md", "sourceDirName": "web/common", "slug": "/web/common/Overlay", "permalink": "/components/web/common/Overlay", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "Overlay", "title": "Overlay"}}, {"id": "web/common/PrimaryButton", "title": "PrimaryButton", "description": "No description available.", "source": "@site/components/web/common/PrimaryButton.md", "sourceDirName": "web/common", "slug": "/web/common/PrimaryButton", "permalink": "/components/web/common/PrimaryButton", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "PrimaryButton", "title": "PrimaryButton"}}, {"id": "web/common/SecondaryButton", "title": "SecondaryButton", "description": "No description available.", "source": "@site/components/web/common/SecondaryButton.md", "sourceDirName": "web/common", "slug": "/web/common/SecondaryButton", "permalink": "/components/web/common/SecondaryButton", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "SecondaryButton", "title": "SecondaryButton"}}, {"id": "web/common/TextInput", "title": "TextInput", "description": "No description available.", "source": "@site/components/web/common/TextInput.md", "sourceDirName": "web/common", "slug": "/web/common/TextInput", "permalink": "/components/web/common/TextInput", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "TextInput", "title": "TextInput"}}, {"id": "web/Icons/Icon", "title": "Icon", "description": "No description available.", "source": "@site/components/web/Icons/Icon.md", "sourceDirName": "web/Icons", "slug": "/web/Icons/Icon", "permalink": "/components/web/Icons/Icon", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "Icon", "title": "Icon"}}, {"id": "web/Icons/TextSettings", "title": "TextSettings", "description": "No description available.", "source": "@site/components/web/Icons/TextSettings.md", "sourceDirName": "web/Icons", "slug": "/web/Icons/TextSettings", "permalink": "/components/web/Icons/TextSettings", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "TextSettings", "title": "TextSettings"}}, {"id": "web/Navigation/BibleBookDropdown", "title": "BibleBookDropdown", "description": "No description available.", "source": "@site/components/web/Navigation/BibleBookDropdown.md", "sourceDirName": "web/Navigation", "slug": "/web/Navigation/BibleBookDropdown", "permalink": "/components/web/Navigation/BibleBookDropdown", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "BibleBookDropdown", "title": "BibleBookDropdown"}}, {"id": "web/Navigation/BibleBookOffcanvas", "title": "BibleBookOffcanvas", "description": "No description available.", "source": "@site/components/web/Navigation/BibleBookOffcanvas.md", "sourceDirName": "web/Navigation", "slug": "/web/Navigation/BibleBookOffcanvas", "permalink": "/components/web/Navigation/BibleBookOffcanvas", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "BibleBookOffcanvas", "title": "BibleBookOffcanvas"}}, {"id": "web/Navigation/BibleBookSelector", "title": "BibleBookSelector", "description": "No description available.", "source": "@site/components/web/Navigation/BibleBookSelector.md", "sourceDirName": "web/Navigation", "slug": "/web/Navigation/BibleBookSelector", "permalink": "/components/web/Navigation/BibleBookSelector", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "BibleBookSelector", "title": "BibleBookSelector"}}, {"id": "web/Navigation/MobileSearchOverlay", "title": "MobileSearchOverlay", "description": "MobileSearchOverlay component  Displays a search overlay for mobile devices with a search input and close button. This component is shown when the search is expanded in the NavigationBar.  @emits close - Emitted when the close button is clicked", "source": "@site/components/web/Navigation/MobileSearchOverlay.md", "sourceDirName": "web/Navigation", "slug": "/web/Navigation/MobileSearchOverlay", "permalink": "/components/web/Navigation/MobileSearchOverlay", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "MobileSearchOverlay", "title": "MobileSearchOverlay"}}, {"id": "web/Navigation/NavigationBar", "title": "NavigationBar", "description": "Interface for scroll handling configuration", "source": "@site/components/web/Navigation/NavigationBar.md", "sourceDirName": "web/Navigation", "slug": "/web/Navigation/NavigationBar", "permalink": "/components/web/Navigation/NavigationBar", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "NavigationBar", "title": "NavigationBar"}}, {"id": "web/Navigation/NavLink", "title": "NavLink", "description": "No description available.", "source": "@site/components/web/Navigation/NavLink.md", "sourceDirName": "web/Navigation", "slug": "/web/Navigation/NavLink", "permalink": "/components/web/Navigation/NavLink", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "NavLink", "title": "NavLink"}}, {"id": "web/Navigation/OffcanvasSidebar", "title": "OffcanvasSidebar", "description": "No description available.", "source": "@site/components/web/Navigation/OffcanvasSidebar.md", "sourceDirName": "web/Navigation", "slug": "/web/Navigation/OffcanvasSidebar", "permalink": "/components/web/Navigation/OffcanvasSidebar", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "OffcanvasSidebar", "title": "OffcanvasSidebar"}}, {"id": "web/Navigation/ReferenceSelector", "title": "ReferenceSelector", "description": "No description available.", "source": "@site/components/web/Navigation/ReferenceSelector.md", "sourceDirName": "web/Navigation", "slug": "/web/Navigation/ReferenceSelector", "permalink": "/components/web/Navigation/ReferenceSelector", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "ReferenceSelector", "title": "ReferenceSelector"}}, {"id": "web/Navigation/ResponsiveNavLink", "title": "ResponsiveNavLink", "description": "No description available.", "source": "@site/components/web/Navigation/ResponsiveNavLink.md", "sourceDirName": "web/Navigation", "slug": "/web/Navigation/ResponsiveNavLink", "permalink": "/components/web/Navigation/ResponsiveNavLink", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "ResponsiveNavLink", "title": "ResponsiveNavLink"}}, {"id": "web/Navigation/SubNavigationBar", "title": "SubNavigationBar", "description": "Toggles the bookmark state TODO: Implement bookmark functionality", "source": "@site/components/web/Navigation/SubNavigationBar.md", "sourceDirName": "web/Navigation", "slug": "/web/Navigation/SubNavigationBar", "permalink": "/components/web/Navigation/SubNavigationBar", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "SubNavigationBar", "title": "SubNavigationBar"}}, {"id": "web/Search/BibleSearch", "title": "BibleSearch", "description": "No description available.", "source": "@site/components/web/Search/BibleSearch.md", "sourceDirName": "web/Search", "slug": "/web/Search/BibleSearch", "permalink": "/components/web/Search/BibleSearch", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "BibleSearch", "title": "BibleSearch"}}, {"id": "web/Search/SearchCategoryToggle", "title": "SearchCategoryToggle", "description": "No description available.", "source": "@site/components/web/Search/SearchCategoryToggle.md", "sourceDirName": "web/Search", "slug": "/web/Search/SearchCategoryToggle", "permalink": "/components/web/Search/SearchCategoryToggle", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "SearchCategoryToggle", "title": "SearchCategoryToggle"}}, {"id": "web/Search/SearchResultItem", "title": "SearchResultItem", "description": "No description available.", "source": "@site/components/web/Search/SearchResultItem.md", "sourceDirName": "web/Search", "slug": "/web/Search/SearchResultItem", "permalink": "/components/web/Search/SearchResultItem", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "SearchResultItem", "title": "SearchResultItem"}}, {"id": "web/Search/SearchTypeDropdown", "title": "SearchTypeDropdown", "description": "No description available.", "source": "@site/components/web/Search/SearchTypeDropdown.md", "sourceDirName": "web/Search", "slug": "/web/Search/SearchTypeDropdown", "permalink": "/components/web/Search/SearchTypeDropdown", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "SearchTypeDropdown", "title": "SearchTypeDropdown"}}, {"id": "web/Settings/SettingsAside", "title": "SettingsAside", "description": "No description available.", "source": "@site/components/web/Settings/SettingsAside.md", "sourceDirName": "web/Settings", "slug": "/web/Settings/SettingsAside", "permalink": "/components/web/Settings/SettingsAside", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "SettingsAside", "title": "SettingsAside"}}, {"id": "web/Settings/TextDisplaySettings", "title": "TextDisplaySettings", "description": "No description available.", "source": "@site/components/web/Settings/TextDisplaySettings.md", "sourceDirName": "web/Settings", "slug": "/web/Settings/TextDisplaySettings", "permalink": "/components/web/Settings/TextDisplaySettings", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "TextDisplaySettings", "title": "TextDisplaySettings"}}, {"id": "web/Settings/ThemeSettings", "title": "ThemeSettings", "description": "No description available.", "source": "@site/components/web/Settings/ThemeSettings.md", "sourceDirName": "web/Settings", "slug": "/web/Settings/ThemeSettings", "permalink": "/components/web/Settings/ThemeSettings", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "ThemeSettings", "title": "ThemeSettings"}}, {"id": "web/Settings/VisibilitySettings", "title": "VisibilitySettings", "description": "No description available.", "source": "@site/components/web/Settings/VisibilitySettings.md", "sourceDirName": "web/Settings", "slug": "/web/Settings/VisibilitySettings", "permalink": "/components/web/Settings/VisibilitySettings", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "VisibilitySettings", "title": "VisibilitySettings"}}], "drafts": [], "sidebars": {"componentsSidebar": [{"type": "category", "label": "Vue Components", "items": [{"type": "doc", "id": "intro"}], "collapsed": true, "collapsible": true}]}}]}, "libs": {"loadedVersions": [{"versionName": "current", "label": "Next", "banner": null, "badge": false, "noIndex": false, "className": "docs-version-current", "path": "/libs", "tagsPath": "/libs/tags", "isLast": true, "routePriority": -1, "sidebarFilePath": "/Applications/MAMP/htdocs/ebtc/esra-bibel/docs/sidebars-libs.ts", "contentPath": "/Applications/MAMP/htdocs/ebtc/esra-bibel/docs/libs", "contentPathLocalized": "/Applications/MAMP/htdocs/ebtc/esra-bibel/docs/i18n/de/docusaurus-plugin-content-docs-libs/current", "docs": [{"id": "bible-types", "title": "Bible Types", "description": "This module contains types related to bible functionality.", "source": "@site/libs/bible-types.md", "sourceDirName": ".", "slug": "/bible-types", "permalink": "/libs/bible-types", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {}, "sidebar": "libsSidebar", "previous": {"title": "ESB Online Library Types", "permalink": "/libs/"}, "next": {"title": "Common Types", "permalink": "/libs/common-types"}}, {"id": "common-types", "title": "Common Types", "description": "This module contains types related to common functionality.", "source": "@site/libs/common-types.md", "sourceDirName": ".", "slug": "/common-types", "permalink": "/libs/common-types", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {}, "sidebar": "libsSidebar", "previous": {"title": "Bible Types", "permalink": "/libs/bible-types"}, "next": {"title": "Display Types", "permalink": "/libs/display-types"}}, {"id": "display-types", "title": "Display Types", "description": "This module contains types related to display functionality.", "source": "@site/libs/display-types.md", "sourceDirName": ".", "slug": "/display-types", "permalink": "/libs/display-types", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {}, "sidebar": "libsSidebar", "previous": {"title": "Common Types", "permalink": "/libs/common-types"}, "next": {"title": "Search Types", "permalink": "/libs/search-types"}}, {"id": "enums", "title": "Enums", "description": "This module contains all enumerations used throughout the ESB Online application.", "source": "@site/libs/enums.md", "sourceDirName": ".", "slug": "/enums", "permalink": "/libs/enums", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {}, "sidebar": "libsSidebar", "previous": {"title": "Text Types", "permalink": "/libs/text-types"}}, {"id": "intro", "title": "Gemeinsame Bibliotheken", "description": "Diese Dokumentation beschreibt die gemeinsam genutzten Bibliotheken (libs) der ESB Online Plattform. Diese Bibliotheken stellen wiederverwendbare Funktionen, Typen und Konstanten bereit, die von verschiedenen Anwendungen innerhalb des Monorepos genutzt werden.", "source": "@site/libs/intro.md", "sourceDirName": ".", "slug": "/intro", "permalink": "/libs/intro", "draft": false, "unlisted": false, "tags": [], "version": "current", "sidebarPosition": 1, "frontMatter": {"id": "intro", "title": "Gemeinsame Bibliotheken", "sidebar_position": 1}, "sidebar": "libsSidebar", "next": {"title": "ESB Online Library Types", "permalink": "/libs/"}}, {"id": "README", "title": "ESB Online Library Types", "description": "This section contains comprehensive documentation for all TypeScript types and enums used in the ESB Online libraries.", "source": "@site/libs/README.md", "sourceDirName": ".", "slug": "/", "permalink": "/libs/", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {}, "sidebar": "libsSidebar", "previous": {"title": "Gemeinsame Bibliotheken", "permalink": "/libs/intro"}, "next": {"title": "Bible Types", "permalink": "/libs/bible-types"}}, {"id": "search-types", "title": "Search Types", "description": "This module contains types related to search functionality.", "source": "@site/libs/search-types.md", "sourceDirName": ".", "slug": "/search-types", "permalink": "/libs/search-types", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {}, "sidebar": "libsSidebar", "previous": {"title": "Display Types", "permalink": "/libs/display-types"}, "next": {"title": "Text Types", "permalink": "/libs/text-types"}}, {"id": "sidebar", "title": "Library Types Sidebar", "description": "- Overview", "source": "@site/libs/sidebar.md", "sourceDirName": ".", "slug": "/sidebar", "permalink": "/libs/sidebar", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {}}, {"id": "text-types", "title": "Text Types", "description": "This module contains types related to text functionality.", "source": "@site/libs/text-types.md", "sourceDirName": ".", "slug": "/text-types", "permalink": "/libs/text-types", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {}, "sidebar": "libsSidebar", "previous": {"title": "Search Types", "permalink": "/libs/search-types"}, "next": {"title": "Enums", "permalink": "/libs/enums"}}], "drafts": [], "sidebars": {"libsSidebar": [{"type": "category", "label": "Bibliotheken", "items": [{"type": "doc", "id": "intro"}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "Typen", "items": [{"type": "doc", "id": "README"}, {"type": "category", "label": "Bibel-Typen", "items": [{"type": "doc", "id": "bible-types"}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "Allgemeine Typen", "items": [{"type": "doc", "id": "common-types"}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "Anzeige-Typen", "items": [{"type": "doc", "id": "display-types"}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "Such-Typen", "items": [{"type": "doc", "id": "search-types"}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "Text-Typen", "items": [{"type": "doc", "id": "text-types"}], "collapsed": true, "collapsible": true}, {"type": "doc", "id": "enums"}], "collapsed": true, "collapsible": true}]}}]}}, "docusaurus-plugin-content-blog": {"default": {"blogSidebarTitle": "Recent posts", "blogPosts": [], "blogListPaginated": [], "blogTags": {}, "blogTagsListPath": "/blog/tags"}}, "docusaurus-plugin-content-pages": {"default": [{"type": "jsx", "permalink": "/", "source": "@site/i18n/de/docusaurus-plugin-content-pages/index.tsx"}]}, "docusaurus-plugin-debug": {}, "docusaurus-plugin-svgr": {}, "docusaurus-theme-classic": {}, "docusaurus-bootstrap-plugin": {}, "docusaurus-mdx-fallback-plugin": {}}}