---
id: TextFormatDropdown
title: TextFormatDropdown
---

# TextFormatDropdown

No description available.





## Source

```vue
<template>
    <div ref="dropdownRef" class="relative">
        <button
            id="TEXT_FORMAT_BTN"
            type="button"
            class="flex h-10 w-10 items-center justify-center rounded-full text-theme-600 hover:bg-theme-100 hover:text-theme-900 focus:outline-hidden dark:text-theme-400 dark:hover:bg-theme-800 dark:hover:text-theme-100"
            :class="{ 'bg-theme-100 dark:bg-theme-800': isOpen }"
            @click="toggleDropdown"
        >
            <svg
                class="h-5 w-5"
                viewBox="0 0 600 600"
                xmlns="http://www.w3.org/2000/svg"
            >
                <path
                    fill="currentColor"
                    fill-rule="evenodd"
                    stroke="none"
                    d="M 101 538.361206 C 101 489.015472 101.315208 485.229218 103.279457 484.475464 C 104.533157 483.994354 188.782425 483.600769 290.5 483.600769 C 392.21759 483.600769 476.466827 483.994354 477.720551 484.475464 C 479.684753 485.229218 480 489.015472 480 511.855713 L 480 538.361206 L 290.5 538.361206 L 101 538.361206 Z"
                />
                <path
                    fill="currentColor"
                    fill-rule="evenodd"
                    stroke="none"
                    d="M 195.239563 377.691772 L 258.426056 376.774933 L 258.857941 249.980713 L 259.289886 123.186493 L 211.257446 123.186493 L 163.225067 123.186493 L 163.225067 154.35849 L 163.225067 185.530487 L 132.05307 185.530487 L 100.881065 185.530487 L 100.881065 133.691284 C 100.881065 86.453247 101.158279 81.330994 104.004089 75.980896 C 105.721764 72.751892 110.336517 67.835144 114.259087 65.054932 C 121.391045 60 121.391045 60 290.287506 60 C 459.183929 60 459.183929 60 466.188751 64.635498 C 470.041382 67.18512 474.725006 72.27301 476.596771 75.942017 C 479.791168 82.203613 480 85.770325 480 134.071716 L 480 185.530487 L 448.828003 185.530487 L 417.656006 185.530487 L 417.656006 154.35849 L 417.656006 123.186493 L 369.623627 123.186493 L 321.591187 123.186493 L 322.023132 249.980713 L 322.455017 376.774933 L 354.048279 377.233337 L 385.64151 377.691772 L 385.64151 408.646301 C 385.64151 435.31842 385.272766 439.742371 382.976227 440.623627 C 381.510315 441.186157 339.617706 441.586548 289.88147 441.513367 C 240.145264 441.440186 198.504196 440.997925 197.345779 440.530457 C 195.767914 439.893799 195.239563 431.905151 195.239563 408.686157 L 195.239563 377.691772 Z"
                />
            </svg>
        </button>

        <div
            v-if="isOpen"
            class="absolute right-0 z-50 mt-2 w-64 rounded-lg border border-theme-300 bg-theme p-4 shadow-lg dark:border-theme-700 dark:bg-theme-800"
        >
            <!-- Font Size Slider -->
            <div class="mb-4">
                <div class="mb-2 flex items-center justify-between">
                    <label
                        class="text-sm font-medium text-theme-700 dark:text-theme-300"
                        >Schriftgröße</label
                    >
                    <span class="text-sm text-theme-500 dark:text-theme-400"
                        >{{ sizeToNumber(textSettings.fontSize) }}px</span
                    >
                </div>
                <div class="flex items-center gap-2">
                    <span class="text-xs text-theme-700 dark:text-theme-300"
                        >A</span
                    >
                    <div class="relative flex-1">
                        <input
                            type="range"
                            :value="sizeToNumber(textSettings.fontSize)"
                            min="14"
                            max="26"
                            step="2"
                            class="h-2 w-full cursor-pointer appearance-none rounded-lg bg-theme-200 dark:bg-theme-700 [&::-moz-range-thumb]:h-4 [&::-moz-range-thumb]:w-4 [&::-moz-range-thumb]:cursor-pointer [&::-moz-range-thumb]:rounded-full [&::-moz-range-thumb]:border-0 [&::-moz-range-thumb]:bg-indigo-600 [&::-moz-range-thumb]:transition-colors [&::-moz-range-thumb]:hover:bg-indigo-700 [&::-webkit-slider-thumb]:h-4 [&::-webkit-slider-thumb]:w-4 [&::-webkit-slider-thumb]:cursor-pointer [&::-webkit-slider-thumb]:appearance-none [&::-webkit-slider-thumb]:rounded-full [&::-webkit-slider-thumb]:bg-indigo-600 [&::-webkit-slider-thumb]:transition-colors [&::-webkit-slider-thumb]:hover:bg-indigo-700"
                            @input="setFontSize"
                        />
                        <div
                            class="absolute right-0 -bottom-4 left-0 flex justify-between px-1"
                        >
                            <span class="text-[10px] text-theme-500">14px</span>
                            <span class="text-[10px] text-theme-500">26px</span>
                        </div>
                    </div>
                    <span
                        class="text-lg font-semibold text-theme-700 dark:text-theme-300"
                        >A</span
                    >
                </div>
            </div>

            <div class="my-3 h-px bg-theme-200 dark:bg-theme-700"></div>

            <!-- Line Spacing -->
            <!--<div class="mb-4">
                <label class="block text-sm font-medium text-theme-700 dark:text-theme-300 mb-2">Zeilenabstand</label>
                <div class="flex gap-2">
                    <button
                        type="button"
                        @click="setLineSpacing('normal')"
                        class="flex-1 px-3 py-2 text-sm rounded-md border transition-colors"
                        :class="[
                            textSettings.lineSpacing === 'normal'
                                ? 'bg-indigo-600 text-white border-indigo-600 dark:bg-indigo-500 dark:border-indigo-500'
                                : 'border-theme-300 text-theme-700 hover:bg-theme-50 dark:border-theme-600 dark:text-theme-300 dark:hover:bg-theme-700'
                        ]"
                    >
                        Normal
                    </button>
                    <button
                        type="button"
                        @click="setLineSpacing('relaxed')"
                        class="flex-1 px-3 py-2 text-sm rounded-md border transition-colors"
                        :class="[
                            textSettings.lineSpacing === 'relaxed'
                                ? 'bg-indigo-600 text-white border-indigo-600 dark:bg-indigo-500 dark:border-indigo-500'
                                : 'border-theme-300 text-theme-700 hover:bg-theme-50 dark:border-theme-600 dark:text-theme-300 dark:hover:bg-theme-700'
                        ]"
                    >
                        Mittel
                    </button>
                    <button
                        type="button"
                        @click="setLineSpacing('loose')"
                        class="flex-1 rounded-md px-2 py-1 text-sm font-medium transition-colors duration-200"
                        :class="[
                            textSettings.lineSpacing === 'loose'
                                ? 'bg-indigo-600 text-white border-indigo-600 dark:bg-indigo-500 dark:border-indigo-500'
                                : 'border-theme-300 text-theme-700 hover:bg-theme-50 dark:border-theme-600 dark:text-theme-300 dark:hover:bg-theme-700'
                        ]"
                    >
                        Weit
                    </button>
                </div>
            </div>-->

            <!-- Margin -->
            <div class="mb-4">
                <label
                    class="mb-2 block text-sm font-medium text-theme-700 dark:text-theme-300"
                    >Textbreite</label
                >
                <div class="flex gap-2">
                    <button
                        v-for="size in marginSizes"
                        :key="size"
                        type="button"
                        class="flex-1 rounded-md px-2 py-1 text-sm font-medium transition-colors duration-200"
                        :class="[
                            textSettings.marginSize === size
                                ? 'border-indigo-600 bg-indigo-600 text-white dark:border-indigo-500 dark:bg-indigo-500'
                                : 'border-theme-300 text-theme-700 hover:bg-theme-50 dark:border-theme-600 dark:text-theme-300 dark:hover:bg-theme-700',
                        ]"
                        @click="setMarginSize(size)"
                    >
                        {{ marginSizeLabels[size] }}
                    </button>
                </div>
            </div>

            <div class="my-3 h-px bg-theme-200 dark:bg-theme-700"></div>

            <!-- Display Toggles -->
            <div class="mb-4 space-y-3">
                <div class="flex items-center justify-between">
                    <label class="text-sm text-theme-700 dark:text-theme-300"
                        >Zeige Versnummern</label
                    >
                    <button
                        type="button"
                        role="switch"
                        :aria-checked="textSettings.showVerseNumbers"
                        class="relative inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:ring-2 focus:ring-indigo-600 focus:ring-offset-2 focus:outline-hidden dark:focus:ring-offset-gray-800"
                        :class="[
                            textSettings.showVerseNumbers
                                ? 'bg-indigo-600 dark:bg-indigo-500'
                                : 'bg-theme-200 dark:bg-theme-600',
                        ]"
                        @click="toggleSetting('showVerseNumbers')"
                    >
                        <span
                            class="pointer-events-none inline-block h-5 w-5 transform rounded-full bg-theme shadow-sm ring-0 transition duration-200 ease-in-out dark:bg-theme-200"
                            :class="[
                                textSettings.showVerseNumbers
                                    ? 'translate-x-5'
                                    : 'translate-x-0',
                            ]"
                        />
                    </button>
                </div>

                <!-- Flow text mode -->
                <div class="flex items-center justify-between">
                    <span class="text-sm text-theme-700 dark:text-theme-300"
                        >Vers für Vers</span
                    >
                    <button
                        type="button"
                        class="relative inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:ring-2 focus:ring-indigo-600 focus:ring-offset-2 focus:outline-hidden dark:focus:ring-offset-gray-800"
                        :class="[
                            !textSettings.flowText
                                ? 'bg-indigo-600 dark:bg-indigo-500'
                                : 'bg-theme-200 dark:bg-theme-600',
                        ]"
                        role="switch"
                        :aria-checked="!textSettings.flowText"
                        @click="toggleSetting('flowText')"
                    >
                        <span
                            class="pointer-events-none inline-block h-5 w-5 transform rounded-full bg-theme shadow-sm ring-0 transition duration-200 ease-in-out dark:bg-theme-200"
                            :class="[
                                !textSettings.flowText
                                    ? 'translate-x-5'
                                    : 'translate-x-0',
                            ]"
                        />
                    </button>
                </div>

                <!-- Footnotes -->
                <div class="flex items-center justify-between">
                    <span class="text-sm text-theme-700 dark:text-theme-300"
                        >Fußnoten</span
                    >
                    <button
                        type="button"
                        class="relative inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:ring-2 focus:ring-indigo-600 focus:ring-offset-2 focus:outline-hidden dark:focus:ring-offset-gray-800"
                        :class="[
                            textSettings.showFootnotes
                                ? 'bg-indigo-600 dark:bg-indigo-500'
                                : 'bg-theme-200 dark:bg-theme-600',
                        ]"
                        role="switch"
                        :aria-checked="textSettings.showFootnotes"
                        @click="toggleSetting('showFootnotes')"
                    >
                        <span
                            class="pointer-events-none inline-block h-5 w-5 transform rounded-full bg-theme shadow-sm ring-0 transition duration-200 ease-in-out dark:bg-theme-200"
                            :class="[
                                textSettings.showFootnotes
                                    ? 'translate-x-5'
                                    : 'translate-x-0',
                            ]"
                        />
                    </button>
                </div>

                <!-- Focus mode -->
                <div class="flex items-center justify-between">
                    <span class="text-sm text-theme-700 dark:text-theme-300"
                        >Fokussiertes Lesen</span
                    >
                    <button
                        type="button"
                        class="relative inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:ring-2 focus:ring-indigo-600 focus:ring-offset-2 focus:outline-hidden dark:focus:ring-offset-gray-800"
                        :class="[
                            textSettings.focusedMode
                                ? 'bg-indigo-600 dark:bg-indigo-500'
                                : 'bg-theme-200 dark:bg-theme-600',
                        ]"
                        role="switch"
                        :aria-checked="textSettings.focusedMode"
                        @click="toggleSetting('focusedMode')"
                    >
                        <span
                            class="pointer-events-none inline-block h-5 w-5 transform rounded-full bg-theme shadow-sm ring-0 transition duration-200 ease-in-out dark:bg-theme-200"
                            :class="[
                                textSettings.focusedMode
                                    ? 'translate-x-5'
                                    : 'translate-x-0',
                            ]"
                        />
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useTextSettingsStore } from '@/stores/textSettingsStore';
import type { FontSize, MarginSize } from '@esbo/types';
import { onBeforeUnmount, onMounted, ref } from 'vue';

type BooleanSettings =
    | 'showVerseNumbers'
    | 'flowText'
    | 'showFootnotes'
    | 'focusedMode';

const textSettings = useTextSettingsStore();
const dropdownRef = ref<HTMLElement | null>(null);
const isOpen = ref(false);

const toggleDropdown = () => {
    isOpen.value = !isOpen.value;
};

const closeDropdown = () => {
    isOpen.value = false;
};

const handleClickOutside = (event: MouseEvent) => {
    if (
        dropdownRef.value &&
        !dropdownRef.value.contains(event.target as Node)
    ) {
        closeDropdown();
    }
};

onMounted(() => {
    document.addEventListener('click', handleClickOutside);
});

onBeforeUnmount(() => {
    document.removeEventListener('click', handleClickOutside);
});

const sizeToNumber = (size: FontSize): number => {
    switch (size) {
        case 'xs':
            return 14;
        case 'sm':
            return 16;
        case 'base':
            return 18;
        case 'lg':
            return 20;
        case 'xl':
            return 22;
        case '2xl':
            return 24;
        case '3xl':
            return 26;
        default:
            return 18;
    }
};

const numberToSize = (value: number): FontSize => {
    if (value <= 15) return 'xs';
    if (value <= 17) return 'sm';
    if (value <= 19) return 'base';
    if (value <= 21) return 'lg';
    if (value <= 23) return 'xl';
    if (value <= 25) return '2xl';
    return '3xl';
};

const setFontSize = (event: Event) => {
    const value = parseInt((event.target as HTMLInputElement).value);
    textSettings.updateSettings({
        ...textSettings.$state,
        fontSize: numberToSize(value),
    });
};

const toggleSetting = (setting: BooleanSettings): void => {
    textSettings.updateSettings({
        ...textSettings.$state,
        [setting]: !textSettings[setting],
    });
};

/*const setLineSpacing = (spacing: LineSpacing): void => {
    const newSettings = {
        ...textSettings.$state,
        lineSpacing: spacing,
    };
    textSettings.updateSettings(newSettings);
};*/

const setMarginSize = (size: MarginSize): void => {
    const newSettings = {
        ...textSettings.$state,
        marginSize: size,
    };
    textSettings.updateSettings(newSettings);
};

const marginSizes: MarginSize[] = [
    'margin-wide',
    'margin-normal',
    'margin-narrow',
];
const marginSizeLabels: Record<MarginSize, string> = {
    'margin-wide': 'Breiter',
    'margin-normal': 'Normal',
    'margin-narrow': 'Schmaler',
} as const;
</script>

```
