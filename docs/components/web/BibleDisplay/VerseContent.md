---
id: VerseContent
title: VerseContent
---

# VerseContent

No description available.





## Source

```vue
<template>
    <VerseNumber
        v-if="showVerseNumbers"
        :number="verse.number"
        :flow-text="flowText"
        :is-highlighted="isVerseHighlighted"
        class="font-thanatos verse-number"
    />
    <WordGroupContainer
        v-for="(group, index) in wordGroups"
        :key="index"
        :group="group"
        :show-footnotes="textSettings.showFootnotes"
        :class="[textContainerClasses, `text-${textSettings.fontSize}`]"
        @footnote-click="onFootnoteClick"
        @footnote-hover="onFootnoteHover"
    />
</template>

<script setup lang="ts">
import { useBibleHighlightStore } from '@/stores/bible/bibleHighlightStore';
import { useBibleStore } from '@/stores/bible/bibleSectionStore';
import type { Footnote, TextSettings, Verse, WordGroup } from '@esbo/types';
import { computed } from 'vue';
import VerseNumber from './VerseNumber.vue';
import WordGroupContainer from './WordGroupContainer.vue';

interface Props {
    verse: Verse;
    showVerseNumbers: boolean;
    flowText: boolean;
    textSettings: TextSettings;
    isHighlighted?: boolean;
}

const props = defineProps<Props>();

const bibleStore = useBibleStore();
const highlightStore = useBibleHighlightStore();

const flowText = props.flowText;

const isVerseHighlighted = computed(() => {
    const currentChapter = bibleStore.currentChapterSection;
    if (!currentChapter?.book) return false;

    const verseId = `${currentChapter.book.slug}.${currentChapter.number}.${props.verse.number}`;
    return highlightStore.highlightedVerses.has(verseId);
});

const emit = defineEmits<{
    (
        e: 'footnoteClick',
        payload: { event: MouseEvent; footnote: Footnote; word: string },
    ): void;
    (
        e: 'footnoteHover',
        payload: {
            event: MouseEvent;
            footnote: Footnote | null;
            word?: string;
        },
    ): void;
}>();

// Compute text container classes
const textContainerClasses = computed(() => ({
    grow: !props.flowText,
    inline: props.flowText,
}));

// Group words by variant status for rendering
const wordGroups = computed(() => {
    const groups: WordGroup[] = [];
    let currentGroup: WordGroup | null = null;

    for (const word of props.verse.words) {
        const shouldCreateNewGroup =
            !currentGroup ||
            (currentGroup.isVariant &&
                (!word.isVariant ||
                    word.variantGroupId !== currentGroup.variantGroupId)) ||
            (!currentGroup.isVariant && word.isVariant) ||
            (word.isOtQuote &&
                word.otQuoteGroupId !== currentGroup.otQuoteGroupId);

        if (shouldCreateNewGroup) {
            if (currentGroup) {
                groups.push(currentGroup);
            }

            currentGroup = {
                words: [],
                isVariant: word.isVariant,
                variantType: word.variantType,
                variantGroupId: word.variantGroupId,
                isOtQuote: word.isOtQuote,
                otQuoteGroupId: word.otQuoteGroupId,
            };
        }

        currentGroup?.words.push(word);
    }

    // Add the last group if it exists
    if (currentGroup) {
        groups.push(currentGroup);
    }

    return groups;
});

// Event handlers
const onFootnoteClick = (
    event: MouseEvent,
    footnote: Footnote,
    word: string,
) => {
    emit('footnoteClick', { event, footnote, word });
};

const onFootnoteHover = (
    event: MouseEvent,
    footnote: Footnote | null,
    word?: string,
) => {
    emit('footnoteHover', { event, footnote, word });
};
</script>

<style scoped>
.variant-group {
    display: inline;
}

.verse-highlight {
    background-color: #ffffe0;
    padding: 0.25rem;
    margin: -0.25rem;
    border-radius: 0.25rem;
}

.verse.pericope-start {
    margin-left: 4em;
}

/* Font size classes */
.text-xs {
    font-size: 0.875rem;
}
.text-sm {
    font-size: 1rem;
}
.text-base {
    font-size: 1.125rem;
}
.text-lg {
    font-size: 1.3rem;
}
.text-xl {
    font-size: 1.5rem;
}
.text-2xl {
    font-size: 1.75rem;
}
.text-3xl {
    font-size: 2rem;
}
</style>

```
