---
id: ChapterContent
title: ChapterContent
---

# ChapterContent

No description available.





## Source

```vue
<template>
    <article
        :id="chapter.number.toString()"
        :data-chapter-id="dataChapterId"
        :class="[
            articleClasses,
            textSettings.flowText ? 'flow-mode' : 'verse-mode',
        ]"
        class="prose prose-lg dark:prose-invert font-calluna max-w-full px-2"
    >
        <header>
            <h1
                v-if="showBookHeader"
                class="font-thanatos text-theme-900 dark:text-theme-100 mb-8 text-center text-4xl"
            >
                {{ chapter.book.name }}
            </h1>
        </header>
        <section
            v-for="(group, index) in paragraphGroups"
            :key="index"
            class="pericope"
            :class="{ 'pericope-start': group.isPericopeStart }"
            :aria-label="'Perikope ' + (index + 1)"
        >
            <!-- Render each verse as a div within the pericope section -->
            <div
                v-for="verse in group.verses"
                :id="`${chapter.book.slug}.${chapter.number}.${verse.number}`"
                :key="verse.number"
                class="verse-unit"
                :data-verse="verse.number"
                :data-reference="`${chapter.book.name} ${chapter.number}:${verse.number}`"
                :data-category="chapter.book.category"
            >
                <ChapterNumber
                    v-if="verse.number === 1"
                    :number="chapter.number"
                />
                <VerseContent
                    :verse="verse"
                    :flow-text="textSettings.flowText"
                    :show-verse-numbers="textSettings.showVerseNumbers"
                    :text-settings="textSettings"
                    @footnote-click="handleFootnoteClick($event, verse)"
                    @footnote-hover="handleFootnoteHover($event, verse)"
                />
            </div>
        </section>
    </article>
</template>

<script setup lang="ts">
import { useBibleStore } from '@/stores/bible/bibleSectionStore';
import { useTextSettingsStore } from '@/stores/textSettingsStore';
import type { ChapterSection, Footnote, Verse } from '@esbo/types';
import { computed, onMounted, onUnmounted } from 'vue';
import ChapterNumber from './ChapterNumber.vue';
import VerseContent from './VerseContent.vue';

interface Props {
    chapter: ChapterSection;
    showBookHeader?: boolean;
}

const props = defineProps<Props>();
const textSettings = useTextSettingsStore();
const bibleStore = useBibleStore();

const articleClasses = computed(() => ({
    'chapter-content': true,
    'text-theme-900 dark:text-theme-100': true,
    current: bibleStore.isCurrentSection(props.chapter),
}));

const dataChapterId = computed(
    () => `${props.chapter.book.slug}${props.chapter.number}`,
);

interface ParagraphGroup {
    isPericopeStart: boolean;
    pericopeTitle?: string;
    verses: Verse[];
    paragraphGroupId: string;
}

function groupVersesByParagraphGroup(verses: Verse[]): ParagraphGroup[] {
    const groupMap = new Map<
        string,
        { verses: Verse[]; isPericopeStart: boolean; pericopeTitle?: string }
    >();
    const groupOrder: string[] = []; // To maintain order of groups

    verses.forEach((verse) => {
        const groupId = verse.paragraphGroupId || `single_${verse.number}`;

        // If this is the first time we see this group ID, add it to our order tracker
        if (!groupMap.has(groupId)) {
            groupMap.set(groupId, {
                verses: [],
                isPericopeStart: verse.isPericopeStart || false,
            });
            groupOrder.push(groupId);
        }

        // Add the verse to its group
        groupMap.get(groupId)?.verses.push(verse);
    });

    // Create the final array of paragraph groups in the correct order
    const groups: ParagraphGroup[] = groupOrder.map((groupId) => {
        const group = groupMap.get(groupId)!;
        return {
            paragraphGroupId: groupId,
            verses: group.verses,
            isPericopeStart: group.isPericopeStart,
            pericopeTitle: group.pericopeTitle,
        };
    });

    return groups;
}

const paragraphGroups = computed<ParagraphGroup[]>(() => {
    return groupVersesByParagraphGroup(props.chapter.verses);
});

const bookAbbreviation = computed(() => {
    const book = props.chapter.book;
    if (!book) return '';
    return book.abbreviation;
});

// Update current verse based on scroll position
const updateVisibleVerse = () => {
    // Only update verse if scroll handling is enabled and not during initial load
    if (!bibleStore.scrollHandlingEnabled || bibleStore.isInitialLoad) {
        return;
    }

    const verses = document.querySelectorAll('.verse-unit');
    const windowMiddle = window.scrollY + window.innerHeight / 3;

    // Find current verse
    let currentVerseNum = 1;
    for (const verse of verses) {
        const rect = verse.getBoundingClientRect();
        const verseTop = window.scrollY + rect.top;
        if (verseTop > windowMiddle) {
            break;
        }
        currentVerseNum = parseInt(verse.getAttribute('data-verse') || '1');
    }

    if (
        !isNaN(currentVerseNum) &&
        bibleStore.currentChapter === props.chapter.number
    ) {
        /*console.log(
            'ChapterContent.vue: Updating verse to:',
            currentVerseNum,
            props.chapter,
            bibleStore.currentChapter,
        );*/
        //bibleStore.updateCurrentVerse(props.chapter.number, currentVerseNum);
    }
};

// Handle footnote interactions
function handleFootnoteClick(
    {
        event,
        footnote,
        word,
    }: { event: MouseEvent; footnote: Footnote; word: string },
    verse: Verse,
) {
    bibleStore.handleFootnoteClick(
        event,
        footnote,
        word,
        bookAbbreviation.value,
        props.chapter.number,
        verse.number,
    );
}

function handleFootnoteHover(
    {
        event,
        footnote,
        word,
    }: { event: MouseEvent; footnote: Footnote | null; word?: string },
    verse: Verse,
) {
    bibleStore.handleFootnoteHover(
        event,
        footnote,
        word,
        bookAbbreviation.value,
        props.chapter.number,
        verse.number,
    );
}

// Set up scroll listener and initialize
onMounted(() => {
    // Initial update after ensuring content is positioned correctly
    /*nextTick(() => {
        if (props.chapter.verses.length > 0) {
            // Only set initial verse if we're on the current chapter
            if (bibleStore.isCurrentSection(props.chapter)) {
                console.log('Setting initial verse for chapter:', props);
                const initialVerse = props.chapter.verses[0].number;
                bibleStore.updateCurrentVerse(
                    props.chapter.number,
                    initialVerse,
                );
            }
        }
    });*/
    window.addEventListener('scroll', updateVisibleVerse, { passive: true });
});

onUnmounted(() => {
    window.removeEventListener('scroll', updateVisibleVerse);
});
</script>

<style scoped>
/* Base styles for both modes */
.pericope {
    margin-bottom: 1rem;
}

.highlighted-verse {
    text-decoration: underline;
    text-decoration-style: solid;
    text-decoration-thickness: 0.05em;
    text-underline-offset: 0.375em; /* Add some space between text and underline */
    border-radius: 0.25em;
    transition: text-decoration-color 0.2s ease;
}

/* Category-specific underline colors */
.highlighted-verse[data-category='history'] {
    text-decoration-color: var(--color-history-surface);
}

.highlighted-verse[data-category='wisdom'],
.highlighted-verse[data-category='law'] {
    text-decoration-color: var(--color-poetic-surface);
}

.highlighted-verse[data-category='prophecy'] {
    text-decoration-color: var(--color-prophecy-surface);
}

.highlighted-verse[data-category='gospel'] {
    text-decoration-color: var(--color-gospel-text);
}

.highlighted-verse[data-category='epistle'] {
    text-decoration-color: var(--color-epistle-surface);
}

.highlighted-verse[data-category='apocalypse'] {
    text-decoration-color: var(--color-revelation-surface);
}

/* Dark mode styles - slightly higher opacity for better visibility */
:global(.dark) .highlighted-verse[data-category='history'] {
    text-decoration-color: var(--color-history-text);
}

:global(.dark) .highlighted-verse[data-category='wisdom'],
:global(.dark) .highlighted-verse[data-category='law'] {
    text-decoration-color: var(--color-poetic-text);
}

:global(.dark) .highlighted-verse[data-category='prophecy'] {
    text-decoration-color: var(--color-prophecy-text);
}

:global(.dark) .highlighted-verse[data-category='gospel'] {
    text-decoration-color: var(--color-gospel-surface);
}

:global(.dark) .highlighted-verse[data-category='epistle'] {
    text-decoration-color: var(--color-epistle-text);
}

:global(.dark) .highlighted-verse[data-category='apocalypse'] {
    text-decoration-color: var(--color-revelation-text);
}

/* Flow text mode - pericope styling */
.flow-mode .pericope-title {
    display: block;
}
.flow-mode .verse-unit {
    display: inline;
}
/* Verse mode - pericope styling */
.verse-mode .pericope {
    margin-bottom: 0;
}
.verse-mode .pericope-title {
    display: none;
}
.verse-mode .verse-unit {
    display: block;
    margin-bottom: 1rem;
}
</style>

```
