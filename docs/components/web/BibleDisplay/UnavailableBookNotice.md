---
id: UnavailableBookNotice
title: UnavailableBookNotice
---

# UnavailableBookNotice

No description available.





## Source

```vue
<template>
    <div class="prose prose-lg dark:prose-invert mx-auto py-12 text-center">
        <h1 class="font-thanatos mb-8 text-4xl">{{ book.name }}</h1>
        <p class="text-theme-600 dark:text-theme-400 text-xl">
            <PERSON><PERSON> steht bald zur Verfügung.
        </p>
        <div v-if="availableBooks.length > 0" class="mt-12">
            <h2 class="mb-4 text-2xl">Verfügbare Bücher:</h2>
            <div
                class="mx-auto grid max-w-4xl grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3"
            >
                <a
                    v-for="book in availableBooks"
                    :key="book.slug"
                    :href="`/${book.slug}1`"
                    class="bg-theme-50 hover:bg-theme-100 dark:bg-theme-800 dark:hover:bg-theme-700 rounded-lg p-3 transition-colors"
                    :aria-label="`Zum Buch ${book.name} wechseln`"
                    role="link"
                >
                    {{ book.name }}
                </a>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import type { Book } from '@esbo/types';

defineProps<{
    book: Book;
    availableBooks: Book[];
}>();
</script>

```
