---
id: VerseNumber
title: VerseNumber
---

# VerseNumber

No description available.





## Source

```vue
<template>
    <span
        :class="[
            'mr-1 max-w-[2rem] cursor-pointer align-super text-[0.7em] select-none',
            versColor,
            flowText ? 'relative pr-1' : 'inline-block',
            isHighlighted ? 'font-bold' : '',
        ]"
        aria-hidden="false"
        role="button"
        :aria-pressed="isHighlighted"
        @click="handleClick"
    >
        {{ number }}
    </span>
</template>

<script setup lang="ts">
import { useBibleHighlightStore } from '@/stores/bible/bibleHighlightStore';
import { useBibleStore } from '@/stores/bible/bibleSectionStore';
import { computed, ref } from 'vue';

const bibleStore = useBibleStore();
const highlightStore = useBibleHighlightStore();
const lastClickedVerse = ref<number | null>(null);

const props = defineProps<{
    number: number;
    flowText: boolean;
    isHighlighted?: boolean;
}>();

const versColor = computed(() => {
    const currentChapter = bibleStore.currentChapterSection;
    return currentChapter?.book && 'category' in currentChapter.book
        ? `${currentChapter.book.category}-light dark:text-theme-100`
        : 'text-theme-500 dark:text-theme-100';
});

const handleClick = (event: MouseEvent) => {
    const currentChapter = bibleStore.currentChapterSection;
    if (!currentChapter?.book) return;

    // Check if shift key is pressed and we have a previous verse click
    if (event.shiftKey && lastClickedVerse.value !== null) {
        // Determine the range (start and end verse)
        const startVerse = Math.min(lastClickedVerse.value, props.number);
        const endVerse = Math.max(lastClickedVerse.value, props.number);

        console.log('Toggling highlight for verse range:', {
            book: currentChapter.book.slug,
            chapter: currentChapter.number,
            verse: startVerse,
            endVerse: endVerse,
        });

        // Toggle highlight for the verse range
        highlightStore.toggleVerseHighlight({
            book: currentChapter.book.slug,
            chapter: currentChapter.number,
            verse: startVerse,
            endVerse: endVerse,
        });

        // Reset the last clicked verse
        lastClickedVerse.value = null;
    } else {
        // Single verse toggle
        console.log('Toggling highlight for verse:', {
            book: currentChapter.book.slug,
            chapter: currentChapter.number,
            verse: props.number,
        });

        // Toggle highlight for a single verse
        highlightStore.toggleVerseHighlight({
            book: currentChapter.book.slug,
            chapter: currentChapter.number,
            verse: props.number,
        });

        // Store this verse as the last clicked
        lastClickedVerse.value = props.number;
    }
};
</script>
<style scoped>
sup {
    display: inline;
    bottom: 1.2em;
}
.flex sup {
    top: 0.8em;
}
</style>

```
