---
id: ChapterWrapper
title: ChapterWrapper
---

# ChapterWrapper

No description available.





## Source

```vue
<template>
    <template v-if="isFrontmatterSection(section)">
        <Frontmatter
            :id="section.book.slug"
            :ref="(el) => setRef(el)"
            :initial-book="section.book"
            :data-chapter-id="section.book.slug"
        />
    </template>
    <template v-else>
        <ChapterContent
            :id="`${section.book.slug}${section.number}`"
            :ref="(el) => setRef(el)"
            :chapter="section"
            :show-book-header="section.number === 1"
            :data-chapter-id="`${section.book.slug}${section.number}`"
        />
    </template>
</template>

<script setup lang="ts">
import ChapterContent from '@/Components/BibleDisplay/ChapterContent.vue';
import Frontmatter from '@/Components/BibleDisplay/FrontMatter.vue';
import { isFrontmatterSection } from '@/utils/bibleNavigationUtils';
import type { Section } from '@esbo/types';
import type { ComponentPublicInstance } from 'vue';

defineProps<{
    section: Section;
}>();

const emit = defineEmits<{
    (e: 'setRef', el: Element | ComponentPublicInstance | null): void;
}>();

const setRef = (el: Element | ComponentPublicInstance | null) => {
    emit('setRef', el);
};
</script>

```
