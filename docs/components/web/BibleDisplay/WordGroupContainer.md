---
id: WordGroupContainer
title: WordGroupContainer
---

# WordGroupContainer

Returns the word type for data attribute





## Source

```vue
<template>
    <span
        :class="groupClasses"
        :data-variant-type="group.isVariant ? group.variantType : undefined"
        :data-word-type="getGroupType()"
        :role="group.isOtQuote ? 'mark' : undefined"
        :aria-label="getAriaLabel()"
    >
        <template v-if="group.isVariant">&nbsp;</template>
        <span
            v-for="(word, index) in group.words"
            :key="index"
            :class="getWordClasses(word)"
            :data-word-type="getWordType(word)"
            @click.stop="(event) => handleFootnoteClick(event, word)"
            @mouseenter.stop="(event) => handleFootnoteHover(event, word, true)"
            @mouseleave.stop="
                (event) => handleFootnoteHover(event, word, false)
            "
        >
            <span
                v-if="word.isFootnote && showFootnotes"
                class="border-theme-400 dark:border-theme-500 border-b border-dotted"
                >{{ word.text }}</span
            >
            <template v-else>{{ word.text }}</template>
            <sup
                v-if="word.isFootnote && showFootnotes"
                class="ml-1 align-super text-xs"
                aria-hidden="true"
                >†</sup
            >
            {{ addSpace(index, group.words) }}
        </span>
        <template v-if="group.isVariant">&nbsp;</template>
    </span>
</template>

<script setup lang="ts">
import type { Footnote, Word, WordGroup } from '@esbo/types';
import { computed } from 'vue';

const props = defineProps<{
    group: WordGroup;
    showFootnotes: boolean;
}>();

const emit = defineEmits<{
    footnoteClick: [event: MouseEvent, footnote: Footnote, word: string];
    footnoteHover: [event: MouseEvent, footnote: Footnote | null, word: string];
}>();

/**
 * Returns the word type for data attribute
 */
const getWordType = (word: Word): string | undefined => {
    if (word.isFootnote) return 'footnote';
    if (word.isVariant) return word.variantType || 'variant';
    if (word.isOtQuote) return 'ot-quote';
    return undefined;
};

/**
 * Returns the type for the entire group
 */
const getGroupType = (): string | undefined => {
    if (props.group.isVariant) return 'variant';
    if (props.group.isOtQuote) return 'ot-quote';
    return undefined;
};

/**
 * Returns an aria-label for accessibility
 */
const getAriaLabel = (): string | undefined => {
    if (props.group.isOtQuote) return 'Old Testament quote';
    if (props.group.isVariant) {
        return `Text variant: ${props.group.variantType || 'alternative reading'}`;
    }
    return undefined;
};

/**
 * Computes classes for individual words
 */
const getWordClasses = (word: Word) => {
    if (word.isFootnote && props.showFootnotes) {
        return 'relative cursor-pointer text-theme-900 dark:text-theme-100';
    }
    return '';
};

/**
 * Computes classes for the word group container
 */
const groupClasses = computed(() => {
    const classes: string[] = ['inline']; // Base class for all groups

    // Handle variant groups
    if (props.group.isVariant) {
        let varClass = 'variant';

        // Add variant-specific classes based on type
        if (props.group.variantType) {
            varClass += '-' + props.group.variantType;
        }
        classes.push(varClass);
    }

    // Handle OT quotes
    if (props.group.isOtQuote) {
        classes.push('ot-quote');
    }

    // Return space-separated class string
    return classes.join(' ');
});

/**
 * Handles click events on footnote words
 */
const handleFootnoteClick = (event: MouseEvent, word: Word) => {
    if (word.isFootnote && word.footnote) {
        emit('footnoteClick', event, word.footnote, word.text);
    }
};

/**
 * Handles hover events on footnote words
 */
const handleFootnoteHover = (
    event: MouseEvent,
    word: Word,
    isHovering: boolean,
) => {
    if (word.isFootnote && word.footnote) {
        emit(
            'footnoteHover',
            event,
            isHovering ? word.footnote : null,
            word.text,
        );
    }
};

/**
 * Determines if a space should be added after a word
 */
const addSpace = (index: number, words: Word[]) =>
    index < words.length - 1 && !/^[.,;:!?'")\]}]/.test(words[index + 1].text)
        ? ' '
        : '';
</script>

<style scoped>
/* All groups should display inline by default */
.inline {
    display: inline;
}

/* Variant styling */
.variant {
    display: inline;
}

/* OT Quote styling */
.ot-quote {
    font-style: italic;
}

/* Variant type specific styling */
.variant-add {
    color: #4a5568;
}

.variant-va {
    font-weight: 500;
}

.variant-omission {
    text-decoration: line-through;
    opacity: 0.7;
}

.variant-alternative {
    color: #718096;
}
</style>

```
