---
id: ReferenceSelector
title: ReferenceSelector
---

# ReferenceSelector

No description available.





## Source

```vue
<template>
    <div class="reference-selector flex items-center space-x-2">
        <!-- Book Selector -->
        <div class="relative">
            <button
                class="font-thanatos flex items-center space-x-1 bg-theme/80 px-2 py-1 text-sm font-medium text-theme-700 hover:bg-theme-100 hover:text-theme-900 dark:border-theme-600 dark:bg-theme-800/80 dark:text-theme-300 dark:hover:bg-theme-700 dark:hover:text-theme-100"
                :aria-expanded="activeDropdown === 'book'"
                aria-haspopup="listbox"
                aria-label="Select book"
                @click="toggleDropdown('book')"
            >
                <span>{{ currentBookName }}</span>
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-4 w-4"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                    aria-hidden="true"
                >
                    <path
                        fill-rule="evenodd"
                        d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                        clip-rule="evenodd"
                    />
                </svg>
            </button>

            <!-- Book dropdown menu -->
            <div
                v-if="activeDropdown === 'book'"
                class="scrollbar-container absolute left-0 z-50 mt-1 max-h-60 w-56 overflow-auto rounded-lg border border-theme-200 bg-theme shadow-lg dark:border-theme-600 dark:bg-theme-800"
                role="listbox"
            >
                <div class="py-1" role="none">
                    <button
                        v-for="book in availableBooks"
                        :key="book.slug"
                        class="block w-full px-4 py-2 text-left text-sm text-theme-700 hover:bg-theme-100 hover:text-theme-900 dark:text-theme-300 dark:hover:bg-theme-600 dark:hover:text-theme-100"
                        :class="{
                            'bg-theme-100 dark:bg-theme-600':
                                currentBookName === book.name,
                        }"
                        role="option"
                        :aria-selected="
                            currentBookName === book.name ? 'true' : 'false'
                        "
                        @click="selectBook(book)"
                    >
                        {{ book.name }}
                    </button>
                </div>
            </div>
        </div>

        <!-- Chapter and Verse Selector -->
        <div class="inline-flex rounded-md shadow-sm" role="group">
            <!-- Chapter Button -->
            <button
                type="button"
                class="font-thanatos relative inline-flex min-w-[3rem] items-center justify-center rounded-l-lg border border-theme-200 bg-theme/80 px-2 py-1 text-sm font-medium text-theme-500 hover:bg-theme-100 hover:text-theme-700 focus:z-10 focus:ring-2 focus:ring-indigo-500 dark:border-theme-600 dark:bg-theme-800/80 dark:text-theme-400 dark:hover:bg-theme-700 dark:hover:text-white"
                aria-haspopup="true"
                :aria-expanded="activeDropdown === 'chapter'"
                @click="toggleDropdown('chapter')"
            >
                {{ currentSection?.number || 1 }}
                <!-- Chapter Dropdown -->
                <div
                    v-if="activeDropdown === 'chapter'"
                    class="scrollbar-container absolute top-full left-0 z-50 mt-1 max-h-96 w-32 rounded-lg border border-theme-200 bg-theme shadow-lg dark:border-theme-600 dark:bg-theme-800"
                    role="menu"
                >
                    <button
                        v-for="chapter in availableChapters"
                        :key="chapter"
                        class="block w-full px-4 py-2 text-left text-sm hover:bg-theme-100 dark:hover:bg-theme-700"
                        :class="{
                            'bg-theme-50 dark:bg-theme-700':
                                chapter === currentSection?.number,
                        }"
                        role="menuitem"
                        @click="navigateToChapter(chapter)"
                    >
                        Kapitel {{ chapter }}
                    </button>
                </div>
            </button>

            <!-- Verse Button -->
            <button
                type="button"
                class="font-thanatos relative inline-flex min-w-[3rem] items-center justify-center rounded-r-lg border border-l-0 border-theme-200 bg-theme/80 px-2 py-1 text-sm font-medium text-theme-500 hover:bg-theme-100 hover:text-theme-700 focus:z-10 focus:ring-2 focus:ring-indigo-500 dark:border-theme-600 dark:bg-theme-800/80 dark:text-theme-400 dark:hover:bg-theme-700 dark:hover:text-white"
                aria-haspopup="true"
                :aria-expanded="activeDropdown === 'verse'"
                @click="toggleDropdown('verse')"
            >
                {{ currentVerseNumber }}
                <!-- Verse Dropdown -->
                <div
                    v-if="activeDropdown === 'verse'"
                    class="scrollbar-container absolute top-full right-0 z-50 mt-1 max-h-96 w-32 rounded-lg border border-theme-200 bg-theme shadow-lg dark:border-theme-600 dark:bg-theme-800"
                    role="menu"
                >
                    <button
                        v-for="verse in availableVerses"
                        :key="verse"
                        class="block w-full px-4 py-2 text-left text-sm hover:bg-theme-100 dark:hover:bg-theme-700"
                        :class="{
                            'bg-theme-50 dark:bg-theme-700':
                                verse === currentVerseNumber,
                        }"
                        role="menuitem"
                        @click="navigateToVerse(verse)"
                    >
                        Vers {{ verse }}
                    </button>
                </div>
            </button>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useVerseReference } from '@/composables/useVerseReference';
import { useBibleStore } from '@/stores/bible/bibleSectionStore';
import type { Book } from '@esbo/types';
import { computed, onMounted, onUnmounted, ref } from 'vue';

// Store and composable access
const bibleStore = useBibleStore();
const { navigateToReference, getAvailableVerses } = useVerseReference();

// State management
const activeDropdown = ref<'book' | 'chapter' | 'verse' | null>(null);

// Computed properties
const currentSection = computed(() => bibleStore.currentChapterSection);
const currentBookName = computed(() => currentSection.value?.book.name || '');
const availableBooks = computed(() => bibleStore.books);
const availableChapters = computed(() => {
    const book = currentSection.value?.book;
    return book
        ? Array.from({ length: book.chapterCount }, (_, i) => i + 1)
        : [];
});
const availableVerses = computed(() =>
    currentSection.value ? getAvailableVerses(currentSection.value) : [],
);
const currentVerseNumber = computed(() => bibleStore.currentVerse || 1);

// Actions
const selectBook = (book: Book) => {
    navigateToReference(book.slug, 1);
    activeDropdown.value = null;
};

const navigateToChapter = (chapter: number) => {
    if (currentSection.value) {
        navigateToReference(currentSection.value.book.slug, chapter);
    }
    activeDropdown.value = null;
};

const navigateToVerse = (verse: number) => {
    if (currentSection.value) {
        navigateToReference(
            currentSection.value.book.slug,
            currentSection.value.number,
            verse,
        );
    }
    activeDropdown.value = null;
};

// Dropdown management
const toggleDropdown = (dropdown: 'book' | 'chapter' | 'verse') => {
    activeDropdown.value = activeDropdown.value === dropdown ? null : dropdown;
};

// Click outside handler
onMounted(() => {
    document.addEventListener('click', closeDropdowns);
});

onUnmounted(() => {
    document.removeEventListener('click', closeDropdowns);
});

const closeDropdowns = (e: MouseEvent) => {
    const target = e.target as HTMLElement;
    if (!target.closest('.reference-selector')) {
        activeDropdown.value = null;
    }
};
</script>

<style scoped>
.scrollbar-container {
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

/* Hide scrollbar when not hovering */
.scrollbar-container:not(:hover)::-webkit-scrollbar {
    width: 0;
    background: transparent;
}

/* Show scrollbar on hover */
.scrollbar-container:hover::-webkit-scrollbar {
    width: 4px;
}

.scrollbar-container:hover::-webkit-scrollbar-thumb {
    background-color: rgba(156, 163, 175, 0.5);
    border-radius: 2px;
}

.scrollbar-container:hover::-webkit-scrollbar-track {
    background: transparent;
}
</style>

```
