---
id: FootnoteTooltip
title: FootnoteTooltip
---

# FootnoteTooltip

No description available.





## Source

```vue
<template>
    <Teleport to="body">
        <Transition name="footnote">
            <div
                v-if="footnoteState"
                ref="tooltipRef"
                class="bg-theme-700 dark:bg-theme-700 fixed z-50 mb-12 max-w-[18.5em] min-w-[10.25em] rounded-lg shadow-lg"
                :class="tooltipClass"
                :style="tooltipStyle"
            >
                <div class="relative p-3">
                    <div
                        class="border-theme-600 dark:border-theme-600 mb-2 flex items-center justify-between border-b pb-2 text-sm font-medium"
                    >
                        <div>
                            <span
                                class="text-theme-100 dark:text-theme-100 mr-2 font-medium"
                                >»{{ footnoteState.word }}«</span
                            >
                            <span class="text-theme-400 dark:text-theme-400">{{
                                footnoteState.reference
                            }}</span>
                        </div>
                        <button
                            class="text-theme-300 hover:text-theme-100 dark:text-theme-400 dark:hover:text-theme-100 flex h-6 w-6 items-center justify-center rounded-full transition-colors duration-150"
                            @click="onClose"
                        >
                            <Icon
                                name="X"
                                class="h-4 w-4"
                                :aria-label="'Close footnote'"
                            />
                        </button>
                    </div>
                    <div class="text-theme-200 dark:text-theme-200 text-sm">
                        <FootnoteContent :footnote="footnoteState.footnote" />
                    </div>
                </div>
                <div
                    class="bg-theme-700 dark:bg-theme-700 absolute bottom-0 h-4 w-4 translate-y-1/2 rotate-45 transform"
                    :style="arrowStyle"
                    style="box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.1)"
                ></div>
            </div>
        </Transition>
    </Teleport>
</template>

<script setup lang="ts">
import { Icon } from '@/Components/Icons';
import type { Footnote } from '@esbo/types';
import { computed, ref } from 'vue';
import FootnoteContent from './FootnoteContent.vue';

interface FootnoteState {
    footnote: Footnote;
    word: string;
    reference: string;
    x: number;
    y: number;
    isClickLocked: boolean;
}

interface Props {
    footnoteState: FootnoteState | null;
}

interface Emits {
    (e: 'close'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();
const tooltipRef = ref<HTMLElement | null>(null);

const VIEWPORT_MARGIN = 20;
const ARROW_OFFSET = 20;

const tooltipPosition = computed(() => {
    if (!props.footnoteState) {
        return {
            x: 0,
            y: 0,
            wordX: 0,
            tooltipWidth: 0,
            alignment: 'center' as const,
        };
    }

    const viewportWidth = window.innerWidth;
    const tooltipWidth = tooltipRef.value?.offsetWidth || 400;
    const wordX = props.footnoteState.x;

    let alignment: 'left' | 'center' | 'right' = 'center';
    let finalX = wordX;

    // Calculate the bounds for the tooltip body
    const leftBound = VIEWPORT_MARGIN;
    const rightBound = viewportWidth - VIEWPORT_MARGIN;
    const halfWidth = tooltipWidth / 2;

    // Check if centering the tooltip would cause overflow
    if (wordX - halfWidth < leftBound) {
        // Too close to left edge
        alignment = 'left';
        finalX = leftBound;
    } else if (wordX + halfWidth > rightBound) {
        // Too close to right edge
        alignment = 'right';
        finalX = rightBound - tooltipWidth;
    } else {
        // Center alignment is possible
        alignment = 'center';
        finalX = wordX - halfWidth;
    }

    return {
        x: finalX,
        y: props.footnoteState.y - 10,
        wordX,
        tooltipWidth,
        alignment,
    };
});

const tooltipStyle = computed(() => {
    const pos = tooltipPosition.value;

    return {
        position: 'fixed' as const,
        left: `${pos.x}px`,
        top: `${pos.y}px`,
        transform: 'translate(0, -100%)',
    };
});

const tooltipClass = computed(() => {
    return {
        [`tooltip-align-${tooltipPosition.value.alignment}`]: true,
    };
});

const arrowStyle = computed(() => {
    const pos = tooltipPosition.value;
    const arrowPos = pos.wordX - pos.x;

    // Ensure arrow stays within tooltip bounds with some padding
    const minArrowPos = ARROW_OFFSET;
    const maxArrowPos = pos.tooltipWidth - ARROW_OFFSET;
    const clampedArrowPos = Math.max(
        minArrowPos,
        Math.min(maxArrowPos, arrowPos),
    );

    return {
        left: `${clampedArrowPos}px`,
        transform: 'translate(-50%, 50%)',
    };
});

function onClose(): void {
    emit('close');
}
</script>

<style>
.footnote-enter-active,
.footnote-leave-active {
    transition:
        opacity 0.2s ease,
        transform 0.2s ease;
}

.footnote-enter-from,
.footnote-leave-to {
    opacity: 0;
    transform: translateY(-10px);
}

.tooltip-align-left,
.tooltip-align-right,
.tooltip-align-center {
    transform-origin: center bottom;
}
</style>

```
