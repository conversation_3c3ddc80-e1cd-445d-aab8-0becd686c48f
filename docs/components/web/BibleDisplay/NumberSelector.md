---
id: NumberSelector
title: NumberSelector
---

# NumberSelector

No description available.





## Source

```vue
<script setup lang="ts">
import { Icon } from '@/Components/Icons';

interface Props {
    isOpen: boolean;
    title: string;
    numbers: number[];
    selectedNumber: number;
    zIndex?: number;
}

interface Emits {
    (e: 'close'): void;
    (e: 'select', number: number): void;
}

withDefaults(defineProps<Props>(), {
    zIndex: 60,
});

const emit = defineEmits<Emits>();

const onSelect = (number: number) => {
    emit('select', number);
};

const onClose = () => {
    emit('close');
};
</script>

<template>
    <transition
        enter-active-class="transition-transform ease-out duration-300"
        enter-from-class="translate-x-full"
        enter-to-class="translate-x-0"
        leave-active-class="transition-transform ease-in duration-300"
        leave-from-class="translate-x-0"
        leave-to-class="translate-x-full"
    >
        <div
            v-show="isOpen"
            class="fixed inset-0 z-30 overflow-hidden"
            aria-labelledby="slide-over-title"
            role="dialog"
            aria-modal="true"
        >
            <div class="bg-[#8BA872] p-4 text-white">
                <div class="flex items-center justify-between">
                    <h2 class="text-xl font-semibold">{{ title }}</h2>
                    <button
                        class="hover:text-theme-200 text-white focus:outline-hidden"
                        @click="onClose"
                    >
                        <Icon
                            name="ChevronDown"
                            class="h-5 w-5"
                            :aria-label="'Toggle number selection'"
                        />
                    </button>
                </div>
            </div>
            <div class="grid grid-cols-5 gap-4 p-4">
                <button
                    v-for="number in numbers"
                    :key="number"
                    class="hover:bg-theme-100 rounded-sm p-4 text-center text-lg"
                    :class="{
                        'bg-[#8BA872] text-white': selectedNumber === number,
                    }"
                    @click="onSelect(number)"
                >
                    {{ number }}
                </button>
            </div>
        </div>
    </transition>
</template>

```
