---
id: InfoItem
title: InfoItem
---

# InfoItem

No description available.





## Source

```vue
<!-- components/Bible/InfoItem.vue -->
<template>
    <div>
        <dt class="text-theme-600 dark:text-theme-400 text-sm">{{ label }}</dt>
        <dd class="text-theme-900 dark:text-theme-100" :class="className">
            {{ value }}
        </dd>
    </div>
</template>

<script setup lang="ts">
defineProps<{
    label: string;
    value: string;
    className?: string;
}>();
</script>

```
