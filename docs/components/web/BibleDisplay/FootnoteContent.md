---
id: FootnoteContent
title: FootnoteContent
---

# FootnoteContent

No description available.





## Source

```vue
<template>
    <template v-if="footnote.contentStructure">
        <template v-for="(element, index) in filteredElements" :key="index">
            <span :class="getElementClasses(element)">{{
                element.content
            }}</span
            >{{ index < filteredElements.length - 1 ? ' ' : '' }}
        </template>
    </template>
    <template v-else>
        {{ footnote.searchableText || '' }}
    </template>
</template>

<script setup lang="ts">
import type { Footnote, FootnoteContentElement } from '@esbo/types';
import { computed } from 'vue';

const props = defineProps<{
    footnote: Footnote;
}>();

console.log(props.footnote);

const filteredElements = computed(() => {
    if (!props.footnote.contentStructure?.elements) return [];
    return props.footnote.contentStructure.elements.filter(
        (element) => element.type !== 'caller',
    );
});

function getElementClasses(element: FootnoteContentElement): string[] {
    const classes: string[] = [];

    // Base styling for different element types
    switch (element.type) {
        case 'text':
            classes.push('text-theme-200');
            break;
        case 'caller':
            classes.push('font-medium', 'text-theme-300');
            break;
        case 'styled':
            classes.push('text-theme-200');
            // Handle different style types
            if (element.style) {
                switch (element.style) {
                    case 'it':
                        classes.push('italic');
                        break;
                    case 'b':
                        classes.push('font-bold');
                        break;
                    case 'u':
                        classes.push('underline');
                        break;
                    // Add more style cases as needed
                }
            }
            break;
    }

    return classes;
}
</script>

```
