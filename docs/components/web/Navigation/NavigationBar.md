---
id: NavigationBar
title: NavigationBar
---

# NavigationBar

Interface for scroll handling configuration





## Source

```vue
<template>
    <div class="relative" role="navigation" aria-label="Bible navigation">
        <!-- Main navbar with slide-up transition -->
        <nav
            class="dark:bg-theme-800/75 bg-theme/75 fixed inset-x-0 top-0 z-50 transform backdrop-blur transition-all duration-300 ease-in-out will-change-transform"
            :class="{
                '-translate-y-full': isScrollingDown && !isSearchExpanded,
            }"
            aria-label="Main navigation"
            @mouseenter="handleMouseEnter"
            @mouseleave="handleMouseLeave"
        >
            <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                <div class="flex h-20 items-center justify-between">
                    <!-- Logo (left) -->
                    <div
                        class="w-1/4 flex-shrink-0"
                        :class="{ invisible: isSearchExpanded }"
                    >
                        <a href="/" class="flex items-center" aria-label="Home">
                            <ApplicationLogo class="h-10 w-auto dark:invert" />
                        </a>
                    </div>

                    <!-- Search Bar (center) - fixed width to ensure centering -->
                    <div class="flex flex-1 justify-center">
                        <!-- Mobile view components -->
                        <div
                            class="flex w-full items-center justify-between lg:hidden"
                        >
                            <BibleBookSelector
                                aria-label="Select Bible book"
                                @book-select="handleBookSelect"
                            />
                            <button
                                class="text-theme-600 hover:text-theme-900 dark:text-theme-400 dark:hover:text-theme-200 p-2"
                                aria-label="Toggle search"
                                aria-expanded="false"
                                :class="{ 'aria-expanded': isSearchExpanded }"
                                @click="toggleSearchExpanded"
                            >
                                <Icon
                                    name="Search"
                                    class="h-5 w-5"
                                    :aria-label="'Toggle search'"
                                />
                            </button>
                        </div>

                        <!-- Expanded Search Overlay (Mobile) -->
                        <MobileSearchOverlay
                            v-if="isSearchExpanded"
                            @close="isSearchExpanded = false"
                        />

                        <!-- Desktop Search Field -->
                        <div v-else class="hidden w-full max-w-xl lg:block">
                            <BibleSearch
                                placeholder="Suche nach Bibelversen..."
                                :aria-label="'Search Bible verses'"
                            />
                        </div>
                    </div>

                    <!-- Text Format Button (right) -->
                    <div
                        class="flex w-1/4 flex-shrink-0 justify-end"
                        :class="{ invisible: isSearchExpanded }"
                    >
                        <button
                            id="txt-format-btn"
                            type="button"
                            class="hover:bg-theme-100 dark:hover:bg-theme-800 text-theme-600 hover:text-theme-900 dark:text-theme-400 dark:hover:text-theme-100 flex h-10 w-10 items-center justify-center rounded-full focus:outline-hidden"
                            :class="{
                                'bg-theme-100 dark:bg-theme-800':
                                    isSettingsOpen,
                            }"
                            aria-label="Text format settings"
                            @click="toggleSettings"
                        >
                            <Icon
                                name="TextSettings"
                                class="h-5 w-5"
                                :aria-label="'Text format settings'"
                            />
                        </button>
                    </div>
                </div>
            </div>

            <!-- Mobile Menu -->
            <div v-show="isSearchExpanded" class="sm:hidden">
                <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                    <div class="py-3">
                        <div class="space-y-1">
                            <BibleBookSelector
                                aria-label="Select Bible book"
                                @book-select="handleBookSelect"
                            />
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Border line that respects max width -->
        <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div class="border-theme-200 dark:border-theme-400 border-b"></div>
        </div>

        <!-- Sub navigation bar - completely independent from main nav -->
        <div
            v-if="!isSearchPage"
            class="fixed inset-x-0 top-0 z-40 transition-all duration-300 ease-in-out"
            :class="{ 'submenu-visible': isScrollingDown && !isSearchExpanded }"
            :style="{
                transform: isScrollingDown
                    ? 'translateY(0)'
                    : 'translateY(80px)',
            }"
        >
            <!-- Add the fine spacer line here -->
            <!-- Fine spacer line that respects margins -->
            <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                <div
                    class="border-theme-200 dark:border-theme-400 border-t"
                ></div>
            </div>

            <SubNavigationBar />
        </div>
    </div>
</template>

<script setup lang="ts">
import ApplicationLogo from '@/Components/common/ApplicationLogo.vue';
import { Icon } from '@/Components/Icons';
import BibleBookSelector from '@/Components/Navigation/BibleBookSelector.vue';
import SubNavigationBar from '@/Components/Navigation/SubNavigationBar.vue';
import BibleSearch from '@/Components/Search/BibleSearch.vue';
import MobileSearchOverlay from './MobileSearchOverlay.vue';

import { router, usePage } from '@inertiajs/vue3';
import { computed, onMounted, onUnmounted, ref } from 'vue';

import { useSettingsAside } from '@/composables/useSettingsAside';
import type { BookView } from '@esbo/types';

/**
 * Interface for scroll handling configuration
 */
interface ScrollConfig {
    threshold: number;
    debounceTime: number;
}

const { isSettingsOpen, toggleSettings } = useSettingsAside();

// Props and emits definition
defineProps<{
    reference?: string;
}>();

// Reactive state
const isSearchExpanded = ref(false);
const isScrollingDown = ref(false);
const isHovering = ref(false);
let lastScrollY = 0;
let scrollTimeout: number | null = null;

// Check if we're on the search page
const isSearchPage = computed(() => {
    const page = usePage();
    return page.component === 'SearchResults';
});

// Scroll configuration
const scrollConfig: ScrollConfig = {
    threshold: 100,
    debounceTime: 50,
};

/**
 * Toggles the search expanded state
 */
const toggleSearchExpanded = () => {
    isSearchExpanded.value = !isSearchExpanded.value;
};

/**
 * Handles scroll events to control navbar visibility
 * Uses debouncing to improve performance
 */
const handleScroll = () => {
    if (scrollTimeout) {
        window.clearTimeout(scrollTimeout);
    }

    scrollTimeout = window.setTimeout(() => {
        const currentScrollY = window.scrollY;
        const isScrollingDownwards = currentScrollY > lastScrollY;
        const isPastThreshold = currentScrollY > scrollConfig.threshold;
        const isNotHovering = !isHovering.value;

        // Only hide navbar when scrolling down past threshold and not hovering
        isScrollingDown.value =
            isScrollingDownwards && isPastThreshold && isNotHovering;

        lastScrollY = currentScrollY;
    }, scrollConfig.debounceTime);
};

/**
 * Handles mouse enter event on the navbar
 * Shows the navbar when mouse enters
 */
const handleMouseEnter = () => {
    isHovering.value = true;
    isScrollingDown.value = false;
};

/**
 * Handles mouse leave event on the navbar
 * Restores scroll state when mouse leaves
 */
const handleMouseLeave = () => {
    isHovering.value = false;
    // Restore scroll state if we're scrolled down
    if (
        window.scrollY > scrollConfig.threshold &&
        window.scrollY > lastScrollY
    ) {
        isScrollingDown.value = true;
    }
};

/**
 * Lifecycle hooks for scroll event handling
 */
onMounted(() => {
    window.addEventListener('scroll', handleScroll, { passive: true });
});

onUnmounted(() => {
    window.removeEventListener('scroll', handleScroll);
    if (scrollTimeout) {
        window.clearTimeout(scrollTimeout);
    }
});

/**
 * Handles book selection and navigates to the selected book/chapter
 * @param book - The selected book
 * @param chapter - Optional chapter number
 */
const handleBookSelect = (book: BookView, chapter?: number) => {
    if (chapter) {
        router.visit(route('read', { book: book.slug, chapter }));
    } else {
        router.visit(route('read', { book: book.slug }));
    }
};
</script>

<style scoped>
.submenu-visible {
    transform: translateY(0) !important;
}

/* When main nav is hidden, show submenu at top */
:deep(.submenu-visible nav) {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 40;
}
</style>

```
