---
id: BibleBookOffcanvas
title: BibleBookOffcanvas
---

# BibleBookOffcanvas

No description available.





## Source

```vue
<template>
    <div v-show="modelValue" class="md:hidden">
        <!-- Backdrop -->
        <transition
            enter-active-class="transition-opacity ease-out duration-300"
            enter-from-class="opacity-0"
            enter-to-class="opacity-100"
            leave-active-class="transition-opacity ease-in duration-300"
            leave-from-class="opacity-100"
            leave-to-class="opacity-0"
        >
            <div
                v-show="modelValue"
                class="bg-opacity-25 fixed inset-0 bg-black transition-opacity"
                @click="$emit('update:modelValue', false)"
            ></div>
        </transition>

        <!-- Offcanvas -->
        <transition
            enter-active-class="transition ease-out duration-300 transform"
            enter-from-class="-translate-x-full"
            enter-to-class="translate-x-0"
            leave-active-class="transition ease-in duration-300 transform"
            leave-from-class="translate-x-0"
            leave-to-class="-translate-x-full"
        >
            <div
                v-show="modelValue"
                class="scrollbar-hide dark:bg-theme-800 bg-theme fixed inset-y-0 left-0 z-50 w-full max-w-sm overflow-y-auto shadow-xl"
                @click.stop
            >
                <!-- Header -->
                <div
                    class="border-theme-200 dark:border-theme-700 flex items-center justify-between border-b p-4"
                >
                    <h2
                        class="text-theme-900 dark:text-theme-100 text-lg font-medium"
                    >
                        Bibel Navigation
                    </h2>
                    <button
                        type="button"
                        class="hover:bg-theme-100 dark:hover:bg-theme-700 text-theme-400 hover:text-theme-500 rounded-lg p-2 focus:ring-2 focus:ring-blue-500 focus:outline-hidden focus:ring-inset"
                        @click="$emit('update:modelValue', false)"
                    >
                        <span class="sr-only">Schließen</span>
                        <svg
                            class="h-6 w-6"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M6 18L18 6M6 6l12 12"
                            />
                        </svg>
                    </button>
                </div>

                <div class="p-4">
                    <!-- Tabs -->
                    <div class="mb-4 flex border-b">
                        <button
                            class="flex-1 pb-2 text-center text-sm"
                            :class="
                                activeTab === 'book'
                                    ? 'border-b-2 border-blue-500 text-blue-600'
                                    : 'text-theme-500'
                            "
                            @click="activeTab = 'book'"
                        >
                            Buch
                        </button>
                        <button
                            class="flex-1 pb-2 text-center text-sm"
                            :class="[
                                activeTab === 'chapter'
                                    ? 'border-b-2 border-blue-500 text-blue-600'
                                    : 'text-theme-500',
                                !selectedBook &&
                                    'cursor-not-allowed opacity-50',
                            ]"
                            :disabled="!selectedBook"
                            @click="activeTab = 'chapter'"
                        >
                            Kapitel
                        </button>
                    </div>

                    <!-- Search Input -->
                    <div class="mb-4">
                        <input
                            v-model="searchQuery"
                            type="text"
                            :placeholder="
                                activeTab === 'book'
                                    ? 'Bibelbuch suchen...'
                                    : 'Kapitel suchen...'
                            "
                            class="border-theme-300 w-full rounded-lg border px-3 py-2 text-sm focus:border-blue-500 focus:ring-2 focus:ring-blue-500"
                        />
                    </div>

                    <!-- Book Tab Content -->
                    <div
                        v-if="activeTab === 'book'"
                        class="space-y-4 overflow-y-auto"
                    >
                        <div
                            v-for="category in filteredCategories"
                            :key="category.name"
                            class="space-y-2"
                        >
                            <h3 class="text-theme-700 font-semibold">
                                {{ category.name }}
                            </h3>
                            <div class="grid grid-cols-2 gap-2">
                                <button
                                    v-for="book in category.books"
                                    :key="book.id"
                                    class="dropdown-item hover:bg-theme-100 rounded-lg px-3 py-2 text-left text-sm focus:ring-2 focus:ring-blue-500 focus:outline-hidden"
                                    :class="{
                                        'bg-blue-50 text-blue-700':
                                            selectedBook?.id === book.id,
                                    }"
                                    @click="selectBook(book)"
                                >
                                    {{ book.name }}
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Chapter Tab Content -->
                    <div
                        v-else-if="activeTab === 'chapter'"
                        class="overflow-y-auto"
                    >
                        <div class="grid grid-cols-4 gap-2">
                            <button
                                v-for="chapter in filteredChapters"
                                :key="chapter"
                                class="dropdown-item hover:bg-theme-100 rounded-lg px-3 py-2 text-center text-sm focus:ring-2 focus:ring-blue-500 focus:outline-hidden"
                                :class="{
                                    'bg-blue-50 text-blue-700':
                                        selectedChapter === chapter,
                                }"
                                @click="selectChapter(chapter)"
                            >
                                {{ chapter }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </transition>
    </div>
</template>

<script setup lang="ts">
import type { BookView } from '@esbo/types';
import { computed, ref } from 'vue';

const props = defineProps<{
    modelValue: boolean;
    categories: Array<{
        name: string;
        books: BookView[];
    }>;
    selectedBook?: BookView;
}>();

const emit = defineEmits<{
    (e: 'update:modelValue', value: boolean): void;
    (e: 'select-book', book: BookView): void;
    (e: 'select-chapter', book: BookView, chapter: number): void;
}>();

const activeTab = ref<'book' | 'chapter'>('book');
const searchQuery = ref('');
const selectedChapter = ref<number>();

const filteredCategories = computed(() => {
    if (!searchQuery.value) return props.categories;

    const query = searchQuery.value.toLowerCase();
    return props.categories
        .map((category) => ({
            ...category,
            books: category.books.filter((book) =>
                book.name.toLowerCase().includes(query),
            ),
        }))
        .filter((category) => category.books.length > 0);
});

const filteredChapters = computed<number[]>(() => {
    if (!props.selectedBook?.chapterCount) return [];
    const chapterCount = props.selectedBook.chapterCount;
    return Array.from({ length: chapterCount }, (_, i) => i + 1);
});

function selectBook(book: BookView) {
    emit('select-book', book);
    activeTab.value = 'chapter';
    searchQuery.value = '';
}

function selectChapter(chapter: number) {
    if (!props.selectedBook) return;
    emit('select-chapter', props.selectedBook, chapter);
    emit('update:modelValue', false);
    // Reset for next use
    setTimeout(() => {
        activeTab.value = 'book';
        selectedChapter.value = undefined;
    }, 100);
}
</script>

<style scoped>
.scrollbar-hide {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
    display: none; /* Chrome, Safari and Opera */
}
</style>

```
