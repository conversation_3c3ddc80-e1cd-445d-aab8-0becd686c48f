---
id: NavLink
title: NavLink
---

# NavLink

No description available.





## Source

```vue
<script setup lang="ts">
import { Link } from '@inertiajs/vue3';
import { computed } from 'vue';

const props = defineProps<{
    href: string;
    active?: boolean;
}>();

const classes = computed(() =>
    props.active
        ? 'inline-flex items-center px-1 pt-1 border-b-2 border-indigo-400 dark:border-indigo-600 text-sm font-medium leading-5 text-theme-900 dark:text-theme-100 focus:outline-hidden focus:border-indigo-700 transition duration-150 ease-in-out'
        : 'inline-flex items-center px-1 pt-1 border-b-2 border-transparent text-sm font-medium leading-5 text-theme-500 dark:text-theme-400 hover:text-theme-700 dark:hover:text-theme-300 hover:border-theme-300 dark:hover:border-theme-700 focus:outline-hidden focus:text-theme-700 dark:focus:text-theme-300 focus:border-theme-300 dark:focus:border-theme-700 transition duration-150 ease-in-out',
);
</script>

<template>
    <Link :href="href" :class="classes">
        <slot />
    </Link>
</template>

```
