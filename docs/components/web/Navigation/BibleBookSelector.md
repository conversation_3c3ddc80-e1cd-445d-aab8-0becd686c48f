---
id: BibleBookSelector
title: BibleBookSelector
---

# BibleBookSelector

No description available.





## Source

```vue
<template>
    <div class="bible-selector dropdown-container relative">
        <div class="flex items-center gap-2">
            <button
                type="button"
                class="hover:text-theme-900 hover:bg-theme flex items-center gap-1 rounded-lg p-1 transition-colors duration-200 focus:ring-2 focus:ring-blue-500 focus:outline-none"
                :class="{
                    'text-blue-600': selectedBook,
                    'bg-theme': isOpen,
                    'text-theme-600': inSearchComponent,
                    'text-theme-900 dark:text-theme-100':
                        !inSearchComponent && !selectedBook,
                }"
                @click="toggleDropdown"
            >
                <Icon
                    name="BookOpen"
                    class="h-5 w-5"
                    :aria-label="'Toggle book selection'"
                />
            </button>
        </div>

        <Overlay :show="isOpen" @click="isOpen = false" />

        <template v-if="isMobile">
            <Teleport to="body">
                <OffcanvasSidebar
                    :is-open="isOpen"
                    :sections="categories"
                    @close="isOpen = false"
                    @select-book="handleBookSelect"
                    @select-chapter="handleChapterSelect"
                />
            </Teleport>
        </template>
        <template v-else>
            <BibleBookDropdown
                v-model="isOpen"
                :categories="categories"
                :selected-book="selectedBook"
                :search-query="searchQuery"
                @select-book="handleBookSelect"
                @select-chapter="handleChapterSelect"
            />
        </template>
    </div>
</template>

<script setup lang="ts">
import Overlay from '@/Components/common/Overlay.vue';
import { Icon } from '@/Components/Icons';
import BibleBookDropdown from '@/Components/Navigation/BibleBookDropdown.vue';
import OffcanvasSidebar from '@/Components/Navigation/OffcanvasSidebar.vue';
import { useDropdown } from '@/composables/useDropdown';
import { logger } from '@/utils/logger';
import type { BookView } from '@esbo/types';
import { Testament } from '@esbo/types';
import { router, usePage } from '@inertiajs/vue3';
import { onMounted, ref, watch } from 'vue';

const props = defineProps<{
    modelValue?: BookView;
    inSearchComponent?: boolean;
}>();

const emit = defineEmits<{
    (e: 'update:modelValue', value: BookView): void;
    (e: 'update:searchQuery', value: string): void;
    (e: 'book-select', book: BookView, chapter?: number): void;
}>();

const { isOpen, toggleDropdown } = useDropdown('bible-selector');
const selectedBook = ref<BookView | undefined>(props.modelValue);
const isMobile = ref(window.innerWidth < 768);
const searchQuery = ref('');
const selectedChapter = ref<number | undefined>();
// Fetch books when component is mounted
const categories = ref<
    Array<{
        name: string;
        books: BookView[];
    }>
>([]);

// Watch for window resize
onMounted(() => {
    const handleResize = () => {
        isMobile.value = window.innerWidth < 768;
    };
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
});

// Watch for external model value changes
watch(
    () => props.modelValue,
    (newValue) => {
        selectedBook.value = newValue;
    },
);

onMounted(async () => {
    const { books } = usePage().props;

    if (!books || !books.sections || !Array.isArray(books.sections)) {
        logger.error('No books data available from Inertia props');
        return;
    }

    const otBooks = books.sections[0].books.filter(
        (book) => book.testament === Testament.OT,
    );
    const ntBooks = books.sections[1].books.filter(
        (book) => book.testament === Testament.NT,
    );

    categories.value = [
        {
            name: 'Altes Testament',
            books: otBooks,
        },
        {
            name: 'Neues Testament',
            books: ntBooks,
        },
    ];
});

function handleBookSelect(book: BookView) {
    selectedBook.value = book;
    emit('update:modelValue', book);
    emit('update:searchQuery', book.name);
}

function handleChapterSelect(book: BookView, chapter: number) {
    selectedBook.value = book;
    selectedChapter.value = chapter;
    isOpen.value = false;

    // Format book name: replace period with hyphen and remove spaces
    const bookName = book.slug
        .replace(/(\d+)\s+(\w+)/, '$1-$2')
        .replace(/\s+/g, '');
    const url = `/${bookName}${chapter}`;

    // Navigate to the book and chapter
    router.visit(url);
}
</script>

```
