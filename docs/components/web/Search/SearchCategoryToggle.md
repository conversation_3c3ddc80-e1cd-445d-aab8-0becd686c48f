---
id: SearchCategoryToggle
title: SearchCategoryToggle
---

# SearchCategoryToggle

No description available.





## Source

```vue
<template>
    <div class="search-category-toggles">
        <h3 class="mb-2 text-sm font-medium">Search in:</h3>
        <div class="flex flex-wrap gap-2">
            <label
                v-for="category in categories"
                :key="category.value"
                class="inline-flex items-center"
            >
                <input
                    v-model="selectedCategories"
                    type="checkbox"
                    :value="category.value"
                    class="form-checkbox text-primary-600 h-4 w-4 transition duration-150 ease-in-out"
                    @change="emitChange"
                />
                <span class="ml-2 text-sm">{{ category.label }}</span>
            </label>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';

const props = defineProps<{
    initialCategories?: string[];
}>();

const emit = defineEmits<{
    (e: 'update:categories', categories: string[]): void;
}>();

const categories = [
    { label: 'Bible Text', value: 'verse' },
    { label: 'Footnotes', value: 'footnote' },
    { label: 'Book Info', value: 'book' },
    { label: 'Metadata', value: 'metadata' },
];

// Initialize with all categories selected if none provided
const selectedCategories = ref<string[]>(
    props.initialCategories || categories.map((c) => c.value),
);

// Watch for external changes
watch(
    () => props.initialCategories,
    (newVal) => {
        if (newVal) {
            selectedCategories.value = newVal;
        }
    },
);

function emitChange() {
    emit('update:categories', selectedCategories.value);
}
</script>

```
