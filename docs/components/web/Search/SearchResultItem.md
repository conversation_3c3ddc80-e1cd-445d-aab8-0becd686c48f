---
id: SearchResultItem
title: SearchResultItem
---

# SearchResultItem

No description available.





## Source

```vue
<template>
    <div
        class="hover:bg-theme-50 dark:hover:bg-theme-800 border-theme-300 rounded-lg border p-4 transition"
    >
        <div class="flex justify-between">
            <div
                class="result-type rounded-full px-2 py-0.5 text-xs uppercase"
                :class="getTypeColorClasses(result.type)"
            >
                {{ getTypeLabel(result.type) }}
            </div>
        </div>

        <h3 class="text-lg font-medium">
            <Link v-if="result.url" :href="result.url">
                {{ result.title }}
            </Link>
            <span v-else>{{ result.title }}</span>
        </h3>

        <!-- Special handling for footnote results -->
        <div
            v-if="result.type === 'footnote' && result.footnoteInfo"
            class="mt-2"
        >
            <div class="content">
                <!-- eslint-disable-next-line vue/no-v-html -->
                <span v-html="highlightedVerseContent"></span>
            </div>

            <!-- Add footnote content -->
            <div class="mt-2 rounded bg-gray-50 p-2 text-sm dark:bg-gray-800">
                <span class="font-medium">Fußnote: </span>
                <!-- eslint-disable-next-line vue/no-v-html -->
                <span v-html="footnoteContent"></span>
            </div>
        </div>

        <!-- eslint-disable-next-line vue/no-v-html -->
        <div v-else class="content mt-2" v-html="highlightedContent"></div>
        <div
            v-if="result.metadata && showMetadata"
            class="metadata mt-2 text-xs text-gray-500"
        >
            <div
                v-if="result.metadata.tags && result.metadata.tags.length"
                class="tags"
            >
                <span
                    v-for="tag in result.metadata.tags"
                    :key="tag"
                    class="tag mr-1"
                >
                    #{{ tag }}
                </span>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useSearchResults } from '@/composables/useSearchResults';
import type { GeneralSearchResult } from '@esbo/types';
import { Link } from '@inertiajs/vue3';
import { computed, ref } from 'vue';

const props = defineProps<{
    result: GeneralSearchResult;
    query: string;
}>();

const showMetadata = ref(false);

const getTypeLabel = (type: string): string => {
    const typeTranslations: Record<string, string> = {
        book: 'Buch',
        footnote: 'Fußnote',
        metadata: 'Metadaten',
        verse: 'Vers',
        word: 'Wort',
    };

    return typeTranslations[type] || type;
};

const { highlightContent } = useSearchResults({
    data: [],
    metadata: { total: 0, current_page: 1, last_page: 1 },
});

const highlightedContent = computed(() => {
    return highlightContent(props.result.content, props.query);
});

const footnoteContent = computed(() => {
    if (!props.result.type === 'footnote' || !props.result.footnoteInfo) {
        return '';
    }

    // If we have content structure, we could render it more nicely
    // For now, just highlight the content
    return highlightContent(props.result.content, props.query);
});

const getTypeColorClasses = (type: string): string => {
    const typeColors: Record<string, string> = {
        book: 'bg-type-book text-white',
        footnote: 'bg-type-footnote text-white',
        metadata: 'bg-type-metadata text-white',
        verse: 'bg-type-verse text-white',
        word: 'bg-type-word text-white',
    };

    return (
        typeColors[type] ||
        'bg-theme-100 text-theme-800 dark:bg-theme-900 dark:text-theme-200'
    );
};

const highlightedVerseContent = computed(() => {
    if (!props.result.type === 'footnote' || !props.result.footnoteInfo) {
        return '';
    }

    const verseContent = props.result.footnoteInfo.verseContent || '';
    const referencedWord = props.result.footnoteInfo.referencedWord || '';

    console.log('Verse content', verseContent, referencedWord);

    if (!referencedWord || !verseContent) {
        return highlightContent(verseContent, props.query);
    }

    // Find the referenced word in the verse content
    // This assumes the referenced word is the last occurrence in the verse
    const lastIndex = verseContent.lastIndexOf(referencedWord);

    if (lastIndex === -1) {
        return highlightContent(verseContent, props.query);
    }

    // Split the verse content into parts
    const beforeWord = verseContent.substring(0, lastIndex);
    const word = verseContent.substring(
        lastIndex,
        lastIndex + referencedWord.length,
    );
    const afterWord = verseContent.substring(lastIndex + referencedWord.length);

    // Highlight the search query in each part
    const highlightedBefore = highlightContent(beforeWord, props.query);
    const highlightedWord = highlightContent(word, props.query);
    const highlightedAfter = highlightContent(afterWord, props.query);

    // Combine the parts with the referenced word highlighted
    return `${highlightedBefore}<span class="referenced-word">${highlightedWord}</span>${highlightedAfter}`;
});
</script>

<style scoped>
.referenced-word {
    border: 2px solid var(--color-theme-500);
    border-radius: 0.25rem;
    padding: 0 0.25rem;
    margin: 0 0.1rem;
    background-color: var(--color-theme-50);
}

:global(.dark) .referenced-word {
    background-color: var(--color-theme-900);
    border-color: var(--color-theme-400);
}
</style>

```
