---
id: BibleSearch
title: BibleSearch
---

# BibleSearch

No description available.





## Source

```vue
<template>
    <div class="search-container relative z-40">
        <div class="absolute inset-y-0 left-0 flex items-center pl-2">
            <BibleBookSelector
                v-model="selectedBook"
                :in-search-component="true"
                @book-select="handleBookSelect"
            />
        </div>
        <form @submit.prevent="handleSubmit">
            <TextInput
                v-model="searchQuery"
                type="text"
                :placeholder="placeholder"
                :aria-label="ariaLabel"
                class="dark:bg-theme-700 text-theme-900 dark:text-theme-100 dark:placeholder:text-theme-400 bg-theme/90 border-theme-300/50 w-full rounded-lg py-3 pr-24 pl-12 placeholder-gray-500 transition-all duration-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-500 dark:ring-gray-600"
                :class="{
                    'border-red-500 focus:border-red-500 focus:ring-red-500':
                        error,
                }"
                @input="handleInput"
                @keydown.down="handleArrowDown"
                @keydown.up="handleArrowUp"
                @keydown.esc="closeSuggestions"
                @blur="handleBlur"
            />

            <Icon
                v-if="searchQuery"
                name="X"
                class="absolute top-1/2 right-10 h-5 w-5 -translate-y-1/2 transform cursor-pointer"
                :aria-label="'Clear search'"
                @click="searchQuery = ''"
            />
        </form>
        <!-- Typeahead Suggestions -->
        <div
            v-show="showSuggestions && suggestions.length > 0"
            class="hide-scrollbar dark:bg-theme-800 bg-theme border-theme-300 dark:border-theme-600 absolute top-full right-0 left-0 z-50 mt-1 max-h-60 overflow-y-auto rounded-lg border shadow-lg"
        >
            <div
                v-for="(suggestion, index) in suggestions"
                :key="index"
                :class="[
                    'hover:bg-theme-100 dark:hover:bg-theme-700 cursor-pointer px-4 py-2',
                    {
                        'bg-theme-100 dark:bg-theme-700':
                            index === selectedIndex,
                    },
                ]"
                class="text-theme-900 dark:text-theme-100"
                @mousedown.prevent="selectSuggestion(suggestion)"
            >
                <div class="flex flex-col">
                    <span class="font-medium">{{
                        suggestion.split(' - ')[0]
                    }}</span>
                    <span class="text-theme-600 dark:text-theme-400 text-sm">{{
                        suggestion.split(' - ')[1]
                    }}</span>
                </div>
            </div>
        </div>
        <div class="absolute inset-y-0 right-0 flex items-center pr-2">
            <SearchTypeDropdown />
        </div>
    </div>
    <InputError :message="error" />
</template>

<script setup lang="ts">
import InputError from '@/Components/common/InputError.vue';
import TextInput from '@/Components/common/TextInput.vue';
import { Icon } from '@/Components/Icons';
import BibleBookSelector from '@/Components/Navigation/BibleBookSelector.vue';
import { useDebounce } from '@/composables/useDebounce';
import { useSearchStore } from '@/stores/searchStore';
import {
    BOOK_SLUGS,
    buildUrl,
    CHAPTERS_COUNT,
    parse,
} from '@/utils/bibleTests';
import { logger } from '@/utils/logger';
import type { BookView } from '@esbo/types';
import { router } from '@inertiajs/vue3';
import axios from 'axios';
import { computed, onMounted, ref } from 'vue';
import SearchTypeDropdown from './SearchTypeDropdown.vue';

type RouteParams = {
    [key: string]: string | number;
};

declare function route(name: string, params?: RouteParams): string;

const searchStore = useSearchStore();

withDefaults(
    defineProps<{
        placeholder?: string;
        ariaLabel?: string;
    }>(),
    {
        placeholder: 'Bibelstelle oder Suchbegriff ...',
        ariaLabel: 'Bibelsuche',
    },
);

const searchQuery = computed({
    get: () => searchStore.query,
    set: (value: string) => searchStore.setQuery(value),
});

const selectedBook = ref<BookView | undefined>(undefined);
const error = ref('');
const suggestions = ref<string[]>([]);
const showSuggestions = ref(false);
const selectedIndex = ref(-1);
const minCharsForTypeahead = 2;

const handleBookSelect = (book: BookView, chapter?: number) => {
    selectedBook.value = book;
    if (chapter) {
        router.visit(`/${book.slug}${chapter}`, {
            preserveState: true,
            preserveScroll: true,
        });
    }
};

const handleSubmit = (e: Event) => {
    e.preventDefault();
    search();
};

const search = () => {
    if (!searchQuery.value && !selectedBook.value) {
        error.value =
            'Bitte gib einen Suchbegriff ein oder wähle ein Bibelbuch aus.';
        return;
    }

    error.value = '';
    const query = searchQuery.value.trim();

    // If we have a selected book from typeahead, go to Display.vue
    if (selectedBook.value) {
        router.visit(`/${selectedBook.value.slug}1`, {
            preserveState: true,
        });
        return;
    }

    // Try to parse as a reference first
    const reference = parse(query);

    // Check if it's a valid reference with a known book
    if (reference.reference.book) {
        if (!CHAPTERS_COUNT[reference.reference.book]) {
            error.value =
                'Bitte gib einen Suchbegriff ein oder wähle ein Bibelbuch aus.';
            return;
        }

        // If we have a chapter, validate it
        if (reference.reference.chapter) {
            if (
                reference.reference.chapter <=
                CHAPTERS_COUNT[reference.reference.book]
            ) {
                // Go to Display.vue with the reference
                const url = buildUrl(reference);
                if (url) {
                    router.visit('/' + url, {
                        preserveState: true,
                    });
                    return;
                }
            }
        } else {
            // If we only have a book, go to chapter 1
            router.visit(`/${reference.reference.book}1`, {
                preserveState: true,
            });
            return;
        }
    }

    // If not a valid reference and query is not empty, perform a search
    if (query && query.trim() !== '') {
        try {
            const url = route('search.query', { query });
            router.visit(url, {
                preserveState: true,
                preserveScroll: true,
                only: ['query', 'results', 'filters'],
            });
        } catch (e) {
            logger.error('Error during search:', e);
            error.value = 'Ein Fehler ist während der Suche aufgetreten.';
        }
    }
};

const fetchSuggestions = async (query: string) => {
    try {
        const url = route('api.search.books');
        const response = await axios.get(url, {
            params: {
                q: query,
                types: ['books'],
            },
        });
        return response.data;
    } catch (error) {
        logger.error('Failed to fetch book suggestions:', error);
        return [];
    }
};

const debouncedGetSuggestions = useDebounce(async (input: string) => {
    if (!input || input.length < minCharsForTypeahead) {
        suggestions.value = [];
        showSuggestions.value = false;
        return;
    }

    // Try to parse as a reference first
    const reference = parse(input);

    // Search for books
    const books = await fetchSuggestions(input);
    const suggestionsArray = [];

    // If we have a valid reference with a book, show chapter suggestions
    if (reference.reference.book && CHAPTERS_COUNT[reference.reference.book]) {
        const bookSlug =
            BOOK_SLUGS[reference.reference.book] || reference.reference.book;

        // If we have a chapter, show verse suggestions
        if (
            reference.reference.chapter &&
            CHAPTERS_COUNT[reference.reference.book] >=
                reference.reference.chapter
        ) {
            suggestionsArray.push(
                `${bookSlug} ${reference.reference.chapter}${reference.reference.verseStart ? ',' + reference.reference.verseStart : ''} - Gehe zu dieser Stelle`,
            );
        } else {
            // Show first chapter suggestion
            suggestionsArray.push(`${bookSlug} 1 - Gehe zum ersten Kapitel`);
        }
    }

    // Add all matching book suggestions
    if (Array.isArray(books)) {
        // First add exact matches
        const exactMatches = books.filter((book) => book.isExactMatch);
        suggestionsArray.push(
            ...exactMatches.map((book) => `${book.slug} - ${book.name}`),
        );

        // Then add partial matches
        const partialMatches = books.filter((book) => !book.isExactMatch);
        if (partialMatches.length > 0) {
            suggestionsArray.push(
                ...partialMatches.map((book) => `${book.slug} - ${book.name}`),
            );
        }
    }

    // Add search suggestion at the end if we have input
    if (input.trim()) {
        suggestionsArray.push(`"${input}" - Suche diesen Begriff`);
    }

    suggestions.value = suggestionsArray;
    showSuggestions.value = suggestions.value.length > 0;
    logger.info(
        'Found suggestions:',
        suggestions.value,
        'showing:',
        showSuggestions.value,
    );
}, 100);

const handleInput = async (event: Event) => {
    const input = (event.target as HTMLInputElement).value;
    searchQuery.value = input;
    selectedIndex.value = -1;
    await debouncedGetSuggestions(input);
};

const selectSuggestion = (suggestion: string) => {
    // Check if it's a search suggestion
    if (suggestion.endsWith('- Suche diesen Begriff')) {
        const searchTerm = suggestion.split(' - ')[0].replace(/"/g, '');
        router.visit(
            route('search.query', { query: encodeURIComponent(searchTerm) }),
        );
        return;
    }

    // Otherwise it's a reference suggestion
    const parts = suggestion.split(' - ')[0];
    searchQuery.value = parts;
    // If it's a book suggestion with a chapter, go directly to that chapter
    if (parts.match(/\d+$/)) {
        router.visit('/' + parts.replace(/\s+/g, ''));
    } else {
        // If it's just a book, go to chapter 1
        router.visit('/' + parts + '1');
    }
    closeSuggestions();
};

const handleArrowDown = (e: KeyboardEvent) => {
    e.preventDefault();
    selectedIndex.value =
        selectedIndex.value < suggestions.value.length - 1
            ? selectedIndex.value + 1
            : 0;
};

const handleArrowUp = (e: KeyboardEvent) => {
    e.preventDefault();
    selectedIndex.value =
        selectedIndex.value > 0
            ? selectedIndex.value - 1
            : suggestions.value.length - 1;
};

const handleBlur = () => {
    // Delay closing to allow click events on suggestions
    setTimeout(() => {
        closeSuggestions();
    }, 200);
};

const closeSuggestions = () => {
    showSuggestions.value = false;
    selectedIndex.value = -1;
};

// Close suggestions when clicking outside
onMounted(() => {
    document.addEventListener('click', (e) => {
        if (!(e.target as HTMLElement).closest('.search-container')) {
            closeSuggestions();
        }
    });
});
</script>

<style>
.hide-scrollbar {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

.hide-scrollbar::-webkit-scrollbar {
    display: none; /* Chrome, Safari and Opera */
}
</style>

```
