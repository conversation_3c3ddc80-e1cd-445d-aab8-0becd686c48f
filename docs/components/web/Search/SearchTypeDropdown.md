---
id: SearchTypeDropdown
title: SearchTypeDropdown
---

# SearchTypeDropdown

No description available.





## Source

```vue
<template>
    <div class="relative">
        <button
            type="button"
            class="text-theme-600 hover:text-theme-900 dark:text-theme-400 dark:hover:text-theme-100 flex items-center gap-1 rounded-lg px-3 py-1 focus:outline-hidden"
            @click="toggleDropdown"
        >
            <Icon
                name="Settings2"
                class="h-5 w-5"
                :aria-label="'Toggle search type'"
            />
        </button>

        <div
            v-show="isOpen"
            class="dark:bg-theme-800 bg-theme border-theme-300 dark:border-theme-700 absolute right-0 z-30 mt-2 w-48 rounded-lg border p-2 shadow-lg"
        >
            <div class="space-y-2">
                <div>Suche in:</div>
                <div
                    class="border-theme-200 dark:border-theme-700 border-t pb-2"
                >
                    <label
                        v-for="type in searchTypes"
                        :key="type.value"
                        class="hover:bg-theme-100 dark:hover:bg-theme-700 flex items-center gap-2 rounded-sm p-2"
                    >
                        <input
                            v-model="selectedTypes"
                            type="checkbox"
                            :value="type.value"
                            class="border-theme-300 h-4 w-4 rounded-sm text-blue-600 focus:ring-blue-500"
                        />
                        <Icon
                            name="Check"
                            class="h-5 w-5"
                            :aria-label="'Selected'"
                        />
                        <span class="text-theme-900 dark:text-theme-100">{{
                            type.label
                        }}</span>
                    </label>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { Icon } from '@/Components/Icons';
import { useSearchSettingsStore } from '@/stores/searchSettingsStore';
import { computed, onBeforeUnmount, onMounted, ref } from 'vue';

const searchSettingsStore = useSearchSettingsStore();

// Use search types from the store
const searchTypes = computed(() => searchSettingsStore.availableSearchTypes);

const isOpen = ref(false);

// Use computed property to directly interact with the store
const selectedTypes = computed({
    get: () => searchSettingsStore.types,
    set: (value) => searchSettingsStore.setTypes(value),
});

function toggleDropdown() {
    isOpen.value = !isOpen.value;
}

function closeDropdown(e: Event) {
    const target = e.target as HTMLElement;
    if (!target.closest('.relative')) {
        isOpen.value = false;
    }
}

// Add click event listener when component is mounted
onMounted(() => {
    document.addEventListener('click', closeDropdown);
});

// Remove event listener when component is unmounted
onBeforeUnmount(() => {
    document.removeEventListener('click', closeDropdown);
});
</script>

```
