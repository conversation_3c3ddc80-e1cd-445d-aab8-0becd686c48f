---
id: TextInput
title: TextInput
---

# TextInput

No description available.





## Source

```vue
<script setup lang="ts">
import { onMounted, ref } from 'vue';

const model = defineModel<string>({ required: true });

const input = ref<HTMLInputElement | null>(null);

onMounted(() => {
    if (input.value?.hasAttribute('autofocus')) {
        input.value?.focus();
    }
});

defineExpose({ focus: () => input.value?.focus() });
</script>

<template>
    <input
        ref="input"
        v-model="model"
        class="dark:bg-theme-900 dark:text-theme-300 border-theme-300 dark:border-theme-700 rounded-md shadow-xs focus:border-indigo-500 focus:ring-indigo-500 dark:focus:border-indigo-600 dark:focus:ring-indigo-600"
    />
</template>

```
