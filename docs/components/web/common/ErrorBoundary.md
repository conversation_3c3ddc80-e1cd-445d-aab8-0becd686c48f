---
id: ErrorBoundary
title: ErrorBoundary
---

# ErrorBoundary

No description available.





## Source

```vue
<script setup lang="ts">
import { ref, watch } from 'vue';
import { Icon } from '@/Components/Icons';

const props = defineProps<{
    error: Error | null;
}>();

const visible = ref(false);

watch(
    () => props.error,
    (newError) => {
        if (newError) {
            visible.value = true;
            setTimeout(() => {
                visible.value = false;
            }, 5000);
        }
    },
);
</script>

<template>
    <div class="relative">
        <Transition
            enter-active-class="transition duration-300 ease-out"
            enter-from-class="transform -translate-y-2 opacity-0"
            enter-to-class="transform translate-y-0 opacity-100"
            leave-active-class="transition duration-200 ease-in"
            leave-from-class="transform translate-y-0 opacity-100"
            leave-to-class="transform -translate-y-2 opacity-0"
        >
            <div
                v-if="visible && error"
                class="absolute top-0 right-0 left-0 z-50 mx-auto max-w-md rounded-lg bg-red-50 p-4 shadow-lg"
            >
                <div class="flex">
                    <div class="shrink-0">
                        <Icon
                            name="AlertTriangle"
                            class="h-5 w-5 text-red-500"
                            :aria-label="'Error'"
                        />
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-red-800">
                            {{ error.message }}
                        </p>
                    </div>
                </div>
            </div>
        </Transition>
        <slot />
    </div>
</template>

```
