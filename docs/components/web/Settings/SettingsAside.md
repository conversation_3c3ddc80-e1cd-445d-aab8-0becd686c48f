---
id: SettingsAside
title: SettingsAside
---

# SettingsAside

No description available.





## Source

```vue
<template>
    <aside
        :class="[
            'bg-theme dark:bg-theme-800 fixed top-0 right-0 z-50 h-screen w-96 translate-x-full overflow-y-auto p-6 shadow-xl transition-transform duration-300 ease-in-out',
            { '!translate-x-0': isOpen },
        ]"
        aria-labelledby="settings-title"
        role="complementary"
    >
        <div class="mb-6 flex items-center justify-between">
            <h2
                class="text-theme-900 dark:text-theme-100 text-xl font-semibold"
            >
                Einstellungen
            </h2>
            <button
                class="hover:bg-theme-100 dark:hover:bg-theme-700 text-theme-400 hover:text-theme-900 rounded-lg p-1.5 focus:ring-2 focus:ring-gray-200 focus:outline-none dark:hover:text-white"
                aria-label="Close settings"
                @click="close"
            >
                <Icon name="X" class="h-5 w-5" :aria-label="'Close settings'" />
            </button>
        </div>

        <div class="space-y-4">
            <details class="group" open>
                <summary
                    class="text-theme-900 dark:text-theme-100 focus-visible:ring-theme-500 flex cursor-pointer items-center justify-between rounded-md py-2 text-lg font-medium focus-visible:ring-2 focus-visible:outline-none"
                >
                    <span>Textanzeige</span>
                    <Icon
                        name="CaseSensitive"
                        class="h-5 w-5 transition-transform group-open:rotate-180"
                        :aria-label="'Font settings'"
                    />
                </summary>
                <div class="mt-2">
                    <TextDisplaySettings />
                </div>
            </details>

            <details class="group">
                <summary
                    class="text-theme-900 dark:text-theme-100 focus-visible:ring-theme-500 flex cursor-pointer items-center justify-between rounded-md py-2 text-lg font-medium focus-visible:ring-2 focus-visible:outline-none"
                >
                    <span>Farbthema</span>
                    <Icon
                        name="PaintBucket"
                        class="h-5 w-5 transition-transform group-open:rotate-180"
                        :aria-label="'Theme settings'"
                    />
                </summary>
                <div class="mt-2">
                    <ThemeSettings />
                </div>
            </details>

            <details class="group">
                <summary
                    class="text-theme-900 dark:text-theme-100 focus-visible:ring-theme-500 flex cursor-pointer items-center justify-between rounded-md py-2 text-lg font-medium focus-visible:ring-2 focus-visible:outline-none"
                >
                    <span>Sichtbarkeit</span>
                    <Icon
                        name="LetterText"
                        class="h-5 w-5 transition-transform group-open:rotate-180"
                        :aria-label="'Layout settings'"
                    />
                </summary>
                <div class="mt-2">
                    <VisibilitySettings />
                </div>
            </details>
        </div>
    </aside>
</template>

<script setup lang="ts">
import { Icon } from '@/Components/Icons';
import TextDisplaySettings from './TextDisplaySettings.vue';
import ThemeSettings from './ThemeSettings.vue';
import VisibilitySettings from './VisibilitySettings.vue';

defineProps<{
    isOpen: boolean;
}>();

const emit = defineEmits<{
    (e: 'close'): void;
}>();

function close() {
    emit('close');
}
</script>

```
