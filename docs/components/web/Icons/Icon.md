---
id: Icon
title: Icon
---

# Icon

No description available.





## Source

```vue
<!-- Icon.vue -->
<template>
  <component
    :is="icon"
    :size="size"
    :aria-label="ariaLabel"
    v-bind="$attrs"
  />
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { getIcon } from './registry'
import type { IconName } from './registry'

interface Props {
  name: IconName
  size?: number | string
  ariaLabel?: string
}

const props = withDefaults(defineProps<Props>(), {
  size: 24,
  ariaLabel: undefined
})

const icon = computed(() => getIcon(props.name))
</script>

```
