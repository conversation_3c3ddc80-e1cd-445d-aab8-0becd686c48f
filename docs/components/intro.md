---
id: intro
title: Vue Komponenten
sidebar_position: 1
---

# Vue Komponenten

Diese Dokumentation beschreibt die Vue 3 Komponenten, die in der ESB Online Plattform verwendet werden. Die Komponenten sind in TypeScript geschrieben und nutzen die Composition API.

## Komponenten-Struktur

Die Komponenten sind in verschiedene Kategorien unterteilt:

### UI-Komponenten

Grundlegende UI-Komponenten, die in der gesamten Anwendung verwendet werden:

- Buttons
- Formulare
- Navigation
- Modals

### Bibel-Komponenten

Spezialisierte Komponenten für die Bibelanzeige und -navigation:

- BibleReader: Hauptkomponente für die Anzeige des Bibeltexts
- BibleNavigation: Komponente für die Navigation zwischen Büchern, Kapiteln und Versen
- BibleSearch: Komponente für die Suche in der Bibel

## Verwendung der Komponenten

Die Komponenten werden mit TypeScript und Vue 3 Composition API implementiert:

```vue
<script setup lang="ts">
/**
 * Bibelnavigationskomponente
 * @displayName BibleNavigation
 * @description Ermöglicht die Navigation durch die Bibel
 */
import { ref } from 'vue'
import { useBibleNavigationStore } from '@/Stores/BibleNavigationStore'

/**
 * Die aktuelle Buch-ID
 * @type {string}
 */
const props = defineProps<{
  bookId: string
}>()

const bibleStore = useBibleNavigationStore()

// Kapitel laden
const loadChapter = (chapter: number) => {
  bibleStore.loadChapter(props.bookId, chapter)
}
</script>

<template>
  <div class="bible-navigation" role="navigation" aria-label="Bibelnavigation">
    <!-- Navigationsinhalt -->
  </div>
</template>
```

## Barrierefreiheit

Alle Komponenten sind mit Fokus auf Barrierefreiheit entwickelt:

- Semantisch korrektes HTML
- ARIA-Attribute für bessere Zugänglichkeit
- Tastaturnavigation
- Kontrastreiche Farbschemata
