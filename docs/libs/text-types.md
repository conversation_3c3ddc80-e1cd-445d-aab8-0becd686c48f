# Text Types

This module contains types related to text functionality.

## paragraph

### Paragraph

```typescript
export interface Paragraph {
    /** The paragraph ID */
    id: number;
    /** The paragraph content */
    content: string;
    /** The paragraph style */
    style: Style;
    /** The paragraph metadata */
    metadata: {
        /** The paragraph type */
        type: string;
        /** The paragraph level */
        level: number;
        /** The paragraph alignment */
        alignment: 'left' | 'center' | 'right' | 'justify';
    }
```

## settings

### TextSettings

```typescript
export interface TextSettings {
    fontSize: FontSize | string;
    lineSpacing: LineSpacing | string;
    showVerseNumbers: boolean;
    flowText: boolean;
    showFootnotes: boolean;
    focusedMode: boolean;
    showChapterHeadings: boolean;
    marginSize: MarginSize;
    showChapterNumbers: boolean;
    showBookNames: boolean;
    themeMode: ThemeMode;
    colorTheme: ColorTheme;
}
```

### MarginSize

```typescript
export type MarginSize = 'margin-wide' | 'margin-normal' | 'margin-narrow';
```

### FontSize

```typescript
export type FontSize =
    | 'xs'
    | 'sm'
    | 'base'
    | 'lg'
    | 'xl'
    | '2xl'
    | '3xl'
    | '4xl';
```

### LineSpacing

```typescript
export type LineSpacing = 'normal' | 'relaxed' | 'loose';
```

### ColorTheme

```typescript
export type ColorTheme = 'default' | 'papyrus' | 'gray' | 'high-contrast';
```

### ThemeMode

```typescript
export type ThemeMode = 'light' | 'dark' | 'system';
```

## style

### Style

```typescript
export interface Style {
    /** The style ID */
    id: number;
    /** The style name */
    name: string;
    /** The style properties */
    properties: {
        /** The font family */
        fontFamily: string;
        /** The font size */
        fontSize: number;
        /** The font weight */
        fontWeight: number;
        /** The font style */
        fontStyle: 'normal' | 'italic';
        /** The text decoration */
        textDecoration: string;
        /** The text color */
        color: string;
        /** The background color */
        backgroundColor: string;
        /** The line height */
        lineHeight: number;
        /** The letter spacing */
        letterSpacing: number;
        /** The text transform */
        textTransform: 'none' | 'uppercase' | 'lowercase' | 'capitalize';
        /** The text align */
        textAlign: 'left' | 'center' | 'right' | 'justify';
        /** The margin */
        margin: {
            /** The top margin */
            top: number;
            /** The right margin */
            right: number;
            /** The bottom margin */
            bottom: number;
            /** The left margin */
            left: number;
        }
```

## text

### Text

```typescript
export interface Text {
    /** The text ID */
    id: number;
    /** The text content */
    content: string;
    /** The text paragraphs */
    paragraphs: Paragraph[];
    /** The text styles */
    styles: Style[];
    /** The text metadata */
    metadata: {
        /** The text language */
        language: string;
        /** The text version */
        version: string;
        /** The text copyright */
        copyright: string;
    }
```

