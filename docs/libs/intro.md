---
id: intro
title: Gemeinsame Bibliotheken
sidebar_position: 1
---

# Gemeinsame Bibliotheken

Diese Dokumentation beschreibt die gemeinsam genutzten Bibliotheken (libs) der ESB Online Plattform. Diese Bibliotheken stellen wiederverwendbare Funktionen, Typen und Konstanten bereit, die von verschiedenen Anwendungen innerhalb des Monorepos genutzt werden.

## Typsystem

Das Typsystem der Kernbibliothek wurde für bessere Tree-Shakeability optimiert:

1. Typen sind in einer modularen Struktur mit expliziten Named Exports organisiert
2. Jede Typenkategorie (Bible, Common, Display, Search, Text) wird aus ihrem eigenen Modul exportiert
3. Enums werden separat von Typen exportiert

### Wichtige Typen

Das Typsystem umfasst folgende Hauptkategorien:

#### Bible

Typen für Bibelstrukturen:

```typescript
// Beispiel für Bible-Typen
export interface BaseBook {
  id: string;
  name: string;
  testament: Testament;
  category: BookCategory;
  originalLanguage: OriginalLanguage;
}

export interface Book extends BaseBook {
  chapters: number[];
}

export interface Chapter {
  id: string;
  bookId: string;
  number: number;
  verses: Verse[];
}

export interface Verse {
  id: string;
  chapterId: string;
  number: number;
  text: string;
  words: Word[];
  has_text_variant: boolean;
}
```

#### Display

Typen für die Anzeige und Navigation:

```typescript
export interface NavigationState {
  bookId: string;
  chapterNumber: number;
  verseNumber?: number;
  verseReference?: string;
}

export interface ChapterWindow {
  id: string;
  bookId: string;
  chapterNumber: number;
  content: string;
}
```

### Wichtige Enums

```typescript
export enum Testament {
  OT = 'OT',
  NT = 'NT',
}

export enum BookCategory {
  LAW = 'LAW',
  HISTORY = 'HISTORY',
  WISDOM = 'WISDOM',
  PROPHETS_MAJOR = 'PROPHETS_MAJOR',
  PROPHETS_MINOR = 'PROPHETS_MINOR',
  GOSPELS = 'GOSPELS',
  ACTS = 'ACTS',
  PAULINE = 'PAULINE',
  GENERAL = 'GENERAL',
  REVELATION = 'REVELATION',
}

export enum OriginalLanguage {
  HEBREW = 'HEBREW',
  GREEK = 'GREEK',
  ARAMAIC = 'ARAMAIC',
  MIXED = 'MIXED',
}
```

## Varianten-Handler

Der Bibeltextparser verarbeitet Textvarianten mit der VariantHandler-Klasse:

1. In USX XML-Format werden Varianten mit `<ms>`-Tags markiert:
   - Start: `<ms sid="variant_id" type="va" />`
   - Ende: `<ms eid="variant_id" />`

2. Der VariantHandler:
   - Verarbeitet diese Milestone-Elemente, um Variantenabschnitte zu verfolgen
   - Generiert eine eindeutige ID für jede Variante: `var_` + md5-Hash von sid + Vers-ID
   - Markiert Verse mit Varianten durch Setzen von `has_text_variant = true`
   - Unterstützt verschachtelte Varianten mit einem Stack-basierten Ansatz

Diese Struktur stellt sicher, dass Bundler ungenutzte Typen und Enums ordnungsgemäß tree-shaken können.
