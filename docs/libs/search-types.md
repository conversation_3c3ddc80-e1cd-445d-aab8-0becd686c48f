# Search Types

This module contains types related to search functionality.

## results

### SearchResults

```typescript
export interface SearchResults {
    /** The search results */
    results: SearchResult[];
    /** The total number of results */
    total: number;
    /** The current page */
    page: number;
    /** The number of results per page */
    limit: number;
    /** The total number of pages */
    totalPages: number;
}
```

## search

### SearchMatch

```typescript
export interface SearchMatch {
    /** The matched text */
    text: string;
    /** Start position of the match */
    start: number;
    /** End position of the match */
    end: number;
}
```

### SearchResult

```typescript
export interface SearchResult {
    /** The book containing the search result */
    book: BookView;
    /** The chapter number */
    chapter: number;
    /** The verse number */
    verse: number;
    /** The text content */
    text: string;
    /** The matched portions */
    matches: SearchMatch[];
}
```

### SearchResultMetadata

```typescript
export interface SearchResultMetadata {
    /** Associated tags */
    tags: string[];
    /** End verse number if applicable */
    end_verse: number | null;
    /** Start verse number if applicable */
    start_verse: number | null;
    /** Whether contains Old Testament quote */
    has_ot_quote: boolean;
    /** Whether contains text variant */
    has_text_variant: boolean;
    /** Book name if applicable */
    book_name: string | null;
    /** Chapter number if applicable */
    chapter_number: number | null;
    /** Verse number if applicable */
    verse_number: number | null;
}
```

### GeneralSearchResult

```typescript
export interface GeneralSearchResult {
    /** Unique identifier */
    id: number;
    /** Type of the result */
    type: string;
    /** Title of the result */
    title: string;
    /** Content of the result */
    content: string;
    /** URL to the result */
    url: string;
    /** Associated metadata */
    metadata: SearchResultMetadata;
}
```

### SearchResultsState

```typescript
export interface SearchResultsState {
    /** Array of search results */
    data: GeneralSearchResult[];
    /** Metadata about the search results */
    metadata: {
        /** Total number of results */
        total: number;
        /** Results per page */
        per_page: number;
        /** Current page number */
        current_page: number;
        /** Last page number */
        last_page: number;
    }
```

### SearchRequest

```typescript
export interface SearchRequest {
    /** Search query string */
    query: string;
    /** Optional book filter */
    book?: string;
    /** Optional chapter filter */
    chapter?: number;
    /** Type of search */
    type?: 'text' | 'reference';
}
```

### SearchResponse

```typescript
export interface SearchResponse {
    /** Array of search results */
    results: SearchResult[];
    /** Total number of results */
    total: number;
    /** Current page number */
    page: number;
    /** Results per page */
    perPage: number;
}
```

### SearchResultType

```typescript
export interface SearchResultType {
    /** Unique identifier */
    id: number;
    /** Name of the result type */
    name: string;
}
```

