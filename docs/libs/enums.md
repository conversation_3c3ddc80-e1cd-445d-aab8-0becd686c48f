# Enums

This module contains all enumerations used throughout the ESB Online application.

## Testament

```typescript
export enum Testament {
    OT = 'ot',
    NT = 'nt',
}
```

## BookCategory

```typescript
export enum BookCategory {
    LAW = 'law',
    HISTORY = 'history',
    WISDOM = 'wisdom',
    PROPHECY = 'prophecy',
    GOSPEL = 'gospel',
    EPISTLE = 'epistle',
    APOCALYPTIC = 'apocalypse',
}
```

## OriginalLanguage

```typescript
export enum OriginalLanguage {
    HEBREW = 'hebrew',
    GREEK = 'greek',
    ARAMAIC = 'aramaic',
    MIXED = 'greek-aramaic',
}
```

