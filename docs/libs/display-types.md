# Display Types

This module contains types related to display functionality.

## navigation

### DisplayReference

```typescript
export interface DisplayReference {
    book: Book;
    chapter: number;
    frontmatter: boolean;
    verseStart: number | null;
    verseEnd: number | null;
    verseRanges?: { start: number; end: number }
```

### NavigationState

```typescript
export interface NavigationState {
    currentBook: BaseBook;
    previousBook: BaseBook | null;
    nextBook: BaseBook | null;
    url?: string;
    verseReference?: string;
    scrollToVerse?: DisplayReference;
    scrollToChapter?: DisplayReference;
    scrollToFrontmatter?: DisplayReference;
    scrollToVerseRange?: DisplayReference;
}
```

### NavigationSection

```typescript
export interface NavigationSection {
    name: string;
    books: BookView[];
}
```

## responses

### LoadingConfig

```typescript
export interface LoadingConfig {
    nextChapter: {
        book: BaseBook;
        number: number;
    }
```

### DisplayResponse

```typescript
export interface DisplayResponse {
    reference: DisplayReference;
    sections: (FrontmatterSection | ChapterSection)[];
    navigation: NavigationState;
    loadingConfig?: LoadingConfig;
}
```

### BibleReference

```typescript
export interface BibleReference {
    book: string;
    chapter?: number;
    verseStart?: number;
    verseEnd?: number;
}
```

### ParsedReference

```typescript
export interface ParsedReference {
    type: 'book' | 'chapter' | 'verse' | 'verse-range';
    reference: BibleReference;
}
```

### SectionResponse

```typescript
export interface SectionResponse {
    sections: {
        previous: FrontmatterSection | ChapterSection | null;
        current: FrontmatterSection | ChapterSection;
        next: FrontmatterSection | ChapterSection | null;
        nextNext: FrontmatterSection | ChapterSection | null;
    }
```

### SectionRequest

```typescript
export interface SectionRequest {
    book: string;
    type: 'frontmatter' | 'chapter';
    chapter?: number;
    loadDirection: 'previous' | 'next' | 'nextNext';
}
```

### LoadMoreOptions

```typescript
export interface LoadMoreOptions {
    preserveState: boolean;
    preserveScroll: boolean;
    only: string[];
}
```

### LoadTarget

```typescript
export interface LoadTarget {
    book: string;
    chapter: number;
}
```

### BibleReaderState

```typescript
export interface BibleReaderState {
    chapters: Map<string, ChapterSection>;
    books: Map<string, Book>;
    currentViewportChapterId: string | null;
    isLoadingNext: boolean;
    isLoadingPrev: boolean;
    maxLoadedChapters: number;
    bookOrder: string[];
}
```

### ScrollDirection

```typescript
export type ScrollDirection = 'forward' | 'backward';
```

## section

### BaseSection

```typescript
export interface BaseSection {
    id: string;
    type:
        | 'frontmatter'
        | 'chapter-current'
        | 'chapter-previous'
        | 'chapter-next'
        | 'chapter-next-next';
    book: BaseBook;
}
```

### FrontmatterSection

```typescript
export interface FrontmatterSection extends BaseSection {
    type: 'frontmatter';
    number: number;
    id: string;
    book: Book;
}
```

### ChapterSection

```typescript
export interface ChapterSection extends BaseSection {
    type:
        | 'chapter-current'
        | 'chapter-previous'
        | 'chapter-next'
        | 'chapter-next-next';
    id: string;
    book: Book; // Book for chapter 1, BaseBook for others
    number: number;
    verses: Verse[];
}
```

### BookSection

```typescript
export interface BookSection {
    name: string;
    books: Book[];
}
```

### Section

```typescript
export type Section = FrontmatterSection | ChapterSection;
```

## window

### Window

```typescript
export interface Window {
    /** The window ID */
    id: number;
    /** The window title */
    title: string;
    /** The window content */
    content: string;
    /** The window sections */
    sections: Section[];
    /** The window navigation */
    navigation: NavigationState;
    /** The window metadata */
    metadata: {
        /** The window type */
        type: string;
        /** The window mode */
        mode: 'single' | 'parallel' | 'split';
        /** The window layout */
        layout: 'default' | 'compact' | 'wide';
        /** The window position */
        position: {
            /** The x position */
            x: number;
            /** The y position */
            y: number;
            /** The width */
            width: number;
            /** The height */
            height: number;
        }
```

### ChapterWindow

```typescript
export interface ChapterWindow {
    /** The current chapter section */
    current: ChapterSection;
    /** The previous chapter section, if any */
    previous?: ChapterSection;
    /** The next chapter section, if any */
    next?: ChapterSection;
    /** The next-next chapter section, if any */
    nextNext?: ChapterSection;
    /** The frontmatter section, if any */
    frontmatter?: FrontmatterSection;
}
```

### FrontmatterWindow

```typescript
export interface FrontmatterWindow extends Window {
    /** The frontmatter sections */
    sections: FrontmatterSection[];
}
```

