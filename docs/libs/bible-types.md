# Bible Types

This module contains types related to bible functionality.

## book

### BaseBook

```typescript
export interface BaseBook {
    id: number;
    order: number;
    slug: string;
    name: string;
    number: number;
    chapterCount: number;
    category: BookCategory; // Fundamental property of any Bible book
    hasContent: boolean;
}
```

### Book

```typescript
export interface Book extends BaseBook {
    abbreviation: string;
    testament: Testament;
    testamentLabel: string;
    searchNames: string; // Changed from string[] to string to match web app definition
    chapters?: Chapter[];
    readingProgress?: ReadingProgress;
    metadata?: BookMetadata;
}
```

### BookView

```typescript
export interface BookView extends BaseBook {
    shortName: string;
    chapters: Chapter[] | number[]; // Allow both types to be compatible
    hasContent: boolean;
}
```

### SimpleBook

```typescript
export type SimpleBook = BaseBook;
```

## content

### Word

```typescript
export interface Word {
    text: string;
    isOtQuote: boolean;
    otQuoteGroupId: string | null;
    isEmphasized: boolean;
    isDivineName: boolean;
    isFootnote: boolean;
    isVariant: boolean;
    variantType: 'addition' | 'omission' | 'alternative' | null;
    variantGroupId: string | null;
    footnoteGroupId: string | null;
    footnote: Footnote | null;
}
```

### Verse

```typescript
export interface Verse {
    /** The verse number */
    number: number;
    /** Array of words in the verse */
    words: Word[];
    /** ID of the paragraph group this verse belongs to */
    paragraphGroupId?: string;
    /** Style ID for the paragraph */
    paragraphStyleId?: string | null;
    /** Whether this verse starts a new pericope */
    isPericopeStart?: boolean;
    /** Whether this verse contains an Old Testament quote */
    has_ot_quote?: boolean;
    /** Whether this verse contains a text variant */
    has_text_variant?: boolean;
}
```

### Chapter

```typescript
export interface Chapter {
    bookId: number;
    number: number;
    verses: Verse[];
    book?: Book | SimpleBook;
}
```

### WordGroup

```typescript
export type WordGroup = {
    words: Word[];
```

## footnotes

### Footnote

```typescript
export interface Footnote {
    number: number;
    text: string;
    groupId: string;
    type: 'text' | 'cross-reference' | 'variant' | 'ot-quote';
    content: string;
    references?: string[];
    variants?: string[];
    otQuotes?: string[];
}
```

### FootnoteContentElement

```typescript
export interface FootnoteContentElement {
    type: 'text' | 'caller' | 'styled' | string;
    content: string;
    style?: string;
}
```

### FootnoteContentStructure

```typescript
export interface FootnoteContentStructure {
    elements: FootnoteContentElement[];
    metadata: {
        has_references: boolean;
        total_elements: number;
    }
```

### Footnote

```typescript
export interface Footnote {
    id: number;
    searchableText: string;
    contentStructure: FootnoteContentStructure;
    isReference: boolean;
    referencedWord: string | null;
    verseId: number;
    hasItalics: boolean;
    position: number;
}
```

## metadata

### ReadingProgress

```typescript
export interface ReadingProgress {
    /** The book ID */
    bookId: number;
    /** The last read chapter number */
    lastReadChapter: number;
    /** The last read verse number */
    lastReadVerse: number;
    /** The last read timestamp */
    lastReadAt: string;
    /** The reading progress percentage */
    progress: number;
}
```

### BookMetadata

```typescript
export interface BookMetadata {
    location: string | null;
    authors: string[];
    writtenYear: number | null;
    theme: string | null;
    keyPeople: string | null;
    keyWords: string | null;
    keyTeachings: string | null;
    keyVerses: string | null;
    covenants: string | null;
    attributesOfGod: string | null;
    historicalPeriod: string | null;
    originalLanguage: OriginalLanguage;
}
```

