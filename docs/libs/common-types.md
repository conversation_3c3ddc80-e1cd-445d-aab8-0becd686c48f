# Common Types

This module contains types related to common functionality.

## enums

### EnumOption

```typescript
export interface EnumOption {
    value: string;
    label: string;
}
```

## filters

### FilterType

```typescript
export type FilterType = 'testament' | 'category' | 'language';
```

## user

### User

```typescript
export interface User {
    id: number; // Unique identifier for the user
    name: string; // Name of the user
    email: string; // Email of the user
    emailVerifiedAt?: string; // Optional verification timestamp
}
```

