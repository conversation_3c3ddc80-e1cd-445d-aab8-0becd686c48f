# ESB Online Library Types

This section contains comprehensive documentation for all TypeScript types and enums used in the ESB Online libraries.

## Type Categories

The core library's type system has been optimized for better tree-shakeability with the following structure:

1. **Bible Types**: Types related to Bible content, structure, and metadata
   - [View all Bible Types](bible-types.md)
   - Key types: BaseBook, Book, BookView, Chapter, Verse, Word, WordGroup, Footnote

2. **Common Types**: Types used across multiple modules
   - [View all Common Types](common-types.md)
   - Key types: User, FilterType

3. **Display Types**: Types related to UI and display logic
   - [View all Display Types](display-types.md)
   - Key types: NavigationState, Section, Window, ChapterWindow, FrontmatterWindow

4. **Search Types**: Types related to search functionality
   - [View all Search Types](search-types.md)
   - Key types: SearchQuery, SearchResult, SearchResults

5. **Text Types**: Types related to text formatting and structure
   - [View all Text Types](text-types.md)
   - Key types: Style, Paragraph, Text

## Enums

All enumerations used in the application are documented in the [Enums](enums.md) section.

Key enums include:
- Testament (OT, NT)
- BookCategory (LAW, HISTORY, WISDOM, etc.)
- OriginalLanguage (HEBREW, GREEK, ARAMAIC, MIXED)

## Type Dependencies

The type system is designed with clear dependencies between modules to ensure proper tree-shaking:

- Bible types may depend on Common types
- Display types may depend on Bible and Common types
- Search types may depend on Bible, Common, and Text types
- Text types may depend on Common types

This structure ensures that bundlers can properly tree-shake unused types and enums.
