openapi: 3.0.3
info:
  title: 'EsraBibel API Documentation'
  description: ''
  version: 1.0.0
servers:
  -
    url: 'http://esra-bibel.local'
tags:
  -
    name: Endpoints
    description: ''
paths:
  /api/upload:
    post:
      summary: ''
      operationId: postApiUpload
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
                  description: 'Must be a file.'
                  nullable: false
              required:
                - file
      security: []
  /api/user:
    get:
      summary: ''
      operationId: getApiUser
      description: ''
      parameters: []
      responses:
        404:
          description: ''
          content:
            text/plain:
              schema:
                type: string
                example: "<!DOCTYPE html>\n<html lang=\"de\">\n\n<head>\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\n\n    <title inertia></title>\n\n    <!-- Fonts -->\n    <!-- Preconnect to font domains -->\n    <link rel=\"preconnect\" href=\"https://fonts.bunny.net\" crossorigin>\n    <link rel=\"preconnect\" href=\"https://use.typekit.net\" as=\"font\" type=\"font/woff2\" crossorigin>\n\n    <!-- Preload critical fonts -->\n    <link rel=\"preload\" href=\"/fonts/ThanatosText-Book.woff2\" as=\"font\" type=\"font/woff2\" crossorigin>\n\n    <!-- Load fonts -->\n    <link href=\"https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap\" rel=\"stylesheet\" />\n    <link rel=\"stylesheet\" href=\"https://use.typekit.net/kzb8yhl.css\" media=\"print\" onload=\"this.media='all'\">\n\n    <!-- Fallback for typekit fonts -->\n    <noscript>\n        <link rel=\"stylesheet\" href=\"https://use.typekit.net/kzb8yhl.css\">\n    </noscript>\n\n    <!-- Local font definition -->\n    <style>\n        @font-face {\n            font-family: 'ThanatosText';\n            src: url('/fonts/ThanatosText-Book.woff2') format('woff2');\n            font-weight: normal;\n            font-style: normal;\n            font-display: swap;\n        }\n    </style>\n\n    <!-- Scripts -->\n    <script type=\"text/javascript\">const Ziggy={\"url\":\"http:\\/\\/esra-bibel.local\",\"port\":null,\"defaults\":{},\"routes\":{\"search.index\":{\"uri\":\"search\",\"methods\":[\"GET\",\"HEAD\"]},\"search.query\":{\"uri\":\"search\\/{query}\",\"methods\":[\"GET\",\"HEAD\"],\"wheres\":{\"query\":\"[^\\/]+\"},\"parameters\":[\"query\"]},\"search.paged\":{\"uri\":\"search\\/{query}\\/{page?}\",\"methods\":[\"GET\",\"HEAD\"],\"wheres\":{\"query\":\"[^\\/]+\",\"page\":\"[0-9]+\"},\"parameters\":[\"query\",\"page\"]},\"search.settings\":{\"uri\":\"search\\/settings\",\"methods\":[\"POST\"]},\"dashboard\":{\"uri\":\"dashboard\",\"methods\":[\"GET\",\"HEAD\"]},\"bible.import\":{\"uri\":\"import-bible\",\"methods\":[\"GET\",\"HEAD\"]},\"bible.import.store\":{\"uri\":\"import-bible\",\"methods\":[\"POST\"]},\"file.upload\":{\"uri\":\"api\\/upload\",\"methods\":[\"POST\"]},\"profile.edit\":{\"uri\":\"profile\",\"methods\":[\"GET\",\"HEAD\"]},\"profile.update\":{\"uri\":\"profile\",\"methods\":[\"PATCH\"]},\"profile.destroy\":{\"uri\":\"profile\",\"methods\":[\"DELETE\"]},\"books.show\":{\"uri\":\"{reference}\",\"methods\":[\"GET\",\"HEAD\"],\"wheres\":{\"reference\":\"[\\\\w\\\\d\\\\.,\\\\-\\\\+]+\"},\"parameters\":[\"reference\"]},\"register\":{\"uri\":\"register\",\"methods\":[\"GET\",\"HEAD\"]},\"login\":{\"uri\":\"login\",\"methods\":[\"GET\",\"HEAD\"]},\"password.request\":{\"uri\":\"forgot-password\",\"methods\":[\"GET\",\"HEAD\"]},\"password.email\":{\"uri\":\"forgot-password\",\"methods\":[\"POST\"]},\"password.reset\":{\"uri\":\"reset-password\\/{token}\",\"methods\":[\"GET\",\"HEAD\"],\"parameters\":[\"token\"]},\"password.store\":{\"uri\":\"reset-password\",\"methods\":[\"POST\"]},\"verification.notice\":{\"uri\":\"verify-email\",\"methods\":[\"GET\",\"HEAD\"]},\"verification.verify\":{\"uri\":\"verify-email\\/{id}\\/{hash}\",\"methods\":[\"GET\",\"HEAD\"],\"parameters\":[\"id\",\"hash\"]},\"verification.send\":{\"uri\":\"email\\/verification-notification\",\"methods\":[\"POST\"]},\"password.confirm\":{\"uri\":\"confirm-password\",\"methods\":[\"GET\",\"HEAD\"]},\"password.update\":{\"uri\":\"password\",\"methods\":[\"PUT\"]},\"logout\":{\"uri\":\"logout\",\"methods\":[\"POST\"]},\"api.chapters.fetch\":{\"uri\":\"api\\/chapters\\/fetch\",\"methods\":[\"GET\",\"HEAD\"]},\"api.books\":{\"uri\":\"api\\/books\",\"methods\":[\"GET\",\"HEAD\"]},\"api.books.content-status\":{\"uri\":\"api\\/books\\/content-status\",\"methods\":[\"GET\",\"HEAD\"]},\"api.books.show\":{\"uri\":\"api\\/books\\/{slug}\",\"methods\":[\"GET\",\"HEAD\"],\"parameters\":[\"slug\"]},\"api.chapters.adjacent\":{\"uri\":\"api\\/chapters\\/{reference}\\/adjacent\",\"methods\":[\"GET\",\"HEAD\"],\"wheres\":{\"reference\":\"[\\\\w\\\\d,\\\\-]+\"},\"parameters\":[\"reference\"]},\"api.search.books\":{\"uri\":\"api\\/search\\/books\",\"methods\":[\"GET\",\"HEAD\"]},\"api.search\":{\"uri\":\"api\\/search\",\"methods\":[\"GET\",\"HEAD\"]},\"api.bible.text\":{\"uri\":\"api\\/bible\\/{reference}\",\"methods\":[\"GET\",\"HEAD\"],\"wheres\":{\"reference\":\"[\\\\w\\\\d,\\\\-]+\"},\"parameters\":[\"reference\"]},\"storage.local\":{\"uri\":\"storage\\/{path}\",\"methods\":[\"GET\",\"HEAD\"],\"wheres\":{\"path\":\".*\"},\"parameters\":[\"path\"]}}};!function(t,r){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=r():\"function\"==typeof define&&define.amd?define(r):(t||self).route=r()}(this,function(){function t(t,r){for(var n=0;n<r.length;n++){var e=r[n];e.enumerable=e.enumerable||!1,e.configurable=!0,\"value\"in e&&(e.writable=!0),Object.defineProperty(t,u(e.key),e)}}function r(r,n,e){return n&&t(r.prototype,n),e&&t(r,e),Object.defineProperty(r,\"prototype\",{writable:!1}),r}function n(){return n=Object.assign?Object.assign.bind():function(t){for(var r=1;r<arguments.length;r++){var n=arguments[r];for(var e in n)({}).hasOwnProperty.call(n,e)&&(t[e]=n[e])}return t},n.apply(null,arguments)}function e(t){return e=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},e(t)}function o(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(o=function(){return!!t})()}function i(t,r){return i=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,r){return t.__proto__=r,t},i(t,r)}function u(t){var r=function(t){if(\"object\"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,\"string\");if(\"object\"!=typeof n)return n;throw new TypeError(\"@@toPrimitive must return a primitive value.\")}return String(t)}(t);return\"symbol\"==typeof r?r:r+\"\"}function f(t){var r=\"function\"==typeof Map?new Map:void 0;return f=function(t){if(null===t||!function(t){try{return-1!==Function.toString.call(t).indexOf(\"[native code]\")}catch(r){return\"function\"==typeof t}}(t))return t;if(\"function\"!=typeof t)throw new TypeError(\"Super expression must either be null or a function\");if(void 0!==r){if(r.has(t))return r.get(t);r.set(t,n)}function n(){return function(t,r,n){if(o())return Reflect.construct.apply(null,arguments);var e=[null];e.push.apply(e,r);var u=new(t.bind.apply(t,e));return n&&i(u,n.prototype),u}(t,arguments,e(this).constructor)}return n.prototype=Object.create(t.prototype,{constructor:{value:n,enumerable:!1,writable:!0,configurable:!0}}),i(n,t)},f(t)}var a=String.prototype.replace,c=/%20/g,l=\"RFC3986\",s={default:l,formatters:{RFC1738:function(t){return a.call(t,c,\"+\")},RFC3986:function(t){return String(t)}},RFC1738:\"RFC1738\",RFC3986:l},v=Object.prototype.hasOwnProperty,p=Array.isArray,y=function(){for(var t=[],r=0;r<256;++r)t.push(\"%\"+((r<16?\"0\":\"\")+r.toString(16)).toUpperCase());return t}(),d=function(t,r){for(var n=r&&r.plainObjects?Object.create(null):{},e=0;e<t.length;++e)void 0!==t[e]&&(n[e]=t[e]);return n},b={arrayToObject:d,assign:function(t,r){return Object.keys(r).reduce(function(t,n){return t[n]=r[n],t},t)},combine:function(t,r){return[].concat(t,r)},compact:function(t){for(var r=[{obj:{o:t},prop:\"o\"}],n=[],e=0;e<r.length;++e)for(var o=r[e],i=o.obj[o.prop],u=Object.keys(i),f=0;f<u.length;++f){var a=u[f],c=i[a];\"object\"==typeof c&&null!==c&&-1===n.indexOf(c)&&(r.push({obj:i,prop:a}),n.push(c))}return function(t){for(;t.length>1;){var r=t.pop(),n=r.obj[r.prop];if(p(n)){for(var e=[],o=0;o<n.length;++o)void 0!==n[o]&&e.push(n[o]);r.obj[r.prop]=e}}}(r),t},decode:function(t,r,n){var e=t.replace(/\\+/g,\" \");if(\"iso-8859-1\"===n)return e.replace(/%[0-9a-f]{2}/gi,unescape);try{return decodeURIComponent(e)}catch(t){return e}},encode:function(t,r,n,e,o){if(0===t.length)return t;var i=t;if(\"symbol\"==typeof t?i=Symbol.prototype.toString.call(t):\"string\"!=typeof t&&(i=String(t)),\"iso-8859-1\"===n)return escape(i).replace(/%u[0-9a-f]{4}/gi,function(t){return\"%26%23\"+parseInt(t.slice(2),16)+\"%3B\"});for(var u=\"\",f=0;f<i.length;++f){var a=i.charCodeAt(f);45===a||46===a||95===a||126===a||a>=48&&a<=57||a>=65&&a<=90||a>=97&&a<=122||o===s.RFC1738&&(40===a||41===a)?u+=i.charAt(f):a<128?u+=y[a]:a<2048?u+=y[192|a>>6]+y[128|63&a]:a<55296||a>=57344?u+=y[224|a>>12]+y[128|a>>6&63]+y[128|63&a]:(a=65536+((1023&a)<<10|1023&i.charCodeAt(f+=1)),u+=y[240|a>>18]+y[128|a>>12&63]+y[128|a>>6&63]+y[128|63&a])}return u},isBuffer:function(t){return!(!t||\"object\"!=typeof t||!(t.constructor&&t.constructor.isBuffer&&t.constructor.isBuffer(t)))},isRegExp:function(t){return\"[object RegExp]\"===Object.prototype.toString.call(t)},maybeMap:function(t,r){if(p(t)){for(var n=[],e=0;e<t.length;e+=1)n.push(r(t[e]));return n}return r(t)},merge:function t(r,n,e){if(!n)return r;if(\"object\"!=typeof n){if(p(r))r.push(n);else{if(!r||\"object\"!=typeof r)return[r,n];(e&&(e.plainObjects||e.allowPrototypes)||!v.call(Object.prototype,n))&&(r[n]=!0)}return r}if(!r||\"object\"!=typeof r)return[r].concat(n);var o=r;return p(r)&&!p(n)&&(o=d(r,e)),p(r)&&p(n)?(n.forEach(function(n,o){if(v.call(r,o)){var i=r[o];i&&\"object\"==typeof i&&n&&\"object\"==typeof n?r[o]=t(i,n,e):r.push(n)}else r[o]=n}),r):Object.keys(n).reduce(function(r,o){var i=n[o];return r[o]=v.call(r,o)?t(r[o],i,e):i,r},o)}},h=Object.prototype.hasOwnProperty,g={brackets:function(t){return t+\"[]\"},comma:\"comma\",indices:function(t,r){return t+\"[\"+r+\"]\"},repeat:function(t){return t}},m=Array.isArray,j=String.prototype.split,w=Array.prototype.push,O=function(t,r){w.apply(t,m(r)?r:[r])},E=Date.prototype.toISOString,R=s.default,S={addQueryPrefix:!1,allowDots:!1,charset:\"utf-8\",charsetSentinel:!1,delimiter:\"&\",encode:!0,encoder:b.encode,encodeValuesOnly:!1,format:R,formatter:s.formatters[R],indices:!1,serializeDate:function(t){return E.call(t)},skipNulls:!1,strictNullHandling:!1},k=function t(r,n,e,o,i,u,f,a,c,l,s,v,p,y){var d,h=r;if(\"function\"==typeof f?h=f(n,h):h instanceof Date?h=l(h):\"comma\"===e&&m(h)&&(h=b.maybeMap(h,function(t){return t instanceof Date?l(t):t})),null===h){if(o)return u&&!p?u(n,S.encoder,y,\"key\",s):n;h=\"\"}if(\"string\"==typeof(d=h)||\"number\"==typeof d||\"boolean\"==typeof d||\"symbol\"==typeof d||\"bigint\"==typeof d||b.isBuffer(h)){if(u){var g=p?n:u(n,S.encoder,y,\"key\",s);if(\"comma\"===e&&p){for(var w=j.call(String(h),\",\"),E=\"\",R=0;R<w.length;++R)E+=(0===R?\"\":\",\")+v(u(w[R],S.encoder,y,\"value\",s));return[v(g)+\"=\"+E]}return[v(g)+\"=\"+v(u(h,S.encoder,y,\"value\",s))]}return[v(n)+\"=\"+v(String(h))]}var k,T=[];if(void 0===h)return T;if(\"comma\"===e&&m(h))k=[{value:h.length>0?h.join(\",\")||null:void 0}];else if(m(f))k=f;else{var $=Object.keys(h);k=a?$.sort(a):$}for(var x=0;x<k.length;++x){var N=k[x],C=\"object\"==typeof N&&void 0!==N.value?N.value:h[N];if(!i||null!==C){var A=m(h)?\"function\"==typeof e?e(n,N):n:n+(c?\".\"+N:\"[\"+N+\"]\");O(T,t(C,A,e,o,i,u,f,a,c,l,s,v,p,y))}}return T},T=Object.prototype.hasOwnProperty,$=Array.isArray,x={allowDots:!1,allowPrototypes:!1,arrayLimit:20,charset:\"utf-8\",charsetSentinel:!1,comma:!1,decoder:b.decode,delimiter:\"&\",depth:5,ignoreQueryPrefix:!1,interpretNumericEntities:!1,parameterLimit:1e3,parseArrays:!0,plainObjects:!1,strictNullHandling:!1},N=function(t){return t.replace(/&#(\\d+);/g,function(t,r){return String.fromCharCode(parseInt(r,10))})},C=function(t,r){return t&&\"string\"==typeof t&&r.comma&&t.indexOf(\",\")>-1?t.split(\",\"):t},A=function(t,r,n,e){if(t){var o=n.allowDots?t.replace(/\\.([^.[]+)/g,\"[$1]\"):t,i=/(\\[[^[\\]]*])/g,u=n.depth>0&&/(\\[[^[\\]]*])/.exec(o),f=u?o.slice(0,u.index):o,a=[];if(f){if(!n.plainObjects&&T.call(Object.prototype,f)&&!n.allowPrototypes)return;a.push(f)}for(var c=0;n.depth>0&&null!==(u=i.exec(o))&&c<n.depth;){if(c+=1,!n.plainObjects&&T.call(Object.prototype,u[1].slice(1,-1))&&!n.allowPrototypes)return;a.push(u[1])}return u&&a.push(\"[\"+o.slice(u.index)+\"]\"),function(t,r,n,e){for(var o=e?r:C(r,n),i=t.length-1;i>=0;--i){var u,f=t[i];if(\"[]\"===f&&n.parseArrays)u=[].concat(o);else{u=n.plainObjects?Object.create(null):{};var a=\"[\"===f.charAt(0)&&\"]\"===f.charAt(f.length-1)?f.slice(1,-1):f,c=parseInt(a,10);n.parseArrays||\"\"!==a?!isNaN(c)&&f!==a&&String(c)===a&&c>=0&&n.parseArrays&&c<=n.arrayLimit?(u=[])[c]=o:\"__proto__\"!==a&&(u[a]=o):u={0:o}}o=u}return o}(a,r,n,e)}},D=function(t,r){var n=function(t){if(!t)return x;if(null!=t.decoder&&\"function\"!=typeof t.decoder)throw new TypeError(\"Decoder has to be a function.\");if(void 0!==t.charset&&\"utf-8\"!==t.charset&&\"iso-8859-1\"!==t.charset)throw new TypeError(\"The charset option must be either utf-8, iso-8859-1, or undefined\");return{allowDots:void 0===t.allowDots?x.allowDots:!!t.allowDots,allowPrototypes:\"boolean\"==typeof t.allowPrototypes?t.allowPrototypes:x.allowPrototypes,arrayLimit:\"number\"==typeof t.arrayLimit?t.arrayLimit:x.arrayLimit,charset:void 0===t.charset?x.charset:t.charset,charsetSentinel:\"boolean\"==typeof t.charsetSentinel?t.charsetSentinel:x.charsetSentinel,comma:\"boolean\"==typeof t.comma?t.comma:x.comma,decoder:\"function\"==typeof t.decoder?t.decoder:x.decoder,delimiter:\"string\"==typeof t.delimiter||b.isRegExp(t.delimiter)?t.delimiter:x.delimiter,depth:\"number\"==typeof t.depth||!1===t.depth?+t.depth:x.depth,ignoreQueryPrefix:!0===t.ignoreQueryPrefix,interpretNumericEntities:\"boolean\"==typeof t.interpretNumericEntities?t.interpretNumericEntities:x.interpretNumericEntities,parameterLimit:\"number\"==typeof t.parameterLimit?t.parameterLimit:x.parameterLimit,parseArrays:!1!==t.parseArrays,plainObjects:\"boolean\"==typeof t.plainObjects?t.plainObjects:x.plainObjects,strictNullHandling:\"boolean\"==typeof t.strictNullHandling?t.strictNullHandling:x.strictNullHandling}}(r);if(\"\"===t||null==t)return n.plainObjects?Object.create(null):{};for(var e=\"string\"==typeof t?function(t,r){var n,e={},o=(r.ignoreQueryPrefix?t.replace(/^\\?/,\"\"):t).split(r.delimiter,Infinity===r.parameterLimit?void 0:r.parameterLimit),i=-1,u=r.charset;if(r.charsetSentinel)for(n=0;n<o.length;++n)0===o[n].indexOf(\"utf8=\")&&(\"utf8=%E2%9C%93\"===o[n]?u=\"utf-8\":\"utf8=%26%2310003%3B\"===o[n]&&(u=\"iso-8859-1\"),i=n,n=o.length);for(n=0;n<o.length;++n)if(n!==i){var f,a,c=o[n],l=c.indexOf(\"]=\"),s=-1===l?c.indexOf(\"=\"):l+1;-1===s?(f=r.decoder(c,x.decoder,u,\"key\"),a=r.strictNullHandling?null:\"\"):(f=r.decoder(c.slice(0,s),x.decoder,u,\"key\"),a=b.maybeMap(C(c.slice(s+1),r),function(t){return r.decoder(t,x.decoder,u,\"value\")})),a&&r.interpretNumericEntities&&\"iso-8859-1\"===u&&(a=N(a)),c.indexOf(\"[]=\")>-1&&(a=$(a)?[a]:a),e[f]=T.call(e,f)?b.combine(e[f],a):a}return e}(t,n):t,o=n.plainObjects?Object.create(null):{},i=Object.keys(e),u=0;u<i.length;++u){var f=i[u],a=A(f,e[f],n,\"string\"==typeof t);o=b.merge(o,a,n)}return b.compact(o)},P=/*#__PURE__*/function(){function t(t,r,n){var e,o;this.name=t,this.definition=r,this.bindings=null!=(e=r.bindings)?e:{},this.wheres=null!=(o=r.wheres)?o:{},this.config=n}var n=t.prototype;return n.matchesUrl=function(t){var r,n=this;if(!this.definition.methods.includes(\"GET\"))return!1;var e=this.template.replace(/[.*+$()[\\]]/g,\"\\\\$&\").replace(/(\\/?){([^}?]*)(\\??)}/g,function(t,r,e,o){var i,u=\"(?<\"+e+\">\"+((null==(i=n.wheres[e])?void 0:i.replace(/(^\\^)|(\\$$)/g,\"\"))||\"[^/?]+\")+\")\";return o?\"(\"+r+u+\")?\":\"\"+r+u}).replace(/^\\w+:\\/\\//,\"\"),o=t.replace(/^\\w+:\\/\\//,\"\").split(\"?\"),i=o[0],u=o[1],f=null!=(r=new RegExp(\"^\"+e+\"/?$\").exec(i))?r:new RegExp(\"^\"+e+\"/?$\").exec(decodeURI(i));if(f){for(var a in f.groups)f.groups[a]=\"string\"==typeof f.groups[a]?decodeURIComponent(f.groups[a]):f.groups[a];return{params:f.groups,query:D(u)}}return!1},n.compile=function(t){var r=this;return this.parameterSegments.length?this.template.replace(/{([^}?]+)(\\??)}/g,function(n,e,o){var i,u;if(!o&&[null,void 0].includes(t[e]))throw new Error(\"Ziggy error: '\"+e+\"' parameter is required for route '\"+r.name+\"'.\");if(r.wheres[e]&&!new RegExp(\"^\"+(o?\"(\"+r.wheres[e]+\")?\":r.wheres[e])+\"$\").test(null!=(u=t[e])?u:\"\"))throw new Error(\"Ziggy error: '\"+e+\"' parameter '\"+t[e]+\"' does not match required format '\"+r.wheres[e]+\"' for route '\"+r.name+\"'.\");return encodeURI(null!=(i=t[e])?i:\"\").replace(/%7C/g,\"|\").replace(/%25/g,\"%\").replace(/\\$/g,\"%24\")}).replace(this.config.absolute?/(\\.[^/]+?)(\\/\\/)/:/(^)(\\/\\/)/,\"$1/\").replace(/\\/+$/,\"\"):this.template},r(t,[{key:\"template\",get:function(){var t=(this.origin+\"/\"+this.definition.uri).replace(/\\/+$/,\"\");return\"\"===t?\"/\":t}},{key:\"origin\",get:function(){return this.config.absolute?this.definition.domain?\"\"+this.config.url.match(/^\\w+:\\/\\//)[0]+this.definition.domain+(this.config.port?\":\"+this.config.port:\"\"):this.config.url:\"\"}},{key:\"parameterSegments\",get:function(){var t,r;return null!=(t=null==(r=this.template.match(/{[^}?]+\\??}/g))?void 0:r.map(function(t){return{name:t.replace(/{|\\??}/g,\"\"),required:!/\\?}$/.test(t)}}))?t:[]}}])}(),F=/*#__PURE__*/function(t){function e(r,e,o,i){var u;if(void 0===o&&(o=!0),(u=t.call(this)||this).t=null!=i?i:\"undefined\"!=typeof Ziggy?Ziggy:null==globalThis?void 0:globalThis.Ziggy,u.t=n({},u.t,{absolute:o}),r){if(!u.t.routes[r])throw new Error(\"Ziggy error: route '\"+r+\"' is not in the route list.\");u.i=new P(r,u.t.routes[r],u.t),u.u=u.l(e)}return u}var o,u;u=t,(o=e).prototype=Object.create(u.prototype),o.prototype.constructor=o,i(o,u);var f=e.prototype;return f.toString=function(){var t=this,r=Object.keys(this.u).filter(function(r){return!t.i.parameterSegments.some(function(t){return t.name===r})}).filter(function(t){return\"_query\"!==t}).reduce(function(r,e){var o;return n({},r,((o={})[e]=t.u[e],o))},{});return this.i.compile(this.u)+function(t,r){var n,e=t,o=function(t){if(!t)return S;if(null!=t.encoder&&\"function\"!=typeof t.encoder)throw new TypeError(\"Encoder has to be a function.\");var r=t.charset||S.charset;if(void 0!==t.charset&&\"utf-8\"!==t.charset&&\"iso-8859-1\"!==t.charset)throw new TypeError(\"The charset option must be either utf-8, iso-8859-1, or undefined\");var n=s.default;if(void 0!==t.format){if(!h.call(s.formatters,t.format))throw new TypeError(\"Unknown format option provided.\");n=t.format}var e=s.formatters[n],o=S.filter;return(\"function\"==typeof t.filter||m(t.filter))&&(o=t.filter),{addQueryPrefix:\"boolean\"==typeof t.addQueryPrefix?t.addQueryPrefix:S.addQueryPrefix,allowDots:void 0===t.allowDots?S.allowDots:!!t.allowDots,charset:r,charsetSentinel:\"boolean\"==typeof t.charsetSentinel?t.charsetSentinel:S.charsetSentinel,delimiter:void 0===t.delimiter?S.delimiter:t.delimiter,encode:\"boolean\"==typeof t.encode?t.encode:S.encode,encoder:\"function\"==typeof t.encoder?t.encoder:S.encoder,encodeValuesOnly:\"boolean\"==typeof t.encodeValuesOnly?t.encodeValuesOnly:S.encodeValuesOnly,filter:o,format:n,formatter:e,serializeDate:\"function\"==typeof t.serializeDate?t.serializeDate:S.serializeDate,skipNulls:\"boolean\"==typeof t.skipNulls?t.skipNulls:S.skipNulls,sort:\"function\"==typeof t.sort?t.sort:null,strictNullHandling:\"boolean\"==typeof t.strictNullHandling?t.strictNullHandling:S.strictNullHandling}}(r);\"function\"==typeof o.filter?e=(0,o.filter)(\"\",e):m(o.filter)&&(n=o.filter);var i=[];if(\"object\"!=typeof e||null===e)return\"\";var u=g[r&&r.arrayFormat in g?r.arrayFormat:r&&\"indices\"in r?r.indices?\"indices\":\"repeat\":\"indices\"];n||(n=Object.keys(e)),o.sort&&n.sort(o.sort);for(var f=0;f<n.length;++f){var a=n[f];o.skipNulls&&null===e[a]||O(i,k(e[a],a,u,o.strictNullHandling,o.skipNulls,o.encode?o.encoder:null,o.filter,o.sort,o.allowDots,o.serializeDate,o.format,o.formatter,o.encodeValuesOnly,o.charset))}var c=i.join(o.delimiter),l=!0===o.addQueryPrefix?\"?\":\"\";return o.charsetSentinel&&(l+=\"iso-8859-1\"===o.charset?\"utf8=%26%2310003%3B&\":\"utf8=%E2%9C%93&\"),c.length>0?l+c:\"\"}(n({},r,this.u._query),{addQueryPrefix:!0,arrayFormat:\"indices\",encodeValuesOnly:!0,skipNulls:!0,encoder:function(t,r){return\"boolean\"==typeof t?Number(t):r(t)}})},f.v=function(t){var r=this;t?this.t.absolute&&t.startsWith(\"/\")&&(t=this.p().host+t):t=this.h();var e={},o=Object.entries(this.t.routes).find(function(n){return e=new P(n[0],n[1],r.t).matchesUrl(t)})||[void 0,void 0];return n({name:o[0]},e,{route:o[1]})},f.h=function(){var t=this.p(),r=t.pathname,n=t.search;return(this.t.absolute?t.host+r:r.replace(this.t.url.replace(/^\\w*:\\/\\/[^/]+/,\"\"),\"\").replace(/^\\/+/,\"/\"))+n},f.current=function(t,r){var e=this.v(),o=e.name,i=e.params,u=e.query,f=e.route;if(!t)return o;var a=new RegExp(\"^\"+t.replace(/\\./g,\"\\\\.\").replace(/\\*/g,\".*\")+\"$\").test(o);if([null,void 0].includes(r)||!a)return a;var c=new P(o,f,this.t);r=this.l(r,c);var l=n({},i,u);if(Object.values(r).every(function(t){return!t})&&!Object.values(l).some(function(t){return void 0!==t}))return!0;var s=function(t,r){return Object.entries(t).every(function(t){var n=t[0],e=t[1];return Array.isArray(e)&&Array.isArray(r[n])?e.every(function(t){return r[n].includes(t)}):\"object\"==typeof e&&\"object\"==typeof r[n]&&null!==e&&null!==r[n]?s(e,r[n]):r[n]==e})};return s(r,l)},f.p=function(){var t,r,n,e,o,i,u=\"undefined\"!=typeof window?window.location:{},f=u.host,a=u.pathname,c=u.search;return{host:null!=(t=null==(r=this.t.location)?void 0:r.host)?t:void 0===f?\"\":f,pathname:null!=(n=null==(e=this.t.location)?void 0:e.pathname)?n:void 0===a?\"\":a,search:null!=(o=null==(i=this.t.location)?void 0:i.search)?o:void 0===c?\"\":c}},f.has=function(t){return this.t.routes.hasOwnProperty(t)},f.l=function(t,r){var e=this;void 0===t&&(t={}),void 0===r&&(r=this.i),null!=t||(t={}),t=[\"string\",\"number\"].includes(typeof t)?[t]:t;var o=r.parameterSegments.filter(function(t){return!e.t.defaults[t.name]});if(Array.isArray(t))t=t.reduce(function(t,r,e){var i,u;return n({},t,o[e]?((i={})[o[e].name]=r,i):\"object\"==typeof r?r:((u={})[r]=\"\",u))},{});else if(1===o.length&&!t[o[0].name]&&(t.hasOwnProperty(Object.values(r.bindings)[0])||t.hasOwnProperty(\"id\"))){var i;(i={})[o[0].name]=t,t=i}return n({},this.m(r),this.j(t,r))},f.m=function(t){var r=this;return t.parameterSegments.filter(function(t){return r.t.defaults[t.name]}).reduce(function(t,e,o){var i,u=e.name;return n({},t,((i={})[u]=r.t.defaults[u],i))},{})},f.j=function(t,r){var e=r.bindings,o=r.parameterSegments;return Object.entries(t).reduce(function(t,r){var i,u,f=r[0],a=r[1];if(!a||\"object\"!=typeof a||Array.isArray(a)||!o.some(function(t){return t.name===f}))return n({},t,((u={})[f]=a,u));if(!a.hasOwnProperty(e[f])){if(!a.hasOwnProperty(\"id\"))throw new Error(\"Ziggy error: object passed as '\"+f+\"' parameter is missing route model binding key '\"+e[f]+\"'.\");e[f]=\"id\"}return n({},t,((i={})[f]=a[e[f]],i))},{})},f.valueOf=function(){return this.toString()},r(e,[{key:\"params\",get:function(){var t=this.v();return n({},t.params,t.query)}},{key:\"routeParams\",get:function(){return this.v().params}},{key:\"queryParams\",get:function(){return this.v().query}}])}(/*#__PURE__*/f(String));return function(t,r,n,e){var o=new F(t,r,n,e);return t?o.toString():o}});\n</script>    <script type=\"module\" src=\"http://[::1]:5173/@vite/client\"></script><script type=\"module\" src=\"http://[::1]:5173/resources/js/app.ts\"></script><script type=\"module\" src=\"http://[::1]:5173/resources/js/Pages/NotFound.vue\"></script>    </head>\n\n<body class=\"font-sans antialiased\">\n    <div id=\"app\" data-page=\"{&quot;component&quot;:&quot;NotFound&quot;,&quot;props&quot;:{&quot;errors&quot;:{},&quot;books&quot;:{&quot;sections&quot;:[{&quot;name&quot;:&quot;Altes Testament&quot;,&quot;books&quot;:[{&quot;id&quot;:1,&quot;name&quot;:&quot;1. Mose&quot;,&quot;shortName&quot;:&quot;1Mo&quot;,&quot;chapterCount&quot;:50,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;law&quot;,&quot;order&quot;:1,&quot;slug&quot;:&quot;1.Mose&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:2,&quot;name&quot;:&quot;2. Mose&quot;,&quot;shortName&quot;:&quot;2Mo&quot;,&quot;chapterCount&quot;:40,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;law&quot;,&quot;order&quot;:2,&quot;slug&quot;:&quot;2.Mose&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:3,&quot;name&quot;:&quot;3. Mose&quot;,&quot;shortName&quot;:&quot;3Mo&quot;,&quot;chapterCount&quot;:27,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;law&quot;,&quot;order&quot;:3,&quot;slug&quot;:&quot;3.Mose&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:4,&quot;name&quot;:&quot;4. Mose&quot;,&quot;shortName&quot;:&quot;4Mo&quot;,&quot;chapterCount&quot;:36,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;law&quot;,&quot;order&quot;:4,&quot;slug&quot;:&quot;4.Mose&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:5,&quot;name&quot;:&quot;5. Mose&quot;,&quot;shortName&quot;:&quot;5Mo&quot;,&quot;chapterCount&quot;:34,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;law&quot;,&quot;order&quot;:5,&quot;slug&quot;:&quot;5.Mose&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:6,&quot;name&quot;:&quot;Josua&quot;,&quot;shortName&quot;:&quot;Jos&quot;,&quot;chapterCount&quot;:24,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:6,&quot;slug&quot;:&quot;Josua&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:7,&quot;name&quot;:&quot;Richter&quot;,&quot;shortName&quot;:&quot;Ri&quot;,&quot;chapterCount&quot;:21,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:7,&quot;slug&quot;:&quot;Richter&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:8,&quot;name&quot;:&quot;Ruth&quot;,&quot;shortName&quot;:&quot;Rt&quot;,&quot;chapterCount&quot;:4,&quot;chapters&quot;:[1,2,3,4],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:8,&quot;slug&quot;:&quot;Ruth&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:9,&quot;name&quot;:&quot;1. Samuel&quot;,&quot;shortName&quot;:&quot;1Sam&quot;,&quot;chapterCount&quot;:31,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:9,&quot;slug&quot;:&quot;1.Samuel&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:10,&quot;name&quot;:&quot;2. Samuel&quot;,&quot;shortName&quot;:&quot;2Sam&quot;,&quot;chapterCount&quot;:24,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:10,&quot;slug&quot;:&quot;2.Samuel&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:11,&quot;name&quot;:&quot;1. K\\u00f6nige&quot;,&quot;shortName&quot;:&quot;1K\\u00f6n&quot;,&quot;chapterCount&quot;:22,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:11,&quot;slug&quot;:&quot;1.K\\u00f6nige&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:12,&quot;name&quot;:&quot;2. K\\u00f6nige&quot;,&quot;shortName&quot;:&quot;2K\\u00f6n&quot;,&quot;chapterCount&quot;:25,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:12,&quot;slug&quot;:&quot;2.K\\u00f6nige&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:13,&quot;name&quot;:&quot;1. Chronik&quot;,&quot;shortName&quot;:&quot;1Chr&quot;,&quot;chapterCount&quot;:29,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:13,&quot;slug&quot;:&quot;1.Chronik&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:14,&quot;name&quot;:&quot;2. Chronik&quot;,&quot;shortName&quot;:&quot;2Chr&quot;,&quot;chapterCount&quot;:36,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:14,&quot;slug&quot;:&quot;2.Chronik&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:15,&quot;name&quot;:&quot;Esra&quot;,&quot;shortName&quot;:&quot;Esra&quot;,&quot;chapterCount&quot;:10,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:15,&quot;slug&quot;:&quot;Esra&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:16,&quot;name&quot;:&quot;Nehemia&quot;,&quot;shortName&quot;:&quot;Neh&quot;,&quot;chapterCount&quot;:13,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:16,&quot;slug&quot;:&quot;Nehemia&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:17,&quot;name&quot;:&quot;Esther&quot;,&quot;shortName&quot;:&quot;Est&quot;,&quot;chapterCount&quot;:10,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:17,&quot;slug&quot;:&quot;Esther&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:18,&quot;name&quot;:&quot;Hiob&quot;,&quot;shortName&quot;:&quot;Hi&quot;,&quot;chapterCount&quot;:42,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;wisdom&quot;,&quot;order&quot;:18,&quot;slug&quot;:&quot;Hiob&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:19,&quot;name&quot;:&quot;Psalmen&quot;,&quot;shortName&quot;:&quot;Ps&quot;,&quot;chapterCount&quot;:150,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;wisdom&quot;,&quot;order&quot;:19,&quot;slug&quot;:&quot;Psalmen&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:20,&quot;name&quot;:&quot;Spr\\u00fcche&quot;,&quot;shortName&quot;:&quot;Spr&quot;,&quot;chapterCount&quot;:31,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;wisdom&quot;,&quot;order&quot;:20,&quot;slug&quot;:&quot;Spr\\u00fcche&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:21,&quot;name&quot;:&quot;Prediger&quot;,&quot;shortName&quot;:&quot;Pred&quot;,&quot;chapterCount&quot;:12,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;wisdom&quot;,&quot;order&quot;:21,&quot;slug&quot;:&quot;Prediger&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:22,&quot;name&quot;:&quot;Hohelied&quot;,&quot;shortName&quot;:&quot;Hl&quot;,&quot;chapterCount&quot;:8,&quot;chapters&quot;:[1,2,3,4,5,6,7,8],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;wisdom&quot;,&quot;order&quot;:22,&quot;slug&quot;:&quot;Hohelied&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:23,&quot;name&quot;:&quot;Jesaja&quot;,&quot;shortName&quot;:&quot;Jes&quot;,&quot;chapterCount&quot;:66,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:23,&quot;slug&quot;:&quot;Jesaja&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:24,&quot;name&quot;:&quot;Jeremia&quot;,&quot;shortName&quot;:&quot;Jer&quot;,&quot;chapterCount&quot;:52,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:24,&quot;slug&quot;:&quot;Jeremia&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:25,&quot;name&quot;:&quot;Klagelieder&quot;,&quot;shortName&quot;:&quot;Kla&quot;,&quot;chapterCount&quot;:5,&quot;chapters&quot;:[1,2,3,4,5],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;wisdom&quot;,&quot;order&quot;:25,&quot;slug&quot;:&quot;Klagelieder&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:26,&quot;name&quot;:&quot;Hesekiel&quot;,&quot;shortName&quot;:&quot;Hes&quot;,&quot;chapterCount&quot;:48,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:26,&quot;slug&quot;:&quot;Hesekiel&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:27,&quot;name&quot;:&quot;Daniel&quot;,&quot;shortName&quot;:&quot;Dan&quot;,&quot;chapterCount&quot;:12,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:27,&quot;slug&quot;:&quot;Daniel&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:28,&quot;name&quot;:&quot;Hosea&quot;,&quot;shortName&quot;:&quot;Hos&quot;,&quot;chapterCount&quot;:14,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:28,&quot;slug&quot;:&quot;Hosea&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:29,&quot;name&quot;:&quot;Joel&quot;,&quot;shortName&quot;:&quot;Joel&quot;,&quot;chapterCount&quot;:3,&quot;chapters&quot;:[1,2,3],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:29,&quot;slug&quot;:&quot;Joel&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:30,&quot;name&quot;:&quot;Amos&quot;,&quot;shortName&quot;:&quot;Am&quot;,&quot;chapterCount&quot;:9,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:30,&quot;slug&quot;:&quot;Amos&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:31,&quot;name&quot;:&quot;Obadja&quot;,&quot;shortName&quot;:&quot;Ob&quot;,&quot;chapterCount&quot;:1,&quot;chapters&quot;:[1],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:31,&quot;slug&quot;:&quot;Obadja&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:32,&quot;name&quot;:&quot;Jona&quot;,&quot;shortName&quot;:&quot;Jon&quot;,&quot;chapterCount&quot;:4,&quot;chapters&quot;:[1,2,3,4],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:32,&quot;slug&quot;:&quot;Jona&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:33,&quot;name&quot;:&quot;Micha&quot;,&quot;shortName&quot;:&quot;Mi&quot;,&quot;chapterCount&quot;:7,&quot;chapters&quot;:[1,2,3,4,5,6,7],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:33,&quot;slug&quot;:&quot;Micha&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:34,&quot;name&quot;:&quot;Nahum&quot;,&quot;shortName&quot;:&quot;Nah&quot;,&quot;chapterCount&quot;:3,&quot;chapters&quot;:[1,2,3],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:34,&quot;slug&quot;:&quot;Nahum&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:35,&quot;name&quot;:&quot;Habakuk&quot;,&quot;shortName&quot;:&quot;Hab&quot;,&quot;chapterCount&quot;:3,&quot;chapters&quot;:[1,2,3],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:35,&quot;slug&quot;:&quot;Habakuk&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:36,&quot;name&quot;:&quot;Zephanja&quot;,&quot;shortName&quot;:&quot;Zeph&quot;,&quot;chapterCount&quot;:3,&quot;chapters&quot;:[1,2,3],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:36,&quot;slug&quot;:&quot;Zephanja&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:37,&quot;name&quot;:&quot;Haggai&quot;,&quot;shortName&quot;:&quot;Hag&quot;,&quot;chapterCount&quot;:2,&quot;chapters&quot;:[1,2],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:37,&quot;slug&quot;:&quot;Haggai&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:38,&quot;name&quot;:&quot;Sacharja&quot;,&quot;shortName&quot;:&quot;Sach&quot;,&quot;chapterCount&quot;:14,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:38,&quot;slug&quot;:&quot;Sacharja&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:39,&quot;name&quot;:&quot;Maleachi&quot;,&quot;shortName&quot;:&quot;Mal&quot;,&quot;chapterCount&quot;:4,&quot;chapters&quot;:[1,2,3,4],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:39,&quot;slug&quot;:&quot;Maleachi&quot;,&quot;hasContent&quot;:false}]},{&quot;name&quot;:&quot;Neues Testament&quot;,&quot;books&quot;:[{&quot;id&quot;:40,&quot;name&quot;:&quot;Matth\\u00e4us&quot;,&quot;shortName&quot;:&quot;Mt&quot;,&quot;chapterCount&quot;:28,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;gospel&quot;,&quot;order&quot;:40,&quot;slug&quot;:&quot;Matth\\u00e4us&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:41,&quot;name&quot;:&quot;Markus&quot;,&quot;shortName&quot;:&quot;Mk&quot;,&quot;chapterCount&quot;:16,&quot;chapters&quot;:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;gospel&quot;,&quot;order&quot;:41,&quot;slug&quot;:&quot;Markus&quot;,&quot;hasContent&quot;:true},{&quot;id&quot;:42,&quot;name&quot;:&quot;Lukas&quot;,&quot;shortName&quot;:&quot;Lk&quot;,&quot;chapterCount&quot;:24,&quot;chapters&quot;:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;gospel&quot;,&quot;order&quot;:42,&quot;slug&quot;:&quot;Lukas&quot;,&quot;hasContent&quot;:true},{&quot;id&quot;:43,&quot;name&quot;:&quot;Die Heilsbotschaft nach Johannes&quot;,&quot;shortName&quot;:&quot;Joh&quot;,&quot;chapterCount&quot;:21,&quot;chapters&quot;:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;gospel&quot;,&quot;order&quot;:43,&quot;slug&quot;:&quot;Johannes&quot;,&quot;hasContent&quot;:true},{&quot;id&quot;:44,&quot;name&quot;:&quot;Apostelgeschichte&quot;,&quot;shortName&quot;:&quot;Apg&quot;,&quot;chapterCount&quot;:28,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:44,&quot;slug&quot;:&quot;Apostelgeschichte&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:45,&quot;name&quot;:&quot;R\\u00f6mer&quot;,&quot;shortName&quot;:&quot;R\\u00f6m&quot;,&quot;chapterCount&quot;:16,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:45,&quot;slug&quot;:&quot;R\\u00f6mer&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:46,&quot;name&quot;:&quot;1. Korinther&quot;,&quot;shortName&quot;:&quot;1Kor&quot;,&quot;chapterCount&quot;:16,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:46,&quot;slug&quot;:&quot;1.Korinther&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:47,&quot;name&quot;:&quot;2. Korinther&quot;,&quot;shortName&quot;:&quot;2Kor&quot;,&quot;chapterCount&quot;:13,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:47,&quot;slug&quot;:&quot;2.Korinther&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:48,&quot;name&quot;:&quot;Galater&quot;,&quot;shortName&quot;:&quot;Gal&quot;,&quot;chapterCount&quot;:6,&quot;chapters&quot;:[1,2,3,4,5,6],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:48,&quot;slug&quot;:&quot;Galater&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:49,&quot;name&quot;:&quot;Epheser&quot;,&quot;shortName&quot;:&quot;Eph&quot;,&quot;chapterCount&quot;:6,&quot;chapters&quot;:[1,2,3,4,5,6],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:49,&quot;slug&quot;:&quot;Epheser&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:50,&quot;name&quot;:&quot;Philipper&quot;,&quot;shortName&quot;:&quot;Phil&quot;,&quot;chapterCount&quot;:4,&quot;chapters&quot;:[1,2,3,4],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:50,&quot;slug&quot;:&quot;Philipper&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:51,&quot;name&quot;:&quot;Kolosser&quot;,&quot;shortName&quot;:&quot;Kol&quot;,&quot;chapterCount&quot;:4,&quot;chapters&quot;:[1,2,3,4],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:51,&quot;slug&quot;:&quot;Kolosser&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:52,&quot;name&quot;:&quot;1. Thessalonicher&quot;,&quot;shortName&quot;:&quot;1Thes&quot;,&quot;chapterCount&quot;:5,&quot;chapters&quot;:[1,2,3,4,5],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:52,&quot;slug&quot;:&quot;1.Thessalonicher&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:53,&quot;name&quot;:&quot;2. Thessalonicher&quot;,&quot;shortName&quot;:&quot;2Thes&quot;,&quot;chapterCount&quot;:3,&quot;chapters&quot;:[1,2,3],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:53,&quot;slug&quot;:&quot;2.Thessalonicher&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:54,&quot;name&quot;:&quot;1. Timotheus&quot;,&quot;shortName&quot;:&quot;1Tim&quot;,&quot;chapterCount&quot;:6,&quot;chapters&quot;:[1,2,3,4,5,6],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:54,&quot;slug&quot;:&quot;1.Timotheus&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:55,&quot;name&quot;:&quot;2. Timotheus&quot;,&quot;shortName&quot;:&quot;2Tim&quot;,&quot;chapterCount&quot;:4,&quot;chapters&quot;:[1,2,3,4],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:55,&quot;slug&quot;:&quot;2.Timotheus&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:56,&quot;name&quot;:&quot;Titus&quot;,&quot;shortName&quot;:&quot;Tit&quot;,&quot;chapterCount&quot;:3,&quot;chapters&quot;:[1,2,3],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:56,&quot;slug&quot;:&quot;Titus&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:57,&quot;name&quot;:&quot;Philemon&quot;,&quot;shortName&quot;:&quot;Phim&quot;,&quot;chapterCount&quot;:1,&quot;chapters&quot;:[1],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:57,&quot;slug&quot;:&quot;Philemon&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:58,&quot;name&quot;:&quot;Hebr\\u00e4er&quot;,&quot;shortName&quot;:&quot;Heb&quot;,&quot;chapterCount&quot;:13,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:58,&quot;slug&quot;:&quot;Hebr\\u00e4er&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:59,&quot;name&quot;:&quot;Jakobus&quot;,&quot;shortName&quot;:&quot;Jak&quot;,&quot;chapterCount&quot;:5,&quot;chapters&quot;:[1,2,3,4,5],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:59,&quot;slug&quot;:&quot;Jakobus&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:60,&quot;name&quot;:&quot;1. Petrus&quot;,&quot;shortName&quot;:&quot;1Pet&quot;,&quot;chapterCount&quot;:5,&quot;chapters&quot;:[1,2,3,4,5],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:60,&quot;slug&quot;:&quot;1.Petrus&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:61,&quot;name&quot;:&quot;2. Petrus&quot;,&quot;shortName&quot;:&quot;2Pet&quot;,&quot;chapterCount&quot;:3,&quot;chapters&quot;:[1,2,3],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:61,&quot;slug&quot;:&quot;2.Petrus&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:62,&quot;name&quot;:&quot;1. Johannes&quot;,&quot;shortName&quot;:&quot;1Joh&quot;,&quot;chapterCount&quot;:5,&quot;chapters&quot;:[1,2,3,4,5],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:62,&quot;slug&quot;:&quot;1.Johannes&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:63,&quot;name&quot;:&quot;2. Johannes&quot;,&quot;shortName&quot;:&quot;2Joh&quot;,&quot;chapterCount&quot;:1,&quot;chapters&quot;:[1],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:63,&quot;slug&quot;:&quot;2.Johannes&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:64,&quot;name&quot;:&quot;3. Johannes&quot;,&quot;shortName&quot;:&quot;3Joh&quot;,&quot;chapterCount&quot;:1,&quot;chapters&quot;:[1],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:64,&quot;slug&quot;:&quot;3.Johannes&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:65,&quot;name&quot;:&quot;Judas&quot;,&quot;shortName&quot;:&quot;Jud&quot;,&quot;chapterCount&quot;:1,&quot;chapters&quot;:[1],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:65,&quot;slug&quot;:&quot;Judas&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:66,&quot;name&quot;:&quot;Offenbarung&quot;,&quot;shortName&quot;:&quot;Offb&quot;,&quot;chapterCount&quot;:22,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;apocalypse&quot;,&quot;order&quot;:66,&quot;slug&quot;:&quot;Offenbarung&quot;,&quot;hasContent&quot;:false}]}],&quot;availableBooks&quot;:[{&quot;slug&quot;:&quot;Markus&quot;,&quot;order&quot;:41},{&quot;slug&quot;:&quot;Lukas&quot;,&quot;order&quot;:42},{&quot;slug&quot;:&quot;Johannes&quot;,&quot;order&quot;:43}]},&quot;env&quot;:&quot;local&quot;,&quot;auth&quot;:{&quot;user&quot;:null},&quot;requestedPath&quot;:&quot;api\\/user&quot;},&quot;url&quot;:&quot;\\/api\\/user&quot;,&quot;version&quot;:&quot;18765f3fa436d07c5ef1cbbbc3fa3b37&quot;,&quot;clearHistory&quot;:false,&quot;encryptHistory&quot;:false}\"></div></body>\n\n</html>\n"
      tags:
        - Endpoints
      security: []
  /api/chapters/fetch:
    get:
      summary: ''
      operationId: getApiChaptersFetch
      description: ''
      parameters: []
      responses:
        404:
          description: ''
          content:
            text/plain:
              schema:
                type: string
                example: "<!DOCTYPE html>\n<html lang=\"de\">\n\n<head>\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\n\n    <title inertia></title>\n\n    <!-- Fonts -->\n    <!-- Preconnect to font domains -->\n    <link rel=\"preconnect\" href=\"https://fonts.bunny.net\" crossorigin>\n    <link rel=\"preconnect\" href=\"https://use.typekit.net\" as=\"font\" type=\"font/woff2\" crossorigin>\n\n    <!-- Preload critical fonts -->\n    <link rel=\"preload\" href=\"/fonts/ThanatosText-Book.woff2\" as=\"font\" type=\"font/woff2\" crossorigin>\n\n    <!-- Load fonts -->\n    <link href=\"https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap\" rel=\"stylesheet\" />\n    <link rel=\"stylesheet\" href=\"https://use.typekit.net/kzb8yhl.css\" media=\"print\" onload=\"this.media='all'\">\n\n    <!-- Fallback for typekit fonts -->\n    <noscript>\n        <link rel=\"stylesheet\" href=\"https://use.typekit.net/kzb8yhl.css\">\n    </noscript>\n\n    <!-- Local font definition -->\n    <style>\n        @font-face {\n            font-family: 'ThanatosText';\n            src: url('/fonts/ThanatosText-Book.woff2') format('woff2');\n            font-weight: normal;\n            font-style: normal;\n            font-display: swap;\n        }\n    </style>\n\n    <!-- Scripts -->\n    <script type=\"text/javascript\">Object.assign(Ziggy.routes,{\"search.index\":{\"uri\":\"search\",\"methods\":[\"GET\",\"HEAD\"]},\"search.query\":{\"uri\":\"search\\/{query}\",\"methods\":[\"GET\",\"HEAD\"],\"wheres\":{\"query\":\"[^\\/]+\"},\"parameters\":[\"query\"]},\"search.paged\":{\"uri\":\"search\\/{query}\\/{page?}\",\"methods\":[\"GET\",\"HEAD\"],\"wheres\":{\"query\":\"[^\\/]+\",\"page\":\"[0-9]+\"},\"parameters\":[\"query\",\"page\"]},\"search.settings\":{\"uri\":\"search\\/settings\",\"methods\":[\"POST\"]},\"dashboard\":{\"uri\":\"dashboard\",\"methods\":[\"GET\",\"HEAD\"]},\"bible.import\":{\"uri\":\"import-bible\",\"methods\":[\"GET\",\"HEAD\"]},\"bible.import.store\":{\"uri\":\"import-bible\",\"methods\":[\"POST\"]},\"file.upload\":{\"uri\":\"api\\/upload\",\"methods\":[\"POST\"]},\"profile.edit\":{\"uri\":\"profile\",\"methods\":[\"GET\",\"HEAD\"]},\"profile.update\":{\"uri\":\"profile\",\"methods\":[\"PATCH\"]},\"profile.destroy\":{\"uri\":\"profile\",\"methods\":[\"DELETE\"]},\"books.show\":{\"uri\":\"{reference}\",\"methods\":[\"GET\",\"HEAD\"],\"wheres\":{\"reference\":\"[\\\\w\\\\d\\\\.,\\\\-\\\\+]+\"},\"parameters\":[\"reference\"]},\"register\":{\"uri\":\"register\",\"methods\":[\"GET\",\"HEAD\"]},\"login\":{\"uri\":\"login\",\"methods\":[\"GET\",\"HEAD\"]},\"password.request\":{\"uri\":\"forgot-password\",\"methods\":[\"GET\",\"HEAD\"]},\"password.email\":{\"uri\":\"forgot-password\",\"methods\":[\"POST\"]},\"password.reset\":{\"uri\":\"reset-password\\/{token}\",\"methods\":[\"GET\",\"HEAD\"],\"parameters\":[\"token\"]},\"password.store\":{\"uri\":\"reset-password\",\"methods\":[\"POST\"]},\"verification.notice\":{\"uri\":\"verify-email\",\"methods\":[\"GET\",\"HEAD\"]},\"verification.verify\":{\"uri\":\"verify-email\\/{id}\\/{hash}\",\"methods\":[\"GET\",\"HEAD\"],\"parameters\":[\"id\",\"hash\"]},\"verification.send\":{\"uri\":\"email\\/verification-notification\",\"methods\":[\"POST\"]},\"password.confirm\":{\"uri\":\"confirm-password\",\"methods\":[\"GET\",\"HEAD\"]},\"password.update\":{\"uri\":\"password\",\"methods\":[\"PUT\"]},\"logout\":{\"uri\":\"logout\",\"methods\":[\"POST\"]},\"api.chapters.fetch\":{\"uri\":\"api\\/chapters\\/fetch\",\"methods\":[\"GET\",\"HEAD\"]},\"api.books\":{\"uri\":\"api\\/books\",\"methods\":[\"GET\",\"HEAD\"]},\"api.books.content-status\":{\"uri\":\"api\\/books\\/content-status\",\"methods\":[\"GET\",\"HEAD\"]},\"api.books.show\":{\"uri\":\"api\\/books\\/{slug}\",\"methods\":[\"GET\",\"HEAD\"],\"parameters\":[\"slug\"]},\"api.chapters.adjacent\":{\"uri\":\"api\\/chapters\\/{reference}\\/adjacent\",\"methods\":[\"GET\",\"HEAD\"],\"wheres\":{\"reference\":\"[\\\\w\\\\d,\\\\-]+\"},\"parameters\":[\"reference\"]},\"api.search.books\":{\"uri\":\"api\\/search\\/books\",\"methods\":[\"GET\",\"HEAD\"]},\"api.search\":{\"uri\":\"api\\/search\",\"methods\":[\"GET\",\"HEAD\"]},\"api.bible.text\":{\"uri\":\"api\\/bible\\/{reference}\",\"methods\":[\"GET\",\"HEAD\"],\"wheres\":{\"reference\":\"[\\\\w\\\\d,\\\\-]+\"},\"parameters\":[\"reference\"]},\"storage.local\":{\"uri\":\"storage\\/{path}\",\"methods\":[\"GET\",\"HEAD\"],\"wheres\":{\"path\":\".*\"},\"parameters\":[\"path\"]}});</script>    <script type=\"module\" src=\"http://[::1]:5173/@vite/client\"></script><script type=\"module\" src=\"http://[::1]:5173/resources/js/app.ts\"></script><script type=\"module\" src=\"http://[::1]:5173/resources/js/Pages/NotFound.vue\"></script>    </head>\n\n<body class=\"font-sans antialiased\">\n    <div id=\"app\" data-page=\"{&quot;component&quot;:&quot;NotFound&quot;,&quot;props&quot;:{&quot;errors&quot;:{},&quot;books&quot;:{&quot;sections&quot;:[{&quot;name&quot;:&quot;Altes Testament&quot;,&quot;books&quot;:[{&quot;id&quot;:1,&quot;name&quot;:&quot;1. Mose&quot;,&quot;shortName&quot;:&quot;1Mo&quot;,&quot;chapterCount&quot;:50,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;law&quot;,&quot;order&quot;:1,&quot;slug&quot;:&quot;1.Mose&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:2,&quot;name&quot;:&quot;2. Mose&quot;,&quot;shortName&quot;:&quot;2Mo&quot;,&quot;chapterCount&quot;:40,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;law&quot;,&quot;order&quot;:2,&quot;slug&quot;:&quot;2.Mose&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:3,&quot;name&quot;:&quot;3. Mose&quot;,&quot;shortName&quot;:&quot;3Mo&quot;,&quot;chapterCount&quot;:27,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;law&quot;,&quot;order&quot;:3,&quot;slug&quot;:&quot;3.Mose&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:4,&quot;name&quot;:&quot;4. Mose&quot;,&quot;shortName&quot;:&quot;4Mo&quot;,&quot;chapterCount&quot;:36,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;law&quot;,&quot;order&quot;:4,&quot;slug&quot;:&quot;4.Mose&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:5,&quot;name&quot;:&quot;5. Mose&quot;,&quot;shortName&quot;:&quot;5Mo&quot;,&quot;chapterCount&quot;:34,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;law&quot;,&quot;order&quot;:5,&quot;slug&quot;:&quot;5.Mose&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:6,&quot;name&quot;:&quot;Josua&quot;,&quot;shortName&quot;:&quot;Jos&quot;,&quot;chapterCount&quot;:24,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:6,&quot;slug&quot;:&quot;Josua&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:7,&quot;name&quot;:&quot;Richter&quot;,&quot;shortName&quot;:&quot;Ri&quot;,&quot;chapterCount&quot;:21,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:7,&quot;slug&quot;:&quot;Richter&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:8,&quot;name&quot;:&quot;Ruth&quot;,&quot;shortName&quot;:&quot;Rt&quot;,&quot;chapterCount&quot;:4,&quot;chapters&quot;:[1,2,3,4],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:8,&quot;slug&quot;:&quot;Ruth&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:9,&quot;name&quot;:&quot;1. Samuel&quot;,&quot;shortName&quot;:&quot;1Sam&quot;,&quot;chapterCount&quot;:31,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:9,&quot;slug&quot;:&quot;1.Samuel&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:10,&quot;name&quot;:&quot;2. Samuel&quot;,&quot;shortName&quot;:&quot;2Sam&quot;,&quot;chapterCount&quot;:24,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:10,&quot;slug&quot;:&quot;2.Samuel&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:11,&quot;name&quot;:&quot;1. K\\u00f6nige&quot;,&quot;shortName&quot;:&quot;1K\\u00f6n&quot;,&quot;chapterCount&quot;:22,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:11,&quot;slug&quot;:&quot;1.K\\u00f6nige&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:12,&quot;name&quot;:&quot;2. K\\u00f6nige&quot;,&quot;shortName&quot;:&quot;2K\\u00f6n&quot;,&quot;chapterCount&quot;:25,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:12,&quot;slug&quot;:&quot;2.K\\u00f6nige&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:13,&quot;name&quot;:&quot;1. Chronik&quot;,&quot;shortName&quot;:&quot;1Chr&quot;,&quot;chapterCount&quot;:29,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:13,&quot;slug&quot;:&quot;1.Chronik&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:14,&quot;name&quot;:&quot;2. Chronik&quot;,&quot;shortName&quot;:&quot;2Chr&quot;,&quot;chapterCount&quot;:36,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:14,&quot;slug&quot;:&quot;2.Chronik&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:15,&quot;name&quot;:&quot;Esra&quot;,&quot;shortName&quot;:&quot;Esra&quot;,&quot;chapterCount&quot;:10,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:15,&quot;slug&quot;:&quot;Esra&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:16,&quot;name&quot;:&quot;Nehemia&quot;,&quot;shortName&quot;:&quot;Neh&quot;,&quot;chapterCount&quot;:13,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:16,&quot;slug&quot;:&quot;Nehemia&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:17,&quot;name&quot;:&quot;Esther&quot;,&quot;shortName&quot;:&quot;Est&quot;,&quot;chapterCount&quot;:10,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:17,&quot;slug&quot;:&quot;Esther&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:18,&quot;name&quot;:&quot;Hiob&quot;,&quot;shortName&quot;:&quot;Hi&quot;,&quot;chapterCount&quot;:42,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;wisdom&quot;,&quot;order&quot;:18,&quot;slug&quot;:&quot;Hiob&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:19,&quot;name&quot;:&quot;Psalmen&quot;,&quot;shortName&quot;:&quot;Ps&quot;,&quot;chapterCount&quot;:150,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;wisdom&quot;,&quot;order&quot;:19,&quot;slug&quot;:&quot;Psalmen&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:20,&quot;name&quot;:&quot;Spr\\u00fcche&quot;,&quot;shortName&quot;:&quot;Spr&quot;,&quot;chapterCount&quot;:31,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;wisdom&quot;,&quot;order&quot;:20,&quot;slug&quot;:&quot;Spr\\u00fcche&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:21,&quot;name&quot;:&quot;Prediger&quot;,&quot;shortName&quot;:&quot;Pred&quot;,&quot;chapterCount&quot;:12,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;wisdom&quot;,&quot;order&quot;:21,&quot;slug&quot;:&quot;Prediger&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:22,&quot;name&quot;:&quot;Hohelied&quot;,&quot;shortName&quot;:&quot;Hl&quot;,&quot;chapterCount&quot;:8,&quot;chapters&quot;:[1,2,3,4,5,6,7,8],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;wisdom&quot;,&quot;order&quot;:22,&quot;slug&quot;:&quot;Hohelied&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:23,&quot;name&quot;:&quot;Jesaja&quot;,&quot;shortName&quot;:&quot;Jes&quot;,&quot;chapterCount&quot;:66,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:23,&quot;slug&quot;:&quot;Jesaja&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:24,&quot;name&quot;:&quot;Jeremia&quot;,&quot;shortName&quot;:&quot;Jer&quot;,&quot;chapterCount&quot;:52,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:24,&quot;slug&quot;:&quot;Jeremia&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:25,&quot;name&quot;:&quot;Klagelieder&quot;,&quot;shortName&quot;:&quot;Kla&quot;,&quot;chapterCount&quot;:5,&quot;chapters&quot;:[1,2,3,4,5],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;wisdom&quot;,&quot;order&quot;:25,&quot;slug&quot;:&quot;Klagelieder&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:26,&quot;name&quot;:&quot;Hesekiel&quot;,&quot;shortName&quot;:&quot;Hes&quot;,&quot;chapterCount&quot;:48,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:26,&quot;slug&quot;:&quot;Hesekiel&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:27,&quot;name&quot;:&quot;Daniel&quot;,&quot;shortName&quot;:&quot;Dan&quot;,&quot;chapterCount&quot;:12,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:27,&quot;slug&quot;:&quot;Daniel&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:28,&quot;name&quot;:&quot;Hosea&quot;,&quot;shortName&quot;:&quot;Hos&quot;,&quot;chapterCount&quot;:14,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:28,&quot;slug&quot;:&quot;Hosea&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:29,&quot;name&quot;:&quot;Joel&quot;,&quot;shortName&quot;:&quot;Joel&quot;,&quot;chapterCount&quot;:3,&quot;chapters&quot;:[1,2,3],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:29,&quot;slug&quot;:&quot;Joel&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:30,&quot;name&quot;:&quot;Amos&quot;,&quot;shortName&quot;:&quot;Am&quot;,&quot;chapterCount&quot;:9,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:30,&quot;slug&quot;:&quot;Amos&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:31,&quot;name&quot;:&quot;Obadja&quot;,&quot;shortName&quot;:&quot;Ob&quot;,&quot;chapterCount&quot;:1,&quot;chapters&quot;:[1],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:31,&quot;slug&quot;:&quot;Obadja&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:32,&quot;name&quot;:&quot;Jona&quot;,&quot;shortName&quot;:&quot;Jon&quot;,&quot;chapterCount&quot;:4,&quot;chapters&quot;:[1,2,3,4],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:32,&quot;slug&quot;:&quot;Jona&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:33,&quot;name&quot;:&quot;Micha&quot;,&quot;shortName&quot;:&quot;Mi&quot;,&quot;chapterCount&quot;:7,&quot;chapters&quot;:[1,2,3,4,5,6,7],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:33,&quot;slug&quot;:&quot;Micha&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:34,&quot;name&quot;:&quot;Nahum&quot;,&quot;shortName&quot;:&quot;Nah&quot;,&quot;chapterCount&quot;:3,&quot;chapters&quot;:[1,2,3],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:34,&quot;slug&quot;:&quot;Nahum&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:35,&quot;name&quot;:&quot;Habakuk&quot;,&quot;shortName&quot;:&quot;Hab&quot;,&quot;chapterCount&quot;:3,&quot;chapters&quot;:[1,2,3],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:35,&quot;slug&quot;:&quot;Habakuk&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:36,&quot;name&quot;:&quot;Zephanja&quot;,&quot;shortName&quot;:&quot;Zeph&quot;,&quot;chapterCount&quot;:3,&quot;chapters&quot;:[1,2,3],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:36,&quot;slug&quot;:&quot;Zephanja&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:37,&quot;name&quot;:&quot;Haggai&quot;,&quot;shortName&quot;:&quot;Hag&quot;,&quot;chapterCount&quot;:2,&quot;chapters&quot;:[1,2],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:37,&quot;slug&quot;:&quot;Haggai&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:38,&quot;name&quot;:&quot;Sacharja&quot;,&quot;shortName&quot;:&quot;Sach&quot;,&quot;chapterCount&quot;:14,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:38,&quot;slug&quot;:&quot;Sacharja&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:39,&quot;name&quot;:&quot;Maleachi&quot;,&quot;shortName&quot;:&quot;Mal&quot;,&quot;chapterCount&quot;:4,&quot;chapters&quot;:[1,2,3,4],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:39,&quot;slug&quot;:&quot;Maleachi&quot;,&quot;hasContent&quot;:false}]},{&quot;name&quot;:&quot;Neues Testament&quot;,&quot;books&quot;:[{&quot;id&quot;:40,&quot;name&quot;:&quot;Matth\\u00e4us&quot;,&quot;shortName&quot;:&quot;Mt&quot;,&quot;chapterCount&quot;:28,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;gospel&quot;,&quot;order&quot;:40,&quot;slug&quot;:&quot;Matth\\u00e4us&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:41,&quot;name&quot;:&quot;Markus&quot;,&quot;shortName&quot;:&quot;Mk&quot;,&quot;chapterCount&quot;:16,&quot;chapters&quot;:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;gospel&quot;,&quot;order&quot;:41,&quot;slug&quot;:&quot;Markus&quot;,&quot;hasContent&quot;:true},{&quot;id&quot;:42,&quot;name&quot;:&quot;Lukas&quot;,&quot;shortName&quot;:&quot;Lk&quot;,&quot;chapterCount&quot;:24,&quot;chapters&quot;:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;gospel&quot;,&quot;order&quot;:42,&quot;slug&quot;:&quot;Lukas&quot;,&quot;hasContent&quot;:true},{&quot;id&quot;:43,&quot;name&quot;:&quot;Die Heilsbotschaft nach Johannes&quot;,&quot;shortName&quot;:&quot;Joh&quot;,&quot;chapterCount&quot;:21,&quot;chapters&quot;:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;gospel&quot;,&quot;order&quot;:43,&quot;slug&quot;:&quot;Johannes&quot;,&quot;hasContent&quot;:true},{&quot;id&quot;:44,&quot;name&quot;:&quot;Apostelgeschichte&quot;,&quot;shortName&quot;:&quot;Apg&quot;,&quot;chapterCount&quot;:28,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:44,&quot;slug&quot;:&quot;Apostelgeschichte&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:45,&quot;name&quot;:&quot;R\\u00f6mer&quot;,&quot;shortName&quot;:&quot;R\\u00f6m&quot;,&quot;chapterCount&quot;:16,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:45,&quot;slug&quot;:&quot;R\\u00f6mer&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:46,&quot;name&quot;:&quot;1. Korinther&quot;,&quot;shortName&quot;:&quot;1Kor&quot;,&quot;chapterCount&quot;:16,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:46,&quot;slug&quot;:&quot;1.Korinther&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:47,&quot;name&quot;:&quot;2. Korinther&quot;,&quot;shortName&quot;:&quot;2Kor&quot;,&quot;chapterCount&quot;:13,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:47,&quot;slug&quot;:&quot;2.Korinther&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:48,&quot;name&quot;:&quot;Galater&quot;,&quot;shortName&quot;:&quot;Gal&quot;,&quot;chapterCount&quot;:6,&quot;chapters&quot;:[1,2,3,4,5,6],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:48,&quot;slug&quot;:&quot;Galater&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:49,&quot;name&quot;:&quot;Epheser&quot;,&quot;shortName&quot;:&quot;Eph&quot;,&quot;chapterCount&quot;:6,&quot;chapters&quot;:[1,2,3,4,5,6],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:49,&quot;slug&quot;:&quot;Epheser&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:50,&quot;name&quot;:&quot;Philipper&quot;,&quot;shortName&quot;:&quot;Phil&quot;,&quot;chapterCount&quot;:4,&quot;chapters&quot;:[1,2,3,4],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:50,&quot;slug&quot;:&quot;Philipper&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:51,&quot;name&quot;:&quot;Kolosser&quot;,&quot;shortName&quot;:&quot;Kol&quot;,&quot;chapterCount&quot;:4,&quot;chapters&quot;:[1,2,3,4],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:51,&quot;slug&quot;:&quot;Kolosser&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:52,&quot;name&quot;:&quot;1. Thessalonicher&quot;,&quot;shortName&quot;:&quot;1Thes&quot;,&quot;chapterCount&quot;:5,&quot;chapters&quot;:[1,2,3,4,5],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:52,&quot;slug&quot;:&quot;1.Thessalonicher&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:53,&quot;name&quot;:&quot;2. Thessalonicher&quot;,&quot;shortName&quot;:&quot;2Thes&quot;,&quot;chapterCount&quot;:3,&quot;chapters&quot;:[1,2,3],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:53,&quot;slug&quot;:&quot;2.Thessalonicher&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:54,&quot;name&quot;:&quot;1. Timotheus&quot;,&quot;shortName&quot;:&quot;1Tim&quot;,&quot;chapterCount&quot;:6,&quot;chapters&quot;:[1,2,3,4,5,6],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:54,&quot;slug&quot;:&quot;1.Timotheus&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:55,&quot;name&quot;:&quot;2. Timotheus&quot;,&quot;shortName&quot;:&quot;2Tim&quot;,&quot;chapterCount&quot;:4,&quot;chapters&quot;:[1,2,3,4],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:55,&quot;slug&quot;:&quot;2.Timotheus&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:56,&quot;name&quot;:&quot;Titus&quot;,&quot;shortName&quot;:&quot;Tit&quot;,&quot;chapterCount&quot;:3,&quot;chapters&quot;:[1,2,3],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:56,&quot;slug&quot;:&quot;Titus&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:57,&quot;name&quot;:&quot;Philemon&quot;,&quot;shortName&quot;:&quot;Phim&quot;,&quot;chapterCount&quot;:1,&quot;chapters&quot;:[1],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:57,&quot;slug&quot;:&quot;Philemon&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:58,&quot;name&quot;:&quot;Hebr\\u00e4er&quot;,&quot;shortName&quot;:&quot;Heb&quot;,&quot;chapterCount&quot;:13,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:58,&quot;slug&quot;:&quot;Hebr\\u00e4er&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:59,&quot;name&quot;:&quot;Jakobus&quot;,&quot;shortName&quot;:&quot;Jak&quot;,&quot;chapterCount&quot;:5,&quot;chapters&quot;:[1,2,3,4,5],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:59,&quot;slug&quot;:&quot;Jakobus&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:60,&quot;name&quot;:&quot;1. Petrus&quot;,&quot;shortName&quot;:&quot;1Pet&quot;,&quot;chapterCount&quot;:5,&quot;chapters&quot;:[1,2,3,4,5],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:60,&quot;slug&quot;:&quot;1.Petrus&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:61,&quot;name&quot;:&quot;2. Petrus&quot;,&quot;shortName&quot;:&quot;2Pet&quot;,&quot;chapterCount&quot;:3,&quot;chapters&quot;:[1,2,3],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:61,&quot;slug&quot;:&quot;2.Petrus&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:62,&quot;name&quot;:&quot;1. Johannes&quot;,&quot;shortName&quot;:&quot;1Joh&quot;,&quot;chapterCount&quot;:5,&quot;chapters&quot;:[1,2,3,4,5],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:62,&quot;slug&quot;:&quot;1.Johannes&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:63,&quot;name&quot;:&quot;2. Johannes&quot;,&quot;shortName&quot;:&quot;2Joh&quot;,&quot;chapterCount&quot;:1,&quot;chapters&quot;:[1],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:63,&quot;slug&quot;:&quot;2.Johannes&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:64,&quot;name&quot;:&quot;3. Johannes&quot;,&quot;shortName&quot;:&quot;3Joh&quot;,&quot;chapterCount&quot;:1,&quot;chapters&quot;:[1],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:64,&quot;slug&quot;:&quot;3.Johannes&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:65,&quot;name&quot;:&quot;Judas&quot;,&quot;shortName&quot;:&quot;Jud&quot;,&quot;chapterCount&quot;:1,&quot;chapters&quot;:[1],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:65,&quot;slug&quot;:&quot;Judas&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:66,&quot;name&quot;:&quot;Offenbarung&quot;,&quot;shortName&quot;:&quot;Offb&quot;,&quot;chapterCount&quot;:22,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;apocalypse&quot;,&quot;order&quot;:66,&quot;slug&quot;:&quot;Offenbarung&quot;,&quot;hasContent&quot;:false}]}],&quot;availableBooks&quot;:[{&quot;slug&quot;:&quot;Markus&quot;,&quot;order&quot;:41},{&quot;slug&quot;:&quot;Lukas&quot;,&quot;order&quot;:42},{&quot;slug&quot;:&quot;Johannes&quot;,&quot;order&quot;:43}]},&quot;env&quot;:&quot;local&quot;,&quot;auth&quot;:{&quot;user&quot;:null},&quot;requestedPath&quot;:&quot;api\\/chapters\\/fetch&quot;},&quot;url&quot;:&quot;\\/api\\/chapters\\/fetch&quot;,&quot;version&quot;:&quot;18765f3fa436d07c5ef1cbbbc3fa3b37&quot;,&quot;clearHistory&quot;:false,&quot;encryptHistory&quot;:false}\"></div></body>\n\n</html>\n"
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                bookId:
                  type: integer
                  description: 'The <code>order</code> of an existing record in the books table.'
                  example: 16
                  nullable: false
                chapter:
                  type: integer
                  description: validation.min.
                  example: 22
                  nullable: false
                direction:
                  type: string
                  description: ''
                  example: previous
                  nullable: false
                  enum:
                    - next
                    - previous
              required:
                - bookId
                - chapter
                - direction
      security: []
  /api/books:
    get:
      summary: ''
      operationId: getApiBooks
      description: ''
      parameters: []
      responses:
        404:
          description: ''
          content:
            text/plain:
              schema:
                type: string
                example: "<!DOCTYPE html>\n<html lang=\"de\">\n\n<head>\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\n\n    <title inertia></title>\n\n    <!-- Fonts -->\n    <!-- Preconnect to font domains -->\n    <link rel=\"preconnect\" href=\"https://fonts.bunny.net\" crossorigin>\n    <link rel=\"preconnect\" href=\"https://use.typekit.net\" as=\"font\" type=\"font/woff2\" crossorigin>\n\n    <!-- Preload critical fonts -->\n    <link rel=\"preload\" href=\"/fonts/ThanatosText-Book.woff2\" as=\"font\" type=\"font/woff2\" crossorigin>\n\n    <!-- Load fonts -->\n    <link href=\"https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap\" rel=\"stylesheet\" />\n    <link rel=\"stylesheet\" href=\"https://use.typekit.net/kzb8yhl.css\" media=\"print\" onload=\"this.media='all'\">\n\n    <!-- Fallback for typekit fonts -->\n    <noscript>\n        <link rel=\"stylesheet\" href=\"https://use.typekit.net/kzb8yhl.css\">\n    </noscript>\n\n    <!-- Local font definition -->\n    <style>\n        @font-face {\n            font-family: 'ThanatosText';\n            src: url('/fonts/ThanatosText-Book.woff2') format('woff2');\n            font-weight: normal;\n            font-style: normal;\n            font-display: swap;\n        }\n    </style>\n\n    <!-- Scripts -->\n    <script type=\"text/javascript\">Object.assign(Ziggy.routes,{\"search.index\":{\"uri\":\"search\",\"methods\":[\"GET\",\"HEAD\"]},\"search.query\":{\"uri\":\"search\\/{query}\",\"methods\":[\"GET\",\"HEAD\"],\"wheres\":{\"query\":\"[^\\/]+\"},\"parameters\":[\"query\"]},\"search.paged\":{\"uri\":\"search\\/{query}\\/{page?}\",\"methods\":[\"GET\",\"HEAD\"],\"wheres\":{\"query\":\"[^\\/]+\",\"page\":\"[0-9]+\"},\"parameters\":[\"query\",\"page\"]},\"search.settings\":{\"uri\":\"search\\/settings\",\"methods\":[\"POST\"]},\"dashboard\":{\"uri\":\"dashboard\",\"methods\":[\"GET\",\"HEAD\"]},\"bible.import\":{\"uri\":\"import-bible\",\"methods\":[\"GET\",\"HEAD\"]},\"bible.import.store\":{\"uri\":\"import-bible\",\"methods\":[\"POST\"]},\"file.upload\":{\"uri\":\"api\\/upload\",\"methods\":[\"POST\"]},\"profile.edit\":{\"uri\":\"profile\",\"methods\":[\"GET\",\"HEAD\"]},\"profile.update\":{\"uri\":\"profile\",\"methods\":[\"PATCH\"]},\"profile.destroy\":{\"uri\":\"profile\",\"methods\":[\"DELETE\"]},\"books.show\":{\"uri\":\"{reference}\",\"methods\":[\"GET\",\"HEAD\"],\"wheres\":{\"reference\":\"[\\\\w\\\\d\\\\.,\\\\-\\\\+]+\"},\"parameters\":[\"reference\"]},\"register\":{\"uri\":\"register\",\"methods\":[\"GET\",\"HEAD\"]},\"login\":{\"uri\":\"login\",\"methods\":[\"GET\",\"HEAD\"]},\"password.request\":{\"uri\":\"forgot-password\",\"methods\":[\"GET\",\"HEAD\"]},\"password.email\":{\"uri\":\"forgot-password\",\"methods\":[\"POST\"]},\"password.reset\":{\"uri\":\"reset-password\\/{token}\",\"methods\":[\"GET\",\"HEAD\"],\"parameters\":[\"token\"]},\"password.store\":{\"uri\":\"reset-password\",\"methods\":[\"POST\"]},\"verification.notice\":{\"uri\":\"verify-email\",\"methods\":[\"GET\",\"HEAD\"]},\"verification.verify\":{\"uri\":\"verify-email\\/{id}\\/{hash}\",\"methods\":[\"GET\",\"HEAD\"],\"parameters\":[\"id\",\"hash\"]},\"verification.send\":{\"uri\":\"email\\/verification-notification\",\"methods\":[\"POST\"]},\"password.confirm\":{\"uri\":\"confirm-password\",\"methods\":[\"GET\",\"HEAD\"]},\"password.update\":{\"uri\":\"password\",\"methods\":[\"PUT\"]},\"logout\":{\"uri\":\"logout\",\"methods\":[\"POST\"]},\"api.chapters.fetch\":{\"uri\":\"api\\/chapters\\/fetch\",\"methods\":[\"GET\",\"HEAD\"]},\"api.books\":{\"uri\":\"api\\/books\",\"methods\":[\"GET\",\"HEAD\"]},\"api.books.content-status\":{\"uri\":\"api\\/books\\/content-status\",\"methods\":[\"GET\",\"HEAD\"]},\"api.books.show\":{\"uri\":\"api\\/books\\/{slug}\",\"methods\":[\"GET\",\"HEAD\"],\"parameters\":[\"slug\"]},\"api.chapters.adjacent\":{\"uri\":\"api\\/chapters\\/{reference}\\/adjacent\",\"methods\":[\"GET\",\"HEAD\"],\"wheres\":{\"reference\":\"[\\\\w\\\\d,\\\\-]+\"},\"parameters\":[\"reference\"]},\"api.search.books\":{\"uri\":\"api\\/search\\/books\",\"methods\":[\"GET\",\"HEAD\"]},\"api.search\":{\"uri\":\"api\\/search\",\"methods\":[\"GET\",\"HEAD\"]},\"api.bible.text\":{\"uri\":\"api\\/bible\\/{reference}\",\"methods\":[\"GET\",\"HEAD\"],\"wheres\":{\"reference\":\"[\\\\w\\\\d,\\\\-]+\"},\"parameters\":[\"reference\"]},\"storage.local\":{\"uri\":\"storage\\/{path}\",\"methods\":[\"GET\",\"HEAD\"],\"wheres\":{\"path\":\".*\"},\"parameters\":[\"path\"]}});</script>    <script type=\"module\" src=\"http://[::1]:5173/@vite/client\"></script><script type=\"module\" src=\"http://[::1]:5173/resources/js/app.ts\"></script><script type=\"module\" src=\"http://[::1]:5173/resources/js/Pages/NotFound.vue\"></script>    </head>\n\n<body class=\"font-sans antialiased\">\n    <div id=\"app\" data-page=\"{&quot;component&quot;:&quot;NotFound&quot;,&quot;props&quot;:{&quot;errors&quot;:{},&quot;books&quot;:{&quot;sections&quot;:[{&quot;name&quot;:&quot;Altes Testament&quot;,&quot;books&quot;:[{&quot;id&quot;:1,&quot;name&quot;:&quot;1. Mose&quot;,&quot;shortName&quot;:&quot;1Mo&quot;,&quot;chapterCount&quot;:50,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;law&quot;,&quot;order&quot;:1,&quot;slug&quot;:&quot;1.Mose&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:2,&quot;name&quot;:&quot;2. Mose&quot;,&quot;shortName&quot;:&quot;2Mo&quot;,&quot;chapterCount&quot;:40,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;law&quot;,&quot;order&quot;:2,&quot;slug&quot;:&quot;2.Mose&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:3,&quot;name&quot;:&quot;3. Mose&quot;,&quot;shortName&quot;:&quot;3Mo&quot;,&quot;chapterCount&quot;:27,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;law&quot;,&quot;order&quot;:3,&quot;slug&quot;:&quot;3.Mose&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:4,&quot;name&quot;:&quot;4. Mose&quot;,&quot;shortName&quot;:&quot;4Mo&quot;,&quot;chapterCount&quot;:36,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;law&quot;,&quot;order&quot;:4,&quot;slug&quot;:&quot;4.Mose&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:5,&quot;name&quot;:&quot;5. Mose&quot;,&quot;shortName&quot;:&quot;5Mo&quot;,&quot;chapterCount&quot;:34,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;law&quot;,&quot;order&quot;:5,&quot;slug&quot;:&quot;5.Mose&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:6,&quot;name&quot;:&quot;Josua&quot;,&quot;shortName&quot;:&quot;Jos&quot;,&quot;chapterCount&quot;:24,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:6,&quot;slug&quot;:&quot;Josua&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:7,&quot;name&quot;:&quot;Richter&quot;,&quot;shortName&quot;:&quot;Ri&quot;,&quot;chapterCount&quot;:21,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:7,&quot;slug&quot;:&quot;Richter&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:8,&quot;name&quot;:&quot;Ruth&quot;,&quot;shortName&quot;:&quot;Rt&quot;,&quot;chapterCount&quot;:4,&quot;chapters&quot;:[1,2,3,4],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:8,&quot;slug&quot;:&quot;Ruth&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:9,&quot;name&quot;:&quot;1. Samuel&quot;,&quot;shortName&quot;:&quot;1Sam&quot;,&quot;chapterCount&quot;:31,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:9,&quot;slug&quot;:&quot;1.Samuel&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:10,&quot;name&quot;:&quot;2. Samuel&quot;,&quot;shortName&quot;:&quot;2Sam&quot;,&quot;chapterCount&quot;:24,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:10,&quot;slug&quot;:&quot;2.Samuel&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:11,&quot;name&quot;:&quot;1. K\\u00f6nige&quot;,&quot;shortName&quot;:&quot;1K\\u00f6n&quot;,&quot;chapterCount&quot;:22,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:11,&quot;slug&quot;:&quot;1.K\\u00f6nige&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:12,&quot;name&quot;:&quot;2. K\\u00f6nige&quot;,&quot;shortName&quot;:&quot;2K\\u00f6n&quot;,&quot;chapterCount&quot;:25,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:12,&quot;slug&quot;:&quot;2.K\\u00f6nige&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:13,&quot;name&quot;:&quot;1. Chronik&quot;,&quot;shortName&quot;:&quot;1Chr&quot;,&quot;chapterCount&quot;:29,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:13,&quot;slug&quot;:&quot;1.Chronik&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:14,&quot;name&quot;:&quot;2. Chronik&quot;,&quot;shortName&quot;:&quot;2Chr&quot;,&quot;chapterCount&quot;:36,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:14,&quot;slug&quot;:&quot;2.Chronik&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:15,&quot;name&quot;:&quot;Esra&quot;,&quot;shortName&quot;:&quot;Esra&quot;,&quot;chapterCount&quot;:10,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:15,&quot;slug&quot;:&quot;Esra&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:16,&quot;name&quot;:&quot;Nehemia&quot;,&quot;shortName&quot;:&quot;Neh&quot;,&quot;chapterCount&quot;:13,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:16,&quot;slug&quot;:&quot;Nehemia&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:17,&quot;name&quot;:&quot;Esther&quot;,&quot;shortName&quot;:&quot;Est&quot;,&quot;chapterCount&quot;:10,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:17,&quot;slug&quot;:&quot;Esther&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:18,&quot;name&quot;:&quot;Hiob&quot;,&quot;shortName&quot;:&quot;Hi&quot;,&quot;chapterCount&quot;:42,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;wisdom&quot;,&quot;order&quot;:18,&quot;slug&quot;:&quot;Hiob&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:19,&quot;name&quot;:&quot;Psalmen&quot;,&quot;shortName&quot;:&quot;Ps&quot;,&quot;chapterCount&quot;:150,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;wisdom&quot;,&quot;order&quot;:19,&quot;slug&quot;:&quot;Psalmen&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:20,&quot;name&quot;:&quot;Spr\\u00fcche&quot;,&quot;shortName&quot;:&quot;Spr&quot;,&quot;chapterCount&quot;:31,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;wisdom&quot;,&quot;order&quot;:20,&quot;slug&quot;:&quot;Spr\\u00fcche&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:21,&quot;name&quot;:&quot;Prediger&quot;,&quot;shortName&quot;:&quot;Pred&quot;,&quot;chapterCount&quot;:12,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;wisdom&quot;,&quot;order&quot;:21,&quot;slug&quot;:&quot;Prediger&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:22,&quot;name&quot;:&quot;Hohelied&quot;,&quot;shortName&quot;:&quot;Hl&quot;,&quot;chapterCount&quot;:8,&quot;chapters&quot;:[1,2,3,4,5,6,7,8],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;wisdom&quot;,&quot;order&quot;:22,&quot;slug&quot;:&quot;Hohelied&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:23,&quot;name&quot;:&quot;Jesaja&quot;,&quot;shortName&quot;:&quot;Jes&quot;,&quot;chapterCount&quot;:66,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:23,&quot;slug&quot;:&quot;Jesaja&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:24,&quot;name&quot;:&quot;Jeremia&quot;,&quot;shortName&quot;:&quot;Jer&quot;,&quot;chapterCount&quot;:52,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:24,&quot;slug&quot;:&quot;Jeremia&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:25,&quot;name&quot;:&quot;Klagelieder&quot;,&quot;shortName&quot;:&quot;Kla&quot;,&quot;chapterCount&quot;:5,&quot;chapters&quot;:[1,2,3,4,5],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;wisdom&quot;,&quot;order&quot;:25,&quot;slug&quot;:&quot;Klagelieder&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:26,&quot;name&quot;:&quot;Hesekiel&quot;,&quot;shortName&quot;:&quot;Hes&quot;,&quot;chapterCount&quot;:48,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:26,&quot;slug&quot;:&quot;Hesekiel&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:27,&quot;name&quot;:&quot;Daniel&quot;,&quot;shortName&quot;:&quot;Dan&quot;,&quot;chapterCount&quot;:12,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:27,&quot;slug&quot;:&quot;Daniel&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:28,&quot;name&quot;:&quot;Hosea&quot;,&quot;shortName&quot;:&quot;Hos&quot;,&quot;chapterCount&quot;:14,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:28,&quot;slug&quot;:&quot;Hosea&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:29,&quot;name&quot;:&quot;Joel&quot;,&quot;shortName&quot;:&quot;Joel&quot;,&quot;chapterCount&quot;:3,&quot;chapters&quot;:[1,2,3],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:29,&quot;slug&quot;:&quot;Joel&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:30,&quot;name&quot;:&quot;Amos&quot;,&quot;shortName&quot;:&quot;Am&quot;,&quot;chapterCount&quot;:9,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:30,&quot;slug&quot;:&quot;Amos&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:31,&quot;name&quot;:&quot;Obadja&quot;,&quot;shortName&quot;:&quot;Ob&quot;,&quot;chapterCount&quot;:1,&quot;chapters&quot;:[1],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:31,&quot;slug&quot;:&quot;Obadja&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:32,&quot;name&quot;:&quot;Jona&quot;,&quot;shortName&quot;:&quot;Jon&quot;,&quot;chapterCount&quot;:4,&quot;chapters&quot;:[1,2,3,4],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:32,&quot;slug&quot;:&quot;Jona&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:33,&quot;name&quot;:&quot;Micha&quot;,&quot;shortName&quot;:&quot;Mi&quot;,&quot;chapterCount&quot;:7,&quot;chapters&quot;:[1,2,3,4,5,6,7],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:33,&quot;slug&quot;:&quot;Micha&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:34,&quot;name&quot;:&quot;Nahum&quot;,&quot;shortName&quot;:&quot;Nah&quot;,&quot;chapterCount&quot;:3,&quot;chapters&quot;:[1,2,3],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:34,&quot;slug&quot;:&quot;Nahum&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:35,&quot;name&quot;:&quot;Habakuk&quot;,&quot;shortName&quot;:&quot;Hab&quot;,&quot;chapterCount&quot;:3,&quot;chapters&quot;:[1,2,3],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:35,&quot;slug&quot;:&quot;Habakuk&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:36,&quot;name&quot;:&quot;Zephanja&quot;,&quot;shortName&quot;:&quot;Zeph&quot;,&quot;chapterCount&quot;:3,&quot;chapters&quot;:[1,2,3],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:36,&quot;slug&quot;:&quot;Zephanja&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:37,&quot;name&quot;:&quot;Haggai&quot;,&quot;shortName&quot;:&quot;Hag&quot;,&quot;chapterCount&quot;:2,&quot;chapters&quot;:[1,2],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:37,&quot;slug&quot;:&quot;Haggai&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:38,&quot;name&quot;:&quot;Sacharja&quot;,&quot;shortName&quot;:&quot;Sach&quot;,&quot;chapterCount&quot;:14,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:38,&quot;slug&quot;:&quot;Sacharja&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:39,&quot;name&quot;:&quot;Maleachi&quot;,&quot;shortName&quot;:&quot;Mal&quot;,&quot;chapterCount&quot;:4,&quot;chapters&quot;:[1,2,3,4],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:39,&quot;slug&quot;:&quot;Maleachi&quot;,&quot;hasContent&quot;:false}]},{&quot;name&quot;:&quot;Neues Testament&quot;,&quot;books&quot;:[{&quot;id&quot;:40,&quot;name&quot;:&quot;Matth\\u00e4us&quot;,&quot;shortName&quot;:&quot;Mt&quot;,&quot;chapterCount&quot;:28,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;gospel&quot;,&quot;order&quot;:40,&quot;slug&quot;:&quot;Matth\\u00e4us&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:41,&quot;name&quot;:&quot;Markus&quot;,&quot;shortName&quot;:&quot;Mk&quot;,&quot;chapterCount&quot;:16,&quot;chapters&quot;:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;gospel&quot;,&quot;order&quot;:41,&quot;slug&quot;:&quot;Markus&quot;,&quot;hasContent&quot;:true},{&quot;id&quot;:42,&quot;name&quot;:&quot;Lukas&quot;,&quot;shortName&quot;:&quot;Lk&quot;,&quot;chapterCount&quot;:24,&quot;chapters&quot;:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;gospel&quot;,&quot;order&quot;:42,&quot;slug&quot;:&quot;Lukas&quot;,&quot;hasContent&quot;:true},{&quot;id&quot;:43,&quot;name&quot;:&quot;Die Heilsbotschaft nach Johannes&quot;,&quot;shortName&quot;:&quot;Joh&quot;,&quot;chapterCount&quot;:21,&quot;chapters&quot;:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;gospel&quot;,&quot;order&quot;:43,&quot;slug&quot;:&quot;Johannes&quot;,&quot;hasContent&quot;:true},{&quot;id&quot;:44,&quot;name&quot;:&quot;Apostelgeschichte&quot;,&quot;shortName&quot;:&quot;Apg&quot;,&quot;chapterCount&quot;:28,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:44,&quot;slug&quot;:&quot;Apostelgeschichte&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:45,&quot;name&quot;:&quot;R\\u00f6mer&quot;,&quot;shortName&quot;:&quot;R\\u00f6m&quot;,&quot;chapterCount&quot;:16,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:45,&quot;slug&quot;:&quot;R\\u00f6mer&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:46,&quot;name&quot;:&quot;1. Korinther&quot;,&quot;shortName&quot;:&quot;1Kor&quot;,&quot;chapterCount&quot;:16,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:46,&quot;slug&quot;:&quot;1.Korinther&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:47,&quot;name&quot;:&quot;2. Korinther&quot;,&quot;shortName&quot;:&quot;2Kor&quot;,&quot;chapterCount&quot;:13,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:47,&quot;slug&quot;:&quot;2.Korinther&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:48,&quot;name&quot;:&quot;Galater&quot;,&quot;shortName&quot;:&quot;Gal&quot;,&quot;chapterCount&quot;:6,&quot;chapters&quot;:[1,2,3,4,5,6],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:48,&quot;slug&quot;:&quot;Galater&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:49,&quot;name&quot;:&quot;Epheser&quot;,&quot;shortName&quot;:&quot;Eph&quot;,&quot;chapterCount&quot;:6,&quot;chapters&quot;:[1,2,3,4,5,6],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:49,&quot;slug&quot;:&quot;Epheser&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:50,&quot;name&quot;:&quot;Philipper&quot;,&quot;shortName&quot;:&quot;Phil&quot;,&quot;chapterCount&quot;:4,&quot;chapters&quot;:[1,2,3,4],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:50,&quot;slug&quot;:&quot;Philipper&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:51,&quot;name&quot;:&quot;Kolosser&quot;,&quot;shortName&quot;:&quot;Kol&quot;,&quot;chapterCount&quot;:4,&quot;chapters&quot;:[1,2,3,4],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:51,&quot;slug&quot;:&quot;Kolosser&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:52,&quot;name&quot;:&quot;1. Thessalonicher&quot;,&quot;shortName&quot;:&quot;1Thes&quot;,&quot;chapterCount&quot;:5,&quot;chapters&quot;:[1,2,3,4,5],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:52,&quot;slug&quot;:&quot;1.Thessalonicher&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:53,&quot;name&quot;:&quot;2. Thessalonicher&quot;,&quot;shortName&quot;:&quot;2Thes&quot;,&quot;chapterCount&quot;:3,&quot;chapters&quot;:[1,2,3],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:53,&quot;slug&quot;:&quot;2.Thessalonicher&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:54,&quot;name&quot;:&quot;1. Timotheus&quot;,&quot;shortName&quot;:&quot;1Tim&quot;,&quot;chapterCount&quot;:6,&quot;chapters&quot;:[1,2,3,4,5,6],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:54,&quot;slug&quot;:&quot;1.Timotheus&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:55,&quot;name&quot;:&quot;2. Timotheus&quot;,&quot;shortName&quot;:&quot;2Tim&quot;,&quot;chapterCount&quot;:4,&quot;chapters&quot;:[1,2,3,4],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:55,&quot;slug&quot;:&quot;2.Timotheus&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:56,&quot;name&quot;:&quot;Titus&quot;,&quot;shortName&quot;:&quot;Tit&quot;,&quot;chapterCount&quot;:3,&quot;chapters&quot;:[1,2,3],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:56,&quot;slug&quot;:&quot;Titus&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:57,&quot;name&quot;:&quot;Philemon&quot;,&quot;shortName&quot;:&quot;Phim&quot;,&quot;chapterCount&quot;:1,&quot;chapters&quot;:[1],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:57,&quot;slug&quot;:&quot;Philemon&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:58,&quot;name&quot;:&quot;Hebr\\u00e4er&quot;,&quot;shortName&quot;:&quot;Heb&quot;,&quot;chapterCount&quot;:13,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:58,&quot;slug&quot;:&quot;Hebr\\u00e4er&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:59,&quot;name&quot;:&quot;Jakobus&quot;,&quot;shortName&quot;:&quot;Jak&quot;,&quot;chapterCount&quot;:5,&quot;chapters&quot;:[1,2,3,4,5],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:59,&quot;slug&quot;:&quot;Jakobus&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:60,&quot;name&quot;:&quot;1. Petrus&quot;,&quot;shortName&quot;:&quot;1Pet&quot;,&quot;chapterCount&quot;:5,&quot;chapters&quot;:[1,2,3,4,5],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:60,&quot;slug&quot;:&quot;1.Petrus&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:61,&quot;name&quot;:&quot;2. Petrus&quot;,&quot;shortName&quot;:&quot;2Pet&quot;,&quot;chapterCount&quot;:3,&quot;chapters&quot;:[1,2,3],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:61,&quot;slug&quot;:&quot;2.Petrus&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:62,&quot;name&quot;:&quot;1. Johannes&quot;,&quot;shortName&quot;:&quot;1Joh&quot;,&quot;chapterCount&quot;:5,&quot;chapters&quot;:[1,2,3,4,5],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:62,&quot;slug&quot;:&quot;1.Johannes&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:63,&quot;name&quot;:&quot;2. Johannes&quot;,&quot;shortName&quot;:&quot;2Joh&quot;,&quot;chapterCount&quot;:1,&quot;chapters&quot;:[1],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:63,&quot;slug&quot;:&quot;2.Johannes&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:64,&quot;name&quot;:&quot;3. Johannes&quot;,&quot;shortName&quot;:&quot;3Joh&quot;,&quot;chapterCount&quot;:1,&quot;chapters&quot;:[1],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:64,&quot;slug&quot;:&quot;3.Johannes&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:65,&quot;name&quot;:&quot;Judas&quot;,&quot;shortName&quot;:&quot;Jud&quot;,&quot;chapterCount&quot;:1,&quot;chapters&quot;:[1],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:65,&quot;slug&quot;:&quot;Judas&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:66,&quot;name&quot;:&quot;Offenbarung&quot;,&quot;shortName&quot;:&quot;Offb&quot;,&quot;chapterCount&quot;:22,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;apocalypse&quot;,&quot;order&quot;:66,&quot;slug&quot;:&quot;Offenbarung&quot;,&quot;hasContent&quot;:false}]}],&quot;availableBooks&quot;:[{&quot;slug&quot;:&quot;Markus&quot;,&quot;order&quot;:41},{&quot;slug&quot;:&quot;Lukas&quot;,&quot;order&quot;:42},{&quot;slug&quot;:&quot;Johannes&quot;,&quot;order&quot;:43}]},&quot;env&quot;:&quot;local&quot;,&quot;auth&quot;:{&quot;user&quot;:null},&quot;requestedPath&quot;:&quot;api\\/books&quot;},&quot;url&quot;:&quot;\\/api\\/books&quot;,&quot;version&quot;:&quot;18765f3fa436d07c5ef1cbbbc3fa3b37&quot;,&quot;clearHistory&quot;:false,&quot;encryptHistory&quot;:false}\"></div></body>\n\n</html>\n"
      tags:
        - Endpoints
      security: []
  /api/books/content-status:
    get:
      summary: 'Get content status for all books'
      operationId: getContentStatusForAllBooks
      description: ''
      parameters: []
      responses:
        404:
          description: ''
          content:
            text/plain:
              schema:
                type: string
                example: "<!DOCTYPE html>\n<html lang=\"de\">\n\n<head>\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\n\n    <title inertia></title>\n\n    <!-- Fonts -->\n    <!-- Preconnect to font domains -->\n    <link rel=\"preconnect\" href=\"https://fonts.bunny.net\" crossorigin>\n    <link rel=\"preconnect\" href=\"https://use.typekit.net\" as=\"font\" type=\"font/woff2\" crossorigin>\n\n    <!-- Preload critical fonts -->\n    <link rel=\"preload\" href=\"/fonts/ThanatosText-Book.woff2\" as=\"font\" type=\"font/woff2\" crossorigin>\n\n    <!-- Load fonts -->\n    <link href=\"https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap\" rel=\"stylesheet\" />\n    <link rel=\"stylesheet\" href=\"https://use.typekit.net/kzb8yhl.css\" media=\"print\" onload=\"this.media='all'\">\n\n    <!-- Fallback for typekit fonts -->\n    <noscript>\n        <link rel=\"stylesheet\" href=\"https://use.typekit.net/kzb8yhl.css\">\n    </noscript>\n\n    <!-- Local font definition -->\n    <style>\n        @font-face {\n            font-family: 'ThanatosText';\n            src: url('/fonts/ThanatosText-Book.woff2') format('woff2');\n            font-weight: normal;\n            font-style: normal;\n            font-display: swap;\n        }\n    </style>\n\n    <!-- Scripts -->\n    <script type=\"text/javascript\">Object.assign(Ziggy.routes,{\"search.index\":{\"uri\":\"search\",\"methods\":[\"GET\",\"HEAD\"]},\"search.query\":{\"uri\":\"search\\/{query}\",\"methods\":[\"GET\",\"HEAD\"],\"wheres\":{\"query\":\"[^\\/]+\"},\"parameters\":[\"query\"]},\"search.paged\":{\"uri\":\"search\\/{query}\\/{page?}\",\"methods\":[\"GET\",\"HEAD\"],\"wheres\":{\"query\":\"[^\\/]+\",\"page\":\"[0-9]+\"},\"parameters\":[\"query\",\"page\"]},\"search.settings\":{\"uri\":\"search\\/settings\",\"methods\":[\"POST\"]},\"dashboard\":{\"uri\":\"dashboard\",\"methods\":[\"GET\",\"HEAD\"]},\"bible.import\":{\"uri\":\"import-bible\",\"methods\":[\"GET\",\"HEAD\"]},\"bible.import.store\":{\"uri\":\"import-bible\",\"methods\":[\"POST\"]},\"file.upload\":{\"uri\":\"api\\/upload\",\"methods\":[\"POST\"]},\"profile.edit\":{\"uri\":\"profile\",\"methods\":[\"GET\",\"HEAD\"]},\"profile.update\":{\"uri\":\"profile\",\"methods\":[\"PATCH\"]},\"profile.destroy\":{\"uri\":\"profile\",\"methods\":[\"DELETE\"]},\"books.show\":{\"uri\":\"{reference}\",\"methods\":[\"GET\",\"HEAD\"],\"wheres\":{\"reference\":\"[\\\\w\\\\d\\\\.,\\\\-\\\\+]+\"},\"parameters\":[\"reference\"]},\"register\":{\"uri\":\"register\",\"methods\":[\"GET\",\"HEAD\"]},\"login\":{\"uri\":\"login\",\"methods\":[\"GET\",\"HEAD\"]},\"password.request\":{\"uri\":\"forgot-password\",\"methods\":[\"GET\",\"HEAD\"]},\"password.email\":{\"uri\":\"forgot-password\",\"methods\":[\"POST\"]},\"password.reset\":{\"uri\":\"reset-password\\/{token}\",\"methods\":[\"GET\",\"HEAD\"],\"parameters\":[\"token\"]},\"password.store\":{\"uri\":\"reset-password\",\"methods\":[\"POST\"]},\"verification.notice\":{\"uri\":\"verify-email\",\"methods\":[\"GET\",\"HEAD\"]},\"verification.verify\":{\"uri\":\"verify-email\\/{id}\\/{hash}\",\"methods\":[\"GET\",\"HEAD\"],\"parameters\":[\"id\",\"hash\"]},\"verification.send\":{\"uri\":\"email\\/verification-notification\",\"methods\":[\"POST\"]},\"password.confirm\":{\"uri\":\"confirm-password\",\"methods\":[\"GET\",\"HEAD\"]},\"password.update\":{\"uri\":\"password\",\"methods\":[\"PUT\"]},\"logout\":{\"uri\":\"logout\",\"methods\":[\"POST\"]},\"api.chapters.fetch\":{\"uri\":\"api\\/chapters\\/fetch\",\"methods\":[\"GET\",\"HEAD\"]},\"api.books\":{\"uri\":\"api\\/books\",\"methods\":[\"GET\",\"HEAD\"]},\"api.books.content-status\":{\"uri\":\"api\\/books\\/content-status\",\"methods\":[\"GET\",\"HEAD\"]},\"api.books.show\":{\"uri\":\"api\\/books\\/{slug}\",\"methods\":[\"GET\",\"HEAD\"],\"parameters\":[\"slug\"]},\"api.chapters.adjacent\":{\"uri\":\"api\\/chapters\\/{reference}\\/adjacent\",\"methods\":[\"GET\",\"HEAD\"],\"wheres\":{\"reference\":\"[\\\\w\\\\d,\\\\-]+\"},\"parameters\":[\"reference\"]},\"api.search.books\":{\"uri\":\"api\\/search\\/books\",\"methods\":[\"GET\",\"HEAD\"]},\"api.search\":{\"uri\":\"api\\/search\",\"methods\":[\"GET\",\"HEAD\"]},\"api.bible.text\":{\"uri\":\"api\\/bible\\/{reference}\",\"methods\":[\"GET\",\"HEAD\"],\"wheres\":{\"reference\":\"[\\\\w\\\\d,\\\\-]+\"},\"parameters\":[\"reference\"]},\"storage.local\":{\"uri\":\"storage\\/{path}\",\"methods\":[\"GET\",\"HEAD\"],\"wheres\":{\"path\":\".*\"},\"parameters\":[\"path\"]}});</script>    <script type=\"module\" src=\"http://[::1]:5173/@vite/client\"></script><script type=\"module\" src=\"http://[::1]:5173/resources/js/app.ts\"></script><script type=\"module\" src=\"http://[::1]:5173/resources/js/Pages/NotFound.vue\"></script>    </head>\n\n<body class=\"font-sans antialiased\">\n    <div id=\"app\" data-page=\"{&quot;component&quot;:&quot;NotFound&quot;,&quot;props&quot;:{&quot;errors&quot;:{},&quot;books&quot;:{&quot;sections&quot;:[{&quot;name&quot;:&quot;Altes Testament&quot;,&quot;books&quot;:[{&quot;id&quot;:1,&quot;name&quot;:&quot;1. Mose&quot;,&quot;shortName&quot;:&quot;1Mo&quot;,&quot;chapterCount&quot;:50,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;law&quot;,&quot;order&quot;:1,&quot;slug&quot;:&quot;1.Mose&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:2,&quot;name&quot;:&quot;2. Mose&quot;,&quot;shortName&quot;:&quot;2Mo&quot;,&quot;chapterCount&quot;:40,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;law&quot;,&quot;order&quot;:2,&quot;slug&quot;:&quot;2.Mose&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:3,&quot;name&quot;:&quot;3. Mose&quot;,&quot;shortName&quot;:&quot;3Mo&quot;,&quot;chapterCount&quot;:27,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;law&quot;,&quot;order&quot;:3,&quot;slug&quot;:&quot;3.Mose&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:4,&quot;name&quot;:&quot;4. Mose&quot;,&quot;shortName&quot;:&quot;4Mo&quot;,&quot;chapterCount&quot;:36,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;law&quot;,&quot;order&quot;:4,&quot;slug&quot;:&quot;4.Mose&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:5,&quot;name&quot;:&quot;5. Mose&quot;,&quot;shortName&quot;:&quot;5Mo&quot;,&quot;chapterCount&quot;:34,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;law&quot;,&quot;order&quot;:5,&quot;slug&quot;:&quot;5.Mose&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:6,&quot;name&quot;:&quot;Josua&quot;,&quot;shortName&quot;:&quot;Jos&quot;,&quot;chapterCount&quot;:24,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:6,&quot;slug&quot;:&quot;Josua&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:7,&quot;name&quot;:&quot;Richter&quot;,&quot;shortName&quot;:&quot;Ri&quot;,&quot;chapterCount&quot;:21,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:7,&quot;slug&quot;:&quot;Richter&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:8,&quot;name&quot;:&quot;Ruth&quot;,&quot;shortName&quot;:&quot;Rt&quot;,&quot;chapterCount&quot;:4,&quot;chapters&quot;:[1,2,3,4],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:8,&quot;slug&quot;:&quot;Ruth&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:9,&quot;name&quot;:&quot;1. Samuel&quot;,&quot;shortName&quot;:&quot;1Sam&quot;,&quot;chapterCount&quot;:31,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:9,&quot;slug&quot;:&quot;1.Samuel&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:10,&quot;name&quot;:&quot;2. Samuel&quot;,&quot;shortName&quot;:&quot;2Sam&quot;,&quot;chapterCount&quot;:24,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:10,&quot;slug&quot;:&quot;2.Samuel&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:11,&quot;name&quot;:&quot;1. K\\u00f6nige&quot;,&quot;shortName&quot;:&quot;1K\\u00f6n&quot;,&quot;chapterCount&quot;:22,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:11,&quot;slug&quot;:&quot;1.K\\u00f6nige&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:12,&quot;name&quot;:&quot;2. K\\u00f6nige&quot;,&quot;shortName&quot;:&quot;2K\\u00f6n&quot;,&quot;chapterCount&quot;:25,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:12,&quot;slug&quot;:&quot;2.K\\u00f6nige&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:13,&quot;name&quot;:&quot;1. Chronik&quot;,&quot;shortName&quot;:&quot;1Chr&quot;,&quot;chapterCount&quot;:29,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:13,&quot;slug&quot;:&quot;1.Chronik&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:14,&quot;name&quot;:&quot;2. Chronik&quot;,&quot;shortName&quot;:&quot;2Chr&quot;,&quot;chapterCount&quot;:36,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:14,&quot;slug&quot;:&quot;2.Chronik&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:15,&quot;name&quot;:&quot;Esra&quot;,&quot;shortName&quot;:&quot;Esra&quot;,&quot;chapterCount&quot;:10,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:15,&quot;slug&quot;:&quot;Esra&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:16,&quot;name&quot;:&quot;Nehemia&quot;,&quot;shortName&quot;:&quot;Neh&quot;,&quot;chapterCount&quot;:13,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:16,&quot;slug&quot;:&quot;Nehemia&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:17,&quot;name&quot;:&quot;Esther&quot;,&quot;shortName&quot;:&quot;Est&quot;,&quot;chapterCount&quot;:10,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:17,&quot;slug&quot;:&quot;Esther&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:18,&quot;name&quot;:&quot;Hiob&quot;,&quot;shortName&quot;:&quot;Hi&quot;,&quot;chapterCount&quot;:42,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;wisdom&quot;,&quot;order&quot;:18,&quot;slug&quot;:&quot;Hiob&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:19,&quot;name&quot;:&quot;Psalmen&quot;,&quot;shortName&quot;:&quot;Ps&quot;,&quot;chapterCount&quot;:150,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;wisdom&quot;,&quot;order&quot;:19,&quot;slug&quot;:&quot;Psalmen&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:20,&quot;name&quot;:&quot;Spr\\u00fcche&quot;,&quot;shortName&quot;:&quot;Spr&quot;,&quot;chapterCount&quot;:31,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;wisdom&quot;,&quot;order&quot;:20,&quot;slug&quot;:&quot;Spr\\u00fcche&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:21,&quot;name&quot;:&quot;Prediger&quot;,&quot;shortName&quot;:&quot;Pred&quot;,&quot;chapterCount&quot;:12,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;wisdom&quot;,&quot;order&quot;:21,&quot;slug&quot;:&quot;Prediger&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:22,&quot;name&quot;:&quot;Hohelied&quot;,&quot;shortName&quot;:&quot;Hl&quot;,&quot;chapterCount&quot;:8,&quot;chapters&quot;:[1,2,3,4,5,6,7,8],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;wisdom&quot;,&quot;order&quot;:22,&quot;slug&quot;:&quot;Hohelied&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:23,&quot;name&quot;:&quot;Jesaja&quot;,&quot;shortName&quot;:&quot;Jes&quot;,&quot;chapterCount&quot;:66,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:23,&quot;slug&quot;:&quot;Jesaja&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:24,&quot;name&quot;:&quot;Jeremia&quot;,&quot;shortName&quot;:&quot;Jer&quot;,&quot;chapterCount&quot;:52,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:24,&quot;slug&quot;:&quot;Jeremia&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:25,&quot;name&quot;:&quot;Klagelieder&quot;,&quot;shortName&quot;:&quot;Kla&quot;,&quot;chapterCount&quot;:5,&quot;chapters&quot;:[1,2,3,4,5],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;wisdom&quot;,&quot;order&quot;:25,&quot;slug&quot;:&quot;Klagelieder&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:26,&quot;name&quot;:&quot;Hesekiel&quot;,&quot;shortName&quot;:&quot;Hes&quot;,&quot;chapterCount&quot;:48,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:26,&quot;slug&quot;:&quot;Hesekiel&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:27,&quot;name&quot;:&quot;Daniel&quot;,&quot;shortName&quot;:&quot;Dan&quot;,&quot;chapterCount&quot;:12,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:27,&quot;slug&quot;:&quot;Daniel&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:28,&quot;name&quot;:&quot;Hosea&quot;,&quot;shortName&quot;:&quot;Hos&quot;,&quot;chapterCount&quot;:14,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:28,&quot;slug&quot;:&quot;Hosea&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:29,&quot;name&quot;:&quot;Joel&quot;,&quot;shortName&quot;:&quot;Joel&quot;,&quot;chapterCount&quot;:3,&quot;chapters&quot;:[1,2,3],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:29,&quot;slug&quot;:&quot;Joel&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:30,&quot;name&quot;:&quot;Amos&quot;,&quot;shortName&quot;:&quot;Am&quot;,&quot;chapterCount&quot;:9,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:30,&quot;slug&quot;:&quot;Amos&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:31,&quot;name&quot;:&quot;Obadja&quot;,&quot;shortName&quot;:&quot;Ob&quot;,&quot;chapterCount&quot;:1,&quot;chapters&quot;:[1],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:31,&quot;slug&quot;:&quot;Obadja&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:32,&quot;name&quot;:&quot;Jona&quot;,&quot;shortName&quot;:&quot;Jon&quot;,&quot;chapterCount&quot;:4,&quot;chapters&quot;:[1,2,3,4],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:32,&quot;slug&quot;:&quot;Jona&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:33,&quot;name&quot;:&quot;Micha&quot;,&quot;shortName&quot;:&quot;Mi&quot;,&quot;chapterCount&quot;:7,&quot;chapters&quot;:[1,2,3,4,5,6,7],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:33,&quot;slug&quot;:&quot;Micha&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:34,&quot;name&quot;:&quot;Nahum&quot;,&quot;shortName&quot;:&quot;Nah&quot;,&quot;chapterCount&quot;:3,&quot;chapters&quot;:[1,2,3],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:34,&quot;slug&quot;:&quot;Nahum&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:35,&quot;name&quot;:&quot;Habakuk&quot;,&quot;shortName&quot;:&quot;Hab&quot;,&quot;chapterCount&quot;:3,&quot;chapters&quot;:[1,2,3],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:35,&quot;slug&quot;:&quot;Habakuk&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:36,&quot;name&quot;:&quot;Zephanja&quot;,&quot;shortName&quot;:&quot;Zeph&quot;,&quot;chapterCount&quot;:3,&quot;chapters&quot;:[1,2,3],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:36,&quot;slug&quot;:&quot;Zephanja&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:37,&quot;name&quot;:&quot;Haggai&quot;,&quot;shortName&quot;:&quot;Hag&quot;,&quot;chapterCount&quot;:2,&quot;chapters&quot;:[1,2],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:37,&quot;slug&quot;:&quot;Haggai&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:38,&quot;name&quot;:&quot;Sacharja&quot;,&quot;shortName&quot;:&quot;Sach&quot;,&quot;chapterCount&quot;:14,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:38,&quot;slug&quot;:&quot;Sacharja&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:39,&quot;name&quot;:&quot;Maleachi&quot;,&quot;shortName&quot;:&quot;Mal&quot;,&quot;chapterCount&quot;:4,&quot;chapters&quot;:[1,2,3,4],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:39,&quot;slug&quot;:&quot;Maleachi&quot;,&quot;hasContent&quot;:false}]},{&quot;name&quot;:&quot;Neues Testament&quot;,&quot;books&quot;:[{&quot;id&quot;:40,&quot;name&quot;:&quot;Matth\\u00e4us&quot;,&quot;shortName&quot;:&quot;Mt&quot;,&quot;chapterCount&quot;:28,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;gospel&quot;,&quot;order&quot;:40,&quot;slug&quot;:&quot;Matth\\u00e4us&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:41,&quot;name&quot;:&quot;Markus&quot;,&quot;shortName&quot;:&quot;Mk&quot;,&quot;chapterCount&quot;:16,&quot;chapters&quot;:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;gospel&quot;,&quot;order&quot;:41,&quot;slug&quot;:&quot;Markus&quot;,&quot;hasContent&quot;:true},{&quot;id&quot;:42,&quot;name&quot;:&quot;Lukas&quot;,&quot;shortName&quot;:&quot;Lk&quot;,&quot;chapterCount&quot;:24,&quot;chapters&quot;:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;gospel&quot;,&quot;order&quot;:42,&quot;slug&quot;:&quot;Lukas&quot;,&quot;hasContent&quot;:true},{&quot;id&quot;:43,&quot;name&quot;:&quot;Die Heilsbotschaft nach Johannes&quot;,&quot;shortName&quot;:&quot;Joh&quot;,&quot;chapterCount&quot;:21,&quot;chapters&quot;:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;gospel&quot;,&quot;order&quot;:43,&quot;slug&quot;:&quot;Johannes&quot;,&quot;hasContent&quot;:true},{&quot;id&quot;:44,&quot;name&quot;:&quot;Apostelgeschichte&quot;,&quot;shortName&quot;:&quot;Apg&quot;,&quot;chapterCount&quot;:28,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:44,&quot;slug&quot;:&quot;Apostelgeschichte&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:45,&quot;name&quot;:&quot;R\\u00f6mer&quot;,&quot;shortName&quot;:&quot;R\\u00f6m&quot;,&quot;chapterCount&quot;:16,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:45,&quot;slug&quot;:&quot;R\\u00f6mer&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:46,&quot;name&quot;:&quot;1. Korinther&quot;,&quot;shortName&quot;:&quot;1Kor&quot;,&quot;chapterCount&quot;:16,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:46,&quot;slug&quot;:&quot;1.Korinther&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:47,&quot;name&quot;:&quot;2. Korinther&quot;,&quot;shortName&quot;:&quot;2Kor&quot;,&quot;chapterCount&quot;:13,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:47,&quot;slug&quot;:&quot;2.Korinther&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:48,&quot;name&quot;:&quot;Galater&quot;,&quot;shortName&quot;:&quot;Gal&quot;,&quot;chapterCount&quot;:6,&quot;chapters&quot;:[1,2,3,4,5,6],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:48,&quot;slug&quot;:&quot;Galater&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:49,&quot;name&quot;:&quot;Epheser&quot;,&quot;shortName&quot;:&quot;Eph&quot;,&quot;chapterCount&quot;:6,&quot;chapters&quot;:[1,2,3,4,5,6],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:49,&quot;slug&quot;:&quot;Epheser&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:50,&quot;name&quot;:&quot;Philipper&quot;,&quot;shortName&quot;:&quot;Phil&quot;,&quot;chapterCount&quot;:4,&quot;chapters&quot;:[1,2,3,4],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:50,&quot;slug&quot;:&quot;Philipper&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:51,&quot;name&quot;:&quot;Kolosser&quot;,&quot;shortName&quot;:&quot;Kol&quot;,&quot;chapterCount&quot;:4,&quot;chapters&quot;:[1,2,3,4],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:51,&quot;slug&quot;:&quot;Kolosser&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:52,&quot;name&quot;:&quot;1. Thessalonicher&quot;,&quot;shortName&quot;:&quot;1Thes&quot;,&quot;chapterCount&quot;:5,&quot;chapters&quot;:[1,2,3,4,5],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:52,&quot;slug&quot;:&quot;1.Thessalonicher&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:53,&quot;name&quot;:&quot;2. Thessalonicher&quot;,&quot;shortName&quot;:&quot;2Thes&quot;,&quot;chapterCount&quot;:3,&quot;chapters&quot;:[1,2,3],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:53,&quot;slug&quot;:&quot;2.Thessalonicher&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:54,&quot;name&quot;:&quot;1. Timotheus&quot;,&quot;shortName&quot;:&quot;1Tim&quot;,&quot;chapterCount&quot;:6,&quot;chapters&quot;:[1,2,3,4,5,6],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:54,&quot;slug&quot;:&quot;1.Timotheus&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:55,&quot;name&quot;:&quot;2. Timotheus&quot;,&quot;shortName&quot;:&quot;2Tim&quot;,&quot;chapterCount&quot;:4,&quot;chapters&quot;:[1,2,3,4],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:55,&quot;slug&quot;:&quot;2.Timotheus&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:56,&quot;name&quot;:&quot;Titus&quot;,&quot;shortName&quot;:&quot;Tit&quot;,&quot;chapterCount&quot;:3,&quot;chapters&quot;:[1,2,3],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:56,&quot;slug&quot;:&quot;Titus&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:57,&quot;name&quot;:&quot;Philemon&quot;,&quot;shortName&quot;:&quot;Phim&quot;,&quot;chapterCount&quot;:1,&quot;chapters&quot;:[1],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:57,&quot;slug&quot;:&quot;Philemon&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:58,&quot;name&quot;:&quot;Hebr\\u00e4er&quot;,&quot;shortName&quot;:&quot;Heb&quot;,&quot;chapterCount&quot;:13,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:58,&quot;slug&quot;:&quot;Hebr\\u00e4er&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:59,&quot;name&quot;:&quot;Jakobus&quot;,&quot;shortName&quot;:&quot;Jak&quot;,&quot;chapterCount&quot;:5,&quot;chapters&quot;:[1,2,3,4,5],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:59,&quot;slug&quot;:&quot;Jakobus&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:60,&quot;name&quot;:&quot;1. Petrus&quot;,&quot;shortName&quot;:&quot;1Pet&quot;,&quot;chapterCount&quot;:5,&quot;chapters&quot;:[1,2,3,4,5],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:60,&quot;slug&quot;:&quot;1.Petrus&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:61,&quot;name&quot;:&quot;2. Petrus&quot;,&quot;shortName&quot;:&quot;2Pet&quot;,&quot;chapterCount&quot;:3,&quot;chapters&quot;:[1,2,3],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:61,&quot;slug&quot;:&quot;2.Petrus&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:62,&quot;name&quot;:&quot;1. Johannes&quot;,&quot;shortName&quot;:&quot;1Joh&quot;,&quot;chapterCount&quot;:5,&quot;chapters&quot;:[1,2,3,4,5],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:62,&quot;slug&quot;:&quot;1.Johannes&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:63,&quot;name&quot;:&quot;2. Johannes&quot;,&quot;shortName&quot;:&quot;2Joh&quot;,&quot;chapterCount&quot;:1,&quot;chapters&quot;:[1],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:63,&quot;slug&quot;:&quot;2.Johannes&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:64,&quot;name&quot;:&quot;3. Johannes&quot;,&quot;shortName&quot;:&quot;3Joh&quot;,&quot;chapterCount&quot;:1,&quot;chapters&quot;:[1],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:64,&quot;slug&quot;:&quot;3.Johannes&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:65,&quot;name&quot;:&quot;Judas&quot;,&quot;shortName&quot;:&quot;Jud&quot;,&quot;chapterCount&quot;:1,&quot;chapters&quot;:[1],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:65,&quot;slug&quot;:&quot;Judas&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:66,&quot;name&quot;:&quot;Offenbarung&quot;,&quot;shortName&quot;:&quot;Offb&quot;,&quot;chapterCount&quot;:22,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;apocalypse&quot;,&quot;order&quot;:66,&quot;slug&quot;:&quot;Offenbarung&quot;,&quot;hasContent&quot;:false}]}],&quot;availableBooks&quot;:[{&quot;slug&quot;:&quot;Markus&quot;,&quot;order&quot;:41},{&quot;slug&quot;:&quot;Lukas&quot;,&quot;order&quot;:42},{&quot;slug&quot;:&quot;Johannes&quot;,&quot;order&quot;:43}]},&quot;env&quot;:&quot;local&quot;,&quot;auth&quot;:{&quot;user&quot;:null},&quot;requestedPath&quot;:&quot;api\\/books\\/content-status&quot;},&quot;url&quot;:&quot;\\/api\\/books\\/content-status&quot;,&quot;version&quot;:&quot;18765f3fa436d07c5ef1cbbbc3fa3b37&quot;,&quot;clearHistory&quot;:false,&quot;encryptHistory&quot;:false}\"></div></body>\n\n</html>\n"
      tags:
        - Endpoints
      security: []
  '/api/books/{slug}':
    get:
      summary: 'Get a single book by slug with all its metadata'
      operationId: getASingleBookBySlugWithAllItsMetadata
      description: ''
      parameters: []
      responses:
        404:
          description: ''
          content:
            text/plain:
              schema:
                type: string
                example: "<!DOCTYPE html>\n<html lang=\"de\">\n\n<head>\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\n\n    <title inertia></title>\n\n    <!-- Fonts -->\n    <!-- Preconnect to font domains -->\n    <link rel=\"preconnect\" href=\"https://fonts.bunny.net\" crossorigin>\n    <link rel=\"preconnect\" href=\"https://use.typekit.net\" as=\"font\" type=\"font/woff2\" crossorigin>\n\n    <!-- Preload critical fonts -->\n    <link rel=\"preload\" href=\"/fonts/ThanatosText-Book.woff2\" as=\"font\" type=\"font/woff2\" crossorigin>\n\n    <!-- Load fonts -->\n    <link href=\"https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap\" rel=\"stylesheet\" />\n    <link rel=\"stylesheet\" href=\"https://use.typekit.net/kzb8yhl.css\" media=\"print\" onload=\"this.media='all'\">\n\n    <!-- Fallback for typekit fonts -->\n    <noscript>\n        <link rel=\"stylesheet\" href=\"https://use.typekit.net/kzb8yhl.css\">\n    </noscript>\n\n    <!-- Local font definition -->\n    <style>\n        @font-face {\n            font-family: 'ThanatosText';\n            src: url('/fonts/ThanatosText-Book.woff2') format('woff2');\n            font-weight: normal;\n            font-style: normal;\n            font-display: swap;\n        }\n    </style>\n\n    <!-- Scripts -->\n    <script type=\"text/javascript\">Object.assign(Ziggy.routes,{\"search.index\":{\"uri\":\"search\",\"methods\":[\"GET\",\"HEAD\"]},\"search.query\":{\"uri\":\"search\\/{query}\",\"methods\":[\"GET\",\"HEAD\"],\"wheres\":{\"query\":\"[^\\/]+\"},\"parameters\":[\"query\"]},\"search.paged\":{\"uri\":\"search\\/{query}\\/{page?}\",\"methods\":[\"GET\",\"HEAD\"],\"wheres\":{\"query\":\"[^\\/]+\",\"page\":\"[0-9]+\"},\"parameters\":[\"query\",\"page\"]},\"search.settings\":{\"uri\":\"search\\/settings\",\"methods\":[\"POST\"]},\"dashboard\":{\"uri\":\"dashboard\",\"methods\":[\"GET\",\"HEAD\"]},\"bible.import\":{\"uri\":\"import-bible\",\"methods\":[\"GET\",\"HEAD\"]},\"bible.import.store\":{\"uri\":\"import-bible\",\"methods\":[\"POST\"]},\"file.upload\":{\"uri\":\"api\\/upload\",\"methods\":[\"POST\"]},\"profile.edit\":{\"uri\":\"profile\",\"methods\":[\"GET\",\"HEAD\"]},\"profile.update\":{\"uri\":\"profile\",\"methods\":[\"PATCH\"]},\"profile.destroy\":{\"uri\":\"profile\",\"methods\":[\"DELETE\"]},\"books.show\":{\"uri\":\"{reference}\",\"methods\":[\"GET\",\"HEAD\"],\"wheres\":{\"reference\":\"[\\\\w\\\\d\\\\.,\\\\-\\\\+]+\"},\"parameters\":[\"reference\"]},\"register\":{\"uri\":\"register\",\"methods\":[\"GET\",\"HEAD\"]},\"login\":{\"uri\":\"login\",\"methods\":[\"GET\",\"HEAD\"]},\"password.request\":{\"uri\":\"forgot-password\",\"methods\":[\"GET\",\"HEAD\"]},\"password.email\":{\"uri\":\"forgot-password\",\"methods\":[\"POST\"]},\"password.reset\":{\"uri\":\"reset-password\\/{token}\",\"methods\":[\"GET\",\"HEAD\"],\"parameters\":[\"token\"]},\"password.store\":{\"uri\":\"reset-password\",\"methods\":[\"POST\"]},\"verification.notice\":{\"uri\":\"verify-email\",\"methods\":[\"GET\",\"HEAD\"]},\"verification.verify\":{\"uri\":\"verify-email\\/{id}\\/{hash}\",\"methods\":[\"GET\",\"HEAD\"],\"parameters\":[\"id\",\"hash\"]},\"verification.send\":{\"uri\":\"email\\/verification-notification\",\"methods\":[\"POST\"]},\"password.confirm\":{\"uri\":\"confirm-password\",\"methods\":[\"GET\",\"HEAD\"]},\"password.update\":{\"uri\":\"password\",\"methods\":[\"PUT\"]},\"logout\":{\"uri\":\"logout\",\"methods\":[\"POST\"]},\"api.chapters.fetch\":{\"uri\":\"api\\/chapters\\/fetch\",\"methods\":[\"GET\",\"HEAD\"]},\"api.books\":{\"uri\":\"api\\/books\",\"methods\":[\"GET\",\"HEAD\"]},\"api.books.content-status\":{\"uri\":\"api\\/books\\/content-status\",\"methods\":[\"GET\",\"HEAD\"]},\"api.books.show\":{\"uri\":\"api\\/books\\/{slug}\",\"methods\":[\"GET\",\"HEAD\"],\"parameters\":[\"slug\"]},\"api.chapters.adjacent\":{\"uri\":\"api\\/chapters\\/{reference}\\/adjacent\",\"methods\":[\"GET\",\"HEAD\"],\"wheres\":{\"reference\":\"[\\\\w\\\\d,\\\\-]+\"},\"parameters\":[\"reference\"]},\"api.search.books\":{\"uri\":\"api\\/search\\/books\",\"methods\":[\"GET\",\"HEAD\"]},\"api.search\":{\"uri\":\"api\\/search\",\"methods\":[\"GET\",\"HEAD\"]},\"api.bible.text\":{\"uri\":\"api\\/bible\\/{reference}\",\"methods\":[\"GET\",\"HEAD\"],\"wheres\":{\"reference\":\"[\\\\w\\\\d,\\\\-]+\"},\"parameters\":[\"reference\"]},\"storage.local\":{\"uri\":\"storage\\/{path}\",\"methods\":[\"GET\",\"HEAD\"],\"wheres\":{\"path\":\".*\"},\"parameters\":[\"path\"]}});</script>    <script type=\"module\" src=\"http://[::1]:5173/@vite/client\"></script><script type=\"module\" src=\"http://[::1]:5173/resources/js/app.ts\"></script><script type=\"module\" src=\"http://[::1]:5173/resources/js/Pages/NotFound.vue\"></script>    </head>\n\n<body class=\"font-sans antialiased\">\n    <div id=\"app\" data-page=\"{&quot;component&quot;:&quot;NotFound&quot;,&quot;props&quot;:{&quot;errors&quot;:{},&quot;books&quot;:{&quot;sections&quot;:[{&quot;name&quot;:&quot;Altes Testament&quot;,&quot;books&quot;:[{&quot;id&quot;:1,&quot;name&quot;:&quot;1. Mose&quot;,&quot;shortName&quot;:&quot;1Mo&quot;,&quot;chapterCount&quot;:50,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;law&quot;,&quot;order&quot;:1,&quot;slug&quot;:&quot;1.Mose&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:2,&quot;name&quot;:&quot;2. Mose&quot;,&quot;shortName&quot;:&quot;2Mo&quot;,&quot;chapterCount&quot;:40,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;law&quot;,&quot;order&quot;:2,&quot;slug&quot;:&quot;2.Mose&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:3,&quot;name&quot;:&quot;3. Mose&quot;,&quot;shortName&quot;:&quot;3Mo&quot;,&quot;chapterCount&quot;:27,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;law&quot;,&quot;order&quot;:3,&quot;slug&quot;:&quot;3.Mose&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:4,&quot;name&quot;:&quot;4. Mose&quot;,&quot;shortName&quot;:&quot;4Mo&quot;,&quot;chapterCount&quot;:36,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;law&quot;,&quot;order&quot;:4,&quot;slug&quot;:&quot;4.Mose&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:5,&quot;name&quot;:&quot;5. Mose&quot;,&quot;shortName&quot;:&quot;5Mo&quot;,&quot;chapterCount&quot;:34,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;law&quot;,&quot;order&quot;:5,&quot;slug&quot;:&quot;5.Mose&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:6,&quot;name&quot;:&quot;Josua&quot;,&quot;shortName&quot;:&quot;Jos&quot;,&quot;chapterCount&quot;:24,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:6,&quot;slug&quot;:&quot;Josua&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:7,&quot;name&quot;:&quot;Richter&quot;,&quot;shortName&quot;:&quot;Ri&quot;,&quot;chapterCount&quot;:21,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:7,&quot;slug&quot;:&quot;Richter&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:8,&quot;name&quot;:&quot;Ruth&quot;,&quot;shortName&quot;:&quot;Rt&quot;,&quot;chapterCount&quot;:4,&quot;chapters&quot;:[1,2,3,4],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:8,&quot;slug&quot;:&quot;Ruth&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:9,&quot;name&quot;:&quot;1. Samuel&quot;,&quot;shortName&quot;:&quot;1Sam&quot;,&quot;chapterCount&quot;:31,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:9,&quot;slug&quot;:&quot;1.Samuel&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:10,&quot;name&quot;:&quot;2. Samuel&quot;,&quot;shortName&quot;:&quot;2Sam&quot;,&quot;chapterCount&quot;:24,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:10,&quot;slug&quot;:&quot;2.Samuel&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:11,&quot;name&quot;:&quot;1. K\\u00f6nige&quot;,&quot;shortName&quot;:&quot;1K\\u00f6n&quot;,&quot;chapterCount&quot;:22,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:11,&quot;slug&quot;:&quot;1.K\\u00f6nige&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:12,&quot;name&quot;:&quot;2. K\\u00f6nige&quot;,&quot;shortName&quot;:&quot;2K\\u00f6n&quot;,&quot;chapterCount&quot;:25,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:12,&quot;slug&quot;:&quot;2.K\\u00f6nige&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:13,&quot;name&quot;:&quot;1. Chronik&quot;,&quot;shortName&quot;:&quot;1Chr&quot;,&quot;chapterCount&quot;:29,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:13,&quot;slug&quot;:&quot;1.Chronik&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:14,&quot;name&quot;:&quot;2. Chronik&quot;,&quot;shortName&quot;:&quot;2Chr&quot;,&quot;chapterCount&quot;:36,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:14,&quot;slug&quot;:&quot;2.Chronik&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:15,&quot;name&quot;:&quot;Esra&quot;,&quot;shortName&quot;:&quot;Esra&quot;,&quot;chapterCount&quot;:10,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:15,&quot;slug&quot;:&quot;Esra&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:16,&quot;name&quot;:&quot;Nehemia&quot;,&quot;shortName&quot;:&quot;Neh&quot;,&quot;chapterCount&quot;:13,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:16,&quot;slug&quot;:&quot;Nehemia&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:17,&quot;name&quot;:&quot;Esther&quot;,&quot;shortName&quot;:&quot;Est&quot;,&quot;chapterCount&quot;:10,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:17,&quot;slug&quot;:&quot;Esther&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:18,&quot;name&quot;:&quot;Hiob&quot;,&quot;shortName&quot;:&quot;Hi&quot;,&quot;chapterCount&quot;:42,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;wisdom&quot;,&quot;order&quot;:18,&quot;slug&quot;:&quot;Hiob&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:19,&quot;name&quot;:&quot;Psalmen&quot;,&quot;shortName&quot;:&quot;Ps&quot;,&quot;chapterCount&quot;:150,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;wisdom&quot;,&quot;order&quot;:19,&quot;slug&quot;:&quot;Psalmen&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:20,&quot;name&quot;:&quot;Spr\\u00fcche&quot;,&quot;shortName&quot;:&quot;Spr&quot;,&quot;chapterCount&quot;:31,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;wisdom&quot;,&quot;order&quot;:20,&quot;slug&quot;:&quot;Spr\\u00fcche&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:21,&quot;name&quot;:&quot;Prediger&quot;,&quot;shortName&quot;:&quot;Pred&quot;,&quot;chapterCount&quot;:12,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;wisdom&quot;,&quot;order&quot;:21,&quot;slug&quot;:&quot;Prediger&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:22,&quot;name&quot;:&quot;Hohelied&quot;,&quot;shortName&quot;:&quot;Hl&quot;,&quot;chapterCount&quot;:8,&quot;chapters&quot;:[1,2,3,4,5,6,7,8],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;wisdom&quot;,&quot;order&quot;:22,&quot;slug&quot;:&quot;Hohelied&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:23,&quot;name&quot;:&quot;Jesaja&quot;,&quot;shortName&quot;:&quot;Jes&quot;,&quot;chapterCount&quot;:66,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:23,&quot;slug&quot;:&quot;Jesaja&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:24,&quot;name&quot;:&quot;Jeremia&quot;,&quot;shortName&quot;:&quot;Jer&quot;,&quot;chapterCount&quot;:52,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:24,&quot;slug&quot;:&quot;Jeremia&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:25,&quot;name&quot;:&quot;Klagelieder&quot;,&quot;shortName&quot;:&quot;Kla&quot;,&quot;chapterCount&quot;:5,&quot;chapters&quot;:[1,2,3,4,5],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;wisdom&quot;,&quot;order&quot;:25,&quot;slug&quot;:&quot;Klagelieder&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:26,&quot;name&quot;:&quot;Hesekiel&quot;,&quot;shortName&quot;:&quot;Hes&quot;,&quot;chapterCount&quot;:48,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:26,&quot;slug&quot;:&quot;Hesekiel&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:27,&quot;name&quot;:&quot;Daniel&quot;,&quot;shortName&quot;:&quot;Dan&quot;,&quot;chapterCount&quot;:12,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:27,&quot;slug&quot;:&quot;Daniel&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:28,&quot;name&quot;:&quot;Hosea&quot;,&quot;shortName&quot;:&quot;Hos&quot;,&quot;chapterCount&quot;:14,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:28,&quot;slug&quot;:&quot;Hosea&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:29,&quot;name&quot;:&quot;Joel&quot;,&quot;shortName&quot;:&quot;Joel&quot;,&quot;chapterCount&quot;:3,&quot;chapters&quot;:[1,2,3],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:29,&quot;slug&quot;:&quot;Joel&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:30,&quot;name&quot;:&quot;Amos&quot;,&quot;shortName&quot;:&quot;Am&quot;,&quot;chapterCount&quot;:9,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:30,&quot;slug&quot;:&quot;Amos&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:31,&quot;name&quot;:&quot;Obadja&quot;,&quot;shortName&quot;:&quot;Ob&quot;,&quot;chapterCount&quot;:1,&quot;chapters&quot;:[1],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:31,&quot;slug&quot;:&quot;Obadja&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:32,&quot;name&quot;:&quot;Jona&quot;,&quot;shortName&quot;:&quot;Jon&quot;,&quot;chapterCount&quot;:4,&quot;chapters&quot;:[1,2,3,4],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:32,&quot;slug&quot;:&quot;Jona&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:33,&quot;name&quot;:&quot;Micha&quot;,&quot;shortName&quot;:&quot;Mi&quot;,&quot;chapterCount&quot;:7,&quot;chapters&quot;:[1,2,3,4,5,6,7],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:33,&quot;slug&quot;:&quot;Micha&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:34,&quot;name&quot;:&quot;Nahum&quot;,&quot;shortName&quot;:&quot;Nah&quot;,&quot;chapterCount&quot;:3,&quot;chapters&quot;:[1,2,3],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:34,&quot;slug&quot;:&quot;Nahum&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:35,&quot;name&quot;:&quot;Habakuk&quot;,&quot;shortName&quot;:&quot;Hab&quot;,&quot;chapterCount&quot;:3,&quot;chapters&quot;:[1,2,3],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:35,&quot;slug&quot;:&quot;Habakuk&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:36,&quot;name&quot;:&quot;Zephanja&quot;,&quot;shortName&quot;:&quot;Zeph&quot;,&quot;chapterCount&quot;:3,&quot;chapters&quot;:[1,2,3],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:36,&quot;slug&quot;:&quot;Zephanja&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:37,&quot;name&quot;:&quot;Haggai&quot;,&quot;shortName&quot;:&quot;Hag&quot;,&quot;chapterCount&quot;:2,&quot;chapters&quot;:[1,2],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:37,&quot;slug&quot;:&quot;Haggai&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:38,&quot;name&quot;:&quot;Sacharja&quot;,&quot;shortName&quot;:&quot;Sach&quot;,&quot;chapterCount&quot;:14,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:38,&quot;slug&quot;:&quot;Sacharja&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:39,&quot;name&quot;:&quot;Maleachi&quot;,&quot;shortName&quot;:&quot;Mal&quot;,&quot;chapterCount&quot;:4,&quot;chapters&quot;:[1,2,3,4],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:39,&quot;slug&quot;:&quot;Maleachi&quot;,&quot;hasContent&quot;:false}]},{&quot;name&quot;:&quot;Neues Testament&quot;,&quot;books&quot;:[{&quot;id&quot;:40,&quot;name&quot;:&quot;Matth\\u00e4us&quot;,&quot;shortName&quot;:&quot;Mt&quot;,&quot;chapterCount&quot;:28,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;gospel&quot;,&quot;order&quot;:40,&quot;slug&quot;:&quot;Matth\\u00e4us&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:41,&quot;name&quot;:&quot;Markus&quot;,&quot;shortName&quot;:&quot;Mk&quot;,&quot;chapterCount&quot;:16,&quot;chapters&quot;:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;gospel&quot;,&quot;order&quot;:41,&quot;slug&quot;:&quot;Markus&quot;,&quot;hasContent&quot;:true},{&quot;id&quot;:42,&quot;name&quot;:&quot;Lukas&quot;,&quot;shortName&quot;:&quot;Lk&quot;,&quot;chapterCount&quot;:24,&quot;chapters&quot;:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;gospel&quot;,&quot;order&quot;:42,&quot;slug&quot;:&quot;Lukas&quot;,&quot;hasContent&quot;:true},{&quot;id&quot;:43,&quot;name&quot;:&quot;Die Heilsbotschaft nach Johannes&quot;,&quot;shortName&quot;:&quot;Joh&quot;,&quot;chapterCount&quot;:21,&quot;chapters&quot;:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;gospel&quot;,&quot;order&quot;:43,&quot;slug&quot;:&quot;Johannes&quot;,&quot;hasContent&quot;:true},{&quot;id&quot;:44,&quot;name&quot;:&quot;Apostelgeschichte&quot;,&quot;shortName&quot;:&quot;Apg&quot;,&quot;chapterCount&quot;:28,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:44,&quot;slug&quot;:&quot;Apostelgeschichte&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:45,&quot;name&quot;:&quot;R\\u00f6mer&quot;,&quot;shortName&quot;:&quot;R\\u00f6m&quot;,&quot;chapterCount&quot;:16,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:45,&quot;slug&quot;:&quot;R\\u00f6mer&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:46,&quot;name&quot;:&quot;1. Korinther&quot;,&quot;shortName&quot;:&quot;1Kor&quot;,&quot;chapterCount&quot;:16,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:46,&quot;slug&quot;:&quot;1.Korinther&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:47,&quot;name&quot;:&quot;2. Korinther&quot;,&quot;shortName&quot;:&quot;2Kor&quot;,&quot;chapterCount&quot;:13,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:47,&quot;slug&quot;:&quot;2.Korinther&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:48,&quot;name&quot;:&quot;Galater&quot;,&quot;shortName&quot;:&quot;Gal&quot;,&quot;chapterCount&quot;:6,&quot;chapters&quot;:[1,2,3,4,5,6],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:48,&quot;slug&quot;:&quot;Galater&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:49,&quot;name&quot;:&quot;Epheser&quot;,&quot;shortName&quot;:&quot;Eph&quot;,&quot;chapterCount&quot;:6,&quot;chapters&quot;:[1,2,3,4,5,6],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:49,&quot;slug&quot;:&quot;Epheser&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:50,&quot;name&quot;:&quot;Philipper&quot;,&quot;shortName&quot;:&quot;Phil&quot;,&quot;chapterCount&quot;:4,&quot;chapters&quot;:[1,2,3,4],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:50,&quot;slug&quot;:&quot;Philipper&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:51,&quot;name&quot;:&quot;Kolosser&quot;,&quot;shortName&quot;:&quot;Kol&quot;,&quot;chapterCount&quot;:4,&quot;chapters&quot;:[1,2,3,4],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:51,&quot;slug&quot;:&quot;Kolosser&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:52,&quot;name&quot;:&quot;1. Thessalonicher&quot;,&quot;shortName&quot;:&quot;1Thes&quot;,&quot;chapterCount&quot;:5,&quot;chapters&quot;:[1,2,3,4,5],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:52,&quot;slug&quot;:&quot;1.Thessalonicher&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:53,&quot;name&quot;:&quot;2. Thessalonicher&quot;,&quot;shortName&quot;:&quot;2Thes&quot;,&quot;chapterCount&quot;:3,&quot;chapters&quot;:[1,2,3],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:53,&quot;slug&quot;:&quot;2.Thessalonicher&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:54,&quot;name&quot;:&quot;1. Timotheus&quot;,&quot;shortName&quot;:&quot;1Tim&quot;,&quot;chapterCount&quot;:6,&quot;chapters&quot;:[1,2,3,4,5,6],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:54,&quot;slug&quot;:&quot;1.Timotheus&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:55,&quot;name&quot;:&quot;2. Timotheus&quot;,&quot;shortName&quot;:&quot;2Tim&quot;,&quot;chapterCount&quot;:4,&quot;chapters&quot;:[1,2,3,4],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:55,&quot;slug&quot;:&quot;2.Timotheus&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:56,&quot;name&quot;:&quot;Titus&quot;,&quot;shortName&quot;:&quot;Tit&quot;,&quot;chapterCount&quot;:3,&quot;chapters&quot;:[1,2,3],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:56,&quot;slug&quot;:&quot;Titus&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:57,&quot;name&quot;:&quot;Philemon&quot;,&quot;shortName&quot;:&quot;Phim&quot;,&quot;chapterCount&quot;:1,&quot;chapters&quot;:[1],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:57,&quot;slug&quot;:&quot;Philemon&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:58,&quot;name&quot;:&quot;Hebr\\u00e4er&quot;,&quot;shortName&quot;:&quot;Heb&quot;,&quot;chapterCount&quot;:13,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:58,&quot;slug&quot;:&quot;Hebr\\u00e4er&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:59,&quot;name&quot;:&quot;Jakobus&quot;,&quot;shortName&quot;:&quot;Jak&quot;,&quot;chapterCount&quot;:5,&quot;chapters&quot;:[1,2,3,4,5],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:59,&quot;slug&quot;:&quot;Jakobus&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:60,&quot;name&quot;:&quot;1. Petrus&quot;,&quot;shortName&quot;:&quot;1Pet&quot;,&quot;chapterCount&quot;:5,&quot;chapters&quot;:[1,2,3,4,5],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:60,&quot;slug&quot;:&quot;1.Petrus&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:61,&quot;name&quot;:&quot;2. Petrus&quot;,&quot;shortName&quot;:&quot;2Pet&quot;,&quot;chapterCount&quot;:3,&quot;chapters&quot;:[1,2,3],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:61,&quot;slug&quot;:&quot;2.Petrus&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:62,&quot;name&quot;:&quot;1. Johannes&quot;,&quot;shortName&quot;:&quot;1Joh&quot;,&quot;chapterCount&quot;:5,&quot;chapters&quot;:[1,2,3,4,5],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:62,&quot;slug&quot;:&quot;1.Johannes&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:63,&quot;name&quot;:&quot;2. Johannes&quot;,&quot;shortName&quot;:&quot;2Joh&quot;,&quot;chapterCount&quot;:1,&quot;chapters&quot;:[1],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:63,&quot;slug&quot;:&quot;2.Johannes&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:64,&quot;name&quot;:&quot;3. Johannes&quot;,&quot;shortName&quot;:&quot;3Joh&quot;,&quot;chapterCount&quot;:1,&quot;chapters&quot;:[1],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:64,&quot;slug&quot;:&quot;3.Johannes&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:65,&quot;name&quot;:&quot;Judas&quot;,&quot;shortName&quot;:&quot;Jud&quot;,&quot;chapterCount&quot;:1,&quot;chapters&quot;:[1],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:65,&quot;slug&quot;:&quot;Judas&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:66,&quot;name&quot;:&quot;Offenbarung&quot;,&quot;shortName&quot;:&quot;Offb&quot;,&quot;chapterCount&quot;:22,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;apocalypse&quot;,&quot;order&quot;:66,&quot;slug&quot;:&quot;Offenbarung&quot;,&quot;hasContent&quot;:false}]}],&quot;availableBooks&quot;:[{&quot;slug&quot;:&quot;Markus&quot;,&quot;order&quot;:41},{&quot;slug&quot;:&quot;Lukas&quot;,&quot;order&quot;:42},{&quot;slug&quot;:&quot;Johannes&quot;,&quot;order&quot;:43}]},&quot;env&quot;:&quot;local&quot;,&quot;auth&quot;:{&quot;user&quot;:null},&quot;requestedPath&quot;:&quot;api\\/books\\/architecto&quot;},&quot;url&quot;:&quot;\\/api\\/books\\/architecto&quot;,&quot;version&quot;:&quot;18765f3fa436d07c5ef1cbbbc3fa3b37&quot;,&quot;clearHistory&quot;:false,&quot;encryptHistory&quot;:false}\"></div></body>\n\n</html>\n"
      tags:
        - Endpoints
      security: []
    parameters:
      -
        in: path
        name: slug
        description: 'The slug of the book.'
        example: architecto
        required: true
        schema:
          type: string
  /api/search/books:
    get:
      summary: ''
      operationId: getApiSearchBooks
      description: ''
      parameters: []
      responses:
        404:
          description: ''
          content:
            text/plain:
              schema:
                type: string
                example: "<!DOCTYPE html>\n<html lang=\"de\">\n\n<head>\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\n\n    <title inertia></title>\n\n    <!-- Fonts -->\n    <!-- Preconnect to font domains -->\n    <link rel=\"preconnect\" href=\"https://fonts.bunny.net\" crossorigin>\n    <link rel=\"preconnect\" href=\"https://use.typekit.net\" as=\"font\" type=\"font/woff2\" crossorigin>\n\n    <!-- Preload critical fonts -->\n    <link rel=\"preload\" href=\"/fonts/ThanatosText-Book.woff2\" as=\"font\" type=\"font/woff2\" crossorigin>\n\n    <!-- Load fonts -->\n    <link href=\"https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap\" rel=\"stylesheet\" />\n    <link rel=\"stylesheet\" href=\"https://use.typekit.net/kzb8yhl.css\" media=\"print\" onload=\"this.media='all'\">\n\n    <!-- Fallback for typekit fonts -->\n    <noscript>\n        <link rel=\"stylesheet\" href=\"https://use.typekit.net/kzb8yhl.css\">\n    </noscript>\n\n    <!-- Local font definition -->\n    <style>\n        @font-face {\n            font-family: 'ThanatosText';\n            src: url('/fonts/ThanatosText-Book.woff2') format('woff2');\n            font-weight: normal;\n            font-style: normal;\n            font-display: swap;\n        }\n    </style>\n\n    <!-- Scripts -->\n    <script type=\"text/javascript\">Object.assign(Ziggy.routes,{\"search.index\":{\"uri\":\"search\",\"methods\":[\"GET\",\"HEAD\"]},\"search.query\":{\"uri\":\"search\\/{query}\",\"methods\":[\"GET\",\"HEAD\"],\"wheres\":{\"query\":\"[^\\/]+\"},\"parameters\":[\"query\"]},\"search.paged\":{\"uri\":\"search\\/{query}\\/{page?}\",\"methods\":[\"GET\",\"HEAD\"],\"wheres\":{\"query\":\"[^\\/]+\",\"page\":\"[0-9]+\"},\"parameters\":[\"query\",\"page\"]},\"search.settings\":{\"uri\":\"search\\/settings\",\"methods\":[\"POST\"]},\"dashboard\":{\"uri\":\"dashboard\",\"methods\":[\"GET\",\"HEAD\"]},\"bible.import\":{\"uri\":\"import-bible\",\"methods\":[\"GET\",\"HEAD\"]},\"bible.import.store\":{\"uri\":\"import-bible\",\"methods\":[\"POST\"]},\"file.upload\":{\"uri\":\"api\\/upload\",\"methods\":[\"POST\"]},\"profile.edit\":{\"uri\":\"profile\",\"methods\":[\"GET\",\"HEAD\"]},\"profile.update\":{\"uri\":\"profile\",\"methods\":[\"PATCH\"]},\"profile.destroy\":{\"uri\":\"profile\",\"methods\":[\"DELETE\"]},\"books.show\":{\"uri\":\"{reference}\",\"methods\":[\"GET\",\"HEAD\"],\"wheres\":{\"reference\":\"[\\\\w\\\\d\\\\.,\\\\-\\\\+]+\"},\"parameters\":[\"reference\"]},\"register\":{\"uri\":\"register\",\"methods\":[\"GET\",\"HEAD\"]},\"login\":{\"uri\":\"login\",\"methods\":[\"GET\",\"HEAD\"]},\"password.request\":{\"uri\":\"forgot-password\",\"methods\":[\"GET\",\"HEAD\"]},\"password.email\":{\"uri\":\"forgot-password\",\"methods\":[\"POST\"]},\"password.reset\":{\"uri\":\"reset-password\\/{token}\",\"methods\":[\"GET\",\"HEAD\"],\"parameters\":[\"token\"]},\"password.store\":{\"uri\":\"reset-password\",\"methods\":[\"POST\"]},\"verification.notice\":{\"uri\":\"verify-email\",\"methods\":[\"GET\",\"HEAD\"]},\"verification.verify\":{\"uri\":\"verify-email\\/{id}\\/{hash}\",\"methods\":[\"GET\",\"HEAD\"],\"parameters\":[\"id\",\"hash\"]},\"verification.send\":{\"uri\":\"email\\/verification-notification\",\"methods\":[\"POST\"]},\"password.confirm\":{\"uri\":\"confirm-password\",\"methods\":[\"GET\",\"HEAD\"]},\"password.update\":{\"uri\":\"password\",\"methods\":[\"PUT\"]},\"logout\":{\"uri\":\"logout\",\"methods\":[\"POST\"]},\"api.chapters.fetch\":{\"uri\":\"api\\/chapters\\/fetch\",\"methods\":[\"GET\",\"HEAD\"]},\"api.books\":{\"uri\":\"api\\/books\",\"methods\":[\"GET\",\"HEAD\"]},\"api.books.content-status\":{\"uri\":\"api\\/books\\/content-status\",\"methods\":[\"GET\",\"HEAD\"]},\"api.books.show\":{\"uri\":\"api\\/books\\/{slug}\",\"methods\":[\"GET\",\"HEAD\"],\"parameters\":[\"slug\"]},\"api.chapters.adjacent\":{\"uri\":\"api\\/chapters\\/{reference}\\/adjacent\",\"methods\":[\"GET\",\"HEAD\"],\"wheres\":{\"reference\":\"[\\\\w\\\\d,\\\\-]+\"},\"parameters\":[\"reference\"]},\"api.search.books\":{\"uri\":\"api\\/search\\/books\",\"methods\":[\"GET\",\"HEAD\"]},\"api.search\":{\"uri\":\"api\\/search\",\"methods\":[\"GET\",\"HEAD\"]},\"api.bible.text\":{\"uri\":\"api\\/bible\\/{reference}\",\"methods\":[\"GET\",\"HEAD\"],\"wheres\":{\"reference\":\"[\\\\w\\\\d,\\\\-]+\"},\"parameters\":[\"reference\"]},\"storage.local\":{\"uri\":\"storage\\/{path}\",\"methods\":[\"GET\",\"HEAD\"],\"wheres\":{\"path\":\".*\"},\"parameters\":[\"path\"]}});</script>    <script type=\"module\" src=\"http://[::1]:5173/@vite/client\"></script><script type=\"module\" src=\"http://[::1]:5173/resources/js/app.ts\"></script><script type=\"module\" src=\"http://[::1]:5173/resources/js/Pages/NotFound.vue\"></script>    </head>\n\n<body class=\"font-sans antialiased\">\n    <div id=\"app\" data-page=\"{&quot;component&quot;:&quot;NotFound&quot;,&quot;props&quot;:{&quot;errors&quot;:{},&quot;books&quot;:{&quot;sections&quot;:[{&quot;name&quot;:&quot;Altes Testament&quot;,&quot;books&quot;:[{&quot;id&quot;:1,&quot;name&quot;:&quot;1. Mose&quot;,&quot;shortName&quot;:&quot;1Mo&quot;,&quot;chapterCount&quot;:50,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;law&quot;,&quot;order&quot;:1,&quot;slug&quot;:&quot;1.Mose&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:2,&quot;name&quot;:&quot;2. Mose&quot;,&quot;shortName&quot;:&quot;2Mo&quot;,&quot;chapterCount&quot;:40,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;law&quot;,&quot;order&quot;:2,&quot;slug&quot;:&quot;2.Mose&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:3,&quot;name&quot;:&quot;3. Mose&quot;,&quot;shortName&quot;:&quot;3Mo&quot;,&quot;chapterCount&quot;:27,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;law&quot;,&quot;order&quot;:3,&quot;slug&quot;:&quot;3.Mose&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:4,&quot;name&quot;:&quot;4. Mose&quot;,&quot;shortName&quot;:&quot;4Mo&quot;,&quot;chapterCount&quot;:36,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;law&quot;,&quot;order&quot;:4,&quot;slug&quot;:&quot;4.Mose&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:5,&quot;name&quot;:&quot;5. Mose&quot;,&quot;shortName&quot;:&quot;5Mo&quot;,&quot;chapterCount&quot;:34,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;law&quot;,&quot;order&quot;:5,&quot;slug&quot;:&quot;5.Mose&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:6,&quot;name&quot;:&quot;Josua&quot;,&quot;shortName&quot;:&quot;Jos&quot;,&quot;chapterCount&quot;:24,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:6,&quot;slug&quot;:&quot;Josua&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:7,&quot;name&quot;:&quot;Richter&quot;,&quot;shortName&quot;:&quot;Ri&quot;,&quot;chapterCount&quot;:21,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:7,&quot;slug&quot;:&quot;Richter&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:8,&quot;name&quot;:&quot;Ruth&quot;,&quot;shortName&quot;:&quot;Rt&quot;,&quot;chapterCount&quot;:4,&quot;chapters&quot;:[1,2,3,4],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:8,&quot;slug&quot;:&quot;Ruth&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:9,&quot;name&quot;:&quot;1. Samuel&quot;,&quot;shortName&quot;:&quot;1Sam&quot;,&quot;chapterCount&quot;:31,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:9,&quot;slug&quot;:&quot;1.Samuel&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:10,&quot;name&quot;:&quot;2. Samuel&quot;,&quot;shortName&quot;:&quot;2Sam&quot;,&quot;chapterCount&quot;:24,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:10,&quot;slug&quot;:&quot;2.Samuel&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:11,&quot;name&quot;:&quot;1. K\\u00f6nige&quot;,&quot;shortName&quot;:&quot;1K\\u00f6n&quot;,&quot;chapterCount&quot;:22,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:11,&quot;slug&quot;:&quot;1.K\\u00f6nige&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:12,&quot;name&quot;:&quot;2. K\\u00f6nige&quot;,&quot;shortName&quot;:&quot;2K\\u00f6n&quot;,&quot;chapterCount&quot;:25,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:12,&quot;slug&quot;:&quot;2.K\\u00f6nige&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:13,&quot;name&quot;:&quot;1. Chronik&quot;,&quot;shortName&quot;:&quot;1Chr&quot;,&quot;chapterCount&quot;:29,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:13,&quot;slug&quot;:&quot;1.Chronik&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:14,&quot;name&quot;:&quot;2. Chronik&quot;,&quot;shortName&quot;:&quot;2Chr&quot;,&quot;chapterCount&quot;:36,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:14,&quot;slug&quot;:&quot;2.Chronik&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:15,&quot;name&quot;:&quot;Esra&quot;,&quot;shortName&quot;:&quot;Esra&quot;,&quot;chapterCount&quot;:10,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:15,&quot;slug&quot;:&quot;Esra&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:16,&quot;name&quot;:&quot;Nehemia&quot;,&quot;shortName&quot;:&quot;Neh&quot;,&quot;chapterCount&quot;:13,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:16,&quot;slug&quot;:&quot;Nehemia&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:17,&quot;name&quot;:&quot;Esther&quot;,&quot;shortName&quot;:&quot;Est&quot;,&quot;chapterCount&quot;:10,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:17,&quot;slug&quot;:&quot;Esther&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:18,&quot;name&quot;:&quot;Hiob&quot;,&quot;shortName&quot;:&quot;Hi&quot;,&quot;chapterCount&quot;:42,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;wisdom&quot;,&quot;order&quot;:18,&quot;slug&quot;:&quot;Hiob&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:19,&quot;name&quot;:&quot;Psalmen&quot;,&quot;shortName&quot;:&quot;Ps&quot;,&quot;chapterCount&quot;:150,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;wisdom&quot;,&quot;order&quot;:19,&quot;slug&quot;:&quot;Psalmen&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:20,&quot;name&quot;:&quot;Spr\\u00fcche&quot;,&quot;shortName&quot;:&quot;Spr&quot;,&quot;chapterCount&quot;:31,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;wisdom&quot;,&quot;order&quot;:20,&quot;slug&quot;:&quot;Spr\\u00fcche&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:21,&quot;name&quot;:&quot;Prediger&quot;,&quot;shortName&quot;:&quot;Pred&quot;,&quot;chapterCount&quot;:12,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;wisdom&quot;,&quot;order&quot;:21,&quot;slug&quot;:&quot;Prediger&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:22,&quot;name&quot;:&quot;Hohelied&quot;,&quot;shortName&quot;:&quot;Hl&quot;,&quot;chapterCount&quot;:8,&quot;chapters&quot;:[1,2,3,4,5,6,7,8],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;wisdom&quot;,&quot;order&quot;:22,&quot;slug&quot;:&quot;Hohelied&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:23,&quot;name&quot;:&quot;Jesaja&quot;,&quot;shortName&quot;:&quot;Jes&quot;,&quot;chapterCount&quot;:66,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:23,&quot;slug&quot;:&quot;Jesaja&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:24,&quot;name&quot;:&quot;Jeremia&quot;,&quot;shortName&quot;:&quot;Jer&quot;,&quot;chapterCount&quot;:52,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:24,&quot;slug&quot;:&quot;Jeremia&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:25,&quot;name&quot;:&quot;Klagelieder&quot;,&quot;shortName&quot;:&quot;Kla&quot;,&quot;chapterCount&quot;:5,&quot;chapters&quot;:[1,2,3,4,5],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;wisdom&quot;,&quot;order&quot;:25,&quot;slug&quot;:&quot;Klagelieder&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:26,&quot;name&quot;:&quot;Hesekiel&quot;,&quot;shortName&quot;:&quot;Hes&quot;,&quot;chapterCount&quot;:48,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:26,&quot;slug&quot;:&quot;Hesekiel&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:27,&quot;name&quot;:&quot;Daniel&quot;,&quot;shortName&quot;:&quot;Dan&quot;,&quot;chapterCount&quot;:12,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:27,&quot;slug&quot;:&quot;Daniel&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:28,&quot;name&quot;:&quot;Hosea&quot;,&quot;shortName&quot;:&quot;Hos&quot;,&quot;chapterCount&quot;:14,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:28,&quot;slug&quot;:&quot;Hosea&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:29,&quot;name&quot;:&quot;Joel&quot;,&quot;shortName&quot;:&quot;Joel&quot;,&quot;chapterCount&quot;:3,&quot;chapters&quot;:[1,2,3],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:29,&quot;slug&quot;:&quot;Joel&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:30,&quot;name&quot;:&quot;Amos&quot;,&quot;shortName&quot;:&quot;Am&quot;,&quot;chapterCount&quot;:9,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:30,&quot;slug&quot;:&quot;Amos&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:31,&quot;name&quot;:&quot;Obadja&quot;,&quot;shortName&quot;:&quot;Ob&quot;,&quot;chapterCount&quot;:1,&quot;chapters&quot;:[1],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:31,&quot;slug&quot;:&quot;Obadja&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:32,&quot;name&quot;:&quot;Jona&quot;,&quot;shortName&quot;:&quot;Jon&quot;,&quot;chapterCount&quot;:4,&quot;chapters&quot;:[1,2,3,4],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:32,&quot;slug&quot;:&quot;Jona&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:33,&quot;name&quot;:&quot;Micha&quot;,&quot;shortName&quot;:&quot;Mi&quot;,&quot;chapterCount&quot;:7,&quot;chapters&quot;:[1,2,3,4,5,6,7],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:33,&quot;slug&quot;:&quot;Micha&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:34,&quot;name&quot;:&quot;Nahum&quot;,&quot;shortName&quot;:&quot;Nah&quot;,&quot;chapterCount&quot;:3,&quot;chapters&quot;:[1,2,3],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:34,&quot;slug&quot;:&quot;Nahum&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:35,&quot;name&quot;:&quot;Habakuk&quot;,&quot;shortName&quot;:&quot;Hab&quot;,&quot;chapterCount&quot;:3,&quot;chapters&quot;:[1,2,3],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:35,&quot;slug&quot;:&quot;Habakuk&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:36,&quot;name&quot;:&quot;Zephanja&quot;,&quot;shortName&quot;:&quot;Zeph&quot;,&quot;chapterCount&quot;:3,&quot;chapters&quot;:[1,2,3],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:36,&quot;slug&quot;:&quot;Zephanja&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:37,&quot;name&quot;:&quot;Haggai&quot;,&quot;shortName&quot;:&quot;Hag&quot;,&quot;chapterCount&quot;:2,&quot;chapters&quot;:[1,2],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:37,&quot;slug&quot;:&quot;Haggai&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:38,&quot;name&quot;:&quot;Sacharja&quot;,&quot;shortName&quot;:&quot;Sach&quot;,&quot;chapterCount&quot;:14,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:38,&quot;slug&quot;:&quot;Sacharja&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:39,&quot;name&quot;:&quot;Maleachi&quot;,&quot;shortName&quot;:&quot;Mal&quot;,&quot;chapterCount&quot;:4,&quot;chapters&quot;:[1,2,3,4],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:39,&quot;slug&quot;:&quot;Maleachi&quot;,&quot;hasContent&quot;:false}]},{&quot;name&quot;:&quot;Neues Testament&quot;,&quot;books&quot;:[{&quot;id&quot;:40,&quot;name&quot;:&quot;Matth\\u00e4us&quot;,&quot;shortName&quot;:&quot;Mt&quot;,&quot;chapterCount&quot;:28,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;gospel&quot;,&quot;order&quot;:40,&quot;slug&quot;:&quot;Matth\\u00e4us&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:41,&quot;name&quot;:&quot;Markus&quot;,&quot;shortName&quot;:&quot;Mk&quot;,&quot;chapterCount&quot;:16,&quot;chapters&quot;:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;gospel&quot;,&quot;order&quot;:41,&quot;slug&quot;:&quot;Markus&quot;,&quot;hasContent&quot;:true},{&quot;id&quot;:42,&quot;name&quot;:&quot;Lukas&quot;,&quot;shortName&quot;:&quot;Lk&quot;,&quot;chapterCount&quot;:24,&quot;chapters&quot;:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;gospel&quot;,&quot;order&quot;:42,&quot;slug&quot;:&quot;Lukas&quot;,&quot;hasContent&quot;:true},{&quot;id&quot;:43,&quot;name&quot;:&quot;Die Heilsbotschaft nach Johannes&quot;,&quot;shortName&quot;:&quot;Joh&quot;,&quot;chapterCount&quot;:21,&quot;chapters&quot;:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;gospel&quot;,&quot;order&quot;:43,&quot;slug&quot;:&quot;Johannes&quot;,&quot;hasContent&quot;:true},{&quot;id&quot;:44,&quot;name&quot;:&quot;Apostelgeschichte&quot;,&quot;shortName&quot;:&quot;Apg&quot;,&quot;chapterCount&quot;:28,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:44,&quot;slug&quot;:&quot;Apostelgeschichte&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:45,&quot;name&quot;:&quot;R\\u00f6mer&quot;,&quot;shortName&quot;:&quot;R\\u00f6m&quot;,&quot;chapterCount&quot;:16,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:45,&quot;slug&quot;:&quot;R\\u00f6mer&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:46,&quot;name&quot;:&quot;1. Korinther&quot;,&quot;shortName&quot;:&quot;1Kor&quot;,&quot;chapterCount&quot;:16,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:46,&quot;slug&quot;:&quot;1.Korinther&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:47,&quot;name&quot;:&quot;2. Korinther&quot;,&quot;shortName&quot;:&quot;2Kor&quot;,&quot;chapterCount&quot;:13,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:47,&quot;slug&quot;:&quot;2.Korinther&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:48,&quot;name&quot;:&quot;Galater&quot;,&quot;shortName&quot;:&quot;Gal&quot;,&quot;chapterCount&quot;:6,&quot;chapters&quot;:[1,2,3,4,5,6],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:48,&quot;slug&quot;:&quot;Galater&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:49,&quot;name&quot;:&quot;Epheser&quot;,&quot;shortName&quot;:&quot;Eph&quot;,&quot;chapterCount&quot;:6,&quot;chapters&quot;:[1,2,3,4,5,6],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:49,&quot;slug&quot;:&quot;Epheser&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:50,&quot;name&quot;:&quot;Philipper&quot;,&quot;shortName&quot;:&quot;Phil&quot;,&quot;chapterCount&quot;:4,&quot;chapters&quot;:[1,2,3,4],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:50,&quot;slug&quot;:&quot;Philipper&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:51,&quot;name&quot;:&quot;Kolosser&quot;,&quot;shortName&quot;:&quot;Kol&quot;,&quot;chapterCount&quot;:4,&quot;chapters&quot;:[1,2,3,4],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:51,&quot;slug&quot;:&quot;Kolosser&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:52,&quot;name&quot;:&quot;1. Thessalonicher&quot;,&quot;shortName&quot;:&quot;1Thes&quot;,&quot;chapterCount&quot;:5,&quot;chapters&quot;:[1,2,3,4,5],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:52,&quot;slug&quot;:&quot;1.Thessalonicher&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:53,&quot;name&quot;:&quot;2. Thessalonicher&quot;,&quot;shortName&quot;:&quot;2Thes&quot;,&quot;chapterCount&quot;:3,&quot;chapters&quot;:[1,2,3],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:53,&quot;slug&quot;:&quot;2.Thessalonicher&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:54,&quot;name&quot;:&quot;1. Timotheus&quot;,&quot;shortName&quot;:&quot;1Tim&quot;,&quot;chapterCount&quot;:6,&quot;chapters&quot;:[1,2,3,4,5,6],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:54,&quot;slug&quot;:&quot;1.Timotheus&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:55,&quot;name&quot;:&quot;2. Timotheus&quot;,&quot;shortName&quot;:&quot;2Tim&quot;,&quot;chapterCount&quot;:4,&quot;chapters&quot;:[1,2,3,4],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:55,&quot;slug&quot;:&quot;2.Timotheus&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:56,&quot;name&quot;:&quot;Titus&quot;,&quot;shortName&quot;:&quot;Tit&quot;,&quot;chapterCount&quot;:3,&quot;chapters&quot;:[1,2,3],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:56,&quot;slug&quot;:&quot;Titus&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:57,&quot;name&quot;:&quot;Philemon&quot;,&quot;shortName&quot;:&quot;Phim&quot;,&quot;chapterCount&quot;:1,&quot;chapters&quot;:[1],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:57,&quot;slug&quot;:&quot;Philemon&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:58,&quot;name&quot;:&quot;Hebr\\u00e4er&quot;,&quot;shortName&quot;:&quot;Heb&quot;,&quot;chapterCount&quot;:13,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:58,&quot;slug&quot;:&quot;Hebr\\u00e4er&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:59,&quot;name&quot;:&quot;Jakobus&quot;,&quot;shortName&quot;:&quot;Jak&quot;,&quot;chapterCount&quot;:5,&quot;chapters&quot;:[1,2,3,4,5],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:59,&quot;slug&quot;:&quot;Jakobus&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:60,&quot;name&quot;:&quot;1. Petrus&quot;,&quot;shortName&quot;:&quot;1Pet&quot;,&quot;chapterCount&quot;:5,&quot;chapters&quot;:[1,2,3,4,5],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:60,&quot;slug&quot;:&quot;1.Petrus&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:61,&quot;name&quot;:&quot;2. Petrus&quot;,&quot;shortName&quot;:&quot;2Pet&quot;,&quot;chapterCount&quot;:3,&quot;chapters&quot;:[1,2,3],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:61,&quot;slug&quot;:&quot;2.Petrus&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:62,&quot;name&quot;:&quot;1. Johannes&quot;,&quot;shortName&quot;:&quot;1Joh&quot;,&quot;chapterCount&quot;:5,&quot;chapters&quot;:[1,2,3,4,5],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:62,&quot;slug&quot;:&quot;1.Johannes&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:63,&quot;name&quot;:&quot;2. Johannes&quot;,&quot;shortName&quot;:&quot;2Joh&quot;,&quot;chapterCount&quot;:1,&quot;chapters&quot;:[1],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:63,&quot;slug&quot;:&quot;2.Johannes&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:64,&quot;name&quot;:&quot;3. Johannes&quot;,&quot;shortName&quot;:&quot;3Joh&quot;,&quot;chapterCount&quot;:1,&quot;chapters&quot;:[1],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:64,&quot;slug&quot;:&quot;3.Johannes&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:65,&quot;name&quot;:&quot;Judas&quot;,&quot;shortName&quot;:&quot;Jud&quot;,&quot;chapterCount&quot;:1,&quot;chapters&quot;:[1],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:65,&quot;slug&quot;:&quot;Judas&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:66,&quot;name&quot;:&quot;Offenbarung&quot;,&quot;shortName&quot;:&quot;Offb&quot;,&quot;chapterCount&quot;:22,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;apocalypse&quot;,&quot;order&quot;:66,&quot;slug&quot;:&quot;Offenbarung&quot;,&quot;hasContent&quot;:false}]}],&quot;availableBooks&quot;:[{&quot;slug&quot;:&quot;Markus&quot;,&quot;order&quot;:41},{&quot;slug&quot;:&quot;Lukas&quot;,&quot;order&quot;:42},{&quot;slug&quot;:&quot;Johannes&quot;,&quot;order&quot;:43}]},&quot;env&quot;:&quot;local&quot;,&quot;auth&quot;:{&quot;user&quot;:null},&quot;requestedPath&quot;:&quot;api\\/search\\/books&quot;},&quot;url&quot;:&quot;\\/api\\/search\\/books&quot;,&quot;version&quot;:&quot;18765f3fa436d07c5ef1cbbbc3fa3b37&quot;,&quot;clearHistory&quot;:false,&quot;encryptHistory&quot;:false}\"></div></body>\n\n</html>\n"
      tags:
        - Endpoints
      security: []
  /api/search:
    get:
      summary: ''
      operationId: getApiSearch
      description: ''
      parameters: []
      responses:
        404:
          description: ''
          content:
            text/plain:
              schema:
                type: string
                example: "<!DOCTYPE html>\n<html lang=\"de\">\n\n<head>\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\n\n    <title inertia></title>\n\n    <!-- Fonts -->\n    <!-- Preconnect to font domains -->\n    <link rel=\"preconnect\" href=\"https://fonts.bunny.net\" crossorigin>\n    <link rel=\"preconnect\" href=\"https://use.typekit.net\" as=\"font\" type=\"font/woff2\" crossorigin>\n\n    <!-- Preload critical fonts -->\n    <link rel=\"preload\" href=\"/fonts/ThanatosText-Book.woff2\" as=\"font\" type=\"font/woff2\" crossorigin>\n\n    <!-- Load fonts -->\n    <link href=\"https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap\" rel=\"stylesheet\" />\n    <link rel=\"stylesheet\" href=\"https://use.typekit.net/kzb8yhl.css\" media=\"print\" onload=\"this.media='all'\">\n\n    <!-- Fallback for typekit fonts -->\n    <noscript>\n        <link rel=\"stylesheet\" href=\"https://use.typekit.net/kzb8yhl.css\">\n    </noscript>\n\n    <!-- Local font definition -->\n    <style>\n        @font-face {\n            font-family: 'ThanatosText';\n            src: url('/fonts/ThanatosText-Book.woff2') format('woff2');\n            font-weight: normal;\n            font-style: normal;\n            font-display: swap;\n        }\n    </style>\n\n    <!-- Scripts -->\n    <script type=\"text/javascript\">Object.assign(Ziggy.routes,{\"search.index\":{\"uri\":\"search\",\"methods\":[\"GET\",\"HEAD\"]},\"search.query\":{\"uri\":\"search\\/{query}\",\"methods\":[\"GET\",\"HEAD\"],\"wheres\":{\"query\":\"[^\\/]+\"},\"parameters\":[\"query\"]},\"search.paged\":{\"uri\":\"search\\/{query}\\/{page?}\",\"methods\":[\"GET\",\"HEAD\"],\"wheres\":{\"query\":\"[^\\/]+\",\"page\":\"[0-9]+\"},\"parameters\":[\"query\",\"page\"]},\"search.settings\":{\"uri\":\"search\\/settings\",\"methods\":[\"POST\"]},\"dashboard\":{\"uri\":\"dashboard\",\"methods\":[\"GET\",\"HEAD\"]},\"bible.import\":{\"uri\":\"import-bible\",\"methods\":[\"GET\",\"HEAD\"]},\"bible.import.store\":{\"uri\":\"import-bible\",\"methods\":[\"POST\"]},\"file.upload\":{\"uri\":\"api\\/upload\",\"methods\":[\"POST\"]},\"profile.edit\":{\"uri\":\"profile\",\"methods\":[\"GET\",\"HEAD\"]},\"profile.update\":{\"uri\":\"profile\",\"methods\":[\"PATCH\"]},\"profile.destroy\":{\"uri\":\"profile\",\"methods\":[\"DELETE\"]},\"books.show\":{\"uri\":\"{reference}\",\"methods\":[\"GET\",\"HEAD\"],\"wheres\":{\"reference\":\"[\\\\w\\\\d\\\\.,\\\\-\\\\+]+\"},\"parameters\":[\"reference\"]},\"register\":{\"uri\":\"register\",\"methods\":[\"GET\",\"HEAD\"]},\"login\":{\"uri\":\"login\",\"methods\":[\"GET\",\"HEAD\"]},\"password.request\":{\"uri\":\"forgot-password\",\"methods\":[\"GET\",\"HEAD\"]},\"password.email\":{\"uri\":\"forgot-password\",\"methods\":[\"POST\"]},\"password.reset\":{\"uri\":\"reset-password\\/{token}\",\"methods\":[\"GET\",\"HEAD\"],\"parameters\":[\"token\"]},\"password.store\":{\"uri\":\"reset-password\",\"methods\":[\"POST\"]},\"verification.notice\":{\"uri\":\"verify-email\",\"methods\":[\"GET\",\"HEAD\"]},\"verification.verify\":{\"uri\":\"verify-email\\/{id}\\/{hash}\",\"methods\":[\"GET\",\"HEAD\"],\"parameters\":[\"id\",\"hash\"]},\"verification.send\":{\"uri\":\"email\\/verification-notification\",\"methods\":[\"POST\"]},\"password.confirm\":{\"uri\":\"confirm-password\",\"methods\":[\"GET\",\"HEAD\"]},\"password.update\":{\"uri\":\"password\",\"methods\":[\"PUT\"]},\"logout\":{\"uri\":\"logout\",\"methods\":[\"POST\"]},\"api.chapters.fetch\":{\"uri\":\"api\\/chapters\\/fetch\",\"methods\":[\"GET\",\"HEAD\"]},\"api.books\":{\"uri\":\"api\\/books\",\"methods\":[\"GET\",\"HEAD\"]},\"api.books.content-status\":{\"uri\":\"api\\/books\\/content-status\",\"methods\":[\"GET\",\"HEAD\"]},\"api.books.show\":{\"uri\":\"api\\/books\\/{slug}\",\"methods\":[\"GET\",\"HEAD\"],\"parameters\":[\"slug\"]},\"api.chapters.adjacent\":{\"uri\":\"api\\/chapters\\/{reference}\\/adjacent\",\"methods\":[\"GET\",\"HEAD\"],\"wheres\":{\"reference\":\"[\\\\w\\\\d,\\\\-]+\"},\"parameters\":[\"reference\"]},\"api.search.books\":{\"uri\":\"api\\/search\\/books\",\"methods\":[\"GET\",\"HEAD\"]},\"api.search\":{\"uri\":\"api\\/search\",\"methods\":[\"GET\",\"HEAD\"]},\"api.bible.text\":{\"uri\":\"api\\/bible\\/{reference}\",\"methods\":[\"GET\",\"HEAD\"],\"wheres\":{\"reference\":\"[\\\\w\\\\d,\\\\-]+\"},\"parameters\":[\"reference\"]},\"storage.local\":{\"uri\":\"storage\\/{path}\",\"methods\":[\"GET\",\"HEAD\"],\"wheres\":{\"path\":\".*\"},\"parameters\":[\"path\"]}});</script>    <script type=\"module\" src=\"http://[::1]:5173/@vite/client\"></script><script type=\"module\" src=\"http://[::1]:5173/resources/js/app.ts\"></script><script type=\"module\" src=\"http://[::1]:5173/resources/js/Pages/NotFound.vue\"></script>    </head>\n\n<body class=\"font-sans antialiased\">\n    <div id=\"app\" data-page=\"{&quot;component&quot;:&quot;NotFound&quot;,&quot;props&quot;:{&quot;errors&quot;:{},&quot;books&quot;:{&quot;sections&quot;:[{&quot;name&quot;:&quot;Altes Testament&quot;,&quot;books&quot;:[{&quot;id&quot;:1,&quot;name&quot;:&quot;1. Mose&quot;,&quot;shortName&quot;:&quot;1Mo&quot;,&quot;chapterCount&quot;:50,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;law&quot;,&quot;order&quot;:1,&quot;slug&quot;:&quot;1.Mose&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:2,&quot;name&quot;:&quot;2. Mose&quot;,&quot;shortName&quot;:&quot;2Mo&quot;,&quot;chapterCount&quot;:40,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;law&quot;,&quot;order&quot;:2,&quot;slug&quot;:&quot;2.Mose&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:3,&quot;name&quot;:&quot;3. Mose&quot;,&quot;shortName&quot;:&quot;3Mo&quot;,&quot;chapterCount&quot;:27,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;law&quot;,&quot;order&quot;:3,&quot;slug&quot;:&quot;3.Mose&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:4,&quot;name&quot;:&quot;4. Mose&quot;,&quot;shortName&quot;:&quot;4Mo&quot;,&quot;chapterCount&quot;:36,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;law&quot;,&quot;order&quot;:4,&quot;slug&quot;:&quot;4.Mose&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:5,&quot;name&quot;:&quot;5. Mose&quot;,&quot;shortName&quot;:&quot;5Mo&quot;,&quot;chapterCount&quot;:34,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;law&quot;,&quot;order&quot;:5,&quot;slug&quot;:&quot;5.Mose&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:6,&quot;name&quot;:&quot;Josua&quot;,&quot;shortName&quot;:&quot;Jos&quot;,&quot;chapterCount&quot;:24,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:6,&quot;slug&quot;:&quot;Josua&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:7,&quot;name&quot;:&quot;Richter&quot;,&quot;shortName&quot;:&quot;Ri&quot;,&quot;chapterCount&quot;:21,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:7,&quot;slug&quot;:&quot;Richter&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:8,&quot;name&quot;:&quot;Ruth&quot;,&quot;shortName&quot;:&quot;Rt&quot;,&quot;chapterCount&quot;:4,&quot;chapters&quot;:[1,2,3,4],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:8,&quot;slug&quot;:&quot;Ruth&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:9,&quot;name&quot;:&quot;1. Samuel&quot;,&quot;shortName&quot;:&quot;1Sam&quot;,&quot;chapterCount&quot;:31,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:9,&quot;slug&quot;:&quot;1.Samuel&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:10,&quot;name&quot;:&quot;2. Samuel&quot;,&quot;shortName&quot;:&quot;2Sam&quot;,&quot;chapterCount&quot;:24,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:10,&quot;slug&quot;:&quot;2.Samuel&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:11,&quot;name&quot;:&quot;1. K\\u00f6nige&quot;,&quot;shortName&quot;:&quot;1K\\u00f6n&quot;,&quot;chapterCount&quot;:22,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:11,&quot;slug&quot;:&quot;1.K\\u00f6nige&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:12,&quot;name&quot;:&quot;2. K\\u00f6nige&quot;,&quot;shortName&quot;:&quot;2K\\u00f6n&quot;,&quot;chapterCount&quot;:25,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:12,&quot;slug&quot;:&quot;2.K\\u00f6nige&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:13,&quot;name&quot;:&quot;1. Chronik&quot;,&quot;shortName&quot;:&quot;1Chr&quot;,&quot;chapterCount&quot;:29,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:13,&quot;slug&quot;:&quot;1.Chronik&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:14,&quot;name&quot;:&quot;2. Chronik&quot;,&quot;shortName&quot;:&quot;2Chr&quot;,&quot;chapterCount&quot;:36,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:14,&quot;slug&quot;:&quot;2.Chronik&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:15,&quot;name&quot;:&quot;Esra&quot;,&quot;shortName&quot;:&quot;Esra&quot;,&quot;chapterCount&quot;:10,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:15,&quot;slug&quot;:&quot;Esra&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:16,&quot;name&quot;:&quot;Nehemia&quot;,&quot;shortName&quot;:&quot;Neh&quot;,&quot;chapterCount&quot;:13,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:16,&quot;slug&quot;:&quot;Nehemia&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:17,&quot;name&quot;:&quot;Esther&quot;,&quot;shortName&quot;:&quot;Est&quot;,&quot;chapterCount&quot;:10,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:17,&quot;slug&quot;:&quot;Esther&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:18,&quot;name&quot;:&quot;Hiob&quot;,&quot;shortName&quot;:&quot;Hi&quot;,&quot;chapterCount&quot;:42,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;wisdom&quot;,&quot;order&quot;:18,&quot;slug&quot;:&quot;Hiob&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:19,&quot;name&quot;:&quot;Psalmen&quot;,&quot;shortName&quot;:&quot;Ps&quot;,&quot;chapterCount&quot;:150,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;wisdom&quot;,&quot;order&quot;:19,&quot;slug&quot;:&quot;Psalmen&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:20,&quot;name&quot;:&quot;Spr\\u00fcche&quot;,&quot;shortName&quot;:&quot;Spr&quot;,&quot;chapterCount&quot;:31,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;wisdom&quot;,&quot;order&quot;:20,&quot;slug&quot;:&quot;Spr\\u00fcche&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:21,&quot;name&quot;:&quot;Prediger&quot;,&quot;shortName&quot;:&quot;Pred&quot;,&quot;chapterCount&quot;:12,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;wisdom&quot;,&quot;order&quot;:21,&quot;slug&quot;:&quot;Prediger&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:22,&quot;name&quot;:&quot;Hohelied&quot;,&quot;shortName&quot;:&quot;Hl&quot;,&quot;chapterCount&quot;:8,&quot;chapters&quot;:[1,2,3,4,5,6,7,8],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;wisdom&quot;,&quot;order&quot;:22,&quot;slug&quot;:&quot;Hohelied&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:23,&quot;name&quot;:&quot;Jesaja&quot;,&quot;shortName&quot;:&quot;Jes&quot;,&quot;chapterCount&quot;:66,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:23,&quot;slug&quot;:&quot;Jesaja&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:24,&quot;name&quot;:&quot;Jeremia&quot;,&quot;shortName&quot;:&quot;Jer&quot;,&quot;chapterCount&quot;:52,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:24,&quot;slug&quot;:&quot;Jeremia&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:25,&quot;name&quot;:&quot;Klagelieder&quot;,&quot;shortName&quot;:&quot;Kla&quot;,&quot;chapterCount&quot;:5,&quot;chapters&quot;:[1,2,3,4,5],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;wisdom&quot;,&quot;order&quot;:25,&quot;slug&quot;:&quot;Klagelieder&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:26,&quot;name&quot;:&quot;Hesekiel&quot;,&quot;shortName&quot;:&quot;Hes&quot;,&quot;chapterCount&quot;:48,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:26,&quot;slug&quot;:&quot;Hesekiel&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:27,&quot;name&quot;:&quot;Daniel&quot;,&quot;shortName&quot;:&quot;Dan&quot;,&quot;chapterCount&quot;:12,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:27,&quot;slug&quot;:&quot;Daniel&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:28,&quot;name&quot;:&quot;Hosea&quot;,&quot;shortName&quot;:&quot;Hos&quot;,&quot;chapterCount&quot;:14,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:28,&quot;slug&quot;:&quot;Hosea&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:29,&quot;name&quot;:&quot;Joel&quot;,&quot;shortName&quot;:&quot;Joel&quot;,&quot;chapterCount&quot;:3,&quot;chapters&quot;:[1,2,3],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:29,&quot;slug&quot;:&quot;Joel&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:30,&quot;name&quot;:&quot;Amos&quot;,&quot;shortName&quot;:&quot;Am&quot;,&quot;chapterCount&quot;:9,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:30,&quot;slug&quot;:&quot;Amos&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:31,&quot;name&quot;:&quot;Obadja&quot;,&quot;shortName&quot;:&quot;Ob&quot;,&quot;chapterCount&quot;:1,&quot;chapters&quot;:[1],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:31,&quot;slug&quot;:&quot;Obadja&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:32,&quot;name&quot;:&quot;Jona&quot;,&quot;shortName&quot;:&quot;Jon&quot;,&quot;chapterCount&quot;:4,&quot;chapters&quot;:[1,2,3,4],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:32,&quot;slug&quot;:&quot;Jona&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:33,&quot;name&quot;:&quot;Micha&quot;,&quot;shortName&quot;:&quot;Mi&quot;,&quot;chapterCount&quot;:7,&quot;chapters&quot;:[1,2,3,4,5,6,7],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:33,&quot;slug&quot;:&quot;Micha&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:34,&quot;name&quot;:&quot;Nahum&quot;,&quot;shortName&quot;:&quot;Nah&quot;,&quot;chapterCount&quot;:3,&quot;chapters&quot;:[1,2,3],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:34,&quot;slug&quot;:&quot;Nahum&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:35,&quot;name&quot;:&quot;Habakuk&quot;,&quot;shortName&quot;:&quot;Hab&quot;,&quot;chapterCount&quot;:3,&quot;chapters&quot;:[1,2,3],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:35,&quot;slug&quot;:&quot;Habakuk&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:36,&quot;name&quot;:&quot;Zephanja&quot;,&quot;shortName&quot;:&quot;Zeph&quot;,&quot;chapterCount&quot;:3,&quot;chapters&quot;:[1,2,3],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:36,&quot;slug&quot;:&quot;Zephanja&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:37,&quot;name&quot;:&quot;Haggai&quot;,&quot;shortName&quot;:&quot;Hag&quot;,&quot;chapterCount&quot;:2,&quot;chapters&quot;:[1,2],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:37,&quot;slug&quot;:&quot;Haggai&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:38,&quot;name&quot;:&quot;Sacharja&quot;,&quot;shortName&quot;:&quot;Sach&quot;,&quot;chapterCount&quot;:14,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:38,&quot;slug&quot;:&quot;Sacharja&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:39,&quot;name&quot;:&quot;Maleachi&quot;,&quot;shortName&quot;:&quot;Mal&quot;,&quot;chapterCount&quot;:4,&quot;chapters&quot;:[1,2,3,4],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:39,&quot;slug&quot;:&quot;Maleachi&quot;,&quot;hasContent&quot;:false}]},{&quot;name&quot;:&quot;Neues Testament&quot;,&quot;books&quot;:[{&quot;id&quot;:40,&quot;name&quot;:&quot;Matth\\u00e4us&quot;,&quot;shortName&quot;:&quot;Mt&quot;,&quot;chapterCount&quot;:28,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;gospel&quot;,&quot;order&quot;:40,&quot;slug&quot;:&quot;Matth\\u00e4us&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:41,&quot;name&quot;:&quot;Markus&quot;,&quot;shortName&quot;:&quot;Mk&quot;,&quot;chapterCount&quot;:16,&quot;chapters&quot;:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;gospel&quot;,&quot;order&quot;:41,&quot;slug&quot;:&quot;Markus&quot;,&quot;hasContent&quot;:true},{&quot;id&quot;:42,&quot;name&quot;:&quot;Lukas&quot;,&quot;shortName&quot;:&quot;Lk&quot;,&quot;chapterCount&quot;:24,&quot;chapters&quot;:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;gospel&quot;,&quot;order&quot;:42,&quot;slug&quot;:&quot;Lukas&quot;,&quot;hasContent&quot;:true},{&quot;id&quot;:43,&quot;name&quot;:&quot;Die Heilsbotschaft nach Johannes&quot;,&quot;shortName&quot;:&quot;Joh&quot;,&quot;chapterCount&quot;:21,&quot;chapters&quot;:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;gospel&quot;,&quot;order&quot;:43,&quot;slug&quot;:&quot;Johannes&quot;,&quot;hasContent&quot;:true},{&quot;id&quot;:44,&quot;name&quot;:&quot;Apostelgeschichte&quot;,&quot;shortName&quot;:&quot;Apg&quot;,&quot;chapterCount&quot;:28,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:44,&quot;slug&quot;:&quot;Apostelgeschichte&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:45,&quot;name&quot;:&quot;R\\u00f6mer&quot;,&quot;shortName&quot;:&quot;R\\u00f6m&quot;,&quot;chapterCount&quot;:16,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:45,&quot;slug&quot;:&quot;R\\u00f6mer&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:46,&quot;name&quot;:&quot;1. Korinther&quot;,&quot;shortName&quot;:&quot;1Kor&quot;,&quot;chapterCount&quot;:16,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:46,&quot;slug&quot;:&quot;1.Korinther&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:47,&quot;name&quot;:&quot;2. Korinther&quot;,&quot;shortName&quot;:&quot;2Kor&quot;,&quot;chapterCount&quot;:13,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:47,&quot;slug&quot;:&quot;2.Korinther&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:48,&quot;name&quot;:&quot;Galater&quot;,&quot;shortName&quot;:&quot;Gal&quot;,&quot;chapterCount&quot;:6,&quot;chapters&quot;:[1,2,3,4,5,6],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:48,&quot;slug&quot;:&quot;Galater&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:49,&quot;name&quot;:&quot;Epheser&quot;,&quot;shortName&quot;:&quot;Eph&quot;,&quot;chapterCount&quot;:6,&quot;chapters&quot;:[1,2,3,4,5,6],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:49,&quot;slug&quot;:&quot;Epheser&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:50,&quot;name&quot;:&quot;Philipper&quot;,&quot;shortName&quot;:&quot;Phil&quot;,&quot;chapterCount&quot;:4,&quot;chapters&quot;:[1,2,3,4],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:50,&quot;slug&quot;:&quot;Philipper&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:51,&quot;name&quot;:&quot;Kolosser&quot;,&quot;shortName&quot;:&quot;Kol&quot;,&quot;chapterCount&quot;:4,&quot;chapters&quot;:[1,2,3,4],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:51,&quot;slug&quot;:&quot;Kolosser&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:52,&quot;name&quot;:&quot;1. Thessalonicher&quot;,&quot;shortName&quot;:&quot;1Thes&quot;,&quot;chapterCount&quot;:5,&quot;chapters&quot;:[1,2,3,4,5],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:52,&quot;slug&quot;:&quot;1.Thessalonicher&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:53,&quot;name&quot;:&quot;2. Thessalonicher&quot;,&quot;shortName&quot;:&quot;2Thes&quot;,&quot;chapterCount&quot;:3,&quot;chapters&quot;:[1,2,3],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:53,&quot;slug&quot;:&quot;2.Thessalonicher&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:54,&quot;name&quot;:&quot;1. Timotheus&quot;,&quot;shortName&quot;:&quot;1Tim&quot;,&quot;chapterCount&quot;:6,&quot;chapters&quot;:[1,2,3,4,5,6],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:54,&quot;slug&quot;:&quot;1.Timotheus&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:55,&quot;name&quot;:&quot;2. Timotheus&quot;,&quot;shortName&quot;:&quot;2Tim&quot;,&quot;chapterCount&quot;:4,&quot;chapters&quot;:[1,2,3,4],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:55,&quot;slug&quot;:&quot;2.Timotheus&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:56,&quot;name&quot;:&quot;Titus&quot;,&quot;shortName&quot;:&quot;Tit&quot;,&quot;chapterCount&quot;:3,&quot;chapters&quot;:[1,2,3],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:56,&quot;slug&quot;:&quot;Titus&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:57,&quot;name&quot;:&quot;Philemon&quot;,&quot;shortName&quot;:&quot;Phim&quot;,&quot;chapterCount&quot;:1,&quot;chapters&quot;:[1],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:57,&quot;slug&quot;:&quot;Philemon&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:58,&quot;name&quot;:&quot;Hebr\\u00e4er&quot;,&quot;shortName&quot;:&quot;Heb&quot;,&quot;chapterCount&quot;:13,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:58,&quot;slug&quot;:&quot;Hebr\\u00e4er&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:59,&quot;name&quot;:&quot;Jakobus&quot;,&quot;shortName&quot;:&quot;Jak&quot;,&quot;chapterCount&quot;:5,&quot;chapters&quot;:[1,2,3,4,5],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:59,&quot;slug&quot;:&quot;Jakobus&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:60,&quot;name&quot;:&quot;1. Petrus&quot;,&quot;shortName&quot;:&quot;1Pet&quot;,&quot;chapterCount&quot;:5,&quot;chapters&quot;:[1,2,3,4,5],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:60,&quot;slug&quot;:&quot;1.Petrus&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:61,&quot;name&quot;:&quot;2. Petrus&quot;,&quot;shortName&quot;:&quot;2Pet&quot;,&quot;chapterCount&quot;:3,&quot;chapters&quot;:[1,2,3],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:61,&quot;slug&quot;:&quot;2.Petrus&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:62,&quot;name&quot;:&quot;1. Johannes&quot;,&quot;shortName&quot;:&quot;1Joh&quot;,&quot;chapterCount&quot;:5,&quot;chapters&quot;:[1,2,3,4,5],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:62,&quot;slug&quot;:&quot;1.Johannes&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:63,&quot;name&quot;:&quot;2. Johannes&quot;,&quot;shortName&quot;:&quot;2Joh&quot;,&quot;chapterCount&quot;:1,&quot;chapters&quot;:[1],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:63,&quot;slug&quot;:&quot;2.Johannes&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:64,&quot;name&quot;:&quot;3. Johannes&quot;,&quot;shortName&quot;:&quot;3Joh&quot;,&quot;chapterCount&quot;:1,&quot;chapters&quot;:[1],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:64,&quot;slug&quot;:&quot;3.Johannes&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:65,&quot;name&quot;:&quot;Judas&quot;,&quot;shortName&quot;:&quot;Jud&quot;,&quot;chapterCount&quot;:1,&quot;chapters&quot;:[1],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:65,&quot;slug&quot;:&quot;Judas&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:66,&quot;name&quot;:&quot;Offenbarung&quot;,&quot;shortName&quot;:&quot;Offb&quot;,&quot;chapterCount&quot;:22,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;apocalypse&quot;,&quot;order&quot;:66,&quot;slug&quot;:&quot;Offenbarung&quot;,&quot;hasContent&quot;:false}]}],&quot;availableBooks&quot;:[{&quot;slug&quot;:&quot;Markus&quot;,&quot;order&quot;:41},{&quot;slug&quot;:&quot;Lukas&quot;,&quot;order&quot;:42},{&quot;slug&quot;:&quot;Johannes&quot;,&quot;order&quot;:43}]},&quot;env&quot;:&quot;local&quot;,&quot;auth&quot;:{&quot;user&quot;:null},&quot;requestedPath&quot;:&quot;api\\/search&quot;},&quot;url&quot;:&quot;\\/api\\/search&quot;,&quot;version&quot;:&quot;18765f3fa436d07c5ef1cbbbc3fa3b37&quot;,&quot;clearHistory&quot;:false,&quot;encryptHistory&quot;:false}\"></div></body>\n\n</html>\n"
      tags:
        - Endpoints
      security: []
  '/api/bible/{reference}':
    get:
      summary: 'Get Bible text based on reference'
      operationId: getBibleTextBasedOnReference
      description: ''
      parameters: []
      responses:
        404:
          description: ''
          content:
            text/plain:
              schema:
                type: string
                example: "<!DOCTYPE html>\n<html lang=\"de\">\n\n<head>\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\n\n    <title inertia></title>\n\n    <!-- Fonts -->\n    <!-- Preconnect to font domains -->\n    <link rel=\"preconnect\" href=\"https://fonts.bunny.net\" crossorigin>\n    <link rel=\"preconnect\" href=\"https://use.typekit.net\" as=\"font\" type=\"font/woff2\" crossorigin>\n\n    <!-- Preload critical fonts -->\n    <link rel=\"preload\" href=\"/fonts/ThanatosText-Book.woff2\" as=\"font\" type=\"font/woff2\" crossorigin>\n\n    <!-- Load fonts -->\n    <link href=\"https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap\" rel=\"stylesheet\" />\n    <link rel=\"stylesheet\" href=\"https://use.typekit.net/kzb8yhl.css\" media=\"print\" onload=\"this.media='all'\">\n\n    <!-- Fallback for typekit fonts -->\n    <noscript>\n        <link rel=\"stylesheet\" href=\"https://use.typekit.net/kzb8yhl.css\">\n    </noscript>\n\n    <!-- Local font definition -->\n    <style>\n        @font-face {\n            font-family: 'ThanatosText';\n            src: url('/fonts/ThanatosText-Book.woff2') format('woff2');\n            font-weight: normal;\n            font-style: normal;\n            font-display: swap;\n        }\n    </style>\n\n    <!-- Scripts -->\n    <script type=\"text/javascript\">Object.assign(Ziggy.routes,{\"search.index\":{\"uri\":\"search\",\"methods\":[\"GET\",\"HEAD\"]},\"search.query\":{\"uri\":\"search\\/{query}\",\"methods\":[\"GET\",\"HEAD\"],\"wheres\":{\"query\":\"[^\\/]+\"},\"parameters\":[\"query\"]},\"search.paged\":{\"uri\":\"search\\/{query}\\/{page?}\",\"methods\":[\"GET\",\"HEAD\"],\"wheres\":{\"query\":\"[^\\/]+\",\"page\":\"[0-9]+\"},\"parameters\":[\"query\",\"page\"]},\"search.settings\":{\"uri\":\"search\\/settings\",\"methods\":[\"POST\"]},\"dashboard\":{\"uri\":\"dashboard\",\"methods\":[\"GET\",\"HEAD\"]},\"bible.import\":{\"uri\":\"import-bible\",\"methods\":[\"GET\",\"HEAD\"]},\"bible.import.store\":{\"uri\":\"import-bible\",\"methods\":[\"POST\"]},\"file.upload\":{\"uri\":\"api\\/upload\",\"methods\":[\"POST\"]},\"profile.edit\":{\"uri\":\"profile\",\"methods\":[\"GET\",\"HEAD\"]},\"profile.update\":{\"uri\":\"profile\",\"methods\":[\"PATCH\"]},\"profile.destroy\":{\"uri\":\"profile\",\"methods\":[\"DELETE\"]},\"books.show\":{\"uri\":\"{reference}\",\"methods\":[\"GET\",\"HEAD\"],\"wheres\":{\"reference\":\"[\\\\w\\\\d\\\\.,\\\\-\\\\+]+\"},\"parameters\":[\"reference\"]},\"register\":{\"uri\":\"register\",\"methods\":[\"GET\",\"HEAD\"]},\"login\":{\"uri\":\"login\",\"methods\":[\"GET\",\"HEAD\"]},\"password.request\":{\"uri\":\"forgot-password\",\"methods\":[\"GET\",\"HEAD\"]},\"password.email\":{\"uri\":\"forgot-password\",\"methods\":[\"POST\"]},\"password.reset\":{\"uri\":\"reset-password\\/{token}\",\"methods\":[\"GET\",\"HEAD\"],\"parameters\":[\"token\"]},\"password.store\":{\"uri\":\"reset-password\",\"methods\":[\"POST\"]},\"verification.notice\":{\"uri\":\"verify-email\",\"methods\":[\"GET\",\"HEAD\"]},\"verification.verify\":{\"uri\":\"verify-email\\/{id}\\/{hash}\",\"methods\":[\"GET\",\"HEAD\"],\"parameters\":[\"id\",\"hash\"]},\"verification.send\":{\"uri\":\"email\\/verification-notification\",\"methods\":[\"POST\"]},\"password.confirm\":{\"uri\":\"confirm-password\",\"methods\":[\"GET\",\"HEAD\"]},\"password.update\":{\"uri\":\"password\",\"methods\":[\"PUT\"]},\"logout\":{\"uri\":\"logout\",\"methods\":[\"POST\"]},\"api.chapters.fetch\":{\"uri\":\"api\\/chapters\\/fetch\",\"methods\":[\"GET\",\"HEAD\"]},\"api.books\":{\"uri\":\"api\\/books\",\"methods\":[\"GET\",\"HEAD\"]},\"api.books.content-status\":{\"uri\":\"api\\/books\\/content-status\",\"methods\":[\"GET\",\"HEAD\"]},\"api.books.show\":{\"uri\":\"api\\/books\\/{slug}\",\"methods\":[\"GET\",\"HEAD\"],\"parameters\":[\"slug\"]},\"api.chapters.adjacent\":{\"uri\":\"api\\/chapters\\/{reference}\\/adjacent\",\"methods\":[\"GET\",\"HEAD\"],\"wheres\":{\"reference\":\"[\\\\w\\\\d,\\\\-]+\"},\"parameters\":[\"reference\"]},\"api.search.books\":{\"uri\":\"api\\/search\\/books\",\"methods\":[\"GET\",\"HEAD\"]},\"api.search\":{\"uri\":\"api\\/search\",\"methods\":[\"GET\",\"HEAD\"]},\"api.bible.text\":{\"uri\":\"api\\/bible\\/{reference}\",\"methods\":[\"GET\",\"HEAD\"],\"wheres\":{\"reference\":\"[\\\\w\\\\d,\\\\-]+\"},\"parameters\":[\"reference\"]},\"storage.local\":{\"uri\":\"storage\\/{path}\",\"methods\":[\"GET\",\"HEAD\"],\"wheres\":{\"path\":\".*\"},\"parameters\":[\"path\"]}});</script>    <script type=\"module\" src=\"http://[::1]:5173/@vite/client\"></script><script type=\"module\" src=\"http://[::1]:5173/resources/js/app.ts\"></script><script type=\"module\" src=\"http://[::1]:5173/resources/js/Pages/NotFound.vue\"></script>    </head>\n\n<body class=\"font-sans antialiased\">\n    <div id=\"app\" data-page=\"{&quot;component&quot;:&quot;NotFound&quot;,&quot;props&quot;:{&quot;errors&quot;:{},&quot;books&quot;:{&quot;sections&quot;:[{&quot;name&quot;:&quot;Altes Testament&quot;,&quot;books&quot;:[{&quot;id&quot;:1,&quot;name&quot;:&quot;1. Mose&quot;,&quot;shortName&quot;:&quot;1Mo&quot;,&quot;chapterCount&quot;:50,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;law&quot;,&quot;order&quot;:1,&quot;slug&quot;:&quot;1.Mose&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:2,&quot;name&quot;:&quot;2. Mose&quot;,&quot;shortName&quot;:&quot;2Mo&quot;,&quot;chapterCount&quot;:40,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;law&quot;,&quot;order&quot;:2,&quot;slug&quot;:&quot;2.Mose&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:3,&quot;name&quot;:&quot;3. Mose&quot;,&quot;shortName&quot;:&quot;3Mo&quot;,&quot;chapterCount&quot;:27,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;law&quot;,&quot;order&quot;:3,&quot;slug&quot;:&quot;3.Mose&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:4,&quot;name&quot;:&quot;4. Mose&quot;,&quot;shortName&quot;:&quot;4Mo&quot;,&quot;chapterCount&quot;:36,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;law&quot;,&quot;order&quot;:4,&quot;slug&quot;:&quot;4.Mose&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:5,&quot;name&quot;:&quot;5. Mose&quot;,&quot;shortName&quot;:&quot;5Mo&quot;,&quot;chapterCount&quot;:34,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;law&quot;,&quot;order&quot;:5,&quot;slug&quot;:&quot;5.Mose&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:6,&quot;name&quot;:&quot;Josua&quot;,&quot;shortName&quot;:&quot;Jos&quot;,&quot;chapterCount&quot;:24,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:6,&quot;slug&quot;:&quot;Josua&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:7,&quot;name&quot;:&quot;Richter&quot;,&quot;shortName&quot;:&quot;Ri&quot;,&quot;chapterCount&quot;:21,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:7,&quot;slug&quot;:&quot;Richter&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:8,&quot;name&quot;:&quot;Ruth&quot;,&quot;shortName&quot;:&quot;Rt&quot;,&quot;chapterCount&quot;:4,&quot;chapters&quot;:[1,2,3,4],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:8,&quot;slug&quot;:&quot;Ruth&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:9,&quot;name&quot;:&quot;1. Samuel&quot;,&quot;shortName&quot;:&quot;1Sam&quot;,&quot;chapterCount&quot;:31,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:9,&quot;slug&quot;:&quot;1.Samuel&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:10,&quot;name&quot;:&quot;2. Samuel&quot;,&quot;shortName&quot;:&quot;2Sam&quot;,&quot;chapterCount&quot;:24,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:10,&quot;slug&quot;:&quot;2.Samuel&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:11,&quot;name&quot;:&quot;1. K\\u00f6nige&quot;,&quot;shortName&quot;:&quot;1K\\u00f6n&quot;,&quot;chapterCount&quot;:22,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:11,&quot;slug&quot;:&quot;1.K\\u00f6nige&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:12,&quot;name&quot;:&quot;2. K\\u00f6nige&quot;,&quot;shortName&quot;:&quot;2K\\u00f6n&quot;,&quot;chapterCount&quot;:25,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:12,&quot;slug&quot;:&quot;2.K\\u00f6nige&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:13,&quot;name&quot;:&quot;1. Chronik&quot;,&quot;shortName&quot;:&quot;1Chr&quot;,&quot;chapterCount&quot;:29,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:13,&quot;slug&quot;:&quot;1.Chronik&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:14,&quot;name&quot;:&quot;2. Chronik&quot;,&quot;shortName&quot;:&quot;2Chr&quot;,&quot;chapterCount&quot;:36,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:14,&quot;slug&quot;:&quot;2.Chronik&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:15,&quot;name&quot;:&quot;Esra&quot;,&quot;shortName&quot;:&quot;Esra&quot;,&quot;chapterCount&quot;:10,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:15,&quot;slug&quot;:&quot;Esra&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:16,&quot;name&quot;:&quot;Nehemia&quot;,&quot;shortName&quot;:&quot;Neh&quot;,&quot;chapterCount&quot;:13,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:16,&quot;slug&quot;:&quot;Nehemia&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:17,&quot;name&quot;:&quot;Esther&quot;,&quot;shortName&quot;:&quot;Est&quot;,&quot;chapterCount&quot;:10,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:17,&quot;slug&quot;:&quot;Esther&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:18,&quot;name&quot;:&quot;Hiob&quot;,&quot;shortName&quot;:&quot;Hi&quot;,&quot;chapterCount&quot;:42,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;wisdom&quot;,&quot;order&quot;:18,&quot;slug&quot;:&quot;Hiob&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:19,&quot;name&quot;:&quot;Psalmen&quot;,&quot;shortName&quot;:&quot;Ps&quot;,&quot;chapterCount&quot;:150,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;wisdom&quot;,&quot;order&quot;:19,&quot;slug&quot;:&quot;Psalmen&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:20,&quot;name&quot;:&quot;Spr\\u00fcche&quot;,&quot;shortName&quot;:&quot;Spr&quot;,&quot;chapterCount&quot;:31,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;wisdom&quot;,&quot;order&quot;:20,&quot;slug&quot;:&quot;Spr\\u00fcche&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:21,&quot;name&quot;:&quot;Prediger&quot;,&quot;shortName&quot;:&quot;Pred&quot;,&quot;chapterCount&quot;:12,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;wisdom&quot;,&quot;order&quot;:21,&quot;slug&quot;:&quot;Prediger&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:22,&quot;name&quot;:&quot;Hohelied&quot;,&quot;shortName&quot;:&quot;Hl&quot;,&quot;chapterCount&quot;:8,&quot;chapters&quot;:[1,2,3,4,5,6,7,8],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;wisdom&quot;,&quot;order&quot;:22,&quot;slug&quot;:&quot;Hohelied&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:23,&quot;name&quot;:&quot;Jesaja&quot;,&quot;shortName&quot;:&quot;Jes&quot;,&quot;chapterCount&quot;:66,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:23,&quot;slug&quot;:&quot;Jesaja&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:24,&quot;name&quot;:&quot;Jeremia&quot;,&quot;shortName&quot;:&quot;Jer&quot;,&quot;chapterCount&quot;:52,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:24,&quot;slug&quot;:&quot;Jeremia&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:25,&quot;name&quot;:&quot;Klagelieder&quot;,&quot;shortName&quot;:&quot;Kla&quot;,&quot;chapterCount&quot;:5,&quot;chapters&quot;:[1,2,3,4,5],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;wisdom&quot;,&quot;order&quot;:25,&quot;slug&quot;:&quot;Klagelieder&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:26,&quot;name&quot;:&quot;Hesekiel&quot;,&quot;shortName&quot;:&quot;Hes&quot;,&quot;chapterCount&quot;:48,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:26,&quot;slug&quot;:&quot;Hesekiel&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:27,&quot;name&quot;:&quot;Daniel&quot;,&quot;shortName&quot;:&quot;Dan&quot;,&quot;chapterCount&quot;:12,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:27,&quot;slug&quot;:&quot;Daniel&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:28,&quot;name&quot;:&quot;Hosea&quot;,&quot;shortName&quot;:&quot;Hos&quot;,&quot;chapterCount&quot;:14,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:28,&quot;slug&quot;:&quot;Hosea&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:29,&quot;name&quot;:&quot;Joel&quot;,&quot;shortName&quot;:&quot;Joel&quot;,&quot;chapterCount&quot;:3,&quot;chapters&quot;:[1,2,3],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:29,&quot;slug&quot;:&quot;Joel&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:30,&quot;name&quot;:&quot;Amos&quot;,&quot;shortName&quot;:&quot;Am&quot;,&quot;chapterCount&quot;:9,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:30,&quot;slug&quot;:&quot;Amos&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:31,&quot;name&quot;:&quot;Obadja&quot;,&quot;shortName&quot;:&quot;Ob&quot;,&quot;chapterCount&quot;:1,&quot;chapters&quot;:[1],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:31,&quot;slug&quot;:&quot;Obadja&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:32,&quot;name&quot;:&quot;Jona&quot;,&quot;shortName&quot;:&quot;Jon&quot;,&quot;chapterCount&quot;:4,&quot;chapters&quot;:[1,2,3,4],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:32,&quot;slug&quot;:&quot;Jona&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:33,&quot;name&quot;:&quot;Micha&quot;,&quot;shortName&quot;:&quot;Mi&quot;,&quot;chapterCount&quot;:7,&quot;chapters&quot;:[1,2,3,4,5,6,7],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:33,&quot;slug&quot;:&quot;Micha&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:34,&quot;name&quot;:&quot;Nahum&quot;,&quot;shortName&quot;:&quot;Nah&quot;,&quot;chapterCount&quot;:3,&quot;chapters&quot;:[1,2,3],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:34,&quot;slug&quot;:&quot;Nahum&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:35,&quot;name&quot;:&quot;Habakuk&quot;,&quot;shortName&quot;:&quot;Hab&quot;,&quot;chapterCount&quot;:3,&quot;chapters&quot;:[1,2,3],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:35,&quot;slug&quot;:&quot;Habakuk&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:36,&quot;name&quot;:&quot;Zephanja&quot;,&quot;shortName&quot;:&quot;Zeph&quot;,&quot;chapterCount&quot;:3,&quot;chapters&quot;:[1,2,3],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:36,&quot;slug&quot;:&quot;Zephanja&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:37,&quot;name&quot;:&quot;Haggai&quot;,&quot;shortName&quot;:&quot;Hag&quot;,&quot;chapterCount&quot;:2,&quot;chapters&quot;:[1,2],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:37,&quot;slug&quot;:&quot;Haggai&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:38,&quot;name&quot;:&quot;Sacharja&quot;,&quot;shortName&quot;:&quot;Sach&quot;,&quot;chapterCount&quot;:14,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:38,&quot;slug&quot;:&quot;Sacharja&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:39,&quot;name&quot;:&quot;Maleachi&quot;,&quot;shortName&quot;:&quot;Mal&quot;,&quot;chapterCount&quot;:4,&quot;chapters&quot;:[1,2,3,4],&quot;testament&quot;:&quot;ot&quot;,&quot;category&quot;:&quot;prophecy&quot;,&quot;order&quot;:39,&quot;slug&quot;:&quot;Maleachi&quot;,&quot;hasContent&quot;:false}]},{&quot;name&quot;:&quot;Neues Testament&quot;,&quot;books&quot;:[{&quot;id&quot;:40,&quot;name&quot;:&quot;Matth\\u00e4us&quot;,&quot;shortName&quot;:&quot;Mt&quot;,&quot;chapterCount&quot;:28,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;gospel&quot;,&quot;order&quot;:40,&quot;slug&quot;:&quot;Matth\\u00e4us&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:41,&quot;name&quot;:&quot;Markus&quot;,&quot;shortName&quot;:&quot;Mk&quot;,&quot;chapterCount&quot;:16,&quot;chapters&quot;:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;gospel&quot;,&quot;order&quot;:41,&quot;slug&quot;:&quot;Markus&quot;,&quot;hasContent&quot;:true},{&quot;id&quot;:42,&quot;name&quot;:&quot;Lukas&quot;,&quot;shortName&quot;:&quot;Lk&quot;,&quot;chapterCount&quot;:24,&quot;chapters&quot;:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;gospel&quot;,&quot;order&quot;:42,&quot;slug&quot;:&quot;Lukas&quot;,&quot;hasContent&quot;:true},{&quot;id&quot;:43,&quot;name&quot;:&quot;Die Heilsbotschaft nach Johannes&quot;,&quot;shortName&quot;:&quot;Joh&quot;,&quot;chapterCount&quot;:21,&quot;chapters&quot;:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;gospel&quot;,&quot;order&quot;:43,&quot;slug&quot;:&quot;Johannes&quot;,&quot;hasContent&quot;:true},{&quot;id&quot;:44,&quot;name&quot;:&quot;Apostelgeschichte&quot;,&quot;shortName&quot;:&quot;Apg&quot;,&quot;chapterCount&quot;:28,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;history&quot;,&quot;order&quot;:44,&quot;slug&quot;:&quot;Apostelgeschichte&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:45,&quot;name&quot;:&quot;R\\u00f6mer&quot;,&quot;shortName&quot;:&quot;R\\u00f6m&quot;,&quot;chapterCount&quot;:16,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:45,&quot;slug&quot;:&quot;R\\u00f6mer&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:46,&quot;name&quot;:&quot;1. Korinther&quot;,&quot;shortName&quot;:&quot;1Kor&quot;,&quot;chapterCount&quot;:16,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:46,&quot;slug&quot;:&quot;1.Korinther&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:47,&quot;name&quot;:&quot;2. Korinther&quot;,&quot;shortName&quot;:&quot;2Kor&quot;,&quot;chapterCount&quot;:13,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:47,&quot;slug&quot;:&quot;2.Korinther&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:48,&quot;name&quot;:&quot;Galater&quot;,&quot;shortName&quot;:&quot;Gal&quot;,&quot;chapterCount&quot;:6,&quot;chapters&quot;:[1,2,3,4,5,6],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:48,&quot;slug&quot;:&quot;Galater&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:49,&quot;name&quot;:&quot;Epheser&quot;,&quot;shortName&quot;:&quot;Eph&quot;,&quot;chapterCount&quot;:6,&quot;chapters&quot;:[1,2,3,4,5,6],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:49,&quot;slug&quot;:&quot;Epheser&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:50,&quot;name&quot;:&quot;Philipper&quot;,&quot;shortName&quot;:&quot;Phil&quot;,&quot;chapterCount&quot;:4,&quot;chapters&quot;:[1,2,3,4],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:50,&quot;slug&quot;:&quot;Philipper&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:51,&quot;name&quot;:&quot;Kolosser&quot;,&quot;shortName&quot;:&quot;Kol&quot;,&quot;chapterCount&quot;:4,&quot;chapters&quot;:[1,2,3,4],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:51,&quot;slug&quot;:&quot;Kolosser&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:52,&quot;name&quot;:&quot;1. Thessalonicher&quot;,&quot;shortName&quot;:&quot;1Thes&quot;,&quot;chapterCount&quot;:5,&quot;chapters&quot;:[1,2,3,4,5],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:52,&quot;slug&quot;:&quot;1.Thessalonicher&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:53,&quot;name&quot;:&quot;2. Thessalonicher&quot;,&quot;shortName&quot;:&quot;2Thes&quot;,&quot;chapterCount&quot;:3,&quot;chapters&quot;:[1,2,3],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:53,&quot;slug&quot;:&quot;2.Thessalonicher&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:54,&quot;name&quot;:&quot;1. Timotheus&quot;,&quot;shortName&quot;:&quot;1Tim&quot;,&quot;chapterCount&quot;:6,&quot;chapters&quot;:[1,2,3,4,5,6],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:54,&quot;slug&quot;:&quot;1.Timotheus&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:55,&quot;name&quot;:&quot;2. Timotheus&quot;,&quot;shortName&quot;:&quot;2Tim&quot;,&quot;chapterCount&quot;:4,&quot;chapters&quot;:[1,2,3,4],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:55,&quot;slug&quot;:&quot;2.Timotheus&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:56,&quot;name&quot;:&quot;Titus&quot;,&quot;shortName&quot;:&quot;Tit&quot;,&quot;chapterCount&quot;:3,&quot;chapters&quot;:[1,2,3],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:56,&quot;slug&quot;:&quot;Titus&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:57,&quot;name&quot;:&quot;Philemon&quot;,&quot;shortName&quot;:&quot;Phim&quot;,&quot;chapterCount&quot;:1,&quot;chapters&quot;:[1],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:57,&quot;slug&quot;:&quot;Philemon&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:58,&quot;name&quot;:&quot;Hebr\\u00e4er&quot;,&quot;shortName&quot;:&quot;Heb&quot;,&quot;chapterCount&quot;:13,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:58,&quot;slug&quot;:&quot;Hebr\\u00e4er&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:59,&quot;name&quot;:&quot;Jakobus&quot;,&quot;shortName&quot;:&quot;Jak&quot;,&quot;chapterCount&quot;:5,&quot;chapters&quot;:[1,2,3,4,5],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:59,&quot;slug&quot;:&quot;Jakobus&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:60,&quot;name&quot;:&quot;1. Petrus&quot;,&quot;shortName&quot;:&quot;1Pet&quot;,&quot;chapterCount&quot;:5,&quot;chapters&quot;:[1,2,3,4,5],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:60,&quot;slug&quot;:&quot;1.Petrus&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:61,&quot;name&quot;:&quot;2. Petrus&quot;,&quot;shortName&quot;:&quot;2Pet&quot;,&quot;chapterCount&quot;:3,&quot;chapters&quot;:[1,2,3],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:61,&quot;slug&quot;:&quot;2.Petrus&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:62,&quot;name&quot;:&quot;1. Johannes&quot;,&quot;shortName&quot;:&quot;1Joh&quot;,&quot;chapterCount&quot;:5,&quot;chapters&quot;:[1,2,3,4,5],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:62,&quot;slug&quot;:&quot;1.Johannes&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:63,&quot;name&quot;:&quot;2. Johannes&quot;,&quot;shortName&quot;:&quot;2Joh&quot;,&quot;chapterCount&quot;:1,&quot;chapters&quot;:[1],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:63,&quot;slug&quot;:&quot;2.Johannes&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:64,&quot;name&quot;:&quot;3. Johannes&quot;,&quot;shortName&quot;:&quot;3Joh&quot;,&quot;chapterCount&quot;:1,&quot;chapters&quot;:[1],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:64,&quot;slug&quot;:&quot;3.Johannes&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:65,&quot;name&quot;:&quot;Judas&quot;,&quot;shortName&quot;:&quot;Jud&quot;,&quot;chapterCount&quot;:1,&quot;chapters&quot;:[1],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;epistle&quot;,&quot;order&quot;:65,&quot;slug&quot;:&quot;Judas&quot;,&quot;hasContent&quot;:false},{&quot;id&quot;:66,&quot;name&quot;:&quot;Offenbarung&quot;,&quot;shortName&quot;:&quot;Offb&quot;,&quot;chapterCount&quot;:22,&quot;chapters&quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22],&quot;testament&quot;:&quot;nt&quot;,&quot;category&quot;:&quot;apocalypse&quot;,&quot;order&quot;:66,&quot;slug&quot;:&quot;Offenbarung&quot;,&quot;hasContent&quot;:false}]}],&quot;availableBooks&quot;:[{&quot;slug&quot;:&quot;Markus&quot;,&quot;order&quot;:41},{&quot;slug&quot;:&quot;Lukas&quot;,&quot;order&quot;:42},{&quot;slug&quot;:&quot;Johannes&quot;,&quot;order&quot;:43}]},&quot;env&quot;:&quot;local&quot;,&quot;auth&quot;:{&quot;user&quot;:null},&quot;requestedPath&quot;:&quot;api\\/bible\\/dww&quot;},&quot;url&quot;:&quot;\\/api\\/bible\\/dww&quot;,&quot;version&quot;:&quot;18765f3fa436d07c5ef1cbbbc3fa3b37&quot;,&quot;clearHistory&quot;:false,&quot;encryptHistory&quot;:false}\"></div></body>\n\n</html>\n"
      tags:
        - Endpoints
      security: []
    parameters:
      -
        in: path
        name: reference
        description: ''
        example: dww
        required: true
        schema:
          type: string
