<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>EsraBibel API Documentation</title>

    <link href="https://fonts.googleapis.com/css?family=Open+Sans&display=swap" rel="stylesheet">

    <link rel="stylesheet" href="../docs/css/theme-default.style.css" media="screen">
    <link rel="stylesheet" href="../docs/css/theme-default.print.css" media="print">

    <script src="https://cdn.jsdelivr.net/npm/lodash@4.17.10/lodash.min.js"></script>

    <link rel="stylesheet"
          href="https://unpkg.com/@highlightjs/cdn-assets@11.6.0/styles/obsidian.min.css">
    <script src="https://unpkg.com/@highlightjs/cdn-assets@11.6.0/highlight.min.js"></script>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jets/0.14.1/jets.min.js"></script>

    <style id="language-style">
        /* starts out as display none and is replaced with js later  */
                    body .content .bash-example code { display: none; }
                    body .content .javascript-example code { display: none; }
            </style>

    <script>
        var tryItOutBaseUrl = "http://esra-bibel.local/";
        var useCsrf = Boolean();
        var csrfUrl = "/sanctum/csrf-cookie";
    </script>
    <script src="../docs/js/tryitout-5.1.0.js"></script>

    <script src="../docs/js/theme-default-5.1.0.js"></script>

</head>

<body data-languages="[&quot;bash&quot;,&quot;javascript&quot;]">

<a href="#" id="nav-button">
    <span>
        MENU
        <img src="../docs/images/navbar.png" alt="navbar-image"/>
    </span>
</a>
<div class="tocify-wrapper">
    
            <div class="lang-selector">
                                            <button type="button" class="lang-button" data-language-name="bash">bash</button>
                                            <button type="button" class="lang-button" data-language-name="javascript">javascript</button>
                    </div>
    
    <div class="search">
        <input type="text" class="search" id="input-search" placeholder="Search">
    </div>

    <div id="toc">
                    <ul id="tocify-header-introduction" class="tocify-header">
                <li class="tocify-item level-1" data-unique="introduction">
                    <a href="#introduction">Introduction</a>
                </li>
                            </ul>
                    <ul id="tocify-header-authenticating-requests" class="tocify-header">
                <li class="tocify-item level-1" data-unique="authenticating-requests">
                    <a href="#authenticating-requests">Authenticating requests</a>
                </li>
                            </ul>
                    <ul id="tocify-header-endpoints" class="tocify-header">
                <li class="tocify-item level-1" data-unique="endpoints">
                    <a href="#endpoints">Endpoints</a>
                </li>
                                    <ul id="tocify-subheader-endpoints" class="tocify-subheader">
                                                    <li class="tocify-item level-2" data-unique="endpoints-POSTapi-upload">
                                <a href="#endpoints-POSTapi-upload">POST api/upload</a>
                            </li>
                                                                                <li class="tocify-item level-2" data-unique="endpoints-GETapi-user">
                                <a href="#endpoints-GETapi-user">GET api/user</a>
                            </li>
                                                                                <li class="tocify-item level-2" data-unique="endpoints-GETapi-chapters-fetch">
                                <a href="#endpoints-GETapi-chapters-fetch">GET api/chapters/fetch</a>
                            </li>
                                                                                <li class="tocify-item level-2" data-unique="endpoints-GETapi-books">
                                <a href="#endpoints-GETapi-books">GET api/books</a>
                            </li>
                                                                                <li class="tocify-item level-2" data-unique="endpoints-GETapi-books-content-status">
                                <a href="#endpoints-GETapi-books-content-status">Get content status for all books</a>
                            </li>
                                                                                <li class="tocify-item level-2" data-unique="endpoints-GETapi-books--slug-">
                                <a href="#endpoints-GETapi-books--slug-">Get a single book by slug with all its metadata</a>
                            </li>
                                                                                <li class="tocify-item level-2" data-unique="endpoints-GETapi-search-books">
                                <a href="#endpoints-GETapi-search-books">GET api/search/books</a>
                            </li>
                                                                                <li class="tocify-item level-2" data-unique="endpoints-GETapi-search">
                                <a href="#endpoints-GETapi-search">GET api/search</a>
                            </li>
                                                                                <li class="tocify-item level-2" data-unique="endpoints-GETapi-bible--reference-">
                                <a href="#endpoints-GETapi-bible--reference-">Get Bible text based on reference</a>
                            </li>
                                                                        </ul>
                            </ul>
            </div>

    <ul class="toc-footer" id="toc-footer">
                    <li style="padding-bottom: 5px;"><a href="../docs/collection.json">View Postman collection</a></li>
                            <li style="padding-bottom: 5px;"><a href="../docs/openapi.yaml">View OpenAPI spec</a></li>
                <li><a href="http://github.com/knuckleswtf/scribe">Documentation powered by Scribe ✍</a></li>
    </ul>

    <ul class="toc-footer" id="last-updated">
        <li>Last updated: April 9, 2025</li>
    </ul>
</div>

<div class="page-wrapper">
    <div class="dark-box"></div>
    <div class="content">
        <h1 id="introduction">Introduction</h1>
<aside>
    <strong>Base URL</strong>: <code>http://esra-bibel.local/</code>
</aside>
<pre><code>This documentation aims to provide all the information you need to work with our API.

&lt;aside&gt;As you scroll, you'll see code examples for working with the API in different programming languages in the dark area to the right (or as part of the content on mobile).
You can switch the language used with the tabs at the top right (or from the nav menu at the top left on mobile).&lt;/aside&gt;</code></pre>

        <h1 id="authenticating-requests">Authenticating requests</h1>
<p>This API is not authenticated.</p>

        <h1 id="endpoints">Endpoints</h1>

    

                                <h2 id="endpoints-POSTapi-upload">POST api/upload</h2>

<p>
</p>



<span id="example-requests-POSTapi-upload">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request POST \
    "http://esra-bibel.local/api/upload" \
    --header "Content-Type: multipart/form-data" \
    --header "Accept: application/json" \
    --form "file=@/private/var/folders/1h/90xgwb_x1zxg56nkpxk430wc0000gn/T/phpm0h5oj4b6cng9w5FC0p" </code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "http://esra-bibel.local/api/upload"
);

const headers = {
    "Content-Type": "multipart/form-data",
    "Accept": "application/json",
};

const body = new FormData();
body.append('file', document.querySelector('input[name="file"]').files[0]);

fetch(url, {
    method: "POST",
    headers,
    body,
}).then(response =&gt; response.json());</code></pre></div>

</span>

<span id="example-responses-POSTapi-upload">
</span>
<span id="execution-results-POSTapi-upload" hidden>
    <blockquote>Received response<span
                id="execution-response-status-POSTapi-upload"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-POSTapi-upload"
      data-empty-response-text="<Empty response>" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-POSTapi-upload" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-POSTapi-upload">

Tip: Check that you&#039;re properly connected to the network.
If you&#039;re a maintainer of ths API, verify that your API is running and you&#039;ve enabled CORS.
You can check the Dev Tools console for debugging information.</code></pre>
</span>
<form id="form-POSTapi-upload" data-method="POST"
      data-path="api/upload"
      data-authed="0"
      data-hasfiles="1"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('POSTapi-upload', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
                    <button type="button"
                    style="background-color: #8fbcd4; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-tryout-POSTapi-upload"
                    onclick="tryItOut('POSTapi-upload');">Try it out ⚡
            </button>
            <button type="button"
                    style="background-color: #c97a7e; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-canceltryout-POSTapi-upload"
                    onclick="cancelTryOut('POSTapi-upload');" hidden>Cancel 🛑
            </button>&nbsp;&nbsp;
            <button type="submit"
                    style="background-color: #6ac174; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-executetryout-POSTapi-upload"
                    data-initial-text="Send Request 💥"
                    data-loading-text="⏱ Sending..."
                    hidden>Send Request 💥
            </button>
            </h3>
            <p>
            <small class="badge badge-black">POST</small>
            <b><code>api/upload</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Content-Type</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Content-Type"                data-endpoint="POSTapi-upload"
               value="multipart/form-data"
               data-component="header">
    <br>
<p>Example: <code>multipart/form-data</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Accept</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Accept"                data-endpoint="POSTapi-upload"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <h4 class="fancy-heading-panel"><b>Body Parameters</b></h4>
        <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>file</code></b>&nbsp;&nbsp;
<small>file</small>&nbsp;
 &nbsp;
                <input type="file" style="display: none"
                              name="file"                data-endpoint="POSTapi-upload"
               value=""
               data-component="body">
    <br>
<p>Must be a file. Example: <code>/private/var/folders/1h/90xgwb_x1zxg56nkpxk430wc0000gn/T/phpm0h5oj4b6cng9w5FC0p</code></p>
        </div>
        </form>

                    <h2 id="endpoints-GETapi-user">GET api/user</h2>

<p>
</p>



<span id="example-requests-GETapi-user">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request GET \
    --get "http://esra-bibel.local/api/user" \
    --header "Content-Type: application/json" \
    --header "Accept: application/json"</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "http://esra-bibel.local/api/user"
);

const headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "GET",
    headers,
}).then(response =&gt; response.json());</code></pre></div>

</span>

<span id="example-responses-GETapi-user">
            <blockquote>
            <p>Example response (404):</p>
        </blockquote>
                <details class="annotation">
            <summary style="cursor: pointer;">
                <small onclick="textContent = parentElement.parentElement.open ? 'Show headers' : 'Hide headers'">Show headers</small>
            </summary>
            <pre><code class="language-http">cache-control: no-cache, private
content-type: application/json
vary: X-Inertia
access-control-allow-origin: *
set-cookie: XSRF-TOKEN=eyJpdiI6Ik5hY0E1SXhQVU5ubGFRdnN5ZFhqOEE9PSIsInZhbHVlIjoidmZYYkRFYStPUFBoYnhhTTR1SkpWb3VYYnFpSllTQXNabVZiVituNHFFZTN4QnlhWUVTVDA4TkZwZGJWRXNwZStUWnF6ZklTcEV1V1ljbDJBQ2k2OHNpOXVvblIvUm1YcVlLdUVWcWw5ejBrdmFkeUFYa0tFai9JTEJPS0NGVy8iLCJtYWMiOiI3MDhmYjUwZDVmYzBkM2FiZTM3Y2U3NDY0NzVjNGY0NzE3YzY5ZDU0OTVlNGYyNjhiMGU1YzJlYWViODQ4NTZkIiwidGFnIjoiIn0%3D; expires=Wed, 09 Apr 2025 13:17:49 GMT; Max-Age=7200; path=/; samesite=lax; esrabibel_session=eyJpdiI6IkFOS0FDVmtqU0NISUZkR2l0cEFka0E9PSIsInZhbHVlIjoiSGlUbDkyZ2c5T2krM2t4cTF4L2dtK2dDSWRrbXBQOWVCQTBGeFFVeWxtNWNDTHpvSWVEVExjWEptM3hpam1GQ295NVNCQ0hWNENXZHorV3dUTk13WXp2WFlRVGtMNkRma05sWThoRkNwVmFUL1lHK1o5YVVaQ01USlFFc0NrODgiLCJtYWMiOiJmNzk3OGU4ZDI3MGI5ZGNmMmE4OGIyMGU1MTI4ZWViZTM5NjJiYjE1ZDExMmEyYTk2OWFjYWYyYjhkOWMzY2NlIiwidGFnIjoiIn0%3D; expires=Wed, 09 Apr 2025 13:17:49 GMT; Max-Age=7200; path=/; httponly; samesite=lax
 </code></pre></details>         <pre>

<code class="language-json" style="max-height: 300px;">&lt;!DOCTYPE html&gt;
&lt;html lang=&quot;de&quot;&gt;

&lt;head&gt;
    &lt;meta charset=&quot;utf-8&quot;&gt;
    &lt;meta name=&quot;viewport&quot; content=&quot;width=device-width, initial-scale=1&quot;&gt;

    &lt;title inertia&gt;&lt;/title&gt;

    &lt;!-- Fonts --&gt;
    &lt;!-- Preconnect to font domains --&gt;
    &lt;link rel=&quot;preconnect&quot; href=&quot;https://fonts.bunny.net&quot; crossorigin&gt;
    &lt;link rel=&quot;preconnect&quot; href=&quot;https://use.typekit.net&quot; as=&quot;font&quot; type=&quot;font/woff2&quot; crossorigin&gt;

    &lt;!-- Preload critical fonts --&gt;
    &lt;link rel=&quot;preload&quot; href=&quot;/fonts/ThanatosText-Book.woff2&quot; as=&quot;font&quot; type=&quot;font/woff2&quot; crossorigin&gt;

    &lt;!-- Load fonts --&gt;
    &lt;link href=&quot;https://fonts.bunny.net/css?family=figtree:400,500,600&amp;display=swap&quot; rel=&quot;stylesheet&quot; /&gt;
    &lt;link rel=&quot;stylesheet&quot; href=&quot;https://use.typekit.net/kzb8yhl.css&quot; media=&quot;print&quot; onload=&quot;this.media=&#039;all&#039;&quot;&gt;

    &lt;!-- Fallback for typekit fonts --&gt;
    &lt;noscript&gt;
        &lt;link rel=&quot;stylesheet&quot; href=&quot;https://use.typekit.net/kzb8yhl.css&quot;&gt;
    &lt;/noscript&gt;

    &lt;!-- Local font definition --&gt;
    &lt;style&gt;
        @font-face {
            font-family: &#039;ThanatosText&#039;;
            src: url(&#039;/fonts/ThanatosText-Book.woff2&#039;) format(&#039;woff2&#039;);
            font-weight: normal;
            font-style: normal;
            font-display: swap;
        }
    &lt;/style&gt;

    &lt;!-- Scripts --&gt;
    &lt;script type=&quot;text/javascript&quot;&gt;const Ziggy={&quot;url&quot;:&quot;http:\/\/esra-bibel.local&quot;,&quot;port&quot;:null,&quot;defaults&quot;:{},&quot;routes&quot;:{&quot;search.index&quot;:{&quot;uri&quot;:&quot;search&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;search.query&quot;:{&quot;uri&quot;:&quot;search\/{query}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;wheres&quot;:{&quot;query&quot;:&quot;[^\/]+&quot;},&quot;parameters&quot;:[&quot;query&quot;]},&quot;search.paged&quot;:{&quot;uri&quot;:&quot;search\/{query}\/{page?}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;wheres&quot;:{&quot;query&quot;:&quot;[^\/]+&quot;,&quot;page&quot;:&quot;[0-9]+&quot;},&quot;parameters&quot;:[&quot;query&quot;,&quot;page&quot;]},&quot;search.settings&quot;:{&quot;uri&quot;:&quot;search\/settings&quot;,&quot;methods&quot;:[&quot;POST&quot;]},&quot;dashboard&quot;:{&quot;uri&quot;:&quot;dashboard&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;bible.import&quot;:{&quot;uri&quot;:&quot;import-bible&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;bible.import.store&quot;:{&quot;uri&quot;:&quot;import-bible&quot;,&quot;methods&quot;:[&quot;POST&quot;]},&quot;file.upload&quot;:{&quot;uri&quot;:&quot;api\/upload&quot;,&quot;methods&quot;:[&quot;POST&quot;]},&quot;profile.edit&quot;:{&quot;uri&quot;:&quot;profile&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;profile.update&quot;:{&quot;uri&quot;:&quot;profile&quot;,&quot;methods&quot;:[&quot;PATCH&quot;]},&quot;profile.destroy&quot;:{&quot;uri&quot;:&quot;profile&quot;,&quot;methods&quot;:[&quot;DELETE&quot;]},&quot;books.show&quot;:{&quot;uri&quot;:&quot;{reference}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;wheres&quot;:{&quot;reference&quot;:&quot;[\\w\\d\\.,\\-\\+]+&quot;},&quot;parameters&quot;:[&quot;reference&quot;]},&quot;register&quot;:{&quot;uri&quot;:&quot;register&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;login&quot;:{&quot;uri&quot;:&quot;login&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;password.request&quot;:{&quot;uri&quot;:&quot;forgot-password&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;password.email&quot;:{&quot;uri&quot;:&quot;forgot-password&quot;,&quot;methods&quot;:[&quot;POST&quot;]},&quot;password.reset&quot;:{&quot;uri&quot;:&quot;reset-password\/{token}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;parameters&quot;:[&quot;token&quot;]},&quot;password.store&quot;:{&quot;uri&quot;:&quot;reset-password&quot;,&quot;methods&quot;:[&quot;POST&quot;]},&quot;verification.notice&quot;:{&quot;uri&quot;:&quot;verify-email&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;verification.verify&quot;:{&quot;uri&quot;:&quot;verify-email\/{id}\/{hash}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;parameters&quot;:[&quot;id&quot;,&quot;hash&quot;]},&quot;verification.send&quot;:{&quot;uri&quot;:&quot;email\/verification-notification&quot;,&quot;methods&quot;:[&quot;POST&quot;]},&quot;password.confirm&quot;:{&quot;uri&quot;:&quot;confirm-password&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;password.update&quot;:{&quot;uri&quot;:&quot;password&quot;,&quot;methods&quot;:[&quot;PUT&quot;]},&quot;logout&quot;:{&quot;uri&quot;:&quot;logout&quot;,&quot;methods&quot;:[&quot;POST&quot;]},&quot;api.chapters.fetch&quot;:{&quot;uri&quot;:&quot;api\/chapters\/fetch&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;api.books&quot;:{&quot;uri&quot;:&quot;api\/books&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;api.books.content-status&quot;:{&quot;uri&quot;:&quot;api\/books\/content-status&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;api.books.show&quot;:{&quot;uri&quot;:&quot;api\/books\/{slug}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;parameters&quot;:[&quot;slug&quot;]},&quot;api.chapters.adjacent&quot;:{&quot;uri&quot;:&quot;api\/chapters\/{reference}\/adjacent&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;wheres&quot;:{&quot;reference&quot;:&quot;[\\w\\d,\\-]+&quot;},&quot;parameters&quot;:[&quot;reference&quot;]},&quot;api.search.books&quot;:{&quot;uri&quot;:&quot;api\/search\/books&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;api.search&quot;:{&quot;uri&quot;:&quot;api\/search&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;api.bible.text&quot;:{&quot;uri&quot;:&quot;api\/bible\/{reference}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;wheres&quot;:{&quot;reference&quot;:&quot;[\\w\\d,\\-]+&quot;},&quot;parameters&quot;:[&quot;reference&quot;]},&quot;storage.local&quot;:{&quot;uri&quot;:&quot;storage\/{path}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;wheres&quot;:{&quot;path&quot;:&quot;.*&quot;},&quot;parameters&quot;:[&quot;path&quot;]}}};!function(t,r){&quot;object&quot;==typeof exports&amp;&amp;&quot;undefined&quot;!=typeof module?module.exports=r():&quot;function&quot;==typeof define&amp;&amp;define.amd?define(r):(t||self).route=r()}(this,function(){function t(t,r){for(var n=0;n&lt;r.length;n++){var e=r[n];e.enumerable=e.enumerable||!1,e.configurable=!0,&quot;value&quot;in e&amp;&amp;(e.writable=!0),Object.defineProperty(t,u(e.key),e)}}function r(r,n,e){return n&amp;&amp;t(r.prototype,n),e&amp;&amp;t(r,e),Object.defineProperty(r,&quot;prototype&quot;,{writable:!1}),r}function n(){return n=Object.assign?Object.assign.bind():function(t){for(var r=1;r&lt;arguments.length;r++){var n=arguments[r];for(var e in n)({}).hasOwnProperty.call(n,e)&amp;&amp;(t[e]=n[e])}return t},n.apply(null,arguments)}function e(t){return e=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},e(t)}function o(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(o=function(){return!!t})()}function i(t,r){return i=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,r){return t.__proto__=r,t},i(t,r)}function u(t){var r=function(t){if(&quot;object&quot;!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,&quot;string&quot;);if(&quot;object&quot;!=typeof n)return n;throw new TypeError(&quot;@@toPrimitive must return a primitive value.&quot;)}return String(t)}(t);return&quot;symbol&quot;==typeof r?r:r+&quot;&quot;}function f(t){var r=&quot;function&quot;==typeof Map?new Map:void 0;return f=function(t){if(null===t||!function(t){try{return-1!==Function.toString.call(t).indexOf(&quot;[native code]&quot;)}catch(r){return&quot;function&quot;==typeof t}}(t))return t;if(&quot;function&quot;!=typeof t)throw new TypeError(&quot;Super expression must either be null or a function&quot;);if(void 0!==r){if(r.has(t))return r.get(t);r.set(t,n)}function n(){return function(t,r,n){if(o())return Reflect.construct.apply(null,arguments);var e=[null];e.push.apply(e,r);var u=new(t.bind.apply(t,e));return n&amp;&amp;i(u,n.prototype),u}(t,arguments,e(this).constructor)}return n.prototype=Object.create(t.prototype,{constructor:{value:n,enumerable:!1,writable:!0,configurable:!0}}),i(n,t)},f(t)}var a=String.prototype.replace,c=/%20/g,l=&quot;RFC3986&quot;,s={default:l,formatters:{RFC1738:function(t){return a.call(t,c,&quot;+&quot;)},RFC3986:function(t){return String(t)}},RFC1738:&quot;RFC1738&quot;,RFC3986:l},v=Object.prototype.hasOwnProperty,p=Array.isArray,y=function(){for(var t=[],r=0;r&lt;256;++r)t.push(&quot;%&quot;+((r&lt;16?&quot;0&quot;:&quot;&quot;)+r.toString(16)).toUpperCase());return t}(),d=function(t,r){for(var n=r&amp;&amp;r.plainObjects?Object.create(null):{},e=0;e&lt;t.length;++e)void 0!==t[e]&amp;&amp;(n[e]=t[e]);return n},b={arrayToObject:d,assign:function(t,r){return Object.keys(r).reduce(function(t,n){return t[n]=r[n],t},t)},combine:function(t,r){return[].concat(t,r)},compact:function(t){for(var r=[{obj:{o:t},prop:&quot;o&quot;}],n=[],e=0;e&lt;r.length;++e)for(var o=r[e],i=o.obj[o.prop],u=Object.keys(i),f=0;f&lt;u.length;++f){var a=u[f],c=i[a];&quot;object&quot;==typeof c&amp;&amp;null!==c&amp;&amp;-1===n.indexOf(c)&amp;&amp;(r.push({obj:i,prop:a}),n.push(c))}return function(t){for(;t.length&gt;1;){var r=t.pop(),n=r.obj[r.prop];if(p(n)){for(var e=[],o=0;o&lt;n.length;++o)void 0!==n[o]&amp;&amp;e.push(n[o]);r.obj[r.prop]=e}}}(r),t},decode:function(t,r,n){var e=t.replace(/\+/g,&quot; &quot;);if(&quot;iso-8859-1&quot;===n)return e.replace(/%[0-9a-f]{2}/gi,unescape);try{return decodeURIComponent(e)}catch(t){return e}},encode:function(t,r,n,e,o){if(0===t.length)return t;var i=t;if(&quot;symbol&quot;==typeof t?i=Symbol.prototype.toString.call(t):&quot;string&quot;!=typeof t&amp;&amp;(i=String(t)),&quot;iso-8859-1&quot;===n)return escape(i).replace(/%u[0-9a-f]{4}/gi,function(t){return&quot;%26%23&quot;+parseInt(t.slice(2),16)+&quot;%3B&quot;});for(var u=&quot;&quot;,f=0;f&lt;i.length;++f){var a=i.charCodeAt(f);45===a||46===a||95===a||126===a||a&gt;=48&amp;&amp;a&lt;=57||a&gt;=65&amp;&amp;a&lt;=90||a&gt;=97&amp;&amp;a&lt;=122||o===s.RFC1738&amp;&amp;(40===a||41===a)?u+=i.charAt(f):a&lt;128?u+=y[a]:a&lt;2048?u+=y[192|a&gt;&gt;6]+y[128|63&amp;a]:a&lt;55296||a&gt;=57344?u+=y[224|a&gt;&gt;12]+y[128|a&gt;&gt;6&amp;63]+y[128|63&amp;a]:(a=65536+((1023&amp;a)&lt;&lt;10|1023&amp;i.charCodeAt(f+=1)),u+=y[240|a&gt;&gt;18]+y[128|a&gt;&gt;12&amp;63]+y[128|a&gt;&gt;6&amp;63]+y[128|63&amp;a])}return u},isBuffer:function(t){return!(!t||&quot;object&quot;!=typeof t||!(t.constructor&amp;&amp;t.constructor.isBuffer&amp;&amp;t.constructor.isBuffer(t)))},isRegExp:function(t){return&quot;[object RegExp]&quot;===Object.prototype.toString.call(t)},maybeMap:function(t,r){if(p(t)){for(var n=[],e=0;e&lt;t.length;e+=1)n.push(r(t[e]));return n}return r(t)},merge:function t(r,n,e){if(!n)return r;if(&quot;object&quot;!=typeof n){if(p(r))r.push(n);else{if(!r||&quot;object&quot;!=typeof r)return[r,n];(e&amp;&amp;(e.plainObjects||e.allowPrototypes)||!v.call(Object.prototype,n))&amp;&amp;(r[n]=!0)}return r}if(!r||&quot;object&quot;!=typeof r)return[r].concat(n);var o=r;return p(r)&amp;&amp;!p(n)&amp;&amp;(o=d(r,e)),p(r)&amp;&amp;p(n)?(n.forEach(function(n,o){if(v.call(r,o)){var i=r[o];i&amp;&amp;&quot;object&quot;==typeof i&amp;&amp;n&amp;&amp;&quot;object&quot;==typeof n?r[o]=t(i,n,e):r.push(n)}else r[o]=n}),r):Object.keys(n).reduce(function(r,o){var i=n[o];return r[o]=v.call(r,o)?t(r[o],i,e):i,r},o)}},h=Object.prototype.hasOwnProperty,g={brackets:function(t){return t+&quot;[]&quot;},comma:&quot;comma&quot;,indices:function(t,r){return t+&quot;[&quot;+r+&quot;]&quot;},repeat:function(t){return t}},m=Array.isArray,j=String.prototype.split,w=Array.prototype.push,O=function(t,r){w.apply(t,m(r)?r:[r])},E=Date.prototype.toISOString,R=s.default,S={addQueryPrefix:!1,allowDots:!1,charset:&quot;utf-8&quot;,charsetSentinel:!1,delimiter:&quot;&amp;&quot;,encode:!0,encoder:b.encode,encodeValuesOnly:!1,format:R,formatter:s.formatters[R],indices:!1,serializeDate:function(t){return E.call(t)},skipNulls:!1,strictNullHandling:!1},k=function t(r,n,e,o,i,u,f,a,c,l,s,v,p,y){var d,h=r;if(&quot;function&quot;==typeof f?h=f(n,h):h instanceof Date?h=l(h):&quot;comma&quot;===e&amp;&amp;m(h)&amp;&amp;(h=b.maybeMap(h,function(t){return t instanceof Date?l(t):t})),null===h){if(o)return u&amp;&amp;!p?u(n,S.encoder,y,&quot;key&quot;,s):n;h=&quot;&quot;}if(&quot;string&quot;==typeof(d=h)||&quot;number&quot;==typeof d||&quot;boolean&quot;==typeof d||&quot;symbol&quot;==typeof d||&quot;bigint&quot;==typeof d||b.isBuffer(h)){if(u){var g=p?n:u(n,S.encoder,y,&quot;key&quot;,s);if(&quot;comma&quot;===e&amp;&amp;p){for(var w=j.call(String(h),&quot;,&quot;),E=&quot;&quot;,R=0;R&lt;w.length;++R)E+=(0===R?&quot;&quot;:&quot;,&quot;)+v(u(w[R],S.encoder,y,&quot;value&quot;,s));return[v(g)+&quot;=&quot;+E]}return[v(g)+&quot;=&quot;+v(u(h,S.encoder,y,&quot;value&quot;,s))]}return[v(n)+&quot;=&quot;+v(String(h))]}var k,T=[];if(void 0===h)return T;if(&quot;comma&quot;===e&amp;&amp;m(h))k=[{value:h.length&gt;0?h.join(&quot;,&quot;)||null:void 0}];else if(m(f))k=f;else{var $=Object.keys(h);k=a?$.sort(a):$}for(var x=0;x&lt;k.length;++x){var N=k[x],C=&quot;object&quot;==typeof N&amp;&amp;void 0!==N.value?N.value:h[N];if(!i||null!==C){var A=m(h)?&quot;function&quot;==typeof e?e(n,N):n:n+(c?&quot;.&quot;+N:&quot;[&quot;+N+&quot;]&quot;);O(T,t(C,A,e,o,i,u,f,a,c,l,s,v,p,y))}}return T},T=Object.prototype.hasOwnProperty,$=Array.isArray,x={allowDots:!1,allowPrototypes:!1,arrayLimit:20,charset:&quot;utf-8&quot;,charsetSentinel:!1,comma:!1,decoder:b.decode,delimiter:&quot;&amp;&quot;,depth:5,ignoreQueryPrefix:!1,interpretNumericEntities:!1,parameterLimit:1e3,parseArrays:!0,plainObjects:!1,strictNullHandling:!1},N=function(t){return t.replace(/&amp;#(\d+);/g,function(t,r){return String.fromCharCode(parseInt(r,10))})},C=function(t,r){return t&amp;&amp;&quot;string&quot;==typeof t&amp;&amp;r.comma&amp;&amp;t.indexOf(&quot;,&quot;)&gt;-1?t.split(&quot;,&quot;):t},A=function(t,r,n,e){if(t){var o=n.allowDots?t.replace(/\.([^.[]+)/g,&quot;[$1]&quot;):t,i=/(\[[^[\]]*])/g,u=n.depth&gt;0&amp;&amp;/(\[[^[\]]*])/.exec(o),f=u?o.slice(0,u.index):o,a=[];if(f){if(!n.plainObjects&amp;&amp;T.call(Object.prototype,f)&amp;&amp;!n.allowPrototypes)return;a.push(f)}for(var c=0;n.depth&gt;0&amp;&amp;null!==(u=i.exec(o))&amp;&amp;c&lt;n.depth;){if(c+=1,!n.plainObjects&amp;&amp;T.call(Object.prototype,u[1].slice(1,-1))&amp;&amp;!n.allowPrototypes)return;a.push(u[1])}return u&amp;&amp;a.push(&quot;[&quot;+o.slice(u.index)+&quot;]&quot;),function(t,r,n,e){for(var o=e?r:C(r,n),i=t.length-1;i&gt;=0;--i){var u,f=t[i];if(&quot;[]&quot;===f&amp;&amp;n.parseArrays)u=[].concat(o);else{u=n.plainObjects?Object.create(null):{};var a=&quot;[&quot;===f.charAt(0)&amp;&amp;&quot;]&quot;===f.charAt(f.length-1)?f.slice(1,-1):f,c=parseInt(a,10);n.parseArrays||&quot;&quot;!==a?!isNaN(c)&amp;&amp;f!==a&amp;&amp;String(c)===a&amp;&amp;c&gt;=0&amp;&amp;n.parseArrays&amp;&amp;c&lt;=n.arrayLimit?(u=[])[c]=o:&quot;__proto__&quot;!==a&amp;&amp;(u[a]=o):u={0:o}}o=u}return o}(a,r,n,e)}},D=function(t,r){var n=function(t){if(!t)return x;if(null!=t.decoder&amp;&amp;&quot;function&quot;!=typeof t.decoder)throw new TypeError(&quot;Decoder has to be a function.&quot;);if(void 0!==t.charset&amp;&amp;&quot;utf-8&quot;!==t.charset&amp;&amp;&quot;iso-8859-1&quot;!==t.charset)throw new TypeError(&quot;The charset option must be either utf-8, iso-8859-1, or undefined&quot;);return{allowDots:void 0===t.allowDots?x.allowDots:!!t.allowDots,allowPrototypes:&quot;boolean&quot;==typeof t.allowPrototypes?t.allowPrototypes:x.allowPrototypes,arrayLimit:&quot;number&quot;==typeof t.arrayLimit?t.arrayLimit:x.arrayLimit,charset:void 0===t.charset?x.charset:t.charset,charsetSentinel:&quot;boolean&quot;==typeof t.charsetSentinel?t.charsetSentinel:x.charsetSentinel,comma:&quot;boolean&quot;==typeof t.comma?t.comma:x.comma,decoder:&quot;function&quot;==typeof t.decoder?t.decoder:x.decoder,delimiter:&quot;string&quot;==typeof t.delimiter||b.isRegExp(t.delimiter)?t.delimiter:x.delimiter,depth:&quot;number&quot;==typeof t.depth||!1===t.depth?+t.depth:x.depth,ignoreQueryPrefix:!0===t.ignoreQueryPrefix,interpretNumericEntities:&quot;boolean&quot;==typeof t.interpretNumericEntities?t.interpretNumericEntities:x.interpretNumericEntities,parameterLimit:&quot;number&quot;==typeof t.parameterLimit?t.parameterLimit:x.parameterLimit,parseArrays:!1!==t.parseArrays,plainObjects:&quot;boolean&quot;==typeof t.plainObjects?t.plainObjects:x.plainObjects,strictNullHandling:&quot;boolean&quot;==typeof t.strictNullHandling?t.strictNullHandling:x.strictNullHandling}}(r);if(&quot;&quot;===t||null==t)return n.plainObjects?Object.create(null):{};for(var e=&quot;string&quot;==typeof t?function(t,r){var n,e={},o=(r.ignoreQueryPrefix?t.replace(/^\?/,&quot;&quot;):t).split(r.delimiter,Infinity===r.parameterLimit?void 0:r.parameterLimit),i=-1,u=r.charset;if(r.charsetSentinel)for(n=0;n&lt;o.length;++n)0===o[n].indexOf(&quot;utf8=&quot;)&amp;&amp;(&quot;utf8=%E2%9C%93&quot;===o[n]?u=&quot;utf-8&quot;:&quot;utf8=%26%2310003%3B&quot;===o[n]&amp;&amp;(u=&quot;iso-8859-1&quot;),i=n,n=o.length);for(n=0;n&lt;o.length;++n)if(n!==i){var f,a,c=o[n],l=c.indexOf(&quot;]=&quot;),s=-1===l?c.indexOf(&quot;=&quot;):l+1;-1===s?(f=r.decoder(c,x.decoder,u,&quot;key&quot;),a=r.strictNullHandling?null:&quot;&quot;):(f=r.decoder(c.slice(0,s),x.decoder,u,&quot;key&quot;),a=b.maybeMap(C(c.slice(s+1),r),function(t){return r.decoder(t,x.decoder,u,&quot;value&quot;)})),a&amp;&amp;r.interpretNumericEntities&amp;&amp;&quot;iso-8859-1&quot;===u&amp;&amp;(a=N(a)),c.indexOf(&quot;[]=&quot;)&gt;-1&amp;&amp;(a=$(a)?[a]:a),e[f]=T.call(e,f)?b.combine(e[f],a):a}return e}(t,n):t,o=n.plainObjects?Object.create(null):{},i=Object.keys(e),u=0;u&lt;i.length;++u){var f=i[u],a=A(f,e[f],n,&quot;string&quot;==typeof t);o=b.merge(o,a,n)}return b.compact(o)},P=/*#__PURE__*/function(){function t(t,r,n){var e,o;this.name=t,this.definition=r,this.bindings=null!=(e=r.bindings)?e:{},this.wheres=null!=(o=r.wheres)?o:{},this.config=n}var n=t.prototype;return n.matchesUrl=function(t){var r,n=this;if(!this.definition.methods.includes(&quot;GET&quot;))return!1;var e=this.template.replace(/[.*+$()[\]]/g,&quot;\\$&amp;&quot;).replace(/(\/?){([^}?]*)(\??)}/g,function(t,r,e,o){var i,u=&quot;(?&lt;&quot;+e+&quot;&gt;&quot;+((null==(i=n.wheres[e])?void 0:i.replace(/(^\^)|(\$$)/g,&quot;&quot;))||&quot;[^/?]+&quot;)+&quot;)&quot;;return o?&quot;(&quot;+r+u+&quot;)?&quot;:&quot;&quot;+r+u}).replace(/^\w+:\/\//,&quot;&quot;),o=t.replace(/^\w+:\/\//,&quot;&quot;).split(&quot;?&quot;),i=o[0],u=o[1],f=null!=(r=new RegExp(&quot;^&quot;+e+&quot;/?$&quot;).exec(i))?r:new RegExp(&quot;^&quot;+e+&quot;/?$&quot;).exec(decodeURI(i));if(f){for(var a in f.groups)f.groups[a]=&quot;string&quot;==typeof f.groups[a]?decodeURIComponent(f.groups[a]):f.groups[a];return{params:f.groups,query:D(u)}}return!1},n.compile=function(t){var r=this;return this.parameterSegments.length?this.template.replace(/{([^}?]+)(\??)}/g,function(n,e,o){var i,u;if(!o&amp;&amp;[null,void 0].includes(t[e]))throw new Error(&quot;Ziggy error: &#039;&quot;+e+&quot;&#039; parameter is required for route &#039;&quot;+r.name+&quot;&#039;.&quot;);if(r.wheres[e]&amp;&amp;!new RegExp(&quot;^&quot;+(o?&quot;(&quot;+r.wheres[e]+&quot;)?&quot;:r.wheres[e])+&quot;$&quot;).test(null!=(u=t[e])?u:&quot;&quot;))throw new Error(&quot;Ziggy error: &#039;&quot;+e+&quot;&#039; parameter &#039;&quot;+t[e]+&quot;&#039; does not match required format &#039;&quot;+r.wheres[e]+&quot;&#039; for route &#039;&quot;+r.name+&quot;&#039;.&quot;);return encodeURI(null!=(i=t[e])?i:&quot;&quot;).replace(/%7C/g,&quot;|&quot;).replace(/%25/g,&quot;%&quot;).replace(/\$/g,&quot;%24&quot;)}).replace(this.config.absolute?/(\.[^/]+?)(\/\/)/:/(^)(\/\/)/,&quot;$1/&quot;).replace(/\/+$/,&quot;&quot;):this.template},r(t,[{key:&quot;template&quot;,get:function(){var t=(this.origin+&quot;/&quot;+this.definition.uri).replace(/\/+$/,&quot;&quot;);return&quot;&quot;===t?&quot;/&quot;:t}},{key:&quot;origin&quot;,get:function(){return this.config.absolute?this.definition.domain?&quot;&quot;+this.config.url.match(/^\w+:\/\//)[0]+this.definition.domain+(this.config.port?&quot;:&quot;+this.config.port:&quot;&quot;):this.config.url:&quot;&quot;}},{key:&quot;parameterSegments&quot;,get:function(){var t,r;return null!=(t=null==(r=this.template.match(/{[^}?]+\??}/g))?void 0:r.map(function(t){return{name:t.replace(/{|\??}/g,&quot;&quot;),required:!/\?}$/.test(t)}}))?t:[]}}])}(),F=/*#__PURE__*/function(t){function e(r,e,o,i){var u;if(void 0===o&amp;&amp;(o=!0),(u=t.call(this)||this).t=null!=i?i:&quot;undefined&quot;!=typeof Ziggy?Ziggy:null==globalThis?void 0:globalThis.Ziggy,u.t=n({},u.t,{absolute:o}),r){if(!u.t.routes[r])throw new Error(&quot;Ziggy error: route &#039;&quot;+r+&quot;&#039; is not in the route list.&quot;);u.i=new P(r,u.t.routes[r],u.t),u.u=u.l(e)}return u}var o,u;u=t,(o=e).prototype=Object.create(u.prototype),o.prototype.constructor=o,i(o,u);var f=e.prototype;return f.toString=function(){var t=this,r=Object.keys(this.u).filter(function(r){return!t.i.parameterSegments.some(function(t){return t.name===r})}).filter(function(t){return&quot;_query&quot;!==t}).reduce(function(r,e){var o;return n({},r,((o={})[e]=t.u[e],o))},{});return this.i.compile(this.u)+function(t,r){var n,e=t,o=function(t){if(!t)return S;if(null!=t.encoder&amp;&amp;&quot;function&quot;!=typeof t.encoder)throw new TypeError(&quot;Encoder has to be a function.&quot;);var r=t.charset||S.charset;if(void 0!==t.charset&amp;&amp;&quot;utf-8&quot;!==t.charset&amp;&amp;&quot;iso-8859-1&quot;!==t.charset)throw new TypeError(&quot;The charset option must be either utf-8, iso-8859-1, or undefined&quot;);var n=s.default;if(void 0!==t.format){if(!h.call(s.formatters,t.format))throw new TypeError(&quot;Unknown format option provided.&quot;);n=t.format}var e=s.formatters[n],o=S.filter;return(&quot;function&quot;==typeof t.filter||m(t.filter))&amp;&amp;(o=t.filter),{addQueryPrefix:&quot;boolean&quot;==typeof t.addQueryPrefix?t.addQueryPrefix:S.addQueryPrefix,allowDots:void 0===t.allowDots?S.allowDots:!!t.allowDots,charset:r,charsetSentinel:&quot;boolean&quot;==typeof t.charsetSentinel?t.charsetSentinel:S.charsetSentinel,delimiter:void 0===t.delimiter?S.delimiter:t.delimiter,encode:&quot;boolean&quot;==typeof t.encode?t.encode:S.encode,encoder:&quot;function&quot;==typeof t.encoder?t.encoder:S.encoder,encodeValuesOnly:&quot;boolean&quot;==typeof t.encodeValuesOnly?t.encodeValuesOnly:S.encodeValuesOnly,filter:o,format:n,formatter:e,serializeDate:&quot;function&quot;==typeof t.serializeDate?t.serializeDate:S.serializeDate,skipNulls:&quot;boolean&quot;==typeof t.skipNulls?t.skipNulls:S.skipNulls,sort:&quot;function&quot;==typeof t.sort?t.sort:null,strictNullHandling:&quot;boolean&quot;==typeof t.strictNullHandling?t.strictNullHandling:S.strictNullHandling}}(r);&quot;function&quot;==typeof o.filter?e=(0,o.filter)(&quot;&quot;,e):m(o.filter)&amp;&amp;(n=o.filter);var i=[];if(&quot;object&quot;!=typeof e||null===e)return&quot;&quot;;var u=g[r&amp;&amp;r.arrayFormat in g?r.arrayFormat:r&amp;&amp;&quot;indices&quot;in r?r.indices?&quot;indices&quot;:&quot;repeat&quot;:&quot;indices&quot;];n||(n=Object.keys(e)),o.sort&amp;&amp;n.sort(o.sort);for(var f=0;f&lt;n.length;++f){var a=n[f];o.skipNulls&amp;&amp;null===e[a]||O(i,k(e[a],a,u,o.strictNullHandling,o.skipNulls,o.encode?o.encoder:null,o.filter,o.sort,o.allowDots,o.serializeDate,o.format,o.formatter,o.encodeValuesOnly,o.charset))}var c=i.join(o.delimiter),l=!0===o.addQueryPrefix?&quot;?&quot;:&quot;&quot;;return o.charsetSentinel&amp;&amp;(l+=&quot;iso-8859-1&quot;===o.charset?&quot;utf8=%26%2310003%3B&amp;&quot;:&quot;utf8=%E2%9C%93&amp;&quot;),c.length&gt;0?l+c:&quot;&quot;}(n({},r,this.u._query),{addQueryPrefix:!0,arrayFormat:&quot;indices&quot;,encodeValuesOnly:!0,skipNulls:!0,encoder:function(t,r){return&quot;boolean&quot;==typeof t?Number(t):r(t)}})},f.v=function(t){var r=this;t?this.t.absolute&amp;&amp;t.startsWith(&quot;/&quot;)&amp;&amp;(t=this.p().host+t):t=this.h();var e={},o=Object.entries(this.t.routes).find(function(n){return e=new P(n[0],n[1],r.t).matchesUrl(t)})||[void 0,void 0];return n({name:o[0]},e,{route:o[1]})},f.h=function(){var t=this.p(),r=t.pathname,n=t.search;return(this.t.absolute?t.host+r:r.replace(this.t.url.replace(/^\w*:\/\/[^/]+/,&quot;&quot;),&quot;&quot;).replace(/^\/+/,&quot;/&quot;))+n},f.current=function(t,r){var e=this.v(),o=e.name,i=e.params,u=e.query,f=e.route;if(!t)return o;var a=new RegExp(&quot;^&quot;+t.replace(/\./g,&quot;\\.&quot;).replace(/\*/g,&quot;.*&quot;)+&quot;$&quot;).test(o);if([null,void 0].includes(r)||!a)return a;var c=new P(o,f,this.t);r=this.l(r,c);var l=n({},i,u);if(Object.values(r).every(function(t){return!t})&amp;&amp;!Object.values(l).some(function(t){return void 0!==t}))return!0;var s=function(t,r){return Object.entries(t).every(function(t){var n=t[0],e=t[1];return Array.isArray(e)&amp;&amp;Array.isArray(r[n])?e.every(function(t){return r[n].includes(t)}):&quot;object&quot;==typeof e&amp;&amp;&quot;object&quot;==typeof r[n]&amp;&amp;null!==e&amp;&amp;null!==r[n]?s(e,r[n]):r[n]==e})};return s(r,l)},f.p=function(){var t,r,n,e,o,i,u=&quot;undefined&quot;!=typeof window?window.location:{},f=u.host,a=u.pathname,c=u.search;return{host:null!=(t=null==(r=this.t.location)?void 0:r.host)?t:void 0===f?&quot;&quot;:f,pathname:null!=(n=null==(e=this.t.location)?void 0:e.pathname)?n:void 0===a?&quot;&quot;:a,search:null!=(o=null==(i=this.t.location)?void 0:i.search)?o:void 0===c?&quot;&quot;:c}},f.has=function(t){return this.t.routes.hasOwnProperty(t)},f.l=function(t,r){var e=this;void 0===t&amp;&amp;(t={}),void 0===r&amp;&amp;(r=this.i),null!=t||(t={}),t=[&quot;string&quot;,&quot;number&quot;].includes(typeof t)?[t]:t;var o=r.parameterSegments.filter(function(t){return!e.t.defaults[t.name]});if(Array.isArray(t))t=t.reduce(function(t,r,e){var i,u;return n({},t,o[e]?((i={})[o[e].name]=r,i):&quot;object&quot;==typeof r?r:((u={})[r]=&quot;&quot;,u))},{});else if(1===o.length&amp;&amp;!t[o[0].name]&amp;&amp;(t.hasOwnProperty(Object.values(r.bindings)[0])||t.hasOwnProperty(&quot;id&quot;))){var i;(i={})[o[0].name]=t,t=i}return n({},this.m(r),this.j(t,r))},f.m=function(t){var r=this;return t.parameterSegments.filter(function(t){return r.t.defaults[t.name]}).reduce(function(t,e,o){var i,u=e.name;return n({},t,((i={})[u]=r.t.defaults[u],i))},{})},f.j=function(t,r){var e=r.bindings,o=r.parameterSegments;return Object.entries(t).reduce(function(t,r){var i,u,f=r[0],a=r[1];if(!a||&quot;object&quot;!=typeof a||Array.isArray(a)||!o.some(function(t){return t.name===f}))return n({},t,((u={})[f]=a,u));if(!a.hasOwnProperty(e[f])){if(!a.hasOwnProperty(&quot;id&quot;))throw new Error(&quot;Ziggy error: object passed as &#039;&quot;+f+&quot;&#039; parameter is missing route model binding key &#039;&quot;+e[f]+&quot;&#039;.&quot;);e[f]=&quot;id&quot;}return n({},t,((i={})[f]=a[e[f]],i))},{})},f.valueOf=function(){return this.toString()},r(e,[{key:&quot;params&quot;,get:function(){var t=this.v();return n({},t.params,t.query)}},{key:&quot;routeParams&quot;,get:function(){return this.v().params}},{key:&quot;queryParams&quot;,get:function(){return this.v().query}}])}(/*#__PURE__*/f(String));return function(t,r,n,e){var o=new F(t,r,n,e);return t?o.toString():o}});
&lt;/script&gt;    &lt;script type=&quot;module&quot; src=&quot;http://[::1]:5173/@vite/client&quot;&gt;&lt;/script&gt;&lt;script type=&quot;module&quot; src=&quot;http://[::1]:5173/resources/js/app.ts&quot;&gt;&lt;/script&gt;&lt;script type=&quot;module&quot; src=&quot;http://[::1]:5173/resources/js/Pages/NotFound.vue&quot;&gt;&lt;/script&gt;    &lt;/head&gt;

&lt;body class=&quot;font-sans antialiased&quot;&gt;
    &lt;div id=&quot;app&quot; data-page=&quot;{&amp;quot;component&amp;quot;:&amp;quot;NotFound&amp;quot;,&amp;quot;props&amp;quot;:{&amp;quot;errors&amp;quot;:{},&amp;quot;books&amp;quot;:{&amp;quot;sections&amp;quot;:[{&amp;quot;name&amp;quot;:&amp;quot;Altes Testament&amp;quot;,&amp;quot;books&amp;quot;:[{&amp;quot;id&amp;quot;:1,&amp;quot;name&amp;quot;:&amp;quot;1. Mose&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Mo&amp;quot;,&amp;quot;chapterCount&amp;quot;:50,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;law&amp;quot;,&amp;quot;order&amp;quot;:1,&amp;quot;slug&amp;quot;:&amp;quot;1.Mose&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:2,&amp;quot;name&amp;quot;:&amp;quot;2. Mose&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Mo&amp;quot;,&amp;quot;chapterCount&amp;quot;:40,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;law&amp;quot;,&amp;quot;order&amp;quot;:2,&amp;quot;slug&amp;quot;:&amp;quot;2.Mose&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:3,&amp;quot;name&amp;quot;:&amp;quot;3. Mose&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;3Mo&amp;quot;,&amp;quot;chapterCount&amp;quot;:27,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;law&amp;quot;,&amp;quot;order&amp;quot;:3,&amp;quot;slug&amp;quot;:&amp;quot;3.Mose&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:4,&amp;quot;name&amp;quot;:&amp;quot;4. Mose&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;4Mo&amp;quot;,&amp;quot;chapterCount&amp;quot;:36,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;law&amp;quot;,&amp;quot;order&amp;quot;:4,&amp;quot;slug&amp;quot;:&amp;quot;4.Mose&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:5,&amp;quot;name&amp;quot;:&amp;quot;5. Mose&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;5Mo&amp;quot;,&amp;quot;chapterCount&amp;quot;:34,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;law&amp;quot;,&amp;quot;order&amp;quot;:5,&amp;quot;slug&amp;quot;:&amp;quot;5.Mose&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:6,&amp;quot;name&amp;quot;:&amp;quot;Josua&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Jos&amp;quot;,&amp;quot;chapterCount&amp;quot;:24,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:6,&amp;quot;slug&amp;quot;:&amp;quot;Josua&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:7,&amp;quot;name&amp;quot;:&amp;quot;Richter&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Ri&amp;quot;,&amp;quot;chapterCount&amp;quot;:21,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:7,&amp;quot;slug&amp;quot;:&amp;quot;Richter&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:8,&amp;quot;name&amp;quot;:&amp;quot;Ruth&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Rt&amp;quot;,&amp;quot;chapterCount&amp;quot;:4,&amp;quot;chapters&amp;quot;:[1,2,3,4],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:8,&amp;quot;slug&amp;quot;:&amp;quot;Ruth&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:9,&amp;quot;name&amp;quot;:&amp;quot;1. Samuel&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Sam&amp;quot;,&amp;quot;chapterCount&amp;quot;:31,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:9,&amp;quot;slug&amp;quot;:&amp;quot;1.Samuel&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:10,&amp;quot;name&amp;quot;:&amp;quot;2. Samuel&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Sam&amp;quot;,&amp;quot;chapterCount&amp;quot;:24,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:10,&amp;quot;slug&amp;quot;:&amp;quot;2.Samuel&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:11,&amp;quot;name&amp;quot;:&amp;quot;1. K\u00f6nige&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1K\u00f6n&amp;quot;,&amp;quot;chapterCount&amp;quot;:22,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:11,&amp;quot;slug&amp;quot;:&amp;quot;1.K\u00f6nige&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:12,&amp;quot;name&amp;quot;:&amp;quot;2. K\u00f6nige&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2K\u00f6n&amp;quot;,&amp;quot;chapterCount&amp;quot;:25,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:12,&amp;quot;slug&amp;quot;:&amp;quot;2.K\u00f6nige&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:13,&amp;quot;name&amp;quot;:&amp;quot;1. Chronik&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Chr&amp;quot;,&amp;quot;chapterCount&amp;quot;:29,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:13,&amp;quot;slug&amp;quot;:&amp;quot;1.Chronik&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:14,&amp;quot;name&amp;quot;:&amp;quot;2. Chronik&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Chr&amp;quot;,&amp;quot;chapterCount&amp;quot;:36,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:14,&amp;quot;slug&amp;quot;:&amp;quot;2.Chronik&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:15,&amp;quot;name&amp;quot;:&amp;quot;Esra&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Esra&amp;quot;,&amp;quot;chapterCount&amp;quot;:10,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:15,&amp;quot;slug&amp;quot;:&amp;quot;Esra&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:16,&amp;quot;name&amp;quot;:&amp;quot;Nehemia&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Neh&amp;quot;,&amp;quot;chapterCount&amp;quot;:13,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:16,&amp;quot;slug&amp;quot;:&amp;quot;Nehemia&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:17,&amp;quot;name&amp;quot;:&amp;quot;Esther&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Est&amp;quot;,&amp;quot;chapterCount&amp;quot;:10,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:17,&amp;quot;slug&amp;quot;:&amp;quot;Esther&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:18,&amp;quot;name&amp;quot;:&amp;quot;Hiob&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Hi&amp;quot;,&amp;quot;chapterCount&amp;quot;:42,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;wisdom&amp;quot;,&amp;quot;order&amp;quot;:18,&amp;quot;slug&amp;quot;:&amp;quot;Hiob&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:19,&amp;quot;name&amp;quot;:&amp;quot;Psalmen&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Ps&amp;quot;,&amp;quot;chapterCount&amp;quot;:150,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;wisdom&amp;quot;,&amp;quot;order&amp;quot;:19,&amp;quot;slug&amp;quot;:&amp;quot;Psalmen&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:20,&amp;quot;name&amp;quot;:&amp;quot;Spr\u00fcche&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Spr&amp;quot;,&amp;quot;chapterCount&amp;quot;:31,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;wisdom&amp;quot;,&amp;quot;order&amp;quot;:20,&amp;quot;slug&amp;quot;:&amp;quot;Spr\u00fcche&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:21,&amp;quot;name&amp;quot;:&amp;quot;Prediger&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Pred&amp;quot;,&amp;quot;chapterCount&amp;quot;:12,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;wisdom&amp;quot;,&amp;quot;order&amp;quot;:21,&amp;quot;slug&amp;quot;:&amp;quot;Prediger&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:22,&amp;quot;name&amp;quot;:&amp;quot;Hohelied&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Hl&amp;quot;,&amp;quot;chapterCount&amp;quot;:8,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;wisdom&amp;quot;,&amp;quot;order&amp;quot;:22,&amp;quot;slug&amp;quot;:&amp;quot;Hohelied&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:23,&amp;quot;name&amp;quot;:&amp;quot;Jesaja&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Jes&amp;quot;,&amp;quot;chapterCount&amp;quot;:66,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:23,&amp;quot;slug&amp;quot;:&amp;quot;Jesaja&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:24,&amp;quot;name&amp;quot;:&amp;quot;Jeremia&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Jer&amp;quot;,&amp;quot;chapterCount&amp;quot;:52,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:24,&amp;quot;slug&amp;quot;:&amp;quot;Jeremia&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:25,&amp;quot;name&amp;quot;:&amp;quot;Klagelieder&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Kla&amp;quot;,&amp;quot;chapterCount&amp;quot;:5,&amp;quot;chapters&amp;quot;:[1,2,3,4,5],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;wisdom&amp;quot;,&amp;quot;order&amp;quot;:25,&amp;quot;slug&amp;quot;:&amp;quot;Klagelieder&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:26,&amp;quot;name&amp;quot;:&amp;quot;Hesekiel&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Hes&amp;quot;,&amp;quot;chapterCount&amp;quot;:48,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:26,&amp;quot;slug&amp;quot;:&amp;quot;Hesekiel&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:27,&amp;quot;name&amp;quot;:&amp;quot;Daniel&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Dan&amp;quot;,&amp;quot;chapterCount&amp;quot;:12,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:27,&amp;quot;slug&amp;quot;:&amp;quot;Daniel&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:28,&amp;quot;name&amp;quot;:&amp;quot;Hosea&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Hos&amp;quot;,&amp;quot;chapterCount&amp;quot;:14,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:28,&amp;quot;slug&amp;quot;:&amp;quot;Hosea&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:29,&amp;quot;name&amp;quot;:&amp;quot;Joel&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Joel&amp;quot;,&amp;quot;chapterCount&amp;quot;:3,&amp;quot;chapters&amp;quot;:[1,2,3],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:29,&amp;quot;slug&amp;quot;:&amp;quot;Joel&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:30,&amp;quot;name&amp;quot;:&amp;quot;Amos&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Am&amp;quot;,&amp;quot;chapterCount&amp;quot;:9,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:30,&amp;quot;slug&amp;quot;:&amp;quot;Amos&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:31,&amp;quot;name&amp;quot;:&amp;quot;Obadja&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Ob&amp;quot;,&amp;quot;chapterCount&amp;quot;:1,&amp;quot;chapters&amp;quot;:[1],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:31,&amp;quot;slug&amp;quot;:&amp;quot;Obadja&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:32,&amp;quot;name&amp;quot;:&amp;quot;Jona&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Jon&amp;quot;,&amp;quot;chapterCount&amp;quot;:4,&amp;quot;chapters&amp;quot;:[1,2,3,4],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:32,&amp;quot;slug&amp;quot;:&amp;quot;Jona&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:33,&amp;quot;name&amp;quot;:&amp;quot;Micha&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Mi&amp;quot;,&amp;quot;chapterCount&amp;quot;:7,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:33,&amp;quot;slug&amp;quot;:&amp;quot;Micha&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:34,&amp;quot;name&amp;quot;:&amp;quot;Nahum&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Nah&amp;quot;,&amp;quot;chapterCount&amp;quot;:3,&amp;quot;chapters&amp;quot;:[1,2,3],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:34,&amp;quot;slug&amp;quot;:&amp;quot;Nahum&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:35,&amp;quot;name&amp;quot;:&amp;quot;Habakuk&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Hab&amp;quot;,&amp;quot;chapterCount&amp;quot;:3,&amp;quot;chapters&amp;quot;:[1,2,3],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:35,&amp;quot;slug&amp;quot;:&amp;quot;Habakuk&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:36,&amp;quot;name&amp;quot;:&amp;quot;Zephanja&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Zeph&amp;quot;,&amp;quot;chapterCount&amp;quot;:3,&amp;quot;chapters&amp;quot;:[1,2,3],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:36,&amp;quot;slug&amp;quot;:&amp;quot;Zephanja&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:37,&amp;quot;name&amp;quot;:&amp;quot;Haggai&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Hag&amp;quot;,&amp;quot;chapterCount&amp;quot;:2,&amp;quot;chapters&amp;quot;:[1,2],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:37,&amp;quot;slug&amp;quot;:&amp;quot;Haggai&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:38,&amp;quot;name&amp;quot;:&amp;quot;Sacharja&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Sach&amp;quot;,&amp;quot;chapterCount&amp;quot;:14,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:38,&amp;quot;slug&amp;quot;:&amp;quot;Sacharja&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:39,&amp;quot;name&amp;quot;:&amp;quot;Maleachi&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Mal&amp;quot;,&amp;quot;chapterCount&amp;quot;:4,&amp;quot;chapters&amp;quot;:[1,2,3,4],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:39,&amp;quot;slug&amp;quot;:&amp;quot;Maleachi&amp;quot;,&amp;quot;hasContent&amp;quot;:false}]},{&amp;quot;name&amp;quot;:&amp;quot;Neues Testament&amp;quot;,&amp;quot;books&amp;quot;:[{&amp;quot;id&amp;quot;:40,&amp;quot;name&amp;quot;:&amp;quot;Matth\u00e4us&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Mt&amp;quot;,&amp;quot;chapterCount&amp;quot;:28,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;gospel&amp;quot;,&amp;quot;order&amp;quot;:40,&amp;quot;slug&amp;quot;:&amp;quot;Matth\u00e4us&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:41,&amp;quot;name&amp;quot;:&amp;quot;Markus&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Mk&amp;quot;,&amp;quot;chapterCount&amp;quot;:16,&amp;quot;chapters&amp;quot;:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;gospel&amp;quot;,&amp;quot;order&amp;quot;:41,&amp;quot;slug&amp;quot;:&amp;quot;Markus&amp;quot;,&amp;quot;hasContent&amp;quot;:true},{&amp;quot;id&amp;quot;:42,&amp;quot;name&amp;quot;:&amp;quot;Lukas&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Lk&amp;quot;,&amp;quot;chapterCount&amp;quot;:24,&amp;quot;chapters&amp;quot;:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;gospel&amp;quot;,&amp;quot;order&amp;quot;:42,&amp;quot;slug&amp;quot;:&amp;quot;Lukas&amp;quot;,&amp;quot;hasContent&amp;quot;:true},{&amp;quot;id&amp;quot;:43,&amp;quot;name&amp;quot;:&amp;quot;Die Heilsbotschaft nach Johannes&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Joh&amp;quot;,&amp;quot;chapterCount&amp;quot;:21,&amp;quot;chapters&amp;quot;:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;gospel&amp;quot;,&amp;quot;order&amp;quot;:43,&amp;quot;slug&amp;quot;:&amp;quot;Johannes&amp;quot;,&amp;quot;hasContent&amp;quot;:true},{&amp;quot;id&amp;quot;:44,&amp;quot;name&amp;quot;:&amp;quot;Apostelgeschichte&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Apg&amp;quot;,&amp;quot;chapterCount&amp;quot;:28,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:44,&amp;quot;slug&amp;quot;:&amp;quot;Apostelgeschichte&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:45,&amp;quot;name&amp;quot;:&amp;quot;R\u00f6mer&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;R\u00f6m&amp;quot;,&amp;quot;chapterCount&amp;quot;:16,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:45,&amp;quot;slug&amp;quot;:&amp;quot;R\u00f6mer&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:46,&amp;quot;name&amp;quot;:&amp;quot;1. Korinther&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Kor&amp;quot;,&amp;quot;chapterCount&amp;quot;:16,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:46,&amp;quot;slug&amp;quot;:&amp;quot;1.Korinther&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:47,&amp;quot;name&amp;quot;:&amp;quot;2. Korinther&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Kor&amp;quot;,&amp;quot;chapterCount&amp;quot;:13,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:47,&amp;quot;slug&amp;quot;:&amp;quot;2.Korinther&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:48,&amp;quot;name&amp;quot;:&amp;quot;Galater&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Gal&amp;quot;,&amp;quot;chapterCount&amp;quot;:6,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:48,&amp;quot;slug&amp;quot;:&amp;quot;Galater&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:49,&amp;quot;name&amp;quot;:&amp;quot;Epheser&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Eph&amp;quot;,&amp;quot;chapterCount&amp;quot;:6,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:49,&amp;quot;slug&amp;quot;:&amp;quot;Epheser&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:50,&amp;quot;name&amp;quot;:&amp;quot;Philipper&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Phil&amp;quot;,&amp;quot;chapterCount&amp;quot;:4,&amp;quot;chapters&amp;quot;:[1,2,3,4],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:50,&amp;quot;slug&amp;quot;:&amp;quot;Philipper&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:51,&amp;quot;name&amp;quot;:&amp;quot;Kolosser&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Kol&amp;quot;,&amp;quot;chapterCount&amp;quot;:4,&amp;quot;chapters&amp;quot;:[1,2,3,4],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:51,&amp;quot;slug&amp;quot;:&amp;quot;Kolosser&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:52,&amp;quot;name&amp;quot;:&amp;quot;1. Thessalonicher&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Thes&amp;quot;,&amp;quot;chapterCount&amp;quot;:5,&amp;quot;chapters&amp;quot;:[1,2,3,4,5],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:52,&amp;quot;slug&amp;quot;:&amp;quot;1.Thessalonicher&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:53,&amp;quot;name&amp;quot;:&amp;quot;2. Thessalonicher&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Thes&amp;quot;,&amp;quot;chapterCount&amp;quot;:3,&amp;quot;chapters&amp;quot;:[1,2,3],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:53,&amp;quot;slug&amp;quot;:&amp;quot;2.Thessalonicher&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:54,&amp;quot;name&amp;quot;:&amp;quot;1. Timotheus&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Tim&amp;quot;,&amp;quot;chapterCount&amp;quot;:6,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:54,&amp;quot;slug&amp;quot;:&amp;quot;1.Timotheus&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:55,&amp;quot;name&amp;quot;:&amp;quot;2. Timotheus&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Tim&amp;quot;,&amp;quot;chapterCount&amp;quot;:4,&amp;quot;chapters&amp;quot;:[1,2,3,4],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:55,&amp;quot;slug&amp;quot;:&amp;quot;2.Timotheus&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:56,&amp;quot;name&amp;quot;:&amp;quot;Titus&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Tit&amp;quot;,&amp;quot;chapterCount&amp;quot;:3,&amp;quot;chapters&amp;quot;:[1,2,3],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:56,&amp;quot;slug&amp;quot;:&amp;quot;Titus&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:57,&amp;quot;name&amp;quot;:&amp;quot;Philemon&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Phim&amp;quot;,&amp;quot;chapterCount&amp;quot;:1,&amp;quot;chapters&amp;quot;:[1],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:57,&amp;quot;slug&amp;quot;:&amp;quot;Philemon&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:58,&amp;quot;name&amp;quot;:&amp;quot;Hebr\u00e4er&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Heb&amp;quot;,&amp;quot;chapterCount&amp;quot;:13,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:58,&amp;quot;slug&amp;quot;:&amp;quot;Hebr\u00e4er&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:59,&amp;quot;name&amp;quot;:&amp;quot;Jakobus&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Jak&amp;quot;,&amp;quot;chapterCount&amp;quot;:5,&amp;quot;chapters&amp;quot;:[1,2,3,4,5],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:59,&amp;quot;slug&amp;quot;:&amp;quot;Jakobus&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:60,&amp;quot;name&amp;quot;:&amp;quot;1. Petrus&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Pet&amp;quot;,&amp;quot;chapterCount&amp;quot;:5,&amp;quot;chapters&amp;quot;:[1,2,3,4,5],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:60,&amp;quot;slug&amp;quot;:&amp;quot;1.Petrus&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:61,&amp;quot;name&amp;quot;:&amp;quot;2. Petrus&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Pet&amp;quot;,&amp;quot;chapterCount&amp;quot;:3,&amp;quot;chapters&amp;quot;:[1,2,3],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:61,&amp;quot;slug&amp;quot;:&amp;quot;2.Petrus&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:62,&amp;quot;name&amp;quot;:&amp;quot;1. Johannes&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Joh&amp;quot;,&amp;quot;chapterCount&amp;quot;:5,&amp;quot;chapters&amp;quot;:[1,2,3,4,5],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:62,&amp;quot;slug&amp;quot;:&amp;quot;1.Johannes&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:63,&amp;quot;name&amp;quot;:&amp;quot;2. Johannes&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Joh&amp;quot;,&amp;quot;chapterCount&amp;quot;:1,&amp;quot;chapters&amp;quot;:[1],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:63,&amp;quot;slug&amp;quot;:&amp;quot;2.Johannes&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:64,&amp;quot;name&amp;quot;:&amp;quot;3. Johannes&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;3Joh&amp;quot;,&amp;quot;chapterCount&amp;quot;:1,&amp;quot;chapters&amp;quot;:[1],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:64,&amp;quot;slug&amp;quot;:&amp;quot;3.Johannes&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:65,&amp;quot;name&amp;quot;:&amp;quot;Judas&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Jud&amp;quot;,&amp;quot;chapterCount&amp;quot;:1,&amp;quot;chapters&amp;quot;:[1],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:65,&amp;quot;slug&amp;quot;:&amp;quot;Judas&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:66,&amp;quot;name&amp;quot;:&amp;quot;Offenbarung&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Offb&amp;quot;,&amp;quot;chapterCount&amp;quot;:22,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;apocalypse&amp;quot;,&amp;quot;order&amp;quot;:66,&amp;quot;slug&amp;quot;:&amp;quot;Offenbarung&amp;quot;,&amp;quot;hasContent&amp;quot;:false}]}],&amp;quot;availableBooks&amp;quot;:[{&amp;quot;slug&amp;quot;:&amp;quot;Markus&amp;quot;,&amp;quot;order&amp;quot;:41},{&amp;quot;slug&amp;quot;:&amp;quot;Lukas&amp;quot;,&amp;quot;order&amp;quot;:42},{&amp;quot;slug&amp;quot;:&amp;quot;Johannes&amp;quot;,&amp;quot;order&amp;quot;:43}]},&amp;quot;env&amp;quot;:&amp;quot;local&amp;quot;,&amp;quot;auth&amp;quot;:{&amp;quot;user&amp;quot;:null},&amp;quot;requestedPath&amp;quot;:&amp;quot;api\/user&amp;quot;},&amp;quot;url&amp;quot;:&amp;quot;\/api\/user&amp;quot;,&amp;quot;version&amp;quot;:&amp;quot;18765f3fa436d07c5ef1cbbbc3fa3b37&amp;quot;,&amp;quot;clearHistory&amp;quot;:false,&amp;quot;encryptHistory&amp;quot;:false}&quot;&gt;&lt;/div&gt;&lt;/body&gt;

&lt;/html&gt;
</code>
 </pre>
    </span>
<span id="execution-results-GETapi-user" hidden>
    <blockquote>Received response<span
                id="execution-response-status-GETapi-user"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-GETapi-user"
      data-empty-response-text="<Empty response>" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-GETapi-user" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-GETapi-user">

Tip: Check that you&#039;re properly connected to the network.
If you&#039;re a maintainer of ths API, verify that your API is running and you&#039;ve enabled CORS.
You can check the Dev Tools console for debugging information.</code></pre>
</span>
<form id="form-GETapi-user" data-method="GET"
      data-path="api/user"
      data-authed="0"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('GETapi-user', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
                    <button type="button"
                    style="background-color: #8fbcd4; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-tryout-GETapi-user"
                    onclick="tryItOut('GETapi-user');">Try it out ⚡
            </button>
            <button type="button"
                    style="background-color: #c97a7e; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-canceltryout-GETapi-user"
                    onclick="cancelTryOut('GETapi-user');" hidden>Cancel 🛑
            </button>&nbsp;&nbsp;
            <button type="submit"
                    style="background-color: #6ac174; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-executetryout-GETapi-user"
                    data-initial-text="Send Request 💥"
                    data-loading-text="⏱ Sending..."
                    hidden>Send Request 💥
            </button>
            </h3>
            <p>
            <small class="badge badge-green">GET</small>
            <b><code>api/user</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Content-Type</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Content-Type"                data-endpoint="GETapi-user"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Accept</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Accept"                data-endpoint="GETapi-user"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                        </form>

                    <h2 id="endpoints-GETapi-chapters-fetch">GET api/chapters/fetch</h2>

<p>
</p>



<span id="example-requests-GETapi-chapters-fetch">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request GET \
    --get "http://esra-bibel.local/api/chapters/fetch" \
    --header "Content-Type: application/json" \
    --header "Accept: application/json" \
    --data "{
    \"bookId\": 16,
    \"chapter\": 22,
    \"direction\": \"previous\"
}"
</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "http://esra-bibel.local/api/chapters/fetch"
);

const headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

let body = {
    "bookId": 16,
    "chapter": 22,
    "direction": "previous"
};

fetch(url, {
    method: "GET",
    headers,
    body: JSON.stringify(body),
}).then(response =&gt; response.json());</code></pre></div>

</span>

<span id="example-responses-GETapi-chapters-fetch">
            <blockquote>
            <p>Example response (404):</p>
        </blockquote>
                <details class="annotation">
            <summary style="cursor: pointer;">
                <small onclick="textContent = parentElement.parentElement.open ? 'Show headers' : 'Hide headers'">Show headers</small>
            </summary>
            <pre><code class="language-http">cache-control: no-cache, private
content-type: application/json
vary: X-Inertia
access-control-allow-origin: *
set-cookie: XSRF-TOKEN=eyJpdiI6Ii8rZCt2cEZFYXRpR0lFMnNjSkhSOUE9PSIsInZhbHVlIjoiUCtNdDVkSHAxR2hJbFROR0xZU3VCTjVycklDZHlWRzVTRk9kZFhMNWtYOFhWaTF6YU55L0tHK3ArTk50NHU0T2V4a0dqOEdhc2ZkcUo0bW9oME9pMG0ybEJ5K091RzhUYTlmMW5oRDlMMnRuREZ5V3krTFpuNVM1Z3k0aXNIU3UiLCJtYWMiOiIyNjA4OGRiYmU3YzgyNDYzMGFhNzZjYTI4MWE2ZTA1Y2Q1MzU3NmRjOGEzY2E4M2I4MmQ3NDIzZWYzYmY4MTQ2IiwidGFnIjoiIn0%3D; expires=Wed, 09 Apr 2025 13:17:49 GMT; Max-Age=7200; path=/; samesite=lax; esrabibel_session=eyJpdiI6IlRvdHZNR2g5dFhvV0NWai9aNjVTQ0E9PSIsInZhbHVlIjoiWG1XS3FXN3R6TXNObE5zb29sTXFBb2ljTWVvMTByVGEvR3J0dTc5V0VjQ1kvVHREQzNjTG0xZDFld1lxY3NsbFNHaDJBVGFWc1lwYW52YVkrNlN4M2FjVVMrRUpvOFFvK1QzSHJBNlEzdGRQWG42dm1ScHYxclFuek9vczl0QmsiLCJtYWMiOiI3ZGIyYTA5YWM2MGRkZDFjZjNmZWRiM2U4NWMyMjQ3NWQ5Njc0MzBhNWFkZjlmMWQ1Yjk4ODdhMTFhNjFjM2ExIiwidGFnIjoiIn0%3D; expires=Wed, 09 Apr 2025 13:17:49 GMT; Max-Age=7200; path=/; httponly; samesite=lax
 </code></pre></details>         <pre>

<code class="language-json" style="max-height: 300px;">&lt;!DOCTYPE html&gt;
&lt;html lang=&quot;de&quot;&gt;

&lt;head&gt;
    &lt;meta charset=&quot;utf-8&quot;&gt;
    &lt;meta name=&quot;viewport&quot; content=&quot;width=device-width, initial-scale=1&quot;&gt;

    &lt;title inertia&gt;&lt;/title&gt;

    &lt;!-- Fonts --&gt;
    &lt;!-- Preconnect to font domains --&gt;
    &lt;link rel=&quot;preconnect&quot; href=&quot;https://fonts.bunny.net&quot; crossorigin&gt;
    &lt;link rel=&quot;preconnect&quot; href=&quot;https://use.typekit.net&quot; as=&quot;font&quot; type=&quot;font/woff2&quot; crossorigin&gt;

    &lt;!-- Preload critical fonts --&gt;
    &lt;link rel=&quot;preload&quot; href=&quot;/fonts/ThanatosText-Book.woff2&quot; as=&quot;font&quot; type=&quot;font/woff2&quot; crossorigin&gt;

    &lt;!-- Load fonts --&gt;
    &lt;link href=&quot;https://fonts.bunny.net/css?family=figtree:400,500,600&amp;display=swap&quot; rel=&quot;stylesheet&quot; /&gt;
    &lt;link rel=&quot;stylesheet&quot; href=&quot;https://use.typekit.net/kzb8yhl.css&quot; media=&quot;print&quot; onload=&quot;this.media=&#039;all&#039;&quot;&gt;

    &lt;!-- Fallback for typekit fonts --&gt;
    &lt;noscript&gt;
        &lt;link rel=&quot;stylesheet&quot; href=&quot;https://use.typekit.net/kzb8yhl.css&quot;&gt;
    &lt;/noscript&gt;

    &lt;!-- Local font definition --&gt;
    &lt;style&gt;
        @font-face {
            font-family: &#039;ThanatosText&#039;;
            src: url(&#039;/fonts/ThanatosText-Book.woff2&#039;) format(&#039;woff2&#039;);
            font-weight: normal;
            font-style: normal;
            font-display: swap;
        }
    &lt;/style&gt;

    &lt;!-- Scripts --&gt;
    &lt;script type=&quot;text/javascript&quot;&gt;Object.assign(Ziggy.routes,{&quot;search.index&quot;:{&quot;uri&quot;:&quot;search&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;search.query&quot;:{&quot;uri&quot;:&quot;search\/{query}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;wheres&quot;:{&quot;query&quot;:&quot;[^\/]+&quot;},&quot;parameters&quot;:[&quot;query&quot;]},&quot;search.paged&quot;:{&quot;uri&quot;:&quot;search\/{query}\/{page?}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;wheres&quot;:{&quot;query&quot;:&quot;[^\/]+&quot;,&quot;page&quot;:&quot;[0-9]+&quot;},&quot;parameters&quot;:[&quot;query&quot;,&quot;page&quot;]},&quot;search.settings&quot;:{&quot;uri&quot;:&quot;search\/settings&quot;,&quot;methods&quot;:[&quot;POST&quot;]},&quot;dashboard&quot;:{&quot;uri&quot;:&quot;dashboard&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;bible.import&quot;:{&quot;uri&quot;:&quot;import-bible&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;bible.import.store&quot;:{&quot;uri&quot;:&quot;import-bible&quot;,&quot;methods&quot;:[&quot;POST&quot;]},&quot;file.upload&quot;:{&quot;uri&quot;:&quot;api\/upload&quot;,&quot;methods&quot;:[&quot;POST&quot;]},&quot;profile.edit&quot;:{&quot;uri&quot;:&quot;profile&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;profile.update&quot;:{&quot;uri&quot;:&quot;profile&quot;,&quot;methods&quot;:[&quot;PATCH&quot;]},&quot;profile.destroy&quot;:{&quot;uri&quot;:&quot;profile&quot;,&quot;methods&quot;:[&quot;DELETE&quot;]},&quot;books.show&quot;:{&quot;uri&quot;:&quot;{reference}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;wheres&quot;:{&quot;reference&quot;:&quot;[\\w\\d\\.,\\-\\+]+&quot;},&quot;parameters&quot;:[&quot;reference&quot;]},&quot;register&quot;:{&quot;uri&quot;:&quot;register&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;login&quot;:{&quot;uri&quot;:&quot;login&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;password.request&quot;:{&quot;uri&quot;:&quot;forgot-password&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;password.email&quot;:{&quot;uri&quot;:&quot;forgot-password&quot;,&quot;methods&quot;:[&quot;POST&quot;]},&quot;password.reset&quot;:{&quot;uri&quot;:&quot;reset-password\/{token}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;parameters&quot;:[&quot;token&quot;]},&quot;password.store&quot;:{&quot;uri&quot;:&quot;reset-password&quot;,&quot;methods&quot;:[&quot;POST&quot;]},&quot;verification.notice&quot;:{&quot;uri&quot;:&quot;verify-email&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;verification.verify&quot;:{&quot;uri&quot;:&quot;verify-email\/{id}\/{hash}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;parameters&quot;:[&quot;id&quot;,&quot;hash&quot;]},&quot;verification.send&quot;:{&quot;uri&quot;:&quot;email\/verification-notification&quot;,&quot;methods&quot;:[&quot;POST&quot;]},&quot;password.confirm&quot;:{&quot;uri&quot;:&quot;confirm-password&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;password.update&quot;:{&quot;uri&quot;:&quot;password&quot;,&quot;methods&quot;:[&quot;PUT&quot;]},&quot;logout&quot;:{&quot;uri&quot;:&quot;logout&quot;,&quot;methods&quot;:[&quot;POST&quot;]},&quot;api.chapters.fetch&quot;:{&quot;uri&quot;:&quot;api\/chapters\/fetch&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;api.books&quot;:{&quot;uri&quot;:&quot;api\/books&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;api.books.content-status&quot;:{&quot;uri&quot;:&quot;api\/books\/content-status&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;api.books.show&quot;:{&quot;uri&quot;:&quot;api\/books\/{slug}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;parameters&quot;:[&quot;slug&quot;]},&quot;api.chapters.adjacent&quot;:{&quot;uri&quot;:&quot;api\/chapters\/{reference}\/adjacent&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;wheres&quot;:{&quot;reference&quot;:&quot;[\\w\\d,\\-]+&quot;},&quot;parameters&quot;:[&quot;reference&quot;]},&quot;api.search.books&quot;:{&quot;uri&quot;:&quot;api\/search\/books&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;api.search&quot;:{&quot;uri&quot;:&quot;api\/search&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;api.bible.text&quot;:{&quot;uri&quot;:&quot;api\/bible\/{reference}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;wheres&quot;:{&quot;reference&quot;:&quot;[\\w\\d,\\-]+&quot;},&quot;parameters&quot;:[&quot;reference&quot;]},&quot;storage.local&quot;:{&quot;uri&quot;:&quot;storage\/{path}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;wheres&quot;:{&quot;path&quot;:&quot;.*&quot;},&quot;parameters&quot;:[&quot;path&quot;]}});&lt;/script&gt;    &lt;script type=&quot;module&quot; src=&quot;http://[::1]:5173/@vite/client&quot;&gt;&lt;/script&gt;&lt;script type=&quot;module&quot; src=&quot;http://[::1]:5173/resources/js/app.ts&quot;&gt;&lt;/script&gt;&lt;script type=&quot;module&quot; src=&quot;http://[::1]:5173/resources/js/Pages/NotFound.vue&quot;&gt;&lt;/script&gt;    &lt;/head&gt;

&lt;body class=&quot;font-sans antialiased&quot;&gt;
    &lt;div id=&quot;app&quot; data-page=&quot;{&amp;quot;component&amp;quot;:&amp;quot;NotFound&amp;quot;,&amp;quot;props&amp;quot;:{&amp;quot;errors&amp;quot;:{},&amp;quot;books&amp;quot;:{&amp;quot;sections&amp;quot;:[{&amp;quot;name&amp;quot;:&amp;quot;Altes Testament&amp;quot;,&amp;quot;books&amp;quot;:[{&amp;quot;id&amp;quot;:1,&amp;quot;name&amp;quot;:&amp;quot;1. Mose&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Mo&amp;quot;,&amp;quot;chapterCount&amp;quot;:50,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;law&amp;quot;,&amp;quot;order&amp;quot;:1,&amp;quot;slug&amp;quot;:&amp;quot;1.Mose&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:2,&amp;quot;name&amp;quot;:&amp;quot;2. Mose&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Mo&amp;quot;,&amp;quot;chapterCount&amp;quot;:40,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;law&amp;quot;,&amp;quot;order&amp;quot;:2,&amp;quot;slug&amp;quot;:&amp;quot;2.Mose&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:3,&amp;quot;name&amp;quot;:&amp;quot;3. Mose&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;3Mo&amp;quot;,&amp;quot;chapterCount&amp;quot;:27,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;law&amp;quot;,&amp;quot;order&amp;quot;:3,&amp;quot;slug&amp;quot;:&amp;quot;3.Mose&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:4,&amp;quot;name&amp;quot;:&amp;quot;4. Mose&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;4Mo&amp;quot;,&amp;quot;chapterCount&amp;quot;:36,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;law&amp;quot;,&amp;quot;order&amp;quot;:4,&amp;quot;slug&amp;quot;:&amp;quot;4.Mose&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:5,&amp;quot;name&amp;quot;:&amp;quot;5. Mose&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;5Mo&amp;quot;,&amp;quot;chapterCount&amp;quot;:34,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;law&amp;quot;,&amp;quot;order&amp;quot;:5,&amp;quot;slug&amp;quot;:&amp;quot;5.Mose&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:6,&amp;quot;name&amp;quot;:&amp;quot;Josua&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Jos&amp;quot;,&amp;quot;chapterCount&amp;quot;:24,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:6,&amp;quot;slug&amp;quot;:&amp;quot;Josua&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:7,&amp;quot;name&amp;quot;:&amp;quot;Richter&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Ri&amp;quot;,&amp;quot;chapterCount&amp;quot;:21,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:7,&amp;quot;slug&amp;quot;:&amp;quot;Richter&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:8,&amp;quot;name&amp;quot;:&amp;quot;Ruth&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Rt&amp;quot;,&amp;quot;chapterCount&amp;quot;:4,&amp;quot;chapters&amp;quot;:[1,2,3,4],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:8,&amp;quot;slug&amp;quot;:&amp;quot;Ruth&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:9,&amp;quot;name&amp;quot;:&amp;quot;1. Samuel&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Sam&amp;quot;,&amp;quot;chapterCount&amp;quot;:31,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:9,&amp;quot;slug&amp;quot;:&amp;quot;1.Samuel&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:10,&amp;quot;name&amp;quot;:&amp;quot;2. Samuel&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Sam&amp;quot;,&amp;quot;chapterCount&amp;quot;:24,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:10,&amp;quot;slug&amp;quot;:&amp;quot;2.Samuel&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:11,&amp;quot;name&amp;quot;:&amp;quot;1. K\u00f6nige&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1K\u00f6n&amp;quot;,&amp;quot;chapterCount&amp;quot;:22,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:11,&amp;quot;slug&amp;quot;:&amp;quot;1.K\u00f6nige&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:12,&amp;quot;name&amp;quot;:&amp;quot;2. K\u00f6nige&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2K\u00f6n&amp;quot;,&amp;quot;chapterCount&amp;quot;:25,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:12,&amp;quot;slug&amp;quot;:&amp;quot;2.K\u00f6nige&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:13,&amp;quot;name&amp;quot;:&amp;quot;1. Chronik&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Chr&amp;quot;,&amp;quot;chapterCount&amp;quot;:29,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:13,&amp;quot;slug&amp;quot;:&amp;quot;1.Chronik&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:14,&amp;quot;name&amp;quot;:&amp;quot;2. Chronik&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Chr&amp;quot;,&amp;quot;chapterCount&amp;quot;:36,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:14,&amp;quot;slug&amp;quot;:&amp;quot;2.Chronik&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:15,&amp;quot;name&amp;quot;:&amp;quot;Esra&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Esra&amp;quot;,&amp;quot;chapterCount&amp;quot;:10,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:15,&amp;quot;slug&amp;quot;:&amp;quot;Esra&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:16,&amp;quot;name&amp;quot;:&amp;quot;Nehemia&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Neh&amp;quot;,&amp;quot;chapterCount&amp;quot;:13,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:16,&amp;quot;slug&amp;quot;:&amp;quot;Nehemia&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:17,&amp;quot;name&amp;quot;:&amp;quot;Esther&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Est&amp;quot;,&amp;quot;chapterCount&amp;quot;:10,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:17,&amp;quot;slug&amp;quot;:&amp;quot;Esther&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:18,&amp;quot;name&amp;quot;:&amp;quot;Hiob&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Hi&amp;quot;,&amp;quot;chapterCount&amp;quot;:42,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;wisdom&amp;quot;,&amp;quot;order&amp;quot;:18,&amp;quot;slug&amp;quot;:&amp;quot;Hiob&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:19,&amp;quot;name&amp;quot;:&amp;quot;Psalmen&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Ps&amp;quot;,&amp;quot;chapterCount&amp;quot;:150,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;wisdom&amp;quot;,&amp;quot;order&amp;quot;:19,&amp;quot;slug&amp;quot;:&amp;quot;Psalmen&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:20,&amp;quot;name&amp;quot;:&amp;quot;Spr\u00fcche&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Spr&amp;quot;,&amp;quot;chapterCount&amp;quot;:31,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;wisdom&amp;quot;,&amp;quot;order&amp;quot;:20,&amp;quot;slug&amp;quot;:&amp;quot;Spr\u00fcche&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:21,&amp;quot;name&amp;quot;:&amp;quot;Prediger&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Pred&amp;quot;,&amp;quot;chapterCount&amp;quot;:12,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;wisdom&amp;quot;,&amp;quot;order&amp;quot;:21,&amp;quot;slug&amp;quot;:&amp;quot;Prediger&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:22,&amp;quot;name&amp;quot;:&amp;quot;Hohelied&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Hl&amp;quot;,&amp;quot;chapterCount&amp;quot;:8,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;wisdom&amp;quot;,&amp;quot;order&amp;quot;:22,&amp;quot;slug&amp;quot;:&amp;quot;Hohelied&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:23,&amp;quot;name&amp;quot;:&amp;quot;Jesaja&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Jes&amp;quot;,&amp;quot;chapterCount&amp;quot;:66,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:23,&amp;quot;slug&amp;quot;:&amp;quot;Jesaja&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:24,&amp;quot;name&amp;quot;:&amp;quot;Jeremia&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Jer&amp;quot;,&amp;quot;chapterCount&amp;quot;:52,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:24,&amp;quot;slug&amp;quot;:&amp;quot;Jeremia&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:25,&amp;quot;name&amp;quot;:&amp;quot;Klagelieder&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Kla&amp;quot;,&amp;quot;chapterCount&amp;quot;:5,&amp;quot;chapters&amp;quot;:[1,2,3,4,5],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;wisdom&amp;quot;,&amp;quot;order&amp;quot;:25,&amp;quot;slug&amp;quot;:&amp;quot;Klagelieder&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:26,&amp;quot;name&amp;quot;:&amp;quot;Hesekiel&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Hes&amp;quot;,&amp;quot;chapterCount&amp;quot;:48,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:26,&amp;quot;slug&amp;quot;:&amp;quot;Hesekiel&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:27,&amp;quot;name&amp;quot;:&amp;quot;Daniel&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Dan&amp;quot;,&amp;quot;chapterCount&amp;quot;:12,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:27,&amp;quot;slug&amp;quot;:&amp;quot;Daniel&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:28,&amp;quot;name&amp;quot;:&amp;quot;Hosea&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Hos&amp;quot;,&amp;quot;chapterCount&amp;quot;:14,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:28,&amp;quot;slug&amp;quot;:&amp;quot;Hosea&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:29,&amp;quot;name&amp;quot;:&amp;quot;Joel&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Joel&amp;quot;,&amp;quot;chapterCount&amp;quot;:3,&amp;quot;chapters&amp;quot;:[1,2,3],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:29,&amp;quot;slug&amp;quot;:&amp;quot;Joel&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:30,&amp;quot;name&amp;quot;:&amp;quot;Amos&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Am&amp;quot;,&amp;quot;chapterCount&amp;quot;:9,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:30,&amp;quot;slug&amp;quot;:&amp;quot;Amos&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:31,&amp;quot;name&amp;quot;:&amp;quot;Obadja&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Ob&amp;quot;,&amp;quot;chapterCount&amp;quot;:1,&amp;quot;chapters&amp;quot;:[1],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:31,&amp;quot;slug&amp;quot;:&amp;quot;Obadja&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:32,&amp;quot;name&amp;quot;:&amp;quot;Jona&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Jon&amp;quot;,&amp;quot;chapterCount&amp;quot;:4,&amp;quot;chapters&amp;quot;:[1,2,3,4],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:32,&amp;quot;slug&amp;quot;:&amp;quot;Jona&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:33,&amp;quot;name&amp;quot;:&amp;quot;Micha&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Mi&amp;quot;,&amp;quot;chapterCount&amp;quot;:7,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:33,&amp;quot;slug&amp;quot;:&amp;quot;Micha&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:34,&amp;quot;name&amp;quot;:&amp;quot;Nahum&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Nah&amp;quot;,&amp;quot;chapterCount&amp;quot;:3,&amp;quot;chapters&amp;quot;:[1,2,3],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:34,&amp;quot;slug&amp;quot;:&amp;quot;Nahum&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:35,&amp;quot;name&amp;quot;:&amp;quot;Habakuk&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Hab&amp;quot;,&amp;quot;chapterCount&amp;quot;:3,&amp;quot;chapters&amp;quot;:[1,2,3],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:35,&amp;quot;slug&amp;quot;:&amp;quot;Habakuk&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:36,&amp;quot;name&amp;quot;:&amp;quot;Zephanja&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Zeph&amp;quot;,&amp;quot;chapterCount&amp;quot;:3,&amp;quot;chapters&amp;quot;:[1,2,3],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:36,&amp;quot;slug&amp;quot;:&amp;quot;Zephanja&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:37,&amp;quot;name&amp;quot;:&amp;quot;Haggai&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Hag&amp;quot;,&amp;quot;chapterCount&amp;quot;:2,&amp;quot;chapters&amp;quot;:[1,2],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:37,&amp;quot;slug&amp;quot;:&amp;quot;Haggai&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:38,&amp;quot;name&amp;quot;:&amp;quot;Sacharja&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Sach&amp;quot;,&amp;quot;chapterCount&amp;quot;:14,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:38,&amp;quot;slug&amp;quot;:&amp;quot;Sacharja&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:39,&amp;quot;name&amp;quot;:&amp;quot;Maleachi&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Mal&amp;quot;,&amp;quot;chapterCount&amp;quot;:4,&amp;quot;chapters&amp;quot;:[1,2,3,4],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:39,&amp;quot;slug&amp;quot;:&amp;quot;Maleachi&amp;quot;,&amp;quot;hasContent&amp;quot;:false}]},{&amp;quot;name&amp;quot;:&amp;quot;Neues Testament&amp;quot;,&amp;quot;books&amp;quot;:[{&amp;quot;id&amp;quot;:40,&amp;quot;name&amp;quot;:&amp;quot;Matth\u00e4us&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Mt&amp;quot;,&amp;quot;chapterCount&amp;quot;:28,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;gospel&amp;quot;,&amp;quot;order&amp;quot;:40,&amp;quot;slug&amp;quot;:&amp;quot;Matth\u00e4us&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:41,&amp;quot;name&amp;quot;:&amp;quot;Markus&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Mk&amp;quot;,&amp;quot;chapterCount&amp;quot;:16,&amp;quot;chapters&amp;quot;:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;gospel&amp;quot;,&amp;quot;order&amp;quot;:41,&amp;quot;slug&amp;quot;:&amp;quot;Markus&amp;quot;,&amp;quot;hasContent&amp;quot;:true},{&amp;quot;id&amp;quot;:42,&amp;quot;name&amp;quot;:&amp;quot;Lukas&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Lk&amp;quot;,&amp;quot;chapterCount&amp;quot;:24,&amp;quot;chapters&amp;quot;:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;gospel&amp;quot;,&amp;quot;order&amp;quot;:42,&amp;quot;slug&amp;quot;:&amp;quot;Lukas&amp;quot;,&amp;quot;hasContent&amp;quot;:true},{&amp;quot;id&amp;quot;:43,&amp;quot;name&amp;quot;:&amp;quot;Die Heilsbotschaft nach Johannes&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Joh&amp;quot;,&amp;quot;chapterCount&amp;quot;:21,&amp;quot;chapters&amp;quot;:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;gospel&amp;quot;,&amp;quot;order&amp;quot;:43,&amp;quot;slug&amp;quot;:&amp;quot;Johannes&amp;quot;,&amp;quot;hasContent&amp;quot;:true},{&amp;quot;id&amp;quot;:44,&amp;quot;name&amp;quot;:&amp;quot;Apostelgeschichte&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Apg&amp;quot;,&amp;quot;chapterCount&amp;quot;:28,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:44,&amp;quot;slug&amp;quot;:&amp;quot;Apostelgeschichte&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:45,&amp;quot;name&amp;quot;:&amp;quot;R\u00f6mer&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;R\u00f6m&amp;quot;,&amp;quot;chapterCount&amp;quot;:16,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:45,&amp;quot;slug&amp;quot;:&amp;quot;R\u00f6mer&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:46,&amp;quot;name&amp;quot;:&amp;quot;1. Korinther&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Kor&amp;quot;,&amp;quot;chapterCount&amp;quot;:16,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:46,&amp;quot;slug&amp;quot;:&amp;quot;1.Korinther&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:47,&amp;quot;name&amp;quot;:&amp;quot;2. Korinther&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Kor&amp;quot;,&amp;quot;chapterCount&amp;quot;:13,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:47,&amp;quot;slug&amp;quot;:&amp;quot;2.Korinther&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:48,&amp;quot;name&amp;quot;:&amp;quot;Galater&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Gal&amp;quot;,&amp;quot;chapterCount&amp;quot;:6,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:48,&amp;quot;slug&amp;quot;:&amp;quot;Galater&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:49,&amp;quot;name&amp;quot;:&amp;quot;Epheser&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Eph&amp;quot;,&amp;quot;chapterCount&amp;quot;:6,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:49,&amp;quot;slug&amp;quot;:&amp;quot;Epheser&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:50,&amp;quot;name&amp;quot;:&amp;quot;Philipper&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Phil&amp;quot;,&amp;quot;chapterCount&amp;quot;:4,&amp;quot;chapters&amp;quot;:[1,2,3,4],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:50,&amp;quot;slug&amp;quot;:&amp;quot;Philipper&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:51,&amp;quot;name&amp;quot;:&amp;quot;Kolosser&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Kol&amp;quot;,&amp;quot;chapterCount&amp;quot;:4,&amp;quot;chapters&amp;quot;:[1,2,3,4],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:51,&amp;quot;slug&amp;quot;:&amp;quot;Kolosser&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:52,&amp;quot;name&amp;quot;:&amp;quot;1. Thessalonicher&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Thes&amp;quot;,&amp;quot;chapterCount&amp;quot;:5,&amp;quot;chapters&amp;quot;:[1,2,3,4,5],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:52,&amp;quot;slug&amp;quot;:&amp;quot;1.Thessalonicher&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:53,&amp;quot;name&amp;quot;:&amp;quot;2. Thessalonicher&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Thes&amp;quot;,&amp;quot;chapterCount&amp;quot;:3,&amp;quot;chapters&amp;quot;:[1,2,3],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:53,&amp;quot;slug&amp;quot;:&amp;quot;2.Thessalonicher&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:54,&amp;quot;name&amp;quot;:&amp;quot;1. Timotheus&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Tim&amp;quot;,&amp;quot;chapterCount&amp;quot;:6,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:54,&amp;quot;slug&amp;quot;:&amp;quot;1.Timotheus&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:55,&amp;quot;name&amp;quot;:&amp;quot;2. Timotheus&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Tim&amp;quot;,&amp;quot;chapterCount&amp;quot;:4,&amp;quot;chapters&amp;quot;:[1,2,3,4],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:55,&amp;quot;slug&amp;quot;:&amp;quot;2.Timotheus&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:56,&amp;quot;name&amp;quot;:&amp;quot;Titus&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Tit&amp;quot;,&amp;quot;chapterCount&amp;quot;:3,&amp;quot;chapters&amp;quot;:[1,2,3],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:56,&amp;quot;slug&amp;quot;:&amp;quot;Titus&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:57,&amp;quot;name&amp;quot;:&amp;quot;Philemon&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Phim&amp;quot;,&amp;quot;chapterCount&amp;quot;:1,&amp;quot;chapters&amp;quot;:[1],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:57,&amp;quot;slug&amp;quot;:&amp;quot;Philemon&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:58,&amp;quot;name&amp;quot;:&amp;quot;Hebr\u00e4er&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Heb&amp;quot;,&amp;quot;chapterCount&amp;quot;:13,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:58,&amp;quot;slug&amp;quot;:&amp;quot;Hebr\u00e4er&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:59,&amp;quot;name&amp;quot;:&amp;quot;Jakobus&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Jak&amp;quot;,&amp;quot;chapterCount&amp;quot;:5,&amp;quot;chapters&amp;quot;:[1,2,3,4,5],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:59,&amp;quot;slug&amp;quot;:&amp;quot;Jakobus&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:60,&amp;quot;name&amp;quot;:&amp;quot;1. Petrus&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Pet&amp;quot;,&amp;quot;chapterCount&amp;quot;:5,&amp;quot;chapters&amp;quot;:[1,2,3,4,5],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:60,&amp;quot;slug&amp;quot;:&amp;quot;1.Petrus&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:61,&amp;quot;name&amp;quot;:&amp;quot;2. Petrus&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Pet&amp;quot;,&amp;quot;chapterCount&amp;quot;:3,&amp;quot;chapters&amp;quot;:[1,2,3],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:61,&amp;quot;slug&amp;quot;:&amp;quot;2.Petrus&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:62,&amp;quot;name&amp;quot;:&amp;quot;1. Johannes&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Joh&amp;quot;,&amp;quot;chapterCount&amp;quot;:5,&amp;quot;chapters&amp;quot;:[1,2,3,4,5],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:62,&amp;quot;slug&amp;quot;:&amp;quot;1.Johannes&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:63,&amp;quot;name&amp;quot;:&amp;quot;2. Johannes&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Joh&amp;quot;,&amp;quot;chapterCount&amp;quot;:1,&amp;quot;chapters&amp;quot;:[1],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:63,&amp;quot;slug&amp;quot;:&amp;quot;2.Johannes&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:64,&amp;quot;name&amp;quot;:&amp;quot;3. Johannes&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;3Joh&amp;quot;,&amp;quot;chapterCount&amp;quot;:1,&amp;quot;chapters&amp;quot;:[1],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:64,&amp;quot;slug&amp;quot;:&amp;quot;3.Johannes&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:65,&amp;quot;name&amp;quot;:&amp;quot;Judas&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Jud&amp;quot;,&amp;quot;chapterCount&amp;quot;:1,&amp;quot;chapters&amp;quot;:[1],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:65,&amp;quot;slug&amp;quot;:&amp;quot;Judas&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:66,&amp;quot;name&amp;quot;:&amp;quot;Offenbarung&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Offb&amp;quot;,&amp;quot;chapterCount&amp;quot;:22,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;apocalypse&amp;quot;,&amp;quot;order&amp;quot;:66,&amp;quot;slug&amp;quot;:&amp;quot;Offenbarung&amp;quot;,&amp;quot;hasContent&amp;quot;:false}]}],&amp;quot;availableBooks&amp;quot;:[{&amp;quot;slug&amp;quot;:&amp;quot;Markus&amp;quot;,&amp;quot;order&amp;quot;:41},{&amp;quot;slug&amp;quot;:&amp;quot;Lukas&amp;quot;,&amp;quot;order&amp;quot;:42},{&amp;quot;slug&amp;quot;:&amp;quot;Johannes&amp;quot;,&amp;quot;order&amp;quot;:43}]},&amp;quot;env&amp;quot;:&amp;quot;local&amp;quot;,&amp;quot;auth&amp;quot;:{&amp;quot;user&amp;quot;:null},&amp;quot;requestedPath&amp;quot;:&amp;quot;api\/chapters\/fetch&amp;quot;},&amp;quot;url&amp;quot;:&amp;quot;\/api\/chapters\/fetch&amp;quot;,&amp;quot;version&amp;quot;:&amp;quot;18765f3fa436d07c5ef1cbbbc3fa3b37&amp;quot;,&amp;quot;clearHistory&amp;quot;:false,&amp;quot;encryptHistory&amp;quot;:false}&quot;&gt;&lt;/div&gt;&lt;/body&gt;

&lt;/html&gt;
</code>
 </pre>
    </span>
<span id="execution-results-GETapi-chapters-fetch" hidden>
    <blockquote>Received response<span
                id="execution-response-status-GETapi-chapters-fetch"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-GETapi-chapters-fetch"
      data-empty-response-text="<Empty response>" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-GETapi-chapters-fetch" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-GETapi-chapters-fetch">

Tip: Check that you&#039;re properly connected to the network.
If you&#039;re a maintainer of ths API, verify that your API is running and you&#039;ve enabled CORS.
You can check the Dev Tools console for debugging information.</code></pre>
</span>
<form id="form-GETapi-chapters-fetch" data-method="GET"
      data-path="api/chapters/fetch"
      data-authed="0"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('GETapi-chapters-fetch', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
                    <button type="button"
                    style="background-color: #8fbcd4; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-tryout-GETapi-chapters-fetch"
                    onclick="tryItOut('GETapi-chapters-fetch');">Try it out ⚡
            </button>
            <button type="button"
                    style="background-color: #c97a7e; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-canceltryout-GETapi-chapters-fetch"
                    onclick="cancelTryOut('GETapi-chapters-fetch');" hidden>Cancel 🛑
            </button>&nbsp;&nbsp;
            <button type="submit"
                    style="background-color: #6ac174; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-executetryout-GETapi-chapters-fetch"
                    data-initial-text="Send Request 💥"
                    data-loading-text="⏱ Sending..."
                    hidden>Send Request 💥
            </button>
            </h3>
            <p>
            <small class="badge badge-green">GET</small>
            <b><code>api/chapters/fetch</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Content-Type</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Content-Type"                data-endpoint="GETapi-chapters-fetch"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Accept</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Accept"                data-endpoint="GETapi-chapters-fetch"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <h4 class="fancy-heading-panel"><b>Body Parameters</b></h4>
        <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>bookId</code></b>&nbsp;&nbsp;
<small>integer</small>&nbsp;
 &nbsp;
                <input type="number" style="display: none"
               step="any"               name="bookId"                data-endpoint="GETapi-chapters-fetch"
               value="16"
               data-component="body">
    <br>
<p>The <code>order</code> of an existing record in the books table. Example: <code>16</code></p>
        </div>
                <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>chapter</code></b>&nbsp;&nbsp;
<small>integer</small>&nbsp;
 &nbsp;
                <input type="number" style="display: none"
               step="any"               name="chapter"                data-endpoint="GETapi-chapters-fetch"
               value="22"
               data-component="body">
    <br>
<p>validation.min. Example: <code>22</code></p>
        </div>
                <div style=" padding-left: 28px;  clear: unset;">
            <b style="line-height: 2;"><code>direction</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="direction"                data-endpoint="GETapi-chapters-fetch"
               value="previous"
               data-component="body">
    <br>
<p>Example: <code>previous</code></p>
Must be one of:
<ul style="list-style-type: square;"><li><code>next</code></li> <li><code>previous</code></li></ul>
        </div>
        </form>

                    <h2 id="endpoints-GETapi-books">GET api/books</h2>

<p>
</p>



<span id="example-requests-GETapi-books">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request GET \
    --get "http://esra-bibel.local/api/books" \
    --header "Content-Type: application/json" \
    --header "Accept: application/json"</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "http://esra-bibel.local/api/books"
);

const headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "GET",
    headers,
}).then(response =&gt; response.json());</code></pre></div>

</span>

<span id="example-responses-GETapi-books">
            <blockquote>
            <p>Example response (404):</p>
        </blockquote>
                <details class="annotation">
            <summary style="cursor: pointer;">
                <small onclick="textContent = parentElement.parentElement.open ? 'Show headers' : 'Hide headers'">Show headers</small>
            </summary>
            <pre><code class="language-http">cache-control: no-cache, private
content-type: application/json
vary: X-Inertia
access-control-allow-origin: *
set-cookie: XSRF-TOKEN=eyJpdiI6IlV3T2NGaGQ1cXVlcldDazRpWUg5Q1E9PSIsInZhbHVlIjoiWGptODdqRU84RFUwcnRBYlpGV2hBWlJ2VFhoVXYxWHBITlZDRUdCWS9GWDQyVThIT2Z3N1F0NlNDZnhKSDdzRVdOUE43WlA0SUJRcC95QlNiamRtOEpDZEhOZktCYUhlRXJkdnAyWVdFbEtaTmt0ZXNJdnJhWFV3S0w4UnQzTnYiLCJtYWMiOiI0MzBhZjkwZDBlZGUzY2FmYjU1OWZjZGUzMmYzNWI5NWQxMzYzNTlmOTRiZDQ5OWVhOWU2ZTFhNTBiNGM0YTEyIiwidGFnIjoiIn0%3D; expires=Wed, 09 Apr 2025 13:17:49 GMT; Max-Age=7200; path=/; samesite=lax; esrabibel_session=eyJpdiI6Ik9nOGQ2Tlp4SytjM0szMDdoVklsUHc9PSIsInZhbHVlIjoiYWtDSUdWRHRhVEhhb0lsQnNNRnpUWFJQUTJkNi9DVUFLZHhvRHJ5TjRWVVlkRFhNQzEyZlZRQm1kV3lsS1F1NG9KOXVoeFRTbko5SnRScXVGd1MyQ0g4SCtBMzFrY3JtWUN0SmZYTm0zY3dxSE9XcTdrMkNQZGZDZm5HaEJ4WUYiLCJtYWMiOiJjYjUxZjBjZjBhMzJjZTlkNDAzMDU0MmZhZDVhNjM0Mjc4MDRiZTJjYzRhN2VmODExNWZkMmY0ZmJmNGQ5YjAxIiwidGFnIjoiIn0%3D; expires=Wed, 09 Apr 2025 13:17:49 GMT; Max-Age=7200; path=/; httponly; samesite=lax
 </code></pre></details>         <pre>

<code class="language-json" style="max-height: 300px;">&lt;!DOCTYPE html&gt;
&lt;html lang=&quot;de&quot;&gt;

&lt;head&gt;
    &lt;meta charset=&quot;utf-8&quot;&gt;
    &lt;meta name=&quot;viewport&quot; content=&quot;width=device-width, initial-scale=1&quot;&gt;

    &lt;title inertia&gt;&lt;/title&gt;

    &lt;!-- Fonts --&gt;
    &lt;!-- Preconnect to font domains --&gt;
    &lt;link rel=&quot;preconnect&quot; href=&quot;https://fonts.bunny.net&quot; crossorigin&gt;
    &lt;link rel=&quot;preconnect&quot; href=&quot;https://use.typekit.net&quot; as=&quot;font&quot; type=&quot;font/woff2&quot; crossorigin&gt;

    &lt;!-- Preload critical fonts --&gt;
    &lt;link rel=&quot;preload&quot; href=&quot;/fonts/ThanatosText-Book.woff2&quot; as=&quot;font&quot; type=&quot;font/woff2&quot; crossorigin&gt;

    &lt;!-- Load fonts --&gt;
    &lt;link href=&quot;https://fonts.bunny.net/css?family=figtree:400,500,600&amp;display=swap&quot; rel=&quot;stylesheet&quot; /&gt;
    &lt;link rel=&quot;stylesheet&quot; href=&quot;https://use.typekit.net/kzb8yhl.css&quot; media=&quot;print&quot; onload=&quot;this.media=&#039;all&#039;&quot;&gt;

    &lt;!-- Fallback for typekit fonts --&gt;
    &lt;noscript&gt;
        &lt;link rel=&quot;stylesheet&quot; href=&quot;https://use.typekit.net/kzb8yhl.css&quot;&gt;
    &lt;/noscript&gt;

    &lt;!-- Local font definition --&gt;
    &lt;style&gt;
        @font-face {
            font-family: &#039;ThanatosText&#039;;
            src: url(&#039;/fonts/ThanatosText-Book.woff2&#039;) format(&#039;woff2&#039;);
            font-weight: normal;
            font-style: normal;
            font-display: swap;
        }
    &lt;/style&gt;

    &lt;!-- Scripts --&gt;
    &lt;script type=&quot;text/javascript&quot;&gt;Object.assign(Ziggy.routes,{&quot;search.index&quot;:{&quot;uri&quot;:&quot;search&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;search.query&quot;:{&quot;uri&quot;:&quot;search\/{query}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;wheres&quot;:{&quot;query&quot;:&quot;[^\/]+&quot;},&quot;parameters&quot;:[&quot;query&quot;]},&quot;search.paged&quot;:{&quot;uri&quot;:&quot;search\/{query}\/{page?}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;wheres&quot;:{&quot;query&quot;:&quot;[^\/]+&quot;,&quot;page&quot;:&quot;[0-9]+&quot;},&quot;parameters&quot;:[&quot;query&quot;,&quot;page&quot;]},&quot;search.settings&quot;:{&quot;uri&quot;:&quot;search\/settings&quot;,&quot;methods&quot;:[&quot;POST&quot;]},&quot;dashboard&quot;:{&quot;uri&quot;:&quot;dashboard&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;bible.import&quot;:{&quot;uri&quot;:&quot;import-bible&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;bible.import.store&quot;:{&quot;uri&quot;:&quot;import-bible&quot;,&quot;methods&quot;:[&quot;POST&quot;]},&quot;file.upload&quot;:{&quot;uri&quot;:&quot;api\/upload&quot;,&quot;methods&quot;:[&quot;POST&quot;]},&quot;profile.edit&quot;:{&quot;uri&quot;:&quot;profile&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;profile.update&quot;:{&quot;uri&quot;:&quot;profile&quot;,&quot;methods&quot;:[&quot;PATCH&quot;]},&quot;profile.destroy&quot;:{&quot;uri&quot;:&quot;profile&quot;,&quot;methods&quot;:[&quot;DELETE&quot;]},&quot;books.show&quot;:{&quot;uri&quot;:&quot;{reference}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;wheres&quot;:{&quot;reference&quot;:&quot;[\\w\\d\\.,\\-\\+]+&quot;},&quot;parameters&quot;:[&quot;reference&quot;]},&quot;register&quot;:{&quot;uri&quot;:&quot;register&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;login&quot;:{&quot;uri&quot;:&quot;login&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;password.request&quot;:{&quot;uri&quot;:&quot;forgot-password&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;password.email&quot;:{&quot;uri&quot;:&quot;forgot-password&quot;,&quot;methods&quot;:[&quot;POST&quot;]},&quot;password.reset&quot;:{&quot;uri&quot;:&quot;reset-password\/{token}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;parameters&quot;:[&quot;token&quot;]},&quot;password.store&quot;:{&quot;uri&quot;:&quot;reset-password&quot;,&quot;methods&quot;:[&quot;POST&quot;]},&quot;verification.notice&quot;:{&quot;uri&quot;:&quot;verify-email&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;verification.verify&quot;:{&quot;uri&quot;:&quot;verify-email\/{id}\/{hash}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;parameters&quot;:[&quot;id&quot;,&quot;hash&quot;]},&quot;verification.send&quot;:{&quot;uri&quot;:&quot;email\/verification-notification&quot;,&quot;methods&quot;:[&quot;POST&quot;]},&quot;password.confirm&quot;:{&quot;uri&quot;:&quot;confirm-password&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;password.update&quot;:{&quot;uri&quot;:&quot;password&quot;,&quot;methods&quot;:[&quot;PUT&quot;]},&quot;logout&quot;:{&quot;uri&quot;:&quot;logout&quot;,&quot;methods&quot;:[&quot;POST&quot;]},&quot;api.chapters.fetch&quot;:{&quot;uri&quot;:&quot;api\/chapters\/fetch&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;api.books&quot;:{&quot;uri&quot;:&quot;api\/books&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;api.books.content-status&quot;:{&quot;uri&quot;:&quot;api\/books\/content-status&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;api.books.show&quot;:{&quot;uri&quot;:&quot;api\/books\/{slug}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;parameters&quot;:[&quot;slug&quot;]},&quot;api.chapters.adjacent&quot;:{&quot;uri&quot;:&quot;api\/chapters\/{reference}\/adjacent&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;wheres&quot;:{&quot;reference&quot;:&quot;[\\w\\d,\\-]+&quot;},&quot;parameters&quot;:[&quot;reference&quot;]},&quot;api.search.books&quot;:{&quot;uri&quot;:&quot;api\/search\/books&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;api.search&quot;:{&quot;uri&quot;:&quot;api\/search&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;api.bible.text&quot;:{&quot;uri&quot;:&quot;api\/bible\/{reference}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;wheres&quot;:{&quot;reference&quot;:&quot;[\\w\\d,\\-]+&quot;},&quot;parameters&quot;:[&quot;reference&quot;]},&quot;storage.local&quot;:{&quot;uri&quot;:&quot;storage\/{path}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;wheres&quot;:{&quot;path&quot;:&quot;.*&quot;},&quot;parameters&quot;:[&quot;path&quot;]}});&lt;/script&gt;    &lt;script type=&quot;module&quot; src=&quot;http://[::1]:5173/@vite/client&quot;&gt;&lt;/script&gt;&lt;script type=&quot;module&quot; src=&quot;http://[::1]:5173/resources/js/app.ts&quot;&gt;&lt;/script&gt;&lt;script type=&quot;module&quot; src=&quot;http://[::1]:5173/resources/js/Pages/NotFound.vue&quot;&gt;&lt;/script&gt;    &lt;/head&gt;

&lt;body class=&quot;font-sans antialiased&quot;&gt;
    &lt;div id=&quot;app&quot; data-page=&quot;{&amp;quot;component&amp;quot;:&amp;quot;NotFound&amp;quot;,&amp;quot;props&amp;quot;:{&amp;quot;errors&amp;quot;:{},&amp;quot;books&amp;quot;:{&amp;quot;sections&amp;quot;:[{&amp;quot;name&amp;quot;:&amp;quot;Altes Testament&amp;quot;,&amp;quot;books&amp;quot;:[{&amp;quot;id&amp;quot;:1,&amp;quot;name&amp;quot;:&amp;quot;1. Mose&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Mo&amp;quot;,&amp;quot;chapterCount&amp;quot;:50,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;law&amp;quot;,&amp;quot;order&amp;quot;:1,&amp;quot;slug&amp;quot;:&amp;quot;1.Mose&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:2,&amp;quot;name&amp;quot;:&amp;quot;2. Mose&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Mo&amp;quot;,&amp;quot;chapterCount&amp;quot;:40,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;law&amp;quot;,&amp;quot;order&amp;quot;:2,&amp;quot;slug&amp;quot;:&amp;quot;2.Mose&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:3,&amp;quot;name&amp;quot;:&amp;quot;3. Mose&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;3Mo&amp;quot;,&amp;quot;chapterCount&amp;quot;:27,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;law&amp;quot;,&amp;quot;order&amp;quot;:3,&amp;quot;slug&amp;quot;:&amp;quot;3.Mose&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:4,&amp;quot;name&amp;quot;:&amp;quot;4. Mose&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;4Mo&amp;quot;,&amp;quot;chapterCount&amp;quot;:36,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;law&amp;quot;,&amp;quot;order&amp;quot;:4,&amp;quot;slug&amp;quot;:&amp;quot;4.Mose&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:5,&amp;quot;name&amp;quot;:&amp;quot;5. Mose&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;5Mo&amp;quot;,&amp;quot;chapterCount&amp;quot;:34,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;law&amp;quot;,&amp;quot;order&amp;quot;:5,&amp;quot;slug&amp;quot;:&amp;quot;5.Mose&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:6,&amp;quot;name&amp;quot;:&amp;quot;Josua&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Jos&amp;quot;,&amp;quot;chapterCount&amp;quot;:24,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:6,&amp;quot;slug&amp;quot;:&amp;quot;Josua&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:7,&amp;quot;name&amp;quot;:&amp;quot;Richter&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Ri&amp;quot;,&amp;quot;chapterCount&amp;quot;:21,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:7,&amp;quot;slug&amp;quot;:&amp;quot;Richter&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:8,&amp;quot;name&amp;quot;:&amp;quot;Ruth&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Rt&amp;quot;,&amp;quot;chapterCount&amp;quot;:4,&amp;quot;chapters&amp;quot;:[1,2,3,4],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:8,&amp;quot;slug&amp;quot;:&amp;quot;Ruth&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:9,&amp;quot;name&amp;quot;:&amp;quot;1. Samuel&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Sam&amp;quot;,&amp;quot;chapterCount&amp;quot;:31,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:9,&amp;quot;slug&amp;quot;:&amp;quot;1.Samuel&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:10,&amp;quot;name&amp;quot;:&amp;quot;2. Samuel&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Sam&amp;quot;,&amp;quot;chapterCount&amp;quot;:24,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:10,&amp;quot;slug&amp;quot;:&amp;quot;2.Samuel&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:11,&amp;quot;name&amp;quot;:&amp;quot;1. K\u00f6nige&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1K\u00f6n&amp;quot;,&amp;quot;chapterCount&amp;quot;:22,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:11,&amp;quot;slug&amp;quot;:&amp;quot;1.K\u00f6nige&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:12,&amp;quot;name&amp;quot;:&amp;quot;2. K\u00f6nige&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2K\u00f6n&amp;quot;,&amp;quot;chapterCount&amp;quot;:25,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:12,&amp;quot;slug&amp;quot;:&amp;quot;2.K\u00f6nige&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:13,&amp;quot;name&amp;quot;:&amp;quot;1. Chronik&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Chr&amp;quot;,&amp;quot;chapterCount&amp;quot;:29,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:13,&amp;quot;slug&amp;quot;:&amp;quot;1.Chronik&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:14,&amp;quot;name&amp;quot;:&amp;quot;2. Chronik&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Chr&amp;quot;,&amp;quot;chapterCount&amp;quot;:36,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:14,&amp;quot;slug&amp;quot;:&amp;quot;2.Chronik&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:15,&amp;quot;name&amp;quot;:&amp;quot;Esra&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Esra&amp;quot;,&amp;quot;chapterCount&amp;quot;:10,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:15,&amp;quot;slug&amp;quot;:&amp;quot;Esra&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:16,&amp;quot;name&amp;quot;:&amp;quot;Nehemia&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Neh&amp;quot;,&amp;quot;chapterCount&amp;quot;:13,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:16,&amp;quot;slug&amp;quot;:&amp;quot;Nehemia&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:17,&amp;quot;name&amp;quot;:&amp;quot;Esther&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Est&amp;quot;,&amp;quot;chapterCount&amp;quot;:10,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:17,&amp;quot;slug&amp;quot;:&amp;quot;Esther&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:18,&amp;quot;name&amp;quot;:&amp;quot;Hiob&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Hi&amp;quot;,&amp;quot;chapterCount&amp;quot;:42,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;wisdom&amp;quot;,&amp;quot;order&amp;quot;:18,&amp;quot;slug&amp;quot;:&amp;quot;Hiob&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:19,&amp;quot;name&amp;quot;:&amp;quot;Psalmen&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Ps&amp;quot;,&amp;quot;chapterCount&amp;quot;:150,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;wisdom&amp;quot;,&amp;quot;order&amp;quot;:19,&amp;quot;slug&amp;quot;:&amp;quot;Psalmen&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:20,&amp;quot;name&amp;quot;:&amp;quot;Spr\u00fcche&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Spr&amp;quot;,&amp;quot;chapterCount&amp;quot;:31,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;wisdom&amp;quot;,&amp;quot;order&amp;quot;:20,&amp;quot;slug&amp;quot;:&amp;quot;Spr\u00fcche&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:21,&amp;quot;name&amp;quot;:&amp;quot;Prediger&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Pred&amp;quot;,&amp;quot;chapterCount&amp;quot;:12,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;wisdom&amp;quot;,&amp;quot;order&amp;quot;:21,&amp;quot;slug&amp;quot;:&amp;quot;Prediger&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:22,&amp;quot;name&amp;quot;:&amp;quot;Hohelied&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Hl&amp;quot;,&amp;quot;chapterCount&amp;quot;:8,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;wisdom&amp;quot;,&amp;quot;order&amp;quot;:22,&amp;quot;slug&amp;quot;:&amp;quot;Hohelied&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:23,&amp;quot;name&amp;quot;:&amp;quot;Jesaja&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Jes&amp;quot;,&amp;quot;chapterCount&amp;quot;:66,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:23,&amp;quot;slug&amp;quot;:&amp;quot;Jesaja&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:24,&amp;quot;name&amp;quot;:&amp;quot;Jeremia&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Jer&amp;quot;,&amp;quot;chapterCount&amp;quot;:52,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:24,&amp;quot;slug&amp;quot;:&amp;quot;Jeremia&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:25,&amp;quot;name&amp;quot;:&amp;quot;Klagelieder&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Kla&amp;quot;,&amp;quot;chapterCount&amp;quot;:5,&amp;quot;chapters&amp;quot;:[1,2,3,4,5],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;wisdom&amp;quot;,&amp;quot;order&amp;quot;:25,&amp;quot;slug&amp;quot;:&amp;quot;Klagelieder&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:26,&amp;quot;name&amp;quot;:&amp;quot;Hesekiel&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Hes&amp;quot;,&amp;quot;chapterCount&amp;quot;:48,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:26,&amp;quot;slug&amp;quot;:&amp;quot;Hesekiel&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:27,&amp;quot;name&amp;quot;:&amp;quot;Daniel&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Dan&amp;quot;,&amp;quot;chapterCount&amp;quot;:12,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:27,&amp;quot;slug&amp;quot;:&amp;quot;Daniel&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:28,&amp;quot;name&amp;quot;:&amp;quot;Hosea&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Hos&amp;quot;,&amp;quot;chapterCount&amp;quot;:14,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:28,&amp;quot;slug&amp;quot;:&amp;quot;Hosea&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:29,&amp;quot;name&amp;quot;:&amp;quot;Joel&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Joel&amp;quot;,&amp;quot;chapterCount&amp;quot;:3,&amp;quot;chapters&amp;quot;:[1,2,3],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:29,&amp;quot;slug&amp;quot;:&amp;quot;Joel&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:30,&amp;quot;name&amp;quot;:&amp;quot;Amos&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Am&amp;quot;,&amp;quot;chapterCount&amp;quot;:9,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:30,&amp;quot;slug&amp;quot;:&amp;quot;Amos&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:31,&amp;quot;name&amp;quot;:&amp;quot;Obadja&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Ob&amp;quot;,&amp;quot;chapterCount&amp;quot;:1,&amp;quot;chapters&amp;quot;:[1],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:31,&amp;quot;slug&amp;quot;:&amp;quot;Obadja&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:32,&amp;quot;name&amp;quot;:&amp;quot;Jona&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Jon&amp;quot;,&amp;quot;chapterCount&amp;quot;:4,&amp;quot;chapters&amp;quot;:[1,2,3,4],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:32,&amp;quot;slug&amp;quot;:&amp;quot;Jona&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:33,&amp;quot;name&amp;quot;:&amp;quot;Micha&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Mi&amp;quot;,&amp;quot;chapterCount&amp;quot;:7,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:33,&amp;quot;slug&amp;quot;:&amp;quot;Micha&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:34,&amp;quot;name&amp;quot;:&amp;quot;Nahum&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Nah&amp;quot;,&amp;quot;chapterCount&amp;quot;:3,&amp;quot;chapters&amp;quot;:[1,2,3],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:34,&amp;quot;slug&amp;quot;:&amp;quot;Nahum&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:35,&amp;quot;name&amp;quot;:&amp;quot;Habakuk&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Hab&amp;quot;,&amp;quot;chapterCount&amp;quot;:3,&amp;quot;chapters&amp;quot;:[1,2,3],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:35,&amp;quot;slug&amp;quot;:&amp;quot;Habakuk&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:36,&amp;quot;name&amp;quot;:&amp;quot;Zephanja&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Zeph&amp;quot;,&amp;quot;chapterCount&amp;quot;:3,&amp;quot;chapters&amp;quot;:[1,2,3],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:36,&amp;quot;slug&amp;quot;:&amp;quot;Zephanja&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:37,&amp;quot;name&amp;quot;:&amp;quot;Haggai&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Hag&amp;quot;,&amp;quot;chapterCount&amp;quot;:2,&amp;quot;chapters&amp;quot;:[1,2],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:37,&amp;quot;slug&amp;quot;:&amp;quot;Haggai&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:38,&amp;quot;name&amp;quot;:&amp;quot;Sacharja&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Sach&amp;quot;,&amp;quot;chapterCount&amp;quot;:14,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:38,&amp;quot;slug&amp;quot;:&amp;quot;Sacharja&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:39,&amp;quot;name&amp;quot;:&amp;quot;Maleachi&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Mal&amp;quot;,&amp;quot;chapterCount&amp;quot;:4,&amp;quot;chapters&amp;quot;:[1,2,3,4],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:39,&amp;quot;slug&amp;quot;:&amp;quot;Maleachi&amp;quot;,&amp;quot;hasContent&amp;quot;:false}]},{&amp;quot;name&amp;quot;:&amp;quot;Neues Testament&amp;quot;,&amp;quot;books&amp;quot;:[{&amp;quot;id&amp;quot;:40,&amp;quot;name&amp;quot;:&amp;quot;Matth\u00e4us&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Mt&amp;quot;,&amp;quot;chapterCount&amp;quot;:28,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;gospel&amp;quot;,&amp;quot;order&amp;quot;:40,&amp;quot;slug&amp;quot;:&amp;quot;Matth\u00e4us&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:41,&amp;quot;name&amp;quot;:&amp;quot;Markus&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Mk&amp;quot;,&amp;quot;chapterCount&amp;quot;:16,&amp;quot;chapters&amp;quot;:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;gospel&amp;quot;,&amp;quot;order&amp;quot;:41,&amp;quot;slug&amp;quot;:&amp;quot;Markus&amp;quot;,&amp;quot;hasContent&amp;quot;:true},{&amp;quot;id&amp;quot;:42,&amp;quot;name&amp;quot;:&amp;quot;Lukas&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Lk&amp;quot;,&amp;quot;chapterCount&amp;quot;:24,&amp;quot;chapters&amp;quot;:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;gospel&amp;quot;,&amp;quot;order&amp;quot;:42,&amp;quot;slug&amp;quot;:&amp;quot;Lukas&amp;quot;,&amp;quot;hasContent&amp;quot;:true},{&amp;quot;id&amp;quot;:43,&amp;quot;name&amp;quot;:&amp;quot;Die Heilsbotschaft nach Johannes&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Joh&amp;quot;,&amp;quot;chapterCount&amp;quot;:21,&amp;quot;chapters&amp;quot;:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;gospel&amp;quot;,&amp;quot;order&amp;quot;:43,&amp;quot;slug&amp;quot;:&amp;quot;Johannes&amp;quot;,&amp;quot;hasContent&amp;quot;:true},{&amp;quot;id&amp;quot;:44,&amp;quot;name&amp;quot;:&amp;quot;Apostelgeschichte&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Apg&amp;quot;,&amp;quot;chapterCount&amp;quot;:28,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:44,&amp;quot;slug&amp;quot;:&amp;quot;Apostelgeschichte&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:45,&amp;quot;name&amp;quot;:&amp;quot;R\u00f6mer&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;R\u00f6m&amp;quot;,&amp;quot;chapterCount&amp;quot;:16,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:45,&amp;quot;slug&amp;quot;:&amp;quot;R\u00f6mer&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:46,&amp;quot;name&amp;quot;:&amp;quot;1. Korinther&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Kor&amp;quot;,&amp;quot;chapterCount&amp;quot;:16,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:46,&amp;quot;slug&amp;quot;:&amp;quot;1.Korinther&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:47,&amp;quot;name&amp;quot;:&amp;quot;2. Korinther&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Kor&amp;quot;,&amp;quot;chapterCount&amp;quot;:13,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:47,&amp;quot;slug&amp;quot;:&amp;quot;2.Korinther&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:48,&amp;quot;name&amp;quot;:&amp;quot;Galater&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Gal&amp;quot;,&amp;quot;chapterCount&amp;quot;:6,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:48,&amp;quot;slug&amp;quot;:&amp;quot;Galater&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:49,&amp;quot;name&amp;quot;:&amp;quot;Epheser&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Eph&amp;quot;,&amp;quot;chapterCount&amp;quot;:6,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:49,&amp;quot;slug&amp;quot;:&amp;quot;Epheser&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:50,&amp;quot;name&amp;quot;:&amp;quot;Philipper&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Phil&amp;quot;,&amp;quot;chapterCount&amp;quot;:4,&amp;quot;chapters&amp;quot;:[1,2,3,4],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:50,&amp;quot;slug&amp;quot;:&amp;quot;Philipper&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:51,&amp;quot;name&amp;quot;:&amp;quot;Kolosser&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Kol&amp;quot;,&amp;quot;chapterCount&amp;quot;:4,&amp;quot;chapters&amp;quot;:[1,2,3,4],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:51,&amp;quot;slug&amp;quot;:&amp;quot;Kolosser&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:52,&amp;quot;name&amp;quot;:&amp;quot;1. Thessalonicher&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Thes&amp;quot;,&amp;quot;chapterCount&amp;quot;:5,&amp;quot;chapters&amp;quot;:[1,2,3,4,5],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:52,&amp;quot;slug&amp;quot;:&amp;quot;1.Thessalonicher&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:53,&amp;quot;name&amp;quot;:&amp;quot;2. Thessalonicher&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Thes&amp;quot;,&amp;quot;chapterCount&amp;quot;:3,&amp;quot;chapters&amp;quot;:[1,2,3],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:53,&amp;quot;slug&amp;quot;:&amp;quot;2.Thessalonicher&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:54,&amp;quot;name&amp;quot;:&amp;quot;1. Timotheus&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Tim&amp;quot;,&amp;quot;chapterCount&amp;quot;:6,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:54,&amp;quot;slug&amp;quot;:&amp;quot;1.Timotheus&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:55,&amp;quot;name&amp;quot;:&amp;quot;2. Timotheus&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Tim&amp;quot;,&amp;quot;chapterCount&amp;quot;:4,&amp;quot;chapters&amp;quot;:[1,2,3,4],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:55,&amp;quot;slug&amp;quot;:&amp;quot;2.Timotheus&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:56,&amp;quot;name&amp;quot;:&amp;quot;Titus&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Tit&amp;quot;,&amp;quot;chapterCount&amp;quot;:3,&amp;quot;chapters&amp;quot;:[1,2,3],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:56,&amp;quot;slug&amp;quot;:&amp;quot;Titus&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:57,&amp;quot;name&amp;quot;:&amp;quot;Philemon&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Phim&amp;quot;,&amp;quot;chapterCount&amp;quot;:1,&amp;quot;chapters&amp;quot;:[1],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:57,&amp;quot;slug&amp;quot;:&amp;quot;Philemon&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:58,&amp;quot;name&amp;quot;:&amp;quot;Hebr\u00e4er&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Heb&amp;quot;,&amp;quot;chapterCount&amp;quot;:13,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:58,&amp;quot;slug&amp;quot;:&amp;quot;Hebr\u00e4er&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:59,&amp;quot;name&amp;quot;:&amp;quot;Jakobus&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Jak&amp;quot;,&amp;quot;chapterCount&amp;quot;:5,&amp;quot;chapters&amp;quot;:[1,2,3,4,5],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:59,&amp;quot;slug&amp;quot;:&amp;quot;Jakobus&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:60,&amp;quot;name&amp;quot;:&amp;quot;1. Petrus&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Pet&amp;quot;,&amp;quot;chapterCount&amp;quot;:5,&amp;quot;chapters&amp;quot;:[1,2,3,4,5],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:60,&amp;quot;slug&amp;quot;:&amp;quot;1.Petrus&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:61,&amp;quot;name&amp;quot;:&amp;quot;2. Petrus&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Pet&amp;quot;,&amp;quot;chapterCount&amp;quot;:3,&amp;quot;chapters&amp;quot;:[1,2,3],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:61,&amp;quot;slug&amp;quot;:&amp;quot;2.Petrus&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:62,&amp;quot;name&amp;quot;:&amp;quot;1. Johannes&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Joh&amp;quot;,&amp;quot;chapterCount&amp;quot;:5,&amp;quot;chapters&amp;quot;:[1,2,3,4,5],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:62,&amp;quot;slug&amp;quot;:&amp;quot;1.Johannes&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:63,&amp;quot;name&amp;quot;:&amp;quot;2. Johannes&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Joh&amp;quot;,&amp;quot;chapterCount&amp;quot;:1,&amp;quot;chapters&amp;quot;:[1],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:63,&amp;quot;slug&amp;quot;:&amp;quot;2.Johannes&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:64,&amp;quot;name&amp;quot;:&amp;quot;3. Johannes&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;3Joh&amp;quot;,&amp;quot;chapterCount&amp;quot;:1,&amp;quot;chapters&amp;quot;:[1],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:64,&amp;quot;slug&amp;quot;:&amp;quot;3.Johannes&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:65,&amp;quot;name&amp;quot;:&amp;quot;Judas&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Jud&amp;quot;,&amp;quot;chapterCount&amp;quot;:1,&amp;quot;chapters&amp;quot;:[1],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:65,&amp;quot;slug&amp;quot;:&amp;quot;Judas&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:66,&amp;quot;name&amp;quot;:&amp;quot;Offenbarung&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Offb&amp;quot;,&amp;quot;chapterCount&amp;quot;:22,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;apocalypse&amp;quot;,&amp;quot;order&amp;quot;:66,&amp;quot;slug&amp;quot;:&amp;quot;Offenbarung&amp;quot;,&amp;quot;hasContent&amp;quot;:false}]}],&amp;quot;availableBooks&amp;quot;:[{&amp;quot;slug&amp;quot;:&amp;quot;Markus&amp;quot;,&amp;quot;order&amp;quot;:41},{&amp;quot;slug&amp;quot;:&amp;quot;Lukas&amp;quot;,&amp;quot;order&amp;quot;:42},{&amp;quot;slug&amp;quot;:&amp;quot;Johannes&amp;quot;,&amp;quot;order&amp;quot;:43}]},&amp;quot;env&amp;quot;:&amp;quot;local&amp;quot;,&amp;quot;auth&amp;quot;:{&amp;quot;user&amp;quot;:null},&amp;quot;requestedPath&amp;quot;:&amp;quot;api\/books&amp;quot;},&amp;quot;url&amp;quot;:&amp;quot;\/api\/books&amp;quot;,&amp;quot;version&amp;quot;:&amp;quot;18765f3fa436d07c5ef1cbbbc3fa3b37&amp;quot;,&amp;quot;clearHistory&amp;quot;:false,&amp;quot;encryptHistory&amp;quot;:false}&quot;&gt;&lt;/div&gt;&lt;/body&gt;

&lt;/html&gt;
</code>
 </pre>
    </span>
<span id="execution-results-GETapi-books" hidden>
    <blockquote>Received response<span
                id="execution-response-status-GETapi-books"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-GETapi-books"
      data-empty-response-text="<Empty response>" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-GETapi-books" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-GETapi-books">

Tip: Check that you&#039;re properly connected to the network.
If you&#039;re a maintainer of ths API, verify that your API is running and you&#039;ve enabled CORS.
You can check the Dev Tools console for debugging information.</code></pre>
</span>
<form id="form-GETapi-books" data-method="GET"
      data-path="api/books"
      data-authed="0"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('GETapi-books', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
                    <button type="button"
                    style="background-color: #8fbcd4; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-tryout-GETapi-books"
                    onclick="tryItOut('GETapi-books');">Try it out ⚡
            </button>
            <button type="button"
                    style="background-color: #c97a7e; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-canceltryout-GETapi-books"
                    onclick="cancelTryOut('GETapi-books');" hidden>Cancel 🛑
            </button>&nbsp;&nbsp;
            <button type="submit"
                    style="background-color: #6ac174; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-executetryout-GETapi-books"
                    data-initial-text="Send Request 💥"
                    data-loading-text="⏱ Sending..."
                    hidden>Send Request 💥
            </button>
            </h3>
            <p>
            <small class="badge badge-green">GET</small>
            <b><code>api/books</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Content-Type</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Content-Type"                data-endpoint="GETapi-books"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Accept</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Accept"                data-endpoint="GETapi-books"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                        </form>

                    <h2 id="endpoints-GETapi-books-content-status">Get content status for all books</h2>

<p>
</p>



<span id="example-requests-GETapi-books-content-status">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request GET \
    --get "http://esra-bibel.local/api/books/content-status" \
    --header "Content-Type: application/json" \
    --header "Accept: application/json"</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "http://esra-bibel.local/api/books/content-status"
);

const headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "GET",
    headers,
}).then(response =&gt; response.json());</code></pre></div>

</span>

<span id="example-responses-GETapi-books-content-status">
            <blockquote>
            <p>Example response (404):</p>
        </blockquote>
                <details class="annotation">
            <summary style="cursor: pointer;">
                <small onclick="textContent = parentElement.parentElement.open ? 'Show headers' : 'Hide headers'">Show headers</small>
            </summary>
            <pre><code class="language-http">cache-control: no-cache, private
content-type: application/json
vary: X-Inertia
access-control-allow-origin: *
set-cookie: XSRF-TOKEN=eyJpdiI6Ims5LzBSVkIvTWhZdzVXTEpHZXpIblE9PSIsInZhbHVlIjoiemZzZGdxZEY0OEVBdWhVWHlERFAwamF3OUl5UXZ4QkJ5K2JTa2FKVk5HVnJ5RTBxU0R6azcvcTlQeDVwRTk1WjlzNWFJOVE5NFBOVXV3Y2cwNzlXMHQ1K01nWVpQNnBOU3JKZEdIN3JuSG5OVUpGeDNuODJpb2VOOXNLeWpUV00iLCJtYWMiOiI1NzEwNzQ3ZDg4OGYxYTU0OTcwODM2NTdjNzQzN2NlOGVkNDRhZTNlOWNhOGRjMzQ0Njc3NjI5Mzk5OTFjYzQ1IiwidGFnIjoiIn0%3D; expires=Wed, 09 Apr 2025 13:17:49 GMT; Max-Age=7200; path=/; samesite=lax; esrabibel_session=eyJpdiI6InRHRURXbzc2NkJLRWNvTUhXZUxCS0E9PSIsInZhbHVlIjoiRGE4UmI2WDJXK1oxclV1NTI1OUMwYjY0UTI4THYzV0NxdXNYd1lzc0ZhVjdGYXRyVTJCSGduc25KdTgyaWd0cHZ5blJvZW9IVkNwcDlJSlNxaWFnMEFOZ2dwYVV4dGxTMW9CR2V4R0dVZXlSL3RoS040NnBCR3U5SVhvb21NUDMiLCJtYWMiOiIyYTc5YmJhNTUwMmUxNDUwNmVhZDAwN2QwNWRmOTA1OThhYWRlNjY3ZmUyMDIyZmY4YjQyNzNhODUzMGUyNjFiIiwidGFnIjoiIn0%3D; expires=Wed, 09 Apr 2025 13:17:49 GMT; Max-Age=7200; path=/; httponly; samesite=lax
 </code></pre></details>         <pre>

<code class="language-json" style="max-height: 300px;">&lt;!DOCTYPE html&gt;
&lt;html lang=&quot;de&quot;&gt;

&lt;head&gt;
    &lt;meta charset=&quot;utf-8&quot;&gt;
    &lt;meta name=&quot;viewport&quot; content=&quot;width=device-width, initial-scale=1&quot;&gt;

    &lt;title inertia&gt;&lt;/title&gt;

    &lt;!-- Fonts --&gt;
    &lt;!-- Preconnect to font domains --&gt;
    &lt;link rel=&quot;preconnect&quot; href=&quot;https://fonts.bunny.net&quot; crossorigin&gt;
    &lt;link rel=&quot;preconnect&quot; href=&quot;https://use.typekit.net&quot; as=&quot;font&quot; type=&quot;font/woff2&quot; crossorigin&gt;

    &lt;!-- Preload critical fonts --&gt;
    &lt;link rel=&quot;preload&quot; href=&quot;/fonts/ThanatosText-Book.woff2&quot; as=&quot;font&quot; type=&quot;font/woff2&quot; crossorigin&gt;

    &lt;!-- Load fonts --&gt;
    &lt;link href=&quot;https://fonts.bunny.net/css?family=figtree:400,500,600&amp;display=swap&quot; rel=&quot;stylesheet&quot; /&gt;
    &lt;link rel=&quot;stylesheet&quot; href=&quot;https://use.typekit.net/kzb8yhl.css&quot; media=&quot;print&quot; onload=&quot;this.media=&#039;all&#039;&quot;&gt;

    &lt;!-- Fallback for typekit fonts --&gt;
    &lt;noscript&gt;
        &lt;link rel=&quot;stylesheet&quot; href=&quot;https://use.typekit.net/kzb8yhl.css&quot;&gt;
    &lt;/noscript&gt;

    &lt;!-- Local font definition --&gt;
    &lt;style&gt;
        @font-face {
            font-family: &#039;ThanatosText&#039;;
            src: url(&#039;/fonts/ThanatosText-Book.woff2&#039;) format(&#039;woff2&#039;);
            font-weight: normal;
            font-style: normal;
            font-display: swap;
        }
    &lt;/style&gt;

    &lt;!-- Scripts --&gt;
    &lt;script type=&quot;text/javascript&quot;&gt;Object.assign(Ziggy.routes,{&quot;search.index&quot;:{&quot;uri&quot;:&quot;search&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;search.query&quot;:{&quot;uri&quot;:&quot;search\/{query}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;wheres&quot;:{&quot;query&quot;:&quot;[^\/]+&quot;},&quot;parameters&quot;:[&quot;query&quot;]},&quot;search.paged&quot;:{&quot;uri&quot;:&quot;search\/{query}\/{page?}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;wheres&quot;:{&quot;query&quot;:&quot;[^\/]+&quot;,&quot;page&quot;:&quot;[0-9]+&quot;},&quot;parameters&quot;:[&quot;query&quot;,&quot;page&quot;]},&quot;search.settings&quot;:{&quot;uri&quot;:&quot;search\/settings&quot;,&quot;methods&quot;:[&quot;POST&quot;]},&quot;dashboard&quot;:{&quot;uri&quot;:&quot;dashboard&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;bible.import&quot;:{&quot;uri&quot;:&quot;import-bible&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;bible.import.store&quot;:{&quot;uri&quot;:&quot;import-bible&quot;,&quot;methods&quot;:[&quot;POST&quot;]},&quot;file.upload&quot;:{&quot;uri&quot;:&quot;api\/upload&quot;,&quot;methods&quot;:[&quot;POST&quot;]},&quot;profile.edit&quot;:{&quot;uri&quot;:&quot;profile&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;profile.update&quot;:{&quot;uri&quot;:&quot;profile&quot;,&quot;methods&quot;:[&quot;PATCH&quot;]},&quot;profile.destroy&quot;:{&quot;uri&quot;:&quot;profile&quot;,&quot;methods&quot;:[&quot;DELETE&quot;]},&quot;books.show&quot;:{&quot;uri&quot;:&quot;{reference}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;wheres&quot;:{&quot;reference&quot;:&quot;[\\w\\d\\.,\\-\\+]+&quot;},&quot;parameters&quot;:[&quot;reference&quot;]},&quot;register&quot;:{&quot;uri&quot;:&quot;register&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;login&quot;:{&quot;uri&quot;:&quot;login&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;password.request&quot;:{&quot;uri&quot;:&quot;forgot-password&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;password.email&quot;:{&quot;uri&quot;:&quot;forgot-password&quot;,&quot;methods&quot;:[&quot;POST&quot;]},&quot;password.reset&quot;:{&quot;uri&quot;:&quot;reset-password\/{token}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;parameters&quot;:[&quot;token&quot;]},&quot;password.store&quot;:{&quot;uri&quot;:&quot;reset-password&quot;,&quot;methods&quot;:[&quot;POST&quot;]},&quot;verification.notice&quot;:{&quot;uri&quot;:&quot;verify-email&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;verification.verify&quot;:{&quot;uri&quot;:&quot;verify-email\/{id}\/{hash}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;parameters&quot;:[&quot;id&quot;,&quot;hash&quot;]},&quot;verification.send&quot;:{&quot;uri&quot;:&quot;email\/verification-notification&quot;,&quot;methods&quot;:[&quot;POST&quot;]},&quot;password.confirm&quot;:{&quot;uri&quot;:&quot;confirm-password&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;password.update&quot;:{&quot;uri&quot;:&quot;password&quot;,&quot;methods&quot;:[&quot;PUT&quot;]},&quot;logout&quot;:{&quot;uri&quot;:&quot;logout&quot;,&quot;methods&quot;:[&quot;POST&quot;]},&quot;api.chapters.fetch&quot;:{&quot;uri&quot;:&quot;api\/chapters\/fetch&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;api.books&quot;:{&quot;uri&quot;:&quot;api\/books&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;api.books.content-status&quot;:{&quot;uri&quot;:&quot;api\/books\/content-status&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;api.books.show&quot;:{&quot;uri&quot;:&quot;api\/books\/{slug}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;parameters&quot;:[&quot;slug&quot;]},&quot;api.chapters.adjacent&quot;:{&quot;uri&quot;:&quot;api\/chapters\/{reference}\/adjacent&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;wheres&quot;:{&quot;reference&quot;:&quot;[\\w\\d,\\-]+&quot;},&quot;parameters&quot;:[&quot;reference&quot;]},&quot;api.search.books&quot;:{&quot;uri&quot;:&quot;api\/search\/books&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;api.search&quot;:{&quot;uri&quot;:&quot;api\/search&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;api.bible.text&quot;:{&quot;uri&quot;:&quot;api\/bible\/{reference}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;wheres&quot;:{&quot;reference&quot;:&quot;[\\w\\d,\\-]+&quot;},&quot;parameters&quot;:[&quot;reference&quot;]},&quot;storage.local&quot;:{&quot;uri&quot;:&quot;storage\/{path}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;wheres&quot;:{&quot;path&quot;:&quot;.*&quot;},&quot;parameters&quot;:[&quot;path&quot;]}});&lt;/script&gt;    &lt;script type=&quot;module&quot; src=&quot;http://[::1]:5173/@vite/client&quot;&gt;&lt;/script&gt;&lt;script type=&quot;module&quot; src=&quot;http://[::1]:5173/resources/js/app.ts&quot;&gt;&lt;/script&gt;&lt;script type=&quot;module&quot; src=&quot;http://[::1]:5173/resources/js/Pages/NotFound.vue&quot;&gt;&lt;/script&gt;    &lt;/head&gt;

&lt;body class=&quot;font-sans antialiased&quot;&gt;
    &lt;div id=&quot;app&quot; data-page=&quot;{&amp;quot;component&amp;quot;:&amp;quot;NotFound&amp;quot;,&amp;quot;props&amp;quot;:{&amp;quot;errors&amp;quot;:{},&amp;quot;books&amp;quot;:{&amp;quot;sections&amp;quot;:[{&amp;quot;name&amp;quot;:&amp;quot;Altes Testament&amp;quot;,&amp;quot;books&amp;quot;:[{&amp;quot;id&amp;quot;:1,&amp;quot;name&amp;quot;:&amp;quot;1. Mose&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Mo&amp;quot;,&amp;quot;chapterCount&amp;quot;:50,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;law&amp;quot;,&amp;quot;order&amp;quot;:1,&amp;quot;slug&amp;quot;:&amp;quot;1.Mose&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:2,&amp;quot;name&amp;quot;:&amp;quot;2. Mose&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Mo&amp;quot;,&amp;quot;chapterCount&amp;quot;:40,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;law&amp;quot;,&amp;quot;order&amp;quot;:2,&amp;quot;slug&amp;quot;:&amp;quot;2.Mose&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:3,&amp;quot;name&amp;quot;:&amp;quot;3. Mose&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;3Mo&amp;quot;,&amp;quot;chapterCount&amp;quot;:27,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;law&amp;quot;,&amp;quot;order&amp;quot;:3,&amp;quot;slug&amp;quot;:&amp;quot;3.Mose&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:4,&amp;quot;name&amp;quot;:&amp;quot;4. Mose&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;4Mo&amp;quot;,&amp;quot;chapterCount&amp;quot;:36,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;law&amp;quot;,&amp;quot;order&amp;quot;:4,&amp;quot;slug&amp;quot;:&amp;quot;4.Mose&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:5,&amp;quot;name&amp;quot;:&amp;quot;5. Mose&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;5Mo&amp;quot;,&amp;quot;chapterCount&amp;quot;:34,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;law&amp;quot;,&amp;quot;order&amp;quot;:5,&amp;quot;slug&amp;quot;:&amp;quot;5.Mose&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:6,&amp;quot;name&amp;quot;:&amp;quot;Josua&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Jos&amp;quot;,&amp;quot;chapterCount&amp;quot;:24,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:6,&amp;quot;slug&amp;quot;:&amp;quot;Josua&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:7,&amp;quot;name&amp;quot;:&amp;quot;Richter&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Ri&amp;quot;,&amp;quot;chapterCount&amp;quot;:21,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:7,&amp;quot;slug&amp;quot;:&amp;quot;Richter&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:8,&amp;quot;name&amp;quot;:&amp;quot;Ruth&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Rt&amp;quot;,&amp;quot;chapterCount&amp;quot;:4,&amp;quot;chapters&amp;quot;:[1,2,3,4],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:8,&amp;quot;slug&amp;quot;:&amp;quot;Ruth&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:9,&amp;quot;name&amp;quot;:&amp;quot;1. Samuel&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Sam&amp;quot;,&amp;quot;chapterCount&amp;quot;:31,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:9,&amp;quot;slug&amp;quot;:&amp;quot;1.Samuel&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:10,&amp;quot;name&amp;quot;:&amp;quot;2. Samuel&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Sam&amp;quot;,&amp;quot;chapterCount&amp;quot;:24,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:10,&amp;quot;slug&amp;quot;:&amp;quot;2.Samuel&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:11,&amp;quot;name&amp;quot;:&amp;quot;1. K\u00f6nige&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1K\u00f6n&amp;quot;,&amp;quot;chapterCount&amp;quot;:22,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:11,&amp;quot;slug&amp;quot;:&amp;quot;1.K\u00f6nige&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:12,&amp;quot;name&amp;quot;:&amp;quot;2. K\u00f6nige&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2K\u00f6n&amp;quot;,&amp;quot;chapterCount&amp;quot;:25,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:12,&amp;quot;slug&amp;quot;:&amp;quot;2.K\u00f6nige&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:13,&amp;quot;name&amp;quot;:&amp;quot;1. Chronik&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Chr&amp;quot;,&amp;quot;chapterCount&amp;quot;:29,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:13,&amp;quot;slug&amp;quot;:&amp;quot;1.Chronik&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:14,&amp;quot;name&amp;quot;:&amp;quot;2. Chronik&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Chr&amp;quot;,&amp;quot;chapterCount&amp;quot;:36,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:14,&amp;quot;slug&amp;quot;:&amp;quot;2.Chronik&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:15,&amp;quot;name&amp;quot;:&amp;quot;Esra&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Esra&amp;quot;,&amp;quot;chapterCount&amp;quot;:10,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:15,&amp;quot;slug&amp;quot;:&amp;quot;Esra&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:16,&amp;quot;name&amp;quot;:&amp;quot;Nehemia&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Neh&amp;quot;,&amp;quot;chapterCount&amp;quot;:13,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:16,&amp;quot;slug&amp;quot;:&amp;quot;Nehemia&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:17,&amp;quot;name&amp;quot;:&amp;quot;Esther&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Est&amp;quot;,&amp;quot;chapterCount&amp;quot;:10,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:17,&amp;quot;slug&amp;quot;:&amp;quot;Esther&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:18,&amp;quot;name&amp;quot;:&amp;quot;Hiob&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Hi&amp;quot;,&amp;quot;chapterCount&amp;quot;:42,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;wisdom&amp;quot;,&amp;quot;order&amp;quot;:18,&amp;quot;slug&amp;quot;:&amp;quot;Hiob&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:19,&amp;quot;name&amp;quot;:&amp;quot;Psalmen&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Ps&amp;quot;,&amp;quot;chapterCount&amp;quot;:150,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;wisdom&amp;quot;,&amp;quot;order&amp;quot;:19,&amp;quot;slug&amp;quot;:&amp;quot;Psalmen&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:20,&amp;quot;name&amp;quot;:&amp;quot;Spr\u00fcche&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Spr&amp;quot;,&amp;quot;chapterCount&amp;quot;:31,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;wisdom&amp;quot;,&amp;quot;order&amp;quot;:20,&amp;quot;slug&amp;quot;:&amp;quot;Spr\u00fcche&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:21,&amp;quot;name&amp;quot;:&amp;quot;Prediger&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Pred&amp;quot;,&amp;quot;chapterCount&amp;quot;:12,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;wisdom&amp;quot;,&amp;quot;order&amp;quot;:21,&amp;quot;slug&amp;quot;:&amp;quot;Prediger&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:22,&amp;quot;name&amp;quot;:&amp;quot;Hohelied&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Hl&amp;quot;,&amp;quot;chapterCount&amp;quot;:8,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;wisdom&amp;quot;,&amp;quot;order&amp;quot;:22,&amp;quot;slug&amp;quot;:&amp;quot;Hohelied&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:23,&amp;quot;name&amp;quot;:&amp;quot;Jesaja&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Jes&amp;quot;,&amp;quot;chapterCount&amp;quot;:66,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:23,&amp;quot;slug&amp;quot;:&amp;quot;Jesaja&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:24,&amp;quot;name&amp;quot;:&amp;quot;Jeremia&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Jer&amp;quot;,&amp;quot;chapterCount&amp;quot;:52,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:24,&amp;quot;slug&amp;quot;:&amp;quot;Jeremia&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:25,&amp;quot;name&amp;quot;:&amp;quot;Klagelieder&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Kla&amp;quot;,&amp;quot;chapterCount&amp;quot;:5,&amp;quot;chapters&amp;quot;:[1,2,3,4,5],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;wisdom&amp;quot;,&amp;quot;order&amp;quot;:25,&amp;quot;slug&amp;quot;:&amp;quot;Klagelieder&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:26,&amp;quot;name&amp;quot;:&amp;quot;Hesekiel&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Hes&amp;quot;,&amp;quot;chapterCount&amp;quot;:48,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:26,&amp;quot;slug&amp;quot;:&amp;quot;Hesekiel&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:27,&amp;quot;name&amp;quot;:&amp;quot;Daniel&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Dan&amp;quot;,&amp;quot;chapterCount&amp;quot;:12,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:27,&amp;quot;slug&amp;quot;:&amp;quot;Daniel&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:28,&amp;quot;name&amp;quot;:&amp;quot;Hosea&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Hos&amp;quot;,&amp;quot;chapterCount&amp;quot;:14,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:28,&amp;quot;slug&amp;quot;:&amp;quot;Hosea&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:29,&amp;quot;name&amp;quot;:&amp;quot;Joel&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Joel&amp;quot;,&amp;quot;chapterCount&amp;quot;:3,&amp;quot;chapters&amp;quot;:[1,2,3],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:29,&amp;quot;slug&amp;quot;:&amp;quot;Joel&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:30,&amp;quot;name&amp;quot;:&amp;quot;Amos&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Am&amp;quot;,&amp;quot;chapterCount&amp;quot;:9,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:30,&amp;quot;slug&amp;quot;:&amp;quot;Amos&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:31,&amp;quot;name&amp;quot;:&amp;quot;Obadja&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Ob&amp;quot;,&amp;quot;chapterCount&amp;quot;:1,&amp;quot;chapters&amp;quot;:[1],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:31,&amp;quot;slug&amp;quot;:&amp;quot;Obadja&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:32,&amp;quot;name&amp;quot;:&amp;quot;Jona&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Jon&amp;quot;,&amp;quot;chapterCount&amp;quot;:4,&amp;quot;chapters&amp;quot;:[1,2,3,4],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:32,&amp;quot;slug&amp;quot;:&amp;quot;Jona&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:33,&amp;quot;name&amp;quot;:&amp;quot;Micha&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Mi&amp;quot;,&amp;quot;chapterCount&amp;quot;:7,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:33,&amp;quot;slug&amp;quot;:&amp;quot;Micha&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:34,&amp;quot;name&amp;quot;:&amp;quot;Nahum&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Nah&amp;quot;,&amp;quot;chapterCount&amp;quot;:3,&amp;quot;chapters&amp;quot;:[1,2,3],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:34,&amp;quot;slug&amp;quot;:&amp;quot;Nahum&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:35,&amp;quot;name&amp;quot;:&amp;quot;Habakuk&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Hab&amp;quot;,&amp;quot;chapterCount&amp;quot;:3,&amp;quot;chapters&amp;quot;:[1,2,3],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:35,&amp;quot;slug&amp;quot;:&amp;quot;Habakuk&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:36,&amp;quot;name&amp;quot;:&amp;quot;Zephanja&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Zeph&amp;quot;,&amp;quot;chapterCount&amp;quot;:3,&amp;quot;chapters&amp;quot;:[1,2,3],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:36,&amp;quot;slug&amp;quot;:&amp;quot;Zephanja&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:37,&amp;quot;name&amp;quot;:&amp;quot;Haggai&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Hag&amp;quot;,&amp;quot;chapterCount&amp;quot;:2,&amp;quot;chapters&amp;quot;:[1,2],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:37,&amp;quot;slug&amp;quot;:&amp;quot;Haggai&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:38,&amp;quot;name&amp;quot;:&amp;quot;Sacharja&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Sach&amp;quot;,&amp;quot;chapterCount&amp;quot;:14,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:38,&amp;quot;slug&amp;quot;:&amp;quot;Sacharja&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:39,&amp;quot;name&amp;quot;:&amp;quot;Maleachi&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Mal&amp;quot;,&amp;quot;chapterCount&amp;quot;:4,&amp;quot;chapters&amp;quot;:[1,2,3,4],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:39,&amp;quot;slug&amp;quot;:&amp;quot;Maleachi&amp;quot;,&amp;quot;hasContent&amp;quot;:false}]},{&amp;quot;name&amp;quot;:&amp;quot;Neues Testament&amp;quot;,&amp;quot;books&amp;quot;:[{&amp;quot;id&amp;quot;:40,&amp;quot;name&amp;quot;:&amp;quot;Matth\u00e4us&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Mt&amp;quot;,&amp;quot;chapterCount&amp;quot;:28,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;gospel&amp;quot;,&amp;quot;order&amp;quot;:40,&amp;quot;slug&amp;quot;:&amp;quot;Matth\u00e4us&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:41,&amp;quot;name&amp;quot;:&amp;quot;Markus&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Mk&amp;quot;,&amp;quot;chapterCount&amp;quot;:16,&amp;quot;chapters&amp;quot;:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;gospel&amp;quot;,&amp;quot;order&amp;quot;:41,&amp;quot;slug&amp;quot;:&amp;quot;Markus&amp;quot;,&amp;quot;hasContent&amp;quot;:true},{&amp;quot;id&amp;quot;:42,&amp;quot;name&amp;quot;:&amp;quot;Lukas&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Lk&amp;quot;,&amp;quot;chapterCount&amp;quot;:24,&amp;quot;chapters&amp;quot;:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;gospel&amp;quot;,&amp;quot;order&amp;quot;:42,&amp;quot;slug&amp;quot;:&amp;quot;Lukas&amp;quot;,&amp;quot;hasContent&amp;quot;:true},{&amp;quot;id&amp;quot;:43,&amp;quot;name&amp;quot;:&amp;quot;Die Heilsbotschaft nach Johannes&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Joh&amp;quot;,&amp;quot;chapterCount&amp;quot;:21,&amp;quot;chapters&amp;quot;:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;gospel&amp;quot;,&amp;quot;order&amp;quot;:43,&amp;quot;slug&amp;quot;:&amp;quot;Johannes&amp;quot;,&amp;quot;hasContent&amp;quot;:true},{&amp;quot;id&amp;quot;:44,&amp;quot;name&amp;quot;:&amp;quot;Apostelgeschichte&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Apg&amp;quot;,&amp;quot;chapterCount&amp;quot;:28,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:44,&amp;quot;slug&amp;quot;:&amp;quot;Apostelgeschichte&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:45,&amp;quot;name&amp;quot;:&amp;quot;R\u00f6mer&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;R\u00f6m&amp;quot;,&amp;quot;chapterCount&amp;quot;:16,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:45,&amp;quot;slug&amp;quot;:&amp;quot;R\u00f6mer&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:46,&amp;quot;name&amp;quot;:&amp;quot;1. Korinther&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Kor&amp;quot;,&amp;quot;chapterCount&amp;quot;:16,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:46,&amp;quot;slug&amp;quot;:&amp;quot;1.Korinther&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:47,&amp;quot;name&amp;quot;:&amp;quot;2. Korinther&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Kor&amp;quot;,&amp;quot;chapterCount&amp;quot;:13,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:47,&amp;quot;slug&amp;quot;:&amp;quot;2.Korinther&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:48,&amp;quot;name&amp;quot;:&amp;quot;Galater&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Gal&amp;quot;,&amp;quot;chapterCount&amp;quot;:6,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:48,&amp;quot;slug&amp;quot;:&amp;quot;Galater&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:49,&amp;quot;name&amp;quot;:&amp;quot;Epheser&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Eph&amp;quot;,&amp;quot;chapterCount&amp;quot;:6,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:49,&amp;quot;slug&amp;quot;:&amp;quot;Epheser&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:50,&amp;quot;name&amp;quot;:&amp;quot;Philipper&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Phil&amp;quot;,&amp;quot;chapterCount&amp;quot;:4,&amp;quot;chapters&amp;quot;:[1,2,3,4],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:50,&amp;quot;slug&amp;quot;:&amp;quot;Philipper&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:51,&amp;quot;name&amp;quot;:&amp;quot;Kolosser&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Kol&amp;quot;,&amp;quot;chapterCount&amp;quot;:4,&amp;quot;chapters&amp;quot;:[1,2,3,4],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:51,&amp;quot;slug&amp;quot;:&amp;quot;Kolosser&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:52,&amp;quot;name&amp;quot;:&amp;quot;1. Thessalonicher&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Thes&amp;quot;,&amp;quot;chapterCount&amp;quot;:5,&amp;quot;chapters&amp;quot;:[1,2,3,4,5],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:52,&amp;quot;slug&amp;quot;:&amp;quot;1.Thessalonicher&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:53,&amp;quot;name&amp;quot;:&amp;quot;2. Thessalonicher&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Thes&amp;quot;,&amp;quot;chapterCount&amp;quot;:3,&amp;quot;chapters&amp;quot;:[1,2,3],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:53,&amp;quot;slug&amp;quot;:&amp;quot;2.Thessalonicher&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:54,&amp;quot;name&amp;quot;:&amp;quot;1. Timotheus&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Tim&amp;quot;,&amp;quot;chapterCount&amp;quot;:6,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:54,&amp;quot;slug&amp;quot;:&amp;quot;1.Timotheus&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:55,&amp;quot;name&amp;quot;:&amp;quot;2. Timotheus&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Tim&amp;quot;,&amp;quot;chapterCount&amp;quot;:4,&amp;quot;chapters&amp;quot;:[1,2,3,4],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:55,&amp;quot;slug&amp;quot;:&amp;quot;2.Timotheus&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:56,&amp;quot;name&amp;quot;:&amp;quot;Titus&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Tit&amp;quot;,&amp;quot;chapterCount&amp;quot;:3,&amp;quot;chapters&amp;quot;:[1,2,3],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:56,&amp;quot;slug&amp;quot;:&amp;quot;Titus&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:57,&amp;quot;name&amp;quot;:&amp;quot;Philemon&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Phim&amp;quot;,&amp;quot;chapterCount&amp;quot;:1,&amp;quot;chapters&amp;quot;:[1],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:57,&amp;quot;slug&amp;quot;:&amp;quot;Philemon&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:58,&amp;quot;name&amp;quot;:&amp;quot;Hebr\u00e4er&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Heb&amp;quot;,&amp;quot;chapterCount&amp;quot;:13,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:58,&amp;quot;slug&amp;quot;:&amp;quot;Hebr\u00e4er&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:59,&amp;quot;name&amp;quot;:&amp;quot;Jakobus&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Jak&amp;quot;,&amp;quot;chapterCount&amp;quot;:5,&amp;quot;chapters&amp;quot;:[1,2,3,4,5],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:59,&amp;quot;slug&amp;quot;:&amp;quot;Jakobus&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:60,&amp;quot;name&amp;quot;:&amp;quot;1. Petrus&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Pet&amp;quot;,&amp;quot;chapterCount&amp;quot;:5,&amp;quot;chapters&amp;quot;:[1,2,3,4,5],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:60,&amp;quot;slug&amp;quot;:&amp;quot;1.Petrus&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:61,&amp;quot;name&amp;quot;:&amp;quot;2. Petrus&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Pet&amp;quot;,&amp;quot;chapterCount&amp;quot;:3,&amp;quot;chapters&amp;quot;:[1,2,3],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:61,&amp;quot;slug&amp;quot;:&amp;quot;2.Petrus&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:62,&amp;quot;name&amp;quot;:&amp;quot;1. Johannes&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Joh&amp;quot;,&amp;quot;chapterCount&amp;quot;:5,&amp;quot;chapters&amp;quot;:[1,2,3,4,5],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:62,&amp;quot;slug&amp;quot;:&amp;quot;1.Johannes&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:63,&amp;quot;name&amp;quot;:&amp;quot;2. Johannes&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Joh&amp;quot;,&amp;quot;chapterCount&amp;quot;:1,&amp;quot;chapters&amp;quot;:[1],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:63,&amp;quot;slug&amp;quot;:&amp;quot;2.Johannes&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:64,&amp;quot;name&amp;quot;:&amp;quot;3. Johannes&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;3Joh&amp;quot;,&amp;quot;chapterCount&amp;quot;:1,&amp;quot;chapters&amp;quot;:[1],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:64,&amp;quot;slug&amp;quot;:&amp;quot;3.Johannes&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:65,&amp;quot;name&amp;quot;:&amp;quot;Judas&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Jud&amp;quot;,&amp;quot;chapterCount&amp;quot;:1,&amp;quot;chapters&amp;quot;:[1],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:65,&amp;quot;slug&amp;quot;:&amp;quot;Judas&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:66,&amp;quot;name&amp;quot;:&amp;quot;Offenbarung&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Offb&amp;quot;,&amp;quot;chapterCount&amp;quot;:22,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;apocalypse&amp;quot;,&amp;quot;order&amp;quot;:66,&amp;quot;slug&amp;quot;:&amp;quot;Offenbarung&amp;quot;,&amp;quot;hasContent&amp;quot;:false}]}],&amp;quot;availableBooks&amp;quot;:[{&amp;quot;slug&amp;quot;:&amp;quot;Markus&amp;quot;,&amp;quot;order&amp;quot;:41},{&amp;quot;slug&amp;quot;:&amp;quot;Lukas&amp;quot;,&amp;quot;order&amp;quot;:42},{&amp;quot;slug&amp;quot;:&amp;quot;Johannes&amp;quot;,&amp;quot;order&amp;quot;:43}]},&amp;quot;env&amp;quot;:&amp;quot;local&amp;quot;,&amp;quot;auth&amp;quot;:{&amp;quot;user&amp;quot;:null},&amp;quot;requestedPath&amp;quot;:&amp;quot;api\/books\/content-status&amp;quot;},&amp;quot;url&amp;quot;:&amp;quot;\/api\/books\/content-status&amp;quot;,&amp;quot;version&amp;quot;:&amp;quot;18765f3fa436d07c5ef1cbbbc3fa3b37&amp;quot;,&amp;quot;clearHistory&amp;quot;:false,&amp;quot;encryptHistory&amp;quot;:false}&quot;&gt;&lt;/div&gt;&lt;/body&gt;

&lt;/html&gt;
</code>
 </pre>
    </span>
<span id="execution-results-GETapi-books-content-status" hidden>
    <blockquote>Received response<span
                id="execution-response-status-GETapi-books-content-status"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-GETapi-books-content-status"
      data-empty-response-text="<Empty response>" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-GETapi-books-content-status" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-GETapi-books-content-status">

Tip: Check that you&#039;re properly connected to the network.
If you&#039;re a maintainer of ths API, verify that your API is running and you&#039;ve enabled CORS.
You can check the Dev Tools console for debugging information.</code></pre>
</span>
<form id="form-GETapi-books-content-status" data-method="GET"
      data-path="api/books/content-status"
      data-authed="0"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('GETapi-books-content-status', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
                    <button type="button"
                    style="background-color: #8fbcd4; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-tryout-GETapi-books-content-status"
                    onclick="tryItOut('GETapi-books-content-status');">Try it out ⚡
            </button>
            <button type="button"
                    style="background-color: #c97a7e; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-canceltryout-GETapi-books-content-status"
                    onclick="cancelTryOut('GETapi-books-content-status');" hidden>Cancel 🛑
            </button>&nbsp;&nbsp;
            <button type="submit"
                    style="background-color: #6ac174; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-executetryout-GETapi-books-content-status"
                    data-initial-text="Send Request 💥"
                    data-loading-text="⏱ Sending..."
                    hidden>Send Request 💥
            </button>
            </h3>
            <p>
            <small class="badge badge-green">GET</small>
            <b><code>api/books/content-status</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Content-Type</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Content-Type"                data-endpoint="GETapi-books-content-status"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Accept</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Accept"                data-endpoint="GETapi-books-content-status"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                        </form>

                    <h2 id="endpoints-GETapi-books--slug-">Get a single book by slug with all its metadata</h2>

<p>
</p>



<span id="example-requests-GETapi-books--slug-">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request GET \
    --get "http://esra-bibel.local/api/books/architecto" \
    --header "Content-Type: application/json" \
    --header "Accept: application/json"</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "http://esra-bibel.local/api/books/architecto"
);

const headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "GET",
    headers,
}).then(response =&gt; response.json());</code></pre></div>

</span>

<span id="example-responses-GETapi-books--slug-">
            <blockquote>
            <p>Example response (404):</p>
        </blockquote>
                <details class="annotation">
            <summary style="cursor: pointer;">
                <small onclick="textContent = parentElement.parentElement.open ? 'Show headers' : 'Hide headers'">Show headers</small>
            </summary>
            <pre><code class="language-http">cache-control: no-cache, private
content-type: application/json
vary: X-Inertia
access-control-allow-origin: *
set-cookie: XSRF-TOKEN=eyJpdiI6InFtazZVSU4zclFGUEplRXJXT0xtRlE9PSIsInZhbHVlIjoiUzdnbWpTdkoveE0yTWZwRkh4a1BLcFI4aXh1VGhCRmswNTFqSHhrdWtnV3IrbDdDMDZxTVZGRUxuU25TcU94TEx2Y1ZsbEt0RjhGTHR3Rm1wWEdqMnlCdnV1NmgrWm5uRWlDcU13eC9pTFJEemlvS1lBSUpKOWNFcTFXZjI1d20iLCJtYWMiOiIzODYzMmIwZjlmMDkyNzMxNDc0ZjlmZTg3Yjc2NDFmMTE4MzQyNmViMmQ5ZTQwNDkyMTc4MzFmNTAyNmM5MmVmIiwidGFnIjoiIn0%3D; expires=Wed, 09 Apr 2025 13:17:49 GMT; Max-Age=7200; path=/; samesite=lax; esrabibel_session=eyJpdiI6InNrazBpcStuWW5RT2kvZzM5ekFMZFE9PSIsInZhbHVlIjoiWXFQdDBha3hiQTRVSGNaNXpzVDRHaHdSUURlbXVtaEZPbnVrd1NHSTIvR2R3ek5EZm5GMmV3RWdGdkloTFpBS1VOZUJDQ2FtdUk1emsvZk16RWZURlRya1M1enB5cEtLTVVvdGkxQ1RBbTFIQitjSUc0dTVra2NjVkJ5SHVqR28iLCJtYWMiOiIyM2E3MzQ0ZDJmZTdkYzUwM2NjZDY4MjgxZjI4OWJjOWVlMGUwOGU4MmRlZjk2MTgzYmRiMTAwZDQ4OWI5YWFjIiwidGFnIjoiIn0%3D; expires=Wed, 09 Apr 2025 13:17:49 GMT; Max-Age=7200; path=/; httponly; samesite=lax
 </code></pre></details>         <pre>

<code class="language-json" style="max-height: 300px;">&lt;!DOCTYPE html&gt;
&lt;html lang=&quot;de&quot;&gt;

&lt;head&gt;
    &lt;meta charset=&quot;utf-8&quot;&gt;
    &lt;meta name=&quot;viewport&quot; content=&quot;width=device-width, initial-scale=1&quot;&gt;

    &lt;title inertia&gt;&lt;/title&gt;

    &lt;!-- Fonts --&gt;
    &lt;!-- Preconnect to font domains --&gt;
    &lt;link rel=&quot;preconnect&quot; href=&quot;https://fonts.bunny.net&quot; crossorigin&gt;
    &lt;link rel=&quot;preconnect&quot; href=&quot;https://use.typekit.net&quot; as=&quot;font&quot; type=&quot;font/woff2&quot; crossorigin&gt;

    &lt;!-- Preload critical fonts --&gt;
    &lt;link rel=&quot;preload&quot; href=&quot;/fonts/ThanatosText-Book.woff2&quot; as=&quot;font&quot; type=&quot;font/woff2&quot; crossorigin&gt;

    &lt;!-- Load fonts --&gt;
    &lt;link href=&quot;https://fonts.bunny.net/css?family=figtree:400,500,600&amp;display=swap&quot; rel=&quot;stylesheet&quot; /&gt;
    &lt;link rel=&quot;stylesheet&quot; href=&quot;https://use.typekit.net/kzb8yhl.css&quot; media=&quot;print&quot; onload=&quot;this.media=&#039;all&#039;&quot;&gt;

    &lt;!-- Fallback for typekit fonts --&gt;
    &lt;noscript&gt;
        &lt;link rel=&quot;stylesheet&quot; href=&quot;https://use.typekit.net/kzb8yhl.css&quot;&gt;
    &lt;/noscript&gt;

    &lt;!-- Local font definition --&gt;
    &lt;style&gt;
        @font-face {
            font-family: &#039;ThanatosText&#039;;
            src: url(&#039;/fonts/ThanatosText-Book.woff2&#039;) format(&#039;woff2&#039;);
            font-weight: normal;
            font-style: normal;
            font-display: swap;
        }
    &lt;/style&gt;

    &lt;!-- Scripts --&gt;
    &lt;script type=&quot;text/javascript&quot;&gt;Object.assign(Ziggy.routes,{&quot;search.index&quot;:{&quot;uri&quot;:&quot;search&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;search.query&quot;:{&quot;uri&quot;:&quot;search\/{query}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;wheres&quot;:{&quot;query&quot;:&quot;[^\/]+&quot;},&quot;parameters&quot;:[&quot;query&quot;]},&quot;search.paged&quot;:{&quot;uri&quot;:&quot;search\/{query}\/{page?}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;wheres&quot;:{&quot;query&quot;:&quot;[^\/]+&quot;,&quot;page&quot;:&quot;[0-9]+&quot;},&quot;parameters&quot;:[&quot;query&quot;,&quot;page&quot;]},&quot;search.settings&quot;:{&quot;uri&quot;:&quot;search\/settings&quot;,&quot;methods&quot;:[&quot;POST&quot;]},&quot;dashboard&quot;:{&quot;uri&quot;:&quot;dashboard&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;bible.import&quot;:{&quot;uri&quot;:&quot;import-bible&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;bible.import.store&quot;:{&quot;uri&quot;:&quot;import-bible&quot;,&quot;methods&quot;:[&quot;POST&quot;]},&quot;file.upload&quot;:{&quot;uri&quot;:&quot;api\/upload&quot;,&quot;methods&quot;:[&quot;POST&quot;]},&quot;profile.edit&quot;:{&quot;uri&quot;:&quot;profile&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;profile.update&quot;:{&quot;uri&quot;:&quot;profile&quot;,&quot;methods&quot;:[&quot;PATCH&quot;]},&quot;profile.destroy&quot;:{&quot;uri&quot;:&quot;profile&quot;,&quot;methods&quot;:[&quot;DELETE&quot;]},&quot;books.show&quot;:{&quot;uri&quot;:&quot;{reference}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;wheres&quot;:{&quot;reference&quot;:&quot;[\\w\\d\\.,\\-\\+]+&quot;},&quot;parameters&quot;:[&quot;reference&quot;]},&quot;register&quot;:{&quot;uri&quot;:&quot;register&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;login&quot;:{&quot;uri&quot;:&quot;login&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;password.request&quot;:{&quot;uri&quot;:&quot;forgot-password&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;password.email&quot;:{&quot;uri&quot;:&quot;forgot-password&quot;,&quot;methods&quot;:[&quot;POST&quot;]},&quot;password.reset&quot;:{&quot;uri&quot;:&quot;reset-password\/{token}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;parameters&quot;:[&quot;token&quot;]},&quot;password.store&quot;:{&quot;uri&quot;:&quot;reset-password&quot;,&quot;methods&quot;:[&quot;POST&quot;]},&quot;verification.notice&quot;:{&quot;uri&quot;:&quot;verify-email&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;verification.verify&quot;:{&quot;uri&quot;:&quot;verify-email\/{id}\/{hash}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;parameters&quot;:[&quot;id&quot;,&quot;hash&quot;]},&quot;verification.send&quot;:{&quot;uri&quot;:&quot;email\/verification-notification&quot;,&quot;methods&quot;:[&quot;POST&quot;]},&quot;password.confirm&quot;:{&quot;uri&quot;:&quot;confirm-password&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;password.update&quot;:{&quot;uri&quot;:&quot;password&quot;,&quot;methods&quot;:[&quot;PUT&quot;]},&quot;logout&quot;:{&quot;uri&quot;:&quot;logout&quot;,&quot;methods&quot;:[&quot;POST&quot;]},&quot;api.chapters.fetch&quot;:{&quot;uri&quot;:&quot;api\/chapters\/fetch&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;api.books&quot;:{&quot;uri&quot;:&quot;api\/books&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;api.books.content-status&quot;:{&quot;uri&quot;:&quot;api\/books\/content-status&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;api.books.show&quot;:{&quot;uri&quot;:&quot;api\/books\/{slug}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;parameters&quot;:[&quot;slug&quot;]},&quot;api.chapters.adjacent&quot;:{&quot;uri&quot;:&quot;api\/chapters\/{reference}\/adjacent&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;wheres&quot;:{&quot;reference&quot;:&quot;[\\w\\d,\\-]+&quot;},&quot;parameters&quot;:[&quot;reference&quot;]},&quot;api.search.books&quot;:{&quot;uri&quot;:&quot;api\/search\/books&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;api.search&quot;:{&quot;uri&quot;:&quot;api\/search&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;api.bible.text&quot;:{&quot;uri&quot;:&quot;api\/bible\/{reference}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;wheres&quot;:{&quot;reference&quot;:&quot;[\\w\\d,\\-]+&quot;},&quot;parameters&quot;:[&quot;reference&quot;]},&quot;storage.local&quot;:{&quot;uri&quot;:&quot;storage\/{path}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;wheres&quot;:{&quot;path&quot;:&quot;.*&quot;},&quot;parameters&quot;:[&quot;path&quot;]}});&lt;/script&gt;    &lt;script type=&quot;module&quot; src=&quot;http://[::1]:5173/@vite/client&quot;&gt;&lt;/script&gt;&lt;script type=&quot;module&quot; src=&quot;http://[::1]:5173/resources/js/app.ts&quot;&gt;&lt;/script&gt;&lt;script type=&quot;module&quot; src=&quot;http://[::1]:5173/resources/js/Pages/NotFound.vue&quot;&gt;&lt;/script&gt;    &lt;/head&gt;

&lt;body class=&quot;font-sans antialiased&quot;&gt;
    &lt;div id=&quot;app&quot; data-page=&quot;{&amp;quot;component&amp;quot;:&amp;quot;NotFound&amp;quot;,&amp;quot;props&amp;quot;:{&amp;quot;errors&amp;quot;:{},&amp;quot;books&amp;quot;:{&amp;quot;sections&amp;quot;:[{&amp;quot;name&amp;quot;:&amp;quot;Altes Testament&amp;quot;,&amp;quot;books&amp;quot;:[{&amp;quot;id&amp;quot;:1,&amp;quot;name&amp;quot;:&amp;quot;1. Mose&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Mo&amp;quot;,&amp;quot;chapterCount&amp;quot;:50,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;law&amp;quot;,&amp;quot;order&amp;quot;:1,&amp;quot;slug&amp;quot;:&amp;quot;1.Mose&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:2,&amp;quot;name&amp;quot;:&amp;quot;2. Mose&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Mo&amp;quot;,&amp;quot;chapterCount&amp;quot;:40,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;law&amp;quot;,&amp;quot;order&amp;quot;:2,&amp;quot;slug&amp;quot;:&amp;quot;2.Mose&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:3,&amp;quot;name&amp;quot;:&amp;quot;3. Mose&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;3Mo&amp;quot;,&amp;quot;chapterCount&amp;quot;:27,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;law&amp;quot;,&amp;quot;order&amp;quot;:3,&amp;quot;slug&amp;quot;:&amp;quot;3.Mose&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:4,&amp;quot;name&amp;quot;:&amp;quot;4. Mose&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;4Mo&amp;quot;,&amp;quot;chapterCount&amp;quot;:36,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;law&amp;quot;,&amp;quot;order&amp;quot;:4,&amp;quot;slug&amp;quot;:&amp;quot;4.Mose&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:5,&amp;quot;name&amp;quot;:&amp;quot;5. Mose&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;5Mo&amp;quot;,&amp;quot;chapterCount&amp;quot;:34,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;law&amp;quot;,&amp;quot;order&amp;quot;:5,&amp;quot;slug&amp;quot;:&amp;quot;5.Mose&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:6,&amp;quot;name&amp;quot;:&amp;quot;Josua&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Jos&amp;quot;,&amp;quot;chapterCount&amp;quot;:24,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:6,&amp;quot;slug&amp;quot;:&amp;quot;Josua&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:7,&amp;quot;name&amp;quot;:&amp;quot;Richter&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Ri&amp;quot;,&amp;quot;chapterCount&amp;quot;:21,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:7,&amp;quot;slug&amp;quot;:&amp;quot;Richter&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:8,&amp;quot;name&amp;quot;:&amp;quot;Ruth&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Rt&amp;quot;,&amp;quot;chapterCount&amp;quot;:4,&amp;quot;chapters&amp;quot;:[1,2,3,4],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:8,&amp;quot;slug&amp;quot;:&amp;quot;Ruth&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:9,&amp;quot;name&amp;quot;:&amp;quot;1. Samuel&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Sam&amp;quot;,&amp;quot;chapterCount&amp;quot;:31,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:9,&amp;quot;slug&amp;quot;:&amp;quot;1.Samuel&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:10,&amp;quot;name&amp;quot;:&amp;quot;2. Samuel&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Sam&amp;quot;,&amp;quot;chapterCount&amp;quot;:24,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:10,&amp;quot;slug&amp;quot;:&amp;quot;2.Samuel&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:11,&amp;quot;name&amp;quot;:&amp;quot;1. K\u00f6nige&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1K\u00f6n&amp;quot;,&amp;quot;chapterCount&amp;quot;:22,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:11,&amp;quot;slug&amp;quot;:&amp;quot;1.K\u00f6nige&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:12,&amp;quot;name&amp;quot;:&amp;quot;2. K\u00f6nige&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2K\u00f6n&amp;quot;,&amp;quot;chapterCount&amp;quot;:25,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:12,&amp;quot;slug&amp;quot;:&amp;quot;2.K\u00f6nige&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:13,&amp;quot;name&amp;quot;:&amp;quot;1. Chronik&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Chr&amp;quot;,&amp;quot;chapterCount&amp;quot;:29,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:13,&amp;quot;slug&amp;quot;:&amp;quot;1.Chronik&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:14,&amp;quot;name&amp;quot;:&amp;quot;2. Chronik&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Chr&amp;quot;,&amp;quot;chapterCount&amp;quot;:36,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:14,&amp;quot;slug&amp;quot;:&amp;quot;2.Chronik&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:15,&amp;quot;name&amp;quot;:&amp;quot;Esra&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Esra&amp;quot;,&amp;quot;chapterCount&amp;quot;:10,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:15,&amp;quot;slug&amp;quot;:&amp;quot;Esra&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:16,&amp;quot;name&amp;quot;:&amp;quot;Nehemia&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Neh&amp;quot;,&amp;quot;chapterCount&amp;quot;:13,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:16,&amp;quot;slug&amp;quot;:&amp;quot;Nehemia&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:17,&amp;quot;name&amp;quot;:&amp;quot;Esther&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Est&amp;quot;,&amp;quot;chapterCount&amp;quot;:10,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:17,&amp;quot;slug&amp;quot;:&amp;quot;Esther&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:18,&amp;quot;name&amp;quot;:&amp;quot;Hiob&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Hi&amp;quot;,&amp;quot;chapterCount&amp;quot;:42,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;wisdom&amp;quot;,&amp;quot;order&amp;quot;:18,&amp;quot;slug&amp;quot;:&amp;quot;Hiob&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:19,&amp;quot;name&amp;quot;:&amp;quot;Psalmen&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Ps&amp;quot;,&amp;quot;chapterCount&amp;quot;:150,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;wisdom&amp;quot;,&amp;quot;order&amp;quot;:19,&amp;quot;slug&amp;quot;:&amp;quot;Psalmen&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:20,&amp;quot;name&amp;quot;:&amp;quot;Spr\u00fcche&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Spr&amp;quot;,&amp;quot;chapterCount&amp;quot;:31,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;wisdom&amp;quot;,&amp;quot;order&amp;quot;:20,&amp;quot;slug&amp;quot;:&amp;quot;Spr\u00fcche&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:21,&amp;quot;name&amp;quot;:&amp;quot;Prediger&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Pred&amp;quot;,&amp;quot;chapterCount&amp;quot;:12,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;wisdom&amp;quot;,&amp;quot;order&amp;quot;:21,&amp;quot;slug&amp;quot;:&amp;quot;Prediger&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:22,&amp;quot;name&amp;quot;:&amp;quot;Hohelied&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Hl&amp;quot;,&amp;quot;chapterCount&amp;quot;:8,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;wisdom&amp;quot;,&amp;quot;order&amp;quot;:22,&amp;quot;slug&amp;quot;:&amp;quot;Hohelied&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:23,&amp;quot;name&amp;quot;:&amp;quot;Jesaja&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Jes&amp;quot;,&amp;quot;chapterCount&amp;quot;:66,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:23,&amp;quot;slug&amp;quot;:&amp;quot;Jesaja&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:24,&amp;quot;name&amp;quot;:&amp;quot;Jeremia&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Jer&amp;quot;,&amp;quot;chapterCount&amp;quot;:52,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:24,&amp;quot;slug&amp;quot;:&amp;quot;Jeremia&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:25,&amp;quot;name&amp;quot;:&amp;quot;Klagelieder&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Kla&amp;quot;,&amp;quot;chapterCount&amp;quot;:5,&amp;quot;chapters&amp;quot;:[1,2,3,4,5],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;wisdom&amp;quot;,&amp;quot;order&amp;quot;:25,&amp;quot;slug&amp;quot;:&amp;quot;Klagelieder&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:26,&amp;quot;name&amp;quot;:&amp;quot;Hesekiel&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Hes&amp;quot;,&amp;quot;chapterCount&amp;quot;:48,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:26,&amp;quot;slug&amp;quot;:&amp;quot;Hesekiel&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:27,&amp;quot;name&amp;quot;:&amp;quot;Daniel&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Dan&amp;quot;,&amp;quot;chapterCount&amp;quot;:12,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:27,&amp;quot;slug&amp;quot;:&amp;quot;Daniel&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:28,&amp;quot;name&amp;quot;:&amp;quot;Hosea&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Hos&amp;quot;,&amp;quot;chapterCount&amp;quot;:14,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:28,&amp;quot;slug&amp;quot;:&amp;quot;Hosea&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:29,&amp;quot;name&amp;quot;:&amp;quot;Joel&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Joel&amp;quot;,&amp;quot;chapterCount&amp;quot;:3,&amp;quot;chapters&amp;quot;:[1,2,3],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:29,&amp;quot;slug&amp;quot;:&amp;quot;Joel&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:30,&amp;quot;name&amp;quot;:&amp;quot;Amos&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Am&amp;quot;,&amp;quot;chapterCount&amp;quot;:9,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:30,&amp;quot;slug&amp;quot;:&amp;quot;Amos&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:31,&amp;quot;name&amp;quot;:&amp;quot;Obadja&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Ob&amp;quot;,&amp;quot;chapterCount&amp;quot;:1,&amp;quot;chapters&amp;quot;:[1],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:31,&amp;quot;slug&amp;quot;:&amp;quot;Obadja&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:32,&amp;quot;name&amp;quot;:&amp;quot;Jona&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Jon&amp;quot;,&amp;quot;chapterCount&amp;quot;:4,&amp;quot;chapters&amp;quot;:[1,2,3,4],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:32,&amp;quot;slug&amp;quot;:&amp;quot;Jona&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:33,&amp;quot;name&amp;quot;:&amp;quot;Micha&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Mi&amp;quot;,&amp;quot;chapterCount&amp;quot;:7,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:33,&amp;quot;slug&amp;quot;:&amp;quot;Micha&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:34,&amp;quot;name&amp;quot;:&amp;quot;Nahum&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Nah&amp;quot;,&amp;quot;chapterCount&amp;quot;:3,&amp;quot;chapters&amp;quot;:[1,2,3],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:34,&amp;quot;slug&amp;quot;:&amp;quot;Nahum&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:35,&amp;quot;name&amp;quot;:&amp;quot;Habakuk&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Hab&amp;quot;,&amp;quot;chapterCount&amp;quot;:3,&amp;quot;chapters&amp;quot;:[1,2,3],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:35,&amp;quot;slug&amp;quot;:&amp;quot;Habakuk&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:36,&amp;quot;name&amp;quot;:&amp;quot;Zephanja&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Zeph&amp;quot;,&amp;quot;chapterCount&amp;quot;:3,&amp;quot;chapters&amp;quot;:[1,2,3],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:36,&amp;quot;slug&amp;quot;:&amp;quot;Zephanja&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:37,&amp;quot;name&amp;quot;:&amp;quot;Haggai&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Hag&amp;quot;,&amp;quot;chapterCount&amp;quot;:2,&amp;quot;chapters&amp;quot;:[1,2],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:37,&amp;quot;slug&amp;quot;:&amp;quot;Haggai&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:38,&amp;quot;name&amp;quot;:&amp;quot;Sacharja&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Sach&amp;quot;,&amp;quot;chapterCount&amp;quot;:14,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:38,&amp;quot;slug&amp;quot;:&amp;quot;Sacharja&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:39,&amp;quot;name&amp;quot;:&amp;quot;Maleachi&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Mal&amp;quot;,&amp;quot;chapterCount&amp;quot;:4,&amp;quot;chapters&amp;quot;:[1,2,3,4],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:39,&amp;quot;slug&amp;quot;:&amp;quot;Maleachi&amp;quot;,&amp;quot;hasContent&amp;quot;:false}]},{&amp;quot;name&amp;quot;:&amp;quot;Neues Testament&amp;quot;,&amp;quot;books&amp;quot;:[{&amp;quot;id&amp;quot;:40,&amp;quot;name&amp;quot;:&amp;quot;Matth\u00e4us&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Mt&amp;quot;,&amp;quot;chapterCount&amp;quot;:28,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;gospel&amp;quot;,&amp;quot;order&amp;quot;:40,&amp;quot;slug&amp;quot;:&amp;quot;Matth\u00e4us&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:41,&amp;quot;name&amp;quot;:&amp;quot;Markus&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Mk&amp;quot;,&amp;quot;chapterCount&amp;quot;:16,&amp;quot;chapters&amp;quot;:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;gospel&amp;quot;,&amp;quot;order&amp;quot;:41,&amp;quot;slug&amp;quot;:&amp;quot;Markus&amp;quot;,&amp;quot;hasContent&amp;quot;:true},{&amp;quot;id&amp;quot;:42,&amp;quot;name&amp;quot;:&amp;quot;Lukas&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Lk&amp;quot;,&amp;quot;chapterCount&amp;quot;:24,&amp;quot;chapters&amp;quot;:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;gospel&amp;quot;,&amp;quot;order&amp;quot;:42,&amp;quot;slug&amp;quot;:&amp;quot;Lukas&amp;quot;,&amp;quot;hasContent&amp;quot;:true},{&amp;quot;id&amp;quot;:43,&amp;quot;name&amp;quot;:&amp;quot;Die Heilsbotschaft nach Johannes&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Joh&amp;quot;,&amp;quot;chapterCount&amp;quot;:21,&amp;quot;chapters&amp;quot;:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;gospel&amp;quot;,&amp;quot;order&amp;quot;:43,&amp;quot;slug&amp;quot;:&amp;quot;Johannes&amp;quot;,&amp;quot;hasContent&amp;quot;:true},{&amp;quot;id&amp;quot;:44,&amp;quot;name&amp;quot;:&amp;quot;Apostelgeschichte&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Apg&amp;quot;,&amp;quot;chapterCount&amp;quot;:28,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:44,&amp;quot;slug&amp;quot;:&amp;quot;Apostelgeschichte&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:45,&amp;quot;name&amp;quot;:&amp;quot;R\u00f6mer&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;R\u00f6m&amp;quot;,&amp;quot;chapterCount&amp;quot;:16,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:45,&amp;quot;slug&amp;quot;:&amp;quot;R\u00f6mer&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:46,&amp;quot;name&amp;quot;:&amp;quot;1. Korinther&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Kor&amp;quot;,&amp;quot;chapterCount&amp;quot;:16,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:46,&amp;quot;slug&amp;quot;:&amp;quot;1.Korinther&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:47,&amp;quot;name&amp;quot;:&amp;quot;2. Korinther&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Kor&amp;quot;,&amp;quot;chapterCount&amp;quot;:13,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:47,&amp;quot;slug&amp;quot;:&amp;quot;2.Korinther&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:48,&amp;quot;name&amp;quot;:&amp;quot;Galater&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Gal&amp;quot;,&amp;quot;chapterCount&amp;quot;:6,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:48,&amp;quot;slug&amp;quot;:&amp;quot;Galater&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:49,&amp;quot;name&amp;quot;:&amp;quot;Epheser&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Eph&amp;quot;,&amp;quot;chapterCount&amp;quot;:6,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:49,&amp;quot;slug&amp;quot;:&amp;quot;Epheser&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:50,&amp;quot;name&amp;quot;:&amp;quot;Philipper&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Phil&amp;quot;,&amp;quot;chapterCount&amp;quot;:4,&amp;quot;chapters&amp;quot;:[1,2,3,4],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:50,&amp;quot;slug&amp;quot;:&amp;quot;Philipper&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:51,&amp;quot;name&amp;quot;:&amp;quot;Kolosser&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Kol&amp;quot;,&amp;quot;chapterCount&amp;quot;:4,&amp;quot;chapters&amp;quot;:[1,2,3,4],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:51,&amp;quot;slug&amp;quot;:&amp;quot;Kolosser&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:52,&amp;quot;name&amp;quot;:&amp;quot;1. Thessalonicher&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Thes&amp;quot;,&amp;quot;chapterCount&amp;quot;:5,&amp;quot;chapters&amp;quot;:[1,2,3,4,5],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:52,&amp;quot;slug&amp;quot;:&amp;quot;1.Thessalonicher&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:53,&amp;quot;name&amp;quot;:&amp;quot;2. Thessalonicher&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Thes&amp;quot;,&amp;quot;chapterCount&amp;quot;:3,&amp;quot;chapters&amp;quot;:[1,2,3],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:53,&amp;quot;slug&amp;quot;:&amp;quot;2.Thessalonicher&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:54,&amp;quot;name&amp;quot;:&amp;quot;1. Timotheus&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Tim&amp;quot;,&amp;quot;chapterCount&amp;quot;:6,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:54,&amp;quot;slug&amp;quot;:&amp;quot;1.Timotheus&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:55,&amp;quot;name&amp;quot;:&amp;quot;2. Timotheus&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Tim&amp;quot;,&amp;quot;chapterCount&amp;quot;:4,&amp;quot;chapters&amp;quot;:[1,2,3,4],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:55,&amp;quot;slug&amp;quot;:&amp;quot;2.Timotheus&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:56,&amp;quot;name&amp;quot;:&amp;quot;Titus&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Tit&amp;quot;,&amp;quot;chapterCount&amp;quot;:3,&amp;quot;chapters&amp;quot;:[1,2,3],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:56,&amp;quot;slug&amp;quot;:&amp;quot;Titus&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:57,&amp;quot;name&amp;quot;:&amp;quot;Philemon&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Phim&amp;quot;,&amp;quot;chapterCount&amp;quot;:1,&amp;quot;chapters&amp;quot;:[1],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:57,&amp;quot;slug&amp;quot;:&amp;quot;Philemon&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:58,&amp;quot;name&amp;quot;:&amp;quot;Hebr\u00e4er&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Heb&amp;quot;,&amp;quot;chapterCount&amp;quot;:13,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:58,&amp;quot;slug&amp;quot;:&amp;quot;Hebr\u00e4er&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:59,&amp;quot;name&amp;quot;:&amp;quot;Jakobus&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Jak&amp;quot;,&amp;quot;chapterCount&amp;quot;:5,&amp;quot;chapters&amp;quot;:[1,2,3,4,5],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:59,&amp;quot;slug&amp;quot;:&amp;quot;Jakobus&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:60,&amp;quot;name&amp;quot;:&amp;quot;1. Petrus&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Pet&amp;quot;,&amp;quot;chapterCount&amp;quot;:5,&amp;quot;chapters&amp;quot;:[1,2,3,4,5],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:60,&amp;quot;slug&amp;quot;:&amp;quot;1.Petrus&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:61,&amp;quot;name&amp;quot;:&amp;quot;2. Petrus&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Pet&amp;quot;,&amp;quot;chapterCount&amp;quot;:3,&amp;quot;chapters&amp;quot;:[1,2,3],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:61,&amp;quot;slug&amp;quot;:&amp;quot;2.Petrus&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:62,&amp;quot;name&amp;quot;:&amp;quot;1. Johannes&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Joh&amp;quot;,&amp;quot;chapterCount&amp;quot;:5,&amp;quot;chapters&amp;quot;:[1,2,3,4,5],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:62,&amp;quot;slug&amp;quot;:&amp;quot;1.Johannes&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:63,&amp;quot;name&amp;quot;:&amp;quot;2. Johannes&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Joh&amp;quot;,&amp;quot;chapterCount&amp;quot;:1,&amp;quot;chapters&amp;quot;:[1],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:63,&amp;quot;slug&amp;quot;:&amp;quot;2.Johannes&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:64,&amp;quot;name&amp;quot;:&amp;quot;3. Johannes&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;3Joh&amp;quot;,&amp;quot;chapterCount&amp;quot;:1,&amp;quot;chapters&amp;quot;:[1],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:64,&amp;quot;slug&amp;quot;:&amp;quot;3.Johannes&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:65,&amp;quot;name&amp;quot;:&amp;quot;Judas&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Jud&amp;quot;,&amp;quot;chapterCount&amp;quot;:1,&amp;quot;chapters&amp;quot;:[1],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:65,&amp;quot;slug&amp;quot;:&amp;quot;Judas&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:66,&amp;quot;name&amp;quot;:&amp;quot;Offenbarung&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Offb&amp;quot;,&amp;quot;chapterCount&amp;quot;:22,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;apocalypse&amp;quot;,&amp;quot;order&amp;quot;:66,&amp;quot;slug&amp;quot;:&amp;quot;Offenbarung&amp;quot;,&amp;quot;hasContent&amp;quot;:false}]}],&amp;quot;availableBooks&amp;quot;:[{&amp;quot;slug&amp;quot;:&amp;quot;Markus&amp;quot;,&amp;quot;order&amp;quot;:41},{&amp;quot;slug&amp;quot;:&amp;quot;Lukas&amp;quot;,&amp;quot;order&amp;quot;:42},{&amp;quot;slug&amp;quot;:&amp;quot;Johannes&amp;quot;,&amp;quot;order&amp;quot;:43}]},&amp;quot;env&amp;quot;:&amp;quot;local&amp;quot;,&amp;quot;auth&amp;quot;:{&amp;quot;user&amp;quot;:null},&amp;quot;requestedPath&amp;quot;:&amp;quot;api\/books\/architecto&amp;quot;},&amp;quot;url&amp;quot;:&amp;quot;\/api\/books\/architecto&amp;quot;,&amp;quot;version&amp;quot;:&amp;quot;18765f3fa436d07c5ef1cbbbc3fa3b37&amp;quot;,&amp;quot;clearHistory&amp;quot;:false,&amp;quot;encryptHistory&amp;quot;:false}&quot;&gt;&lt;/div&gt;&lt;/body&gt;

&lt;/html&gt;
</code>
 </pre>
    </span>
<span id="execution-results-GETapi-books--slug-" hidden>
    <blockquote>Received response<span
                id="execution-response-status-GETapi-books--slug-"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-GETapi-books--slug-"
      data-empty-response-text="<Empty response>" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-GETapi-books--slug-" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-GETapi-books--slug-">

Tip: Check that you&#039;re properly connected to the network.
If you&#039;re a maintainer of ths API, verify that your API is running and you&#039;ve enabled CORS.
You can check the Dev Tools console for debugging information.</code></pre>
</span>
<form id="form-GETapi-books--slug-" data-method="GET"
      data-path="api/books/{slug}"
      data-authed="0"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('GETapi-books--slug-', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
                    <button type="button"
                    style="background-color: #8fbcd4; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-tryout-GETapi-books--slug-"
                    onclick="tryItOut('GETapi-books--slug-');">Try it out ⚡
            </button>
            <button type="button"
                    style="background-color: #c97a7e; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-canceltryout-GETapi-books--slug-"
                    onclick="cancelTryOut('GETapi-books--slug-');" hidden>Cancel 🛑
            </button>&nbsp;&nbsp;
            <button type="submit"
                    style="background-color: #6ac174; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-executetryout-GETapi-books--slug-"
                    data-initial-text="Send Request 💥"
                    data-loading-text="⏱ Sending..."
                    hidden>Send Request 💥
            </button>
            </h3>
            <p>
            <small class="badge badge-green">GET</small>
            <b><code>api/books/{slug}</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Content-Type</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Content-Type"                data-endpoint="GETapi-books--slug-"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Accept</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Accept"                data-endpoint="GETapi-books--slug-"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                        <h4 class="fancy-heading-panel"><b>URL Parameters</b></h4>
                    <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>slug</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="slug"                data-endpoint="GETapi-books--slug-"
               value="architecto"
               data-component="url">
    <br>
<p>The slug of the book. Example: <code>architecto</code></p>
            </div>
                    </form>

                    <h2 id="endpoints-GETapi-search-books">GET api/search/books</h2>

<p>
</p>



<span id="example-requests-GETapi-search-books">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request GET \
    --get "http://esra-bibel.local/api/search/books" \
    --header "Content-Type: application/json" \
    --header "Accept: application/json"</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "http://esra-bibel.local/api/search/books"
);

const headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "GET",
    headers,
}).then(response =&gt; response.json());</code></pre></div>

</span>

<span id="example-responses-GETapi-search-books">
            <blockquote>
            <p>Example response (404):</p>
        </blockquote>
                <details class="annotation">
            <summary style="cursor: pointer;">
                <small onclick="textContent = parentElement.parentElement.open ? 'Show headers' : 'Hide headers'">Show headers</small>
            </summary>
            <pre><code class="language-http">cache-control: no-cache, private
content-type: application/json
vary: X-Inertia
access-control-allow-origin: *
set-cookie: XSRF-TOKEN=eyJpdiI6IkphaUFWdWx0RjIxRUpycVBsajNuakE9PSIsInZhbHVlIjoidU9qcmlkUEx4eVhnUHhBUmFsQmhGbHZ4cjFNNUlPaFRVV0tHa09qL0Mrdmt3UmxnUUU4QWM2bmdrcnBBZTJGSUNtV0twZVRFWnQrMXZweUE2cTQrNEQwOG5pZS9TdXhFaVluenlwR0JBTG5ubjljUWp2R2R4QWFEZDE4N3ZtdHUiLCJtYWMiOiI1NWE2YWZhNjc1NzJjMWY3OWJiZWI0MzMxYmUyYWY0ODI0N2FmNjA0YTIxYWQ1YWEwMGQ0NjgyMDczZTk5OWM4IiwidGFnIjoiIn0%3D; expires=Wed, 09 Apr 2025 13:17:49 GMT; Max-Age=7200; path=/; samesite=lax; esrabibel_session=eyJpdiI6InBiRFJjeFcwT2NQYUtKUFZSWHlseFE9PSIsInZhbHVlIjoieEZLRUd2WkVhNmN0OEtxRk81TUJsL3JHcmtuYnJ4RnZrcjR2OFVFekFtMHVSVk1uZS9UOFFkdDY5TkNha1ZHOEZjQkhWREU4NTdJMjM3czF1dlZkU0xGN2VFdFRMMFhxZVVMbEptNDVnTzQzWk1FRlRHTzlFMDlwbFd4VDlyMEoiLCJtYWMiOiJmMjg0OWIwNWRiMzEwY2UyOGZlYzVlYTE0Y2MzNGY4OGIwN2IxZmJiNTAwZDBlNDE0ZDcxNGIyYzhkYjVmYjUyIiwidGFnIjoiIn0%3D; expires=Wed, 09 Apr 2025 13:17:49 GMT; Max-Age=7200; path=/; httponly; samesite=lax
 </code></pre></details>         <pre>

<code class="language-json" style="max-height: 300px;">&lt;!DOCTYPE html&gt;
&lt;html lang=&quot;de&quot;&gt;

&lt;head&gt;
    &lt;meta charset=&quot;utf-8&quot;&gt;
    &lt;meta name=&quot;viewport&quot; content=&quot;width=device-width, initial-scale=1&quot;&gt;

    &lt;title inertia&gt;&lt;/title&gt;

    &lt;!-- Fonts --&gt;
    &lt;!-- Preconnect to font domains --&gt;
    &lt;link rel=&quot;preconnect&quot; href=&quot;https://fonts.bunny.net&quot; crossorigin&gt;
    &lt;link rel=&quot;preconnect&quot; href=&quot;https://use.typekit.net&quot; as=&quot;font&quot; type=&quot;font/woff2&quot; crossorigin&gt;

    &lt;!-- Preload critical fonts --&gt;
    &lt;link rel=&quot;preload&quot; href=&quot;/fonts/ThanatosText-Book.woff2&quot; as=&quot;font&quot; type=&quot;font/woff2&quot; crossorigin&gt;

    &lt;!-- Load fonts --&gt;
    &lt;link href=&quot;https://fonts.bunny.net/css?family=figtree:400,500,600&amp;display=swap&quot; rel=&quot;stylesheet&quot; /&gt;
    &lt;link rel=&quot;stylesheet&quot; href=&quot;https://use.typekit.net/kzb8yhl.css&quot; media=&quot;print&quot; onload=&quot;this.media=&#039;all&#039;&quot;&gt;

    &lt;!-- Fallback for typekit fonts --&gt;
    &lt;noscript&gt;
        &lt;link rel=&quot;stylesheet&quot; href=&quot;https://use.typekit.net/kzb8yhl.css&quot;&gt;
    &lt;/noscript&gt;

    &lt;!-- Local font definition --&gt;
    &lt;style&gt;
        @font-face {
            font-family: &#039;ThanatosText&#039;;
            src: url(&#039;/fonts/ThanatosText-Book.woff2&#039;) format(&#039;woff2&#039;);
            font-weight: normal;
            font-style: normal;
            font-display: swap;
        }
    &lt;/style&gt;

    &lt;!-- Scripts --&gt;
    &lt;script type=&quot;text/javascript&quot;&gt;Object.assign(Ziggy.routes,{&quot;search.index&quot;:{&quot;uri&quot;:&quot;search&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;search.query&quot;:{&quot;uri&quot;:&quot;search\/{query}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;wheres&quot;:{&quot;query&quot;:&quot;[^\/]+&quot;},&quot;parameters&quot;:[&quot;query&quot;]},&quot;search.paged&quot;:{&quot;uri&quot;:&quot;search\/{query}\/{page?}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;wheres&quot;:{&quot;query&quot;:&quot;[^\/]+&quot;,&quot;page&quot;:&quot;[0-9]+&quot;},&quot;parameters&quot;:[&quot;query&quot;,&quot;page&quot;]},&quot;search.settings&quot;:{&quot;uri&quot;:&quot;search\/settings&quot;,&quot;methods&quot;:[&quot;POST&quot;]},&quot;dashboard&quot;:{&quot;uri&quot;:&quot;dashboard&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;bible.import&quot;:{&quot;uri&quot;:&quot;import-bible&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;bible.import.store&quot;:{&quot;uri&quot;:&quot;import-bible&quot;,&quot;methods&quot;:[&quot;POST&quot;]},&quot;file.upload&quot;:{&quot;uri&quot;:&quot;api\/upload&quot;,&quot;methods&quot;:[&quot;POST&quot;]},&quot;profile.edit&quot;:{&quot;uri&quot;:&quot;profile&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;profile.update&quot;:{&quot;uri&quot;:&quot;profile&quot;,&quot;methods&quot;:[&quot;PATCH&quot;]},&quot;profile.destroy&quot;:{&quot;uri&quot;:&quot;profile&quot;,&quot;methods&quot;:[&quot;DELETE&quot;]},&quot;books.show&quot;:{&quot;uri&quot;:&quot;{reference}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;wheres&quot;:{&quot;reference&quot;:&quot;[\\w\\d\\.,\\-\\+]+&quot;},&quot;parameters&quot;:[&quot;reference&quot;]},&quot;register&quot;:{&quot;uri&quot;:&quot;register&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;login&quot;:{&quot;uri&quot;:&quot;login&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;password.request&quot;:{&quot;uri&quot;:&quot;forgot-password&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;password.email&quot;:{&quot;uri&quot;:&quot;forgot-password&quot;,&quot;methods&quot;:[&quot;POST&quot;]},&quot;password.reset&quot;:{&quot;uri&quot;:&quot;reset-password\/{token}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;parameters&quot;:[&quot;token&quot;]},&quot;password.store&quot;:{&quot;uri&quot;:&quot;reset-password&quot;,&quot;methods&quot;:[&quot;POST&quot;]},&quot;verification.notice&quot;:{&quot;uri&quot;:&quot;verify-email&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;verification.verify&quot;:{&quot;uri&quot;:&quot;verify-email\/{id}\/{hash}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;parameters&quot;:[&quot;id&quot;,&quot;hash&quot;]},&quot;verification.send&quot;:{&quot;uri&quot;:&quot;email\/verification-notification&quot;,&quot;methods&quot;:[&quot;POST&quot;]},&quot;password.confirm&quot;:{&quot;uri&quot;:&quot;confirm-password&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;password.update&quot;:{&quot;uri&quot;:&quot;password&quot;,&quot;methods&quot;:[&quot;PUT&quot;]},&quot;logout&quot;:{&quot;uri&quot;:&quot;logout&quot;,&quot;methods&quot;:[&quot;POST&quot;]},&quot;api.chapters.fetch&quot;:{&quot;uri&quot;:&quot;api\/chapters\/fetch&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;api.books&quot;:{&quot;uri&quot;:&quot;api\/books&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;api.books.content-status&quot;:{&quot;uri&quot;:&quot;api\/books\/content-status&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;api.books.show&quot;:{&quot;uri&quot;:&quot;api\/books\/{slug}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;parameters&quot;:[&quot;slug&quot;]},&quot;api.chapters.adjacent&quot;:{&quot;uri&quot;:&quot;api\/chapters\/{reference}\/adjacent&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;wheres&quot;:{&quot;reference&quot;:&quot;[\\w\\d,\\-]+&quot;},&quot;parameters&quot;:[&quot;reference&quot;]},&quot;api.search.books&quot;:{&quot;uri&quot;:&quot;api\/search\/books&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;api.search&quot;:{&quot;uri&quot;:&quot;api\/search&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;api.bible.text&quot;:{&quot;uri&quot;:&quot;api\/bible\/{reference}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;wheres&quot;:{&quot;reference&quot;:&quot;[\\w\\d,\\-]+&quot;},&quot;parameters&quot;:[&quot;reference&quot;]},&quot;storage.local&quot;:{&quot;uri&quot;:&quot;storage\/{path}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;wheres&quot;:{&quot;path&quot;:&quot;.*&quot;},&quot;parameters&quot;:[&quot;path&quot;]}});&lt;/script&gt;    &lt;script type=&quot;module&quot; src=&quot;http://[::1]:5173/@vite/client&quot;&gt;&lt;/script&gt;&lt;script type=&quot;module&quot; src=&quot;http://[::1]:5173/resources/js/app.ts&quot;&gt;&lt;/script&gt;&lt;script type=&quot;module&quot; src=&quot;http://[::1]:5173/resources/js/Pages/NotFound.vue&quot;&gt;&lt;/script&gt;    &lt;/head&gt;

&lt;body class=&quot;font-sans antialiased&quot;&gt;
    &lt;div id=&quot;app&quot; data-page=&quot;{&amp;quot;component&amp;quot;:&amp;quot;NotFound&amp;quot;,&amp;quot;props&amp;quot;:{&amp;quot;errors&amp;quot;:{},&amp;quot;books&amp;quot;:{&amp;quot;sections&amp;quot;:[{&amp;quot;name&amp;quot;:&amp;quot;Altes Testament&amp;quot;,&amp;quot;books&amp;quot;:[{&amp;quot;id&amp;quot;:1,&amp;quot;name&amp;quot;:&amp;quot;1. Mose&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Mo&amp;quot;,&amp;quot;chapterCount&amp;quot;:50,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;law&amp;quot;,&amp;quot;order&amp;quot;:1,&amp;quot;slug&amp;quot;:&amp;quot;1.Mose&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:2,&amp;quot;name&amp;quot;:&amp;quot;2. Mose&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Mo&amp;quot;,&amp;quot;chapterCount&amp;quot;:40,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;law&amp;quot;,&amp;quot;order&amp;quot;:2,&amp;quot;slug&amp;quot;:&amp;quot;2.Mose&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:3,&amp;quot;name&amp;quot;:&amp;quot;3. Mose&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;3Mo&amp;quot;,&amp;quot;chapterCount&amp;quot;:27,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;law&amp;quot;,&amp;quot;order&amp;quot;:3,&amp;quot;slug&amp;quot;:&amp;quot;3.Mose&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:4,&amp;quot;name&amp;quot;:&amp;quot;4. Mose&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;4Mo&amp;quot;,&amp;quot;chapterCount&amp;quot;:36,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;law&amp;quot;,&amp;quot;order&amp;quot;:4,&amp;quot;slug&amp;quot;:&amp;quot;4.Mose&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:5,&amp;quot;name&amp;quot;:&amp;quot;5. Mose&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;5Mo&amp;quot;,&amp;quot;chapterCount&amp;quot;:34,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;law&amp;quot;,&amp;quot;order&amp;quot;:5,&amp;quot;slug&amp;quot;:&amp;quot;5.Mose&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:6,&amp;quot;name&amp;quot;:&amp;quot;Josua&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Jos&amp;quot;,&amp;quot;chapterCount&amp;quot;:24,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:6,&amp;quot;slug&amp;quot;:&amp;quot;Josua&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:7,&amp;quot;name&amp;quot;:&amp;quot;Richter&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Ri&amp;quot;,&amp;quot;chapterCount&amp;quot;:21,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:7,&amp;quot;slug&amp;quot;:&amp;quot;Richter&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:8,&amp;quot;name&amp;quot;:&amp;quot;Ruth&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Rt&amp;quot;,&amp;quot;chapterCount&amp;quot;:4,&amp;quot;chapters&amp;quot;:[1,2,3,4],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:8,&amp;quot;slug&amp;quot;:&amp;quot;Ruth&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:9,&amp;quot;name&amp;quot;:&amp;quot;1. Samuel&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Sam&amp;quot;,&amp;quot;chapterCount&amp;quot;:31,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:9,&amp;quot;slug&amp;quot;:&amp;quot;1.Samuel&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:10,&amp;quot;name&amp;quot;:&amp;quot;2. Samuel&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Sam&amp;quot;,&amp;quot;chapterCount&amp;quot;:24,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:10,&amp;quot;slug&amp;quot;:&amp;quot;2.Samuel&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:11,&amp;quot;name&amp;quot;:&amp;quot;1. K\u00f6nige&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1K\u00f6n&amp;quot;,&amp;quot;chapterCount&amp;quot;:22,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:11,&amp;quot;slug&amp;quot;:&amp;quot;1.K\u00f6nige&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:12,&amp;quot;name&amp;quot;:&amp;quot;2. K\u00f6nige&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2K\u00f6n&amp;quot;,&amp;quot;chapterCount&amp;quot;:25,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:12,&amp;quot;slug&amp;quot;:&amp;quot;2.K\u00f6nige&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:13,&amp;quot;name&amp;quot;:&amp;quot;1. Chronik&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Chr&amp;quot;,&amp;quot;chapterCount&amp;quot;:29,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:13,&amp;quot;slug&amp;quot;:&amp;quot;1.Chronik&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:14,&amp;quot;name&amp;quot;:&amp;quot;2. Chronik&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Chr&amp;quot;,&amp;quot;chapterCount&amp;quot;:36,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:14,&amp;quot;slug&amp;quot;:&amp;quot;2.Chronik&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:15,&amp;quot;name&amp;quot;:&amp;quot;Esra&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Esra&amp;quot;,&amp;quot;chapterCount&amp;quot;:10,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:15,&amp;quot;slug&amp;quot;:&amp;quot;Esra&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:16,&amp;quot;name&amp;quot;:&amp;quot;Nehemia&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Neh&amp;quot;,&amp;quot;chapterCount&amp;quot;:13,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:16,&amp;quot;slug&amp;quot;:&amp;quot;Nehemia&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:17,&amp;quot;name&amp;quot;:&amp;quot;Esther&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Est&amp;quot;,&amp;quot;chapterCount&amp;quot;:10,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:17,&amp;quot;slug&amp;quot;:&amp;quot;Esther&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:18,&amp;quot;name&amp;quot;:&amp;quot;Hiob&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Hi&amp;quot;,&amp;quot;chapterCount&amp;quot;:42,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;wisdom&amp;quot;,&amp;quot;order&amp;quot;:18,&amp;quot;slug&amp;quot;:&amp;quot;Hiob&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:19,&amp;quot;name&amp;quot;:&amp;quot;Psalmen&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Ps&amp;quot;,&amp;quot;chapterCount&amp;quot;:150,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;wisdom&amp;quot;,&amp;quot;order&amp;quot;:19,&amp;quot;slug&amp;quot;:&amp;quot;Psalmen&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:20,&amp;quot;name&amp;quot;:&amp;quot;Spr\u00fcche&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Spr&amp;quot;,&amp;quot;chapterCount&amp;quot;:31,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;wisdom&amp;quot;,&amp;quot;order&amp;quot;:20,&amp;quot;slug&amp;quot;:&amp;quot;Spr\u00fcche&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:21,&amp;quot;name&amp;quot;:&amp;quot;Prediger&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Pred&amp;quot;,&amp;quot;chapterCount&amp;quot;:12,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;wisdom&amp;quot;,&amp;quot;order&amp;quot;:21,&amp;quot;slug&amp;quot;:&amp;quot;Prediger&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:22,&amp;quot;name&amp;quot;:&amp;quot;Hohelied&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Hl&amp;quot;,&amp;quot;chapterCount&amp;quot;:8,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;wisdom&amp;quot;,&amp;quot;order&amp;quot;:22,&amp;quot;slug&amp;quot;:&amp;quot;Hohelied&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:23,&amp;quot;name&amp;quot;:&amp;quot;Jesaja&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Jes&amp;quot;,&amp;quot;chapterCount&amp;quot;:66,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:23,&amp;quot;slug&amp;quot;:&amp;quot;Jesaja&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:24,&amp;quot;name&amp;quot;:&amp;quot;Jeremia&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Jer&amp;quot;,&amp;quot;chapterCount&amp;quot;:52,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:24,&amp;quot;slug&amp;quot;:&amp;quot;Jeremia&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:25,&amp;quot;name&amp;quot;:&amp;quot;Klagelieder&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Kla&amp;quot;,&amp;quot;chapterCount&amp;quot;:5,&amp;quot;chapters&amp;quot;:[1,2,3,4,5],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;wisdom&amp;quot;,&amp;quot;order&amp;quot;:25,&amp;quot;slug&amp;quot;:&amp;quot;Klagelieder&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:26,&amp;quot;name&amp;quot;:&amp;quot;Hesekiel&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Hes&amp;quot;,&amp;quot;chapterCount&amp;quot;:48,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:26,&amp;quot;slug&amp;quot;:&amp;quot;Hesekiel&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:27,&amp;quot;name&amp;quot;:&amp;quot;Daniel&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Dan&amp;quot;,&amp;quot;chapterCount&amp;quot;:12,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:27,&amp;quot;slug&amp;quot;:&amp;quot;Daniel&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:28,&amp;quot;name&amp;quot;:&amp;quot;Hosea&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Hos&amp;quot;,&amp;quot;chapterCount&amp;quot;:14,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:28,&amp;quot;slug&amp;quot;:&amp;quot;Hosea&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:29,&amp;quot;name&amp;quot;:&amp;quot;Joel&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Joel&amp;quot;,&amp;quot;chapterCount&amp;quot;:3,&amp;quot;chapters&amp;quot;:[1,2,3],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:29,&amp;quot;slug&amp;quot;:&amp;quot;Joel&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:30,&amp;quot;name&amp;quot;:&amp;quot;Amos&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Am&amp;quot;,&amp;quot;chapterCount&amp;quot;:9,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:30,&amp;quot;slug&amp;quot;:&amp;quot;Amos&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:31,&amp;quot;name&amp;quot;:&amp;quot;Obadja&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Ob&amp;quot;,&amp;quot;chapterCount&amp;quot;:1,&amp;quot;chapters&amp;quot;:[1],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:31,&amp;quot;slug&amp;quot;:&amp;quot;Obadja&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:32,&amp;quot;name&amp;quot;:&amp;quot;Jona&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Jon&amp;quot;,&amp;quot;chapterCount&amp;quot;:4,&amp;quot;chapters&amp;quot;:[1,2,3,4],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:32,&amp;quot;slug&amp;quot;:&amp;quot;Jona&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:33,&amp;quot;name&amp;quot;:&amp;quot;Micha&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Mi&amp;quot;,&amp;quot;chapterCount&amp;quot;:7,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:33,&amp;quot;slug&amp;quot;:&amp;quot;Micha&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:34,&amp;quot;name&amp;quot;:&amp;quot;Nahum&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Nah&amp;quot;,&amp;quot;chapterCount&amp;quot;:3,&amp;quot;chapters&amp;quot;:[1,2,3],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:34,&amp;quot;slug&amp;quot;:&amp;quot;Nahum&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:35,&amp;quot;name&amp;quot;:&amp;quot;Habakuk&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Hab&amp;quot;,&amp;quot;chapterCount&amp;quot;:3,&amp;quot;chapters&amp;quot;:[1,2,3],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:35,&amp;quot;slug&amp;quot;:&amp;quot;Habakuk&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:36,&amp;quot;name&amp;quot;:&amp;quot;Zephanja&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Zeph&amp;quot;,&amp;quot;chapterCount&amp;quot;:3,&amp;quot;chapters&amp;quot;:[1,2,3],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:36,&amp;quot;slug&amp;quot;:&amp;quot;Zephanja&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:37,&amp;quot;name&amp;quot;:&amp;quot;Haggai&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Hag&amp;quot;,&amp;quot;chapterCount&amp;quot;:2,&amp;quot;chapters&amp;quot;:[1,2],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:37,&amp;quot;slug&amp;quot;:&amp;quot;Haggai&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:38,&amp;quot;name&amp;quot;:&amp;quot;Sacharja&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Sach&amp;quot;,&amp;quot;chapterCount&amp;quot;:14,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:38,&amp;quot;slug&amp;quot;:&amp;quot;Sacharja&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:39,&amp;quot;name&amp;quot;:&amp;quot;Maleachi&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Mal&amp;quot;,&amp;quot;chapterCount&amp;quot;:4,&amp;quot;chapters&amp;quot;:[1,2,3,4],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:39,&amp;quot;slug&amp;quot;:&amp;quot;Maleachi&amp;quot;,&amp;quot;hasContent&amp;quot;:false}]},{&amp;quot;name&amp;quot;:&amp;quot;Neues Testament&amp;quot;,&amp;quot;books&amp;quot;:[{&amp;quot;id&amp;quot;:40,&amp;quot;name&amp;quot;:&amp;quot;Matth\u00e4us&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Mt&amp;quot;,&amp;quot;chapterCount&amp;quot;:28,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;gospel&amp;quot;,&amp;quot;order&amp;quot;:40,&amp;quot;slug&amp;quot;:&amp;quot;Matth\u00e4us&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:41,&amp;quot;name&amp;quot;:&amp;quot;Markus&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Mk&amp;quot;,&amp;quot;chapterCount&amp;quot;:16,&amp;quot;chapters&amp;quot;:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;gospel&amp;quot;,&amp;quot;order&amp;quot;:41,&amp;quot;slug&amp;quot;:&amp;quot;Markus&amp;quot;,&amp;quot;hasContent&amp;quot;:true},{&amp;quot;id&amp;quot;:42,&amp;quot;name&amp;quot;:&amp;quot;Lukas&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Lk&amp;quot;,&amp;quot;chapterCount&amp;quot;:24,&amp;quot;chapters&amp;quot;:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;gospel&amp;quot;,&amp;quot;order&amp;quot;:42,&amp;quot;slug&amp;quot;:&amp;quot;Lukas&amp;quot;,&amp;quot;hasContent&amp;quot;:true},{&amp;quot;id&amp;quot;:43,&amp;quot;name&amp;quot;:&amp;quot;Die Heilsbotschaft nach Johannes&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Joh&amp;quot;,&amp;quot;chapterCount&amp;quot;:21,&amp;quot;chapters&amp;quot;:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;gospel&amp;quot;,&amp;quot;order&amp;quot;:43,&amp;quot;slug&amp;quot;:&amp;quot;Johannes&amp;quot;,&amp;quot;hasContent&amp;quot;:true},{&amp;quot;id&amp;quot;:44,&amp;quot;name&amp;quot;:&amp;quot;Apostelgeschichte&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Apg&amp;quot;,&amp;quot;chapterCount&amp;quot;:28,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:44,&amp;quot;slug&amp;quot;:&amp;quot;Apostelgeschichte&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:45,&amp;quot;name&amp;quot;:&amp;quot;R\u00f6mer&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;R\u00f6m&amp;quot;,&amp;quot;chapterCount&amp;quot;:16,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:45,&amp;quot;slug&amp;quot;:&amp;quot;R\u00f6mer&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:46,&amp;quot;name&amp;quot;:&amp;quot;1. Korinther&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Kor&amp;quot;,&amp;quot;chapterCount&amp;quot;:16,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:46,&amp;quot;slug&amp;quot;:&amp;quot;1.Korinther&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:47,&amp;quot;name&amp;quot;:&amp;quot;2. Korinther&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Kor&amp;quot;,&amp;quot;chapterCount&amp;quot;:13,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:47,&amp;quot;slug&amp;quot;:&amp;quot;2.Korinther&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:48,&amp;quot;name&amp;quot;:&amp;quot;Galater&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Gal&amp;quot;,&amp;quot;chapterCount&amp;quot;:6,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:48,&amp;quot;slug&amp;quot;:&amp;quot;Galater&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:49,&amp;quot;name&amp;quot;:&amp;quot;Epheser&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Eph&amp;quot;,&amp;quot;chapterCount&amp;quot;:6,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:49,&amp;quot;slug&amp;quot;:&amp;quot;Epheser&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:50,&amp;quot;name&amp;quot;:&amp;quot;Philipper&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Phil&amp;quot;,&amp;quot;chapterCount&amp;quot;:4,&amp;quot;chapters&amp;quot;:[1,2,3,4],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:50,&amp;quot;slug&amp;quot;:&amp;quot;Philipper&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:51,&amp;quot;name&amp;quot;:&amp;quot;Kolosser&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Kol&amp;quot;,&amp;quot;chapterCount&amp;quot;:4,&amp;quot;chapters&amp;quot;:[1,2,3,4],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:51,&amp;quot;slug&amp;quot;:&amp;quot;Kolosser&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:52,&amp;quot;name&amp;quot;:&amp;quot;1. Thessalonicher&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Thes&amp;quot;,&amp;quot;chapterCount&amp;quot;:5,&amp;quot;chapters&amp;quot;:[1,2,3,4,5],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:52,&amp;quot;slug&amp;quot;:&amp;quot;1.Thessalonicher&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:53,&amp;quot;name&amp;quot;:&amp;quot;2. Thessalonicher&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Thes&amp;quot;,&amp;quot;chapterCount&amp;quot;:3,&amp;quot;chapters&amp;quot;:[1,2,3],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:53,&amp;quot;slug&amp;quot;:&amp;quot;2.Thessalonicher&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:54,&amp;quot;name&amp;quot;:&amp;quot;1. Timotheus&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Tim&amp;quot;,&amp;quot;chapterCount&amp;quot;:6,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:54,&amp;quot;slug&amp;quot;:&amp;quot;1.Timotheus&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:55,&amp;quot;name&amp;quot;:&amp;quot;2. Timotheus&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Tim&amp;quot;,&amp;quot;chapterCount&amp;quot;:4,&amp;quot;chapters&amp;quot;:[1,2,3,4],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:55,&amp;quot;slug&amp;quot;:&amp;quot;2.Timotheus&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:56,&amp;quot;name&amp;quot;:&amp;quot;Titus&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Tit&amp;quot;,&amp;quot;chapterCount&amp;quot;:3,&amp;quot;chapters&amp;quot;:[1,2,3],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:56,&amp;quot;slug&amp;quot;:&amp;quot;Titus&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:57,&amp;quot;name&amp;quot;:&amp;quot;Philemon&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Phim&amp;quot;,&amp;quot;chapterCount&amp;quot;:1,&amp;quot;chapters&amp;quot;:[1],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:57,&amp;quot;slug&amp;quot;:&amp;quot;Philemon&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:58,&amp;quot;name&amp;quot;:&amp;quot;Hebr\u00e4er&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Heb&amp;quot;,&amp;quot;chapterCount&amp;quot;:13,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:58,&amp;quot;slug&amp;quot;:&amp;quot;Hebr\u00e4er&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:59,&amp;quot;name&amp;quot;:&amp;quot;Jakobus&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Jak&amp;quot;,&amp;quot;chapterCount&amp;quot;:5,&amp;quot;chapters&amp;quot;:[1,2,3,4,5],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:59,&amp;quot;slug&amp;quot;:&amp;quot;Jakobus&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:60,&amp;quot;name&amp;quot;:&amp;quot;1. Petrus&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Pet&amp;quot;,&amp;quot;chapterCount&amp;quot;:5,&amp;quot;chapters&amp;quot;:[1,2,3,4,5],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:60,&amp;quot;slug&amp;quot;:&amp;quot;1.Petrus&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:61,&amp;quot;name&amp;quot;:&amp;quot;2. Petrus&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Pet&amp;quot;,&amp;quot;chapterCount&amp;quot;:3,&amp;quot;chapters&amp;quot;:[1,2,3],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:61,&amp;quot;slug&amp;quot;:&amp;quot;2.Petrus&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:62,&amp;quot;name&amp;quot;:&amp;quot;1. Johannes&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Joh&amp;quot;,&amp;quot;chapterCount&amp;quot;:5,&amp;quot;chapters&amp;quot;:[1,2,3,4,5],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:62,&amp;quot;slug&amp;quot;:&amp;quot;1.Johannes&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:63,&amp;quot;name&amp;quot;:&amp;quot;2. Johannes&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Joh&amp;quot;,&amp;quot;chapterCount&amp;quot;:1,&amp;quot;chapters&amp;quot;:[1],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:63,&amp;quot;slug&amp;quot;:&amp;quot;2.Johannes&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:64,&amp;quot;name&amp;quot;:&amp;quot;3. Johannes&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;3Joh&amp;quot;,&amp;quot;chapterCount&amp;quot;:1,&amp;quot;chapters&amp;quot;:[1],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:64,&amp;quot;slug&amp;quot;:&amp;quot;3.Johannes&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:65,&amp;quot;name&amp;quot;:&amp;quot;Judas&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Jud&amp;quot;,&amp;quot;chapterCount&amp;quot;:1,&amp;quot;chapters&amp;quot;:[1],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:65,&amp;quot;slug&amp;quot;:&amp;quot;Judas&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:66,&amp;quot;name&amp;quot;:&amp;quot;Offenbarung&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Offb&amp;quot;,&amp;quot;chapterCount&amp;quot;:22,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;apocalypse&amp;quot;,&amp;quot;order&amp;quot;:66,&amp;quot;slug&amp;quot;:&amp;quot;Offenbarung&amp;quot;,&amp;quot;hasContent&amp;quot;:false}]}],&amp;quot;availableBooks&amp;quot;:[{&amp;quot;slug&amp;quot;:&amp;quot;Markus&amp;quot;,&amp;quot;order&amp;quot;:41},{&amp;quot;slug&amp;quot;:&amp;quot;Lukas&amp;quot;,&amp;quot;order&amp;quot;:42},{&amp;quot;slug&amp;quot;:&amp;quot;Johannes&amp;quot;,&amp;quot;order&amp;quot;:43}]},&amp;quot;env&amp;quot;:&amp;quot;local&amp;quot;,&amp;quot;auth&amp;quot;:{&amp;quot;user&amp;quot;:null},&amp;quot;requestedPath&amp;quot;:&amp;quot;api\/search\/books&amp;quot;},&amp;quot;url&amp;quot;:&amp;quot;\/api\/search\/books&amp;quot;,&amp;quot;version&amp;quot;:&amp;quot;18765f3fa436d07c5ef1cbbbc3fa3b37&amp;quot;,&amp;quot;clearHistory&amp;quot;:false,&amp;quot;encryptHistory&amp;quot;:false}&quot;&gt;&lt;/div&gt;&lt;/body&gt;

&lt;/html&gt;
</code>
 </pre>
    </span>
<span id="execution-results-GETapi-search-books" hidden>
    <blockquote>Received response<span
                id="execution-response-status-GETapi-search-books"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-GETapi-search-books"
      data-empty-response-text="<Empty response>" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-GETapi-search-books" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-GETapi-search-books">

Tip: Check that you&#039;re properly connected to the network.
If you&#039;re a maintainer of ths API, verify that your API is running and you&#039;ve enabled CORS.
You can check the Dev Tools console for debugging information.</code></pre>
</span>
<form id="form-GETapi-search-books" data-method="GET"
      data-path="api/search/books"
      data-authed="0"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('GETapi-search-books', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
                    <button type="button"
                    style="background-color: #8fbcd4; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-tryout-GETapi-search-books"
                    onclick="tryItOut('GETapi-search-books');">Try it out ⚡
            </button>
            <button type="button"
                    style="background-color: #c97a7e; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-canceltryout-GETapi-search-books"
                    onclick="cancelTryOut('GETapi-search-books');" hidden>Cancel 🛑
            </button>&nbsp;&nbsp;
            <button type="submit"
                    style="background-color: #6ac174; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-executetryout-GETapi-search-books"
                    data-initial-text="Send Request 💥"
                    data-loading-text="⏱ Sending..."
                    hidden>Send Request 💥
            </button>
            </h3>
            <p>
            <small class="badge badge-green">GET</small>
            <b><code>api/search/books</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Content-Type</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Content-Type"                data-endpoint="GETapi-search-books"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Accept</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Accept"                data-endpoint="GETapi-search-books"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                        </form>

                    <h2 id="endpoints-GETapi-search">GET api/search</h2>

<p>
</p>



<span id="example-requests-GETapi-search">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request GET \
    --get "http://esra-bibel.local/api/search" \
    --header "Content-Type: application/json" \
    --header "Accept: application/json"</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "http://esra-bibel.local/api/search"
);

const headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "GET",
    headers,
}).then(response =&gt; response.json());</code></pre></div>

</span>

<span id="example-responses-GETapi-search">
            <blockquote>
            <p>Example response (404):</p>
        </blockquote>
                <details class="annotation">
            <summary style="cursor: pointer;">
                <small onclick="textContent = parentElement.parentElement.open ? 'Show headers' : 'Hide headers'">Show headers</small>
            </summary>
            <pre><code class="language-http">cache-control: no-cache, private
content-type: application/json
vary: X-Inertia
access-control-allow-origin: *
set-cookie: XSRF-TOKEN=eyJpdiI6IlBTVFpZcDZ6bmVJN0FtU25pZjhEMlE9PSIsInZhbHVlIjoiK0JLMHdkaEpZbVhaalZ3TmZPQ0Y0L1NxZDRjYnJUcFE1Q1dsNGVrRVdqNnFqN0srSi94MjBwcFdMY3pIbUk2UkZHRURXbTg0UjU0R3lWQy9Db0VCMWNBV09XaTJWam9aUHhRdTRBRldLNmdZOVRqWlJRWURxblhjcnVyOGMxTEQiLCJtYWMiOiJhN2M1MmZmMGUzMzEyMTZhZGU4MmY5N2YxYjQ4M2ZmMmUwN2U4ODRhZmVhYTI1MjNmN2E2OGI1MmZiOTZhNzFkIiwidGFnIjoiIn0%3D; expires=Wed, 09 Apr 2025 13:17:49 GMT; Max-Age=7200; path=/; samesite=lax; esrabibel_session=eyJpdiI6IkZlNDZJNjdFbnE0UGJVb2JkenVBeVE9PSIsInZhbHVlIjoicTZCaU9oRG0rd0t6OGlHUTVCL1c5ZmdsSzJQdFZnYU5RaWVoOTY4RnVGUHFNWm9xaG53U25PMWIvOUFFQ0F2bkt6TzRFUStPcXM0d1Jua2dzQlRWc0IwbllYYXBtTFE5S25FWGd4NmxuS2tTQzgzQ3BoNDZrYVNFN2RsWlpucHoiLCJtYWMiOiI3YTRlNDdmMGU1NjYwYzFmNTJlNWNkZGRjZTkyY2RkYzFjZGY3ZTU3NTI4Y2Y5M2I0NGRjZGUxMWEwYzkwZDI5IiwidGFnIjoiIn0%3D; expires=Wed, 09 Apr 2025 13:17:49 GMT; Max-Age=7200; path=/; httponly; samesite=lax
 </code></pre></details>         <pre>

<code class="language-json" style="max-height: 300px;">&lt;!DOCTYPE html&gt;
&lt;html lang=&quot;de&quot;&gt;

&lt;head&gt;
    &lt;meta charset=&quot;utf-8&quot;&gt;
    &lt;meta name=&quot;viewport&quot; content=&quot;width=device-width, initial-scale=1&quot;&gt;

    &lt;title inertia&gt;&lt;/title&gt;

    &lt;!-- Fonts --&gt;
    &lt;!-- Preconnect to font domains --&gt;
    &lt;link rel=&quot;preconnect&quot; href=&quot;https://fonts.bunny.net&quot; crossorigin&gt;
    &lt;link rel=&quot;preconnect&quot; href=&quot;https://use.typekit.net&quot; as=&quot;font&quot; type=&quot;font/woff2&quot; crossorigin&gt;

    &lt;!-- Preload critical fonts --&gt;
    &lt;link rel=&quot;preload&quot; href=&quot;/fonts/ThanatosText-Book.woff2&quot; as=&quot;font&quot; type=&quot;font/woff2&quot; crossorigin&gt;

    &lt;!-- Load fonts --&gt;
    &lt;link href=&quot;https://fonts.bunny.net/css?family=figtree:400,500,600&amp;display=swap&quot; rel=&quot;stylesheet&quot; /&gt;
    &lt;link rel=&quot;stylesheet&quot; href=&quot;https://use.typekit.net/kzb8yhl.css&quot; media=&quot;print&quot; onload=&quot;this.media=&#039;all&#039;&quot;&gt;

    &lt;!-- Fallback for typekit fonts --&gt;
    &lt;noscript&gt;
        &lt;link rel=&quot;stylesheet&quot; href=&quot;https://use.typekit.net/kzb8yhl.css&quot;&gt;
    &lt;/noscript&gt;

    &lt;!-- Local font definition --&gt;
    &lt;style&gt;
        @font-face {
            font-family: &#039;ThanatosText&#039;;
            src: url(&#039;/fonts/ThanatosText-Book.woff2&#039;) format(&#039;woff2&#039;);
            font-weight: normal;
            font-style: normal;
            font-display: swap;
        }
    &lt;/style&gt;

    &lt;!-- Scripts --&gt;
    &lt;script type=&quot;text/javascript&quot;&gt;Object.assign(Ziggy.routes,{&quot;search.index&quot;:{&quot;uri&quot;:&quot;search&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;search.query&quot;:{&quot;uri&quot;:&quot;search\/{query}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;wheres&quot;:{&quot;query&quot;:&quot;[^\/]+&quot;},&quot;parameters&quot;:[&quot;query&quot;]},&quot;search.paged&quot;:{&quot;uri&quot;:&quot;search\/{query}\/{page?}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;wheres&quot;:{&quot;query&quot;:&quot;[^\/]+&quot;,&quot;page&quot;:&quot;[0-9]+&quot;},&quot;parameters&quot;:[&quot;query&quot;,&quot;page&quot;]},&quot;search.settings&quot;:{&quot;uri&quot;:&quot;search\/settings&quot;,&quot;methods&quot;:[&quot;POST&quot;]},&quot;dashboard&quot;:{&quot;uri&quot;:&quot;dashboard&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;bible.import&quot;:{&quot;uri&quot;:&quot;import-bible&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;bible.import.store&quot;:{&quot;uri&quot;:&quot;import-bible&quot;,&quot;methods&quot;:[&quot;POST&quot;]},&quot;file.upload&quot;:{&quot;uri&quot;:&quot;api\/upload&quot;,&quot;methods&quot;:[&quot;POST&quot;]},&quot;profile.edit&quot;:{&quot;uri&quot;:&quot;profile&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;profile.update&quot;:{&quot;uri&quot;:&quot;profile&quot;,&quot;methods&quot;:[&quot;PATCH&quot;]},&quot;profile.destroy&quot;:{&quot;uri&quot;:&quot;profile&quot;,&quot;methods&quot;:[&quot;DELETE&quot;]},&quot;books.show&quot;:{&quot;uri&quot;:&quot;{reference}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;wheres&quot;:{&quot;reference&quot;:&quot;[\\w\\d\\.,\\-\\+]+&quot;},&quot;parameters&quot;:[&quot;reference&quot;]},&quot;register&quot;:{&quot;uri&quot;:&quot;register&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;login&quot;:{&quot;uri&quot;:&quot;login&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;password.request&quot;:{&quot;uri&quot;:&quot;forgot-password&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;password.email&quot;:{&quot;uri&quot;:&quot;forgot-password&quot;,&quot;methods&quot;:[&quot;POST&quot;]},&quot;password.reset&quot;:{&quot;uri&quot;:&quot;reset-password\/{token}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;parameters&quot;:[&quot;token&quot;]},&quot;password.store&quot;:{&quot;uri&quot;:&quot;reset-password&quot;,&quot;methods&quot;:[&quot;POST&quot;]},&quot;verification.notice&quot;:{&quot;uri&quot;:&quot;verify-email&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;verification.verify&quot;:{&quot;uri&quot;:&quot;verify-email\/{id}\/{hash}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;parameters&quot;:[&quot;id&quot;,&quot;hash&quot;]},&quot;verification.send&quot;:{&quot;uri&quot;:&quot;email\/verification-notification&quot;,&quot;methods&quot;:[&quot;POST&quot;]},&quot;password.confirm&quot;:{&quot;uri&quot;:&quot;confirm-password&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;password.update&quot;:{&quot;uri&quot;:&quot;password&quot;,&quot;methods&quot;:[&quot;PUT&quot;]},&quot;logout&quot;:{&quot;uri&quot;:&quot;logout&quot;,&quot;methods&quot;:[&quot;POST&quot;]},&quot;api.chapters.fetch&quot;:{&quot;uri&quot;:&quot;api\/chapters\/fetch&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;api.books&quot;:{&quot;uri&quot;:&quot;api\/books&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;api.books.content-status&quot;:{&quot;uri&quot;:&quot;api\/books\/content-status&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;api.books.show&quot;:{&quot;uri&quot;:&quot;api\/books\/{slug}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;parameters&quot;:[&quot;slug&quot;]},&quot;api.chapters.adjacent&quot;:{&quot;uri&quot;:&quot;api\/chapters\/{reference}\/adjacent&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;wheres&quot;:{&quot;reference&quot;:&quot;[\\w\\d,\\-]+&quot;},&quot;parameters&quot;:[&quot;reference&quot;]},&quot;api.search.books&quot;:{&quot;uri&quot;:&quot;api\/search\/books&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;api.search&quot;:{&quot;uri&quot;:&quot;api\/search&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;api.bible.text&quot;:{&quot;uri&quot;:&quot;api\/bible\/{reference}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;wheres&quot;:{&quot;reference&quot;:&quot;[\\w\\d,\\-]+&quot;},&quot;parameters&quot;:[&quot;reference&quot;]},&quot;storage.local&quot;:{&quot;uri&quot;:&quot;storage\/{path}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;wheres&quot;:{&quot;path&quot;:&quot;.*&quot;},&quot;parameters&quot;:[&quot;path&quot;]}});&lt;/script&gt;    &lt;script type=&quot;module&quot; src=&quot;http://[::1]:5173/@vite/client&quot;&gt;&lt;/script&gt;&lt;script type=&quot;module&quot; src=&quot;http://[::1]:5173/resources/js/app.ts&quot;&gt;&lt;/script&gt;&lt;script type=&quot;module&quot; src=&quot;http://[::1]:5173/resources/js/Pages/NotFound.vue&quot;&gt;&lt;/script&gt;    &lt;/head&gt;

&lt;body class=&quot;font-sans antialiased&quot;&gt;
    &lt;div id=&quot;app&quot; data-page=&quot;{&amp;quot;component&amp;quot;:&amp;quot;NotFound&amp;quot;,&amp;quot;props&amp;quot;:{&amp;quot;errors&amp;quot;:{},&amp;quot;books&amp;quot;:{&amp;quot;sections&amp;quot;:[{&amp;quot;name&amp;quot;:&amp;quot;Altes Testament&amp;quot;,&amp;quot;books&amp;quot;:[{&amp;quot;id&amp;quot;:1,&amp;quot;name&amp;quot;:&amp;quot;1. Mose&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Mo&amp;quot;,&amp;quot;chapterCount&amp;quot;:50,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;law&amp;quot;,&amp;quot;order&amp;quot;:1,&amp;quot;slug&amp;quot;:&amp;quot;1.Mose&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:2,&amp;quot;name&amp;quot;:&amp;quot;2. Mose&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Mo&amp;quot;,&amp;quot;chapterCount&amp;quot;:40,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;law&amp;quot;,&amp;quot;order&amp;quot;:2,&amp;quot;slug&amp;quot;:&amp;quot;2.Mose&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:3,&amp;quot;name&amp;quot;:&amp;quot;3. Mose&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;3Mo&amp;quot;,&amp;quot;chapterCount&amp;quot;:27,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;law&amp;quot;,&amp;quot;order&amp;quot;:3,&amp;quot;slug&amp;quot;:&amp;quot;3.Mose&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:4,&amp;quot;name&amp;quot;:&amp;quot;4. Mose&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;4Mo&amp;quot;,&amp;quot;chapterCount&amp;quot;:36,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;law&amp;quot;,&amp;quot;order&amp;quot;:4,&amp;quot;slug&amp;quot;:&amp;quot;4.Mose&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:5,&amp;quot;name&amp;quot;:&amp;quot;5. Mose&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;5Mo&amp;quot;,&amp;quot;chapterCount&amp;quot;:34,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;law&amp;quot;,&amp;quot;order&amp;quot;:5,&amp;quot;slug&amp;quot;:&amp;quot;5.Mose&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:6,&amp;quot;name&amp;quot;:&amp;quot;Josua&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Jos&amp;quot;,&amp;quot;chapterCount&amp;quot;:24,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:6,&amp;quot;slug&amp;quot;:&amp;quot;Josua&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:7,&amp;quot;name&amp;quot;:&amp;quot;Richter&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Ri&amp;quot;,&amp;quot;chapterCount&amp;quot;:21,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:7,&amp;quot;slug&amp;quot;:&amp;quot;Richter&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:8,&amp;quot;name&amp;quot;:&amp;quot;Ruth&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Rt&amp;quot;,&amp;quot;chapterCount&amp;quot;:4,&amp;quot;chapters&amp;quot;:[1,2,3,4],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:8,&amp;quot;slug&amp;quot;:&amp;quot;Ruth&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:9,&amp;quot;name&amp;quot;:&amp;quot;1. Samuel&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Sam&amp;quot;,&amp;quot;chapterCount&amp;quot;:31,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:9,&amp;quot;slug&amp;quot;:&amp;quot;1.Samuel&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:10,&amp;quot;name&amp;quot;:&amp;quot;2. Samuel&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Sam&amp;quot;,&amp;quot;chapterCount&amp;quot;:24,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:10,&amp;quot;slug&amp;quot;:&amp;quot;2.Samuel&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:11,&amp;quot;name&amp;quot;:&amp;quot;1. K\u00f6nige&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1K\u00f6n&amp;quot;,&amp;quot;chapterCount&amp;quot;:22,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:11,&amp;quot;slug&amp;quot;:&amp;quot;1.K\u00f6nige&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:12,&amp;quot;name&amp;quot;:&amp;quot;2. K\u00f6nige&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2K\u00f6n&amp;quot;,&amp;quot;chapterCount&amp;quot;:25,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:12,&amp;quot;slug&amp;quot;:&amp;quot;2.K\u00f6nige&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:13,&amp;quot;name&amp;quot;:&amp;quot;1. Chronik&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Chr&amp;quot;,&amp;quot;chapterCount&amp;quot;:29,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:13,&amp;quot;slug&amp;quot;:&amp;quot;1.Chronik&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:14,&amp;quot;name&amp;quot;:&amp;quot;2. Chronik&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Chr&amp;quot;,&amp;quot;chapterCount&amp;quot;:36,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:14,&amp;quot;slug&amp;quot;:&amp;quot;2.Chronik&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:15,&amp;quot;name&amp;quot;:&amp;quot;Esra&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Esra&amp;quot;,&amp;quot;chapterCount&amp;quot;:10,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:15,&amp;quot;slug&amp;quot;:&amp;quot;Esra&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:16,&amp;quot;name&amp;quot;:&amp;quot;Nehemia&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Neh&amp;quot;,&amp;quot;chapterCount&amp;quot;:13,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:16,&amp;quot;slug&amp;quot;:&amp;quot;Nehemia&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:17,&amp;quot;name&amp;quot;:&amp;quot;Esther&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Est&amp;quot;,&amp;quot;chapterCount&amp;quot;:10,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:17,&amp;quot;slug&amp;quot;:&amp;quot;Esther&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:18,&amp;quot;name&amp;quot;:&amp;quot;Hiob&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Hi&amp;quot;,&amp;quot;chapterCount&amp;quot;:42,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;wisdom&amp;quot;,&amp;quot;order&amp;quot;:18,&amp;quot;slug&amp;quot;:&amp;quot;Hiob&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:19,&amp;quot;name&amp;quot;:&amp;quot;Psalmen&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Ps&amp;quot;,&amp;quot;chapterCount&amp;quot;:150,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;wisdom&amp;quot;,&amp;quot;order&amp;quot;:19,&amp;quot;slug&amp;quot;:&amp;quot;Psalmen&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:20,&amp;quot;name&amp;quot;:&amp;quot;Spr\u00fcche&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Spr&amp;quot;,&amp;quot;chapterCount&amp;quot;:31,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;wisdom&amp;quot;,&amp;quot;order&amp;quot;:20,&amp;quot;slug&amp;quot;:&amp;quot;Spr\u00fcche&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:21,&amp;quot;name&amp;quot;:&amp;quot;Prediger&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Pred&amp;quot;,&amp;quot;chapterCount&amp;quot;:12,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;wisdom&amp;quot;,&amp;quot;order&amp;quot;:21,&amp;quot;slug&amp;quot;:&amp;quot;Prediger&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:22,&amp;quot;name&amp;quot;:&amp;quot;Hohelied&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Hl&amp;quot;,&amp;quot;chapterCount&amp;quot;:8,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;wisdom&amp;quot;,&amp;quot;order&amp;quot;:22,&amp;quot;slug&amp;quot;:&amp;quot;Hohelied&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:23,&amp;quot;name&amp;quot;:&amp;quot;Jesaja&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Jes&amp;quot;,&amp;quot;chapterCount&amp;quot;:66,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:23,&amp;quot;slug&amp;quot;:&amp;quot;Jesaja&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:24,&amp;quot;name&amp;quot;:&amp;quot;Jeremia&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Jer&amp;quot;,&amp;quot;chapterCount&amp;quot;:52,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:24,&amp;quot;slug&amp;quot;:&amp;quot;Jeremia&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:25,&amp;quot;name&amp;quot;:&amp;quot;Klagelieder&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Kla&amp;quot;,&amp;quot;chapterCount&amp;quot;:5,&amp;quot;chapters&amp;quot;:[1,2,3,4,5],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;wisdom&amp;quot;,&amp;quot;order&amp;quot;:25,&amp;quot;slug&amp;quot;:&amp;quot;Klagelieder&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:26,&amp;quot;name&amp;quot;:&amp;quot;Hesekiel&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Hes&amp;quot;,&amp;quot;chapterCount&amp;quot;:48,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:26,&amp;quot;slug&amp;quot;:&amp;quot;Hesekiel&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:27,&amp;quot;name&amp;quot;:&amp;quot;Daniel&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Dan&amp;quot;,&amp;quot;chapterCount&amp;quot;:12,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:27,&amp;quot;slug&amp;quot;:&amp;quot;Daniel&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:28,&amp;quot;name&amp;quot;:&amp;quot;Hosea&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Hos&amp;quot;,&amp;quot;chapterCount&amp;quot;:14,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:28,&amp;quot;slug&amp;quot;:&amp;quot;Hosea&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:29,&amp;quot;name&amp;quot;:&amp;quot;Joel&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Joel&amp;quot;,&amp;quot;chapterCount&amp;quot;:3,&amp;quot;chapters&amp;quot;:[1,2,3],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:29,&amp;quot;slug&amp;quot;:&amp;quot;Joel&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:30,&amp;quot;name&amp;quot;:&amp;quot;Amos&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Am&amp;quot;,&amp;quot;chapterCount&amp;quot;:9,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:30,&amp;quot;slug&amp;quot;:&amp;quot;Amos&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:31,&amp;quot;name&amp;quot;:&amp;quot;Obadja&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Ob&amp;quot;,&amp;quot;chapterCount&amp;quot;:1,&amp;quot;chapters&amp;quot;:[1],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:31,&amp;quot;slug&amp;quot;:&amp;quot;Obadja&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:32,&amp;quot;name&amp;quot;:&amp;quot;Jona&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Jon&amp;quot;,&amp;quot;chapterCount&amp;quot;:4,&amp;quot;chapters&amp;quot;:[1,2,3,4],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:32,&amp;quot;slug&amp;quot;:&amp;quot;Jona&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:33,&amp;quot;name&amp;quot;:&amp;quot;Micha&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Mi&amp;quot;,&amp;quot;chapterCount&amp;quot;:7,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:33,&amp;quot;slug&amp;quot;:&amp;quot;Micha&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:34,&amp;quot;name&amp;quot;:&amp;quot;Nahum&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Nah&amp;quot;,&amp;quot;chapterCount&amp;quot;:3,&amp;quot;chapters&amp;quot;:[1,2,3],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:34,&amp;quot;slug&amp;quot;:&amp;quot;Nahum&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:35,&amp;quot;name&amp;quot;:&amp;quot;Habakuk&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Hab&amp;quot;,&amp;quot;chapterCount&amp;quot;:3,&amp;quot;chapters&amp;quot;:[1,2,3],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:35,&amp;quot;slug&amp;quot;:&amp;quot;Habakuk&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:36,&amp;quot;name&amp;quot;:&amp;quot;Zephanja&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Zeph&amp;quot;,&amp;quot;chapterCount&amp;quot;:3,&amp;quot;chapters&amp;quot;:[1,2,3],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:36,&amp;quot;slug&amp;quot;:&amp;quot;Zephanja&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:37,&amp;quot;name&amp;quot;:&amp;quot;Haggai&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Hag&amp;quot;,&amp;quot;chapterCount&amp;quot;:2,&amp;quot;chapters&amp;quot;:[1,2],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:37,&amp;quot;slug&amp;quot;:&amp;quot;Haggai&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:38,&amp;quot;name&amp;quot;:&amp;quot;Sacharja&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Sach&amp;quot;,&amp;quot;chapterCount&amp;quot;:14,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:38,&amp;quot;slug&amp;quot;:&amp;quot;Sacharja&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:39,&amp;quot;name&amp;quot;:&amp;quot;Maleachi&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Mal&amp;quot;,&amp;quot;chapterCount&amp;quot;:4,&amp;quot;chapters&amp;quot;:[1,2,3,4],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:39,&amp;quot;slug&amp;quot;:&amp;quot;Maleachi&amp;quot;,&amp;quot;hasContent&amp;quot;:false}]},{&amp;quot;name&amp;quot;:&amp;quot;Neues Testament&amp;quot;,&amp;quot;books&amp;quot;:[{&amp;quot;id&amp;quot;:40,&amp;quot;name&amp;quot;:&amp;quot;Matth\u00e4us&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Mt&amp;quot;,&amp;quot;chapterCount&amp;quot;:28,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;gospel&amp;quot;,&amp;quot;order&amp;quot;:40,&amp;quot;slug&amp;quot;:&amp;quot;Matth\u00e4us&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:41,&amp;quot;name&amp;quot;:&amp;quot;Markus&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Mk&amp;quot;,&amp;quot;chapterCount&amp;quot;:16,&amp;quot;chapters&amp;quot;:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;gospel&amp;quot;,&amp;quot;order&amp;quot;:41,&amp;quot;slug&amp;quot;:&amp;quot;Markus&amp;quot;,&amp;quot;hasContent&amp;quot;:true},{&amp;quot;id&amp;quot;:42,&amp;quot;name&amp;quot;:&amp;quot;Lukas&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Lk&amp;quot;,&amp;quot;chapterCount&amp;quot;:24,&amp;quot;chapters&amp;quot;:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;gospel&amp;quot;,&amp;quot;order&amp;quot;:42,&amp;quot;slug&amp;quot;:&amp;quot;Lukas&amp;quot;,&amp;quot;hasContent&amp;quot;:true},{&amp;quot;id&amp;quot;:43,&amp;quot;name&amp;quot;:&amp;quot;Die Heilsbotschaft nach Johannes&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Joh&amp;quot;,&amp;quot;chapterCount&amp;quot;:21,&amp;quot;chapters&amp;quot;:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;gospel&amp;quot;,&amp;quot;order&amp;quot;:43,&amp;quot;slug&amp;quot;:&amp;quot;Johannes&amp;quot;,&amp;quot;hasContent&amp;quot;:true},{&amp;quot;id&amp;quot;:44,&amp;quot;name&amp;quot;:&amp;quot;Apostelgeschichte&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Apg&amp;quot;,&amp;quot;chapterCount&amp;quot;:28,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:44,&amp;quot;slug&amp;quot;:&amp;quot;Apostelgeschichte&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:45,&amp;quot;name&amp;quot;:&amp;quot;R\u00f6mer&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;R\u00f6m&amp;quot;,&amp;quot;chapterCount&amp;quot;:16,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:45,&amp;quot;slug&amp;quot;:&amp;quot;R\u00f6mer&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:46,&amp;quot;name&amp;quot;:&amp;quot;1. Korinther&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Kor&amp;quot;,&amp;quot;chapterCount&amp;quot;:16,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:46,&amp;quot;slug&amp;quot;:&amp;quot;1.Korinther&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:47,&amp;quot;name&amp;quot;:&amp;quot;2. Korinther&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Kor&amp;quot;,&amp;quot;chapterCount&amp;quot;:13,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:47,&amp;quot;slug&amp;quot;:&amp;quot;2.Korinther&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:48,&amp;quot;name&amp;quot;:&amp;quot;Galater&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Gal&amp;quot;,&amp;quot;chapterCount&amp;quot;:6,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:48,&amp;quot;slug&amp;quot;:&amp;quot;Galater&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:49,&amp;quot;name&amp;quot;:&amp;quot;Epheser&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Eph&amp;quot;,&amp;quot;chapterCount&amp;quot;:6,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:49,&amp;quot;slug&amp;quot;:&amp;quot;Epheser&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:50,&amp;quot;name&amp;quot;:&amp;quot;Philipper&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Phil&amp;quot;,&amp;quot;chapterCount&amp;quot;:4,&amp;quot;chapters&amp;quot;:[1,2,3,4],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:50,&amp;quot;slug&amp;quot;:&amp;quot;Philipper&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:51,&amp;quot;name&amp;quot;:&amp;quot;Kolosser&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Kol&amp;quot;,&amp;quot;chapterCount&amp;quot;:4,&amp;quot;chapters&amp;quot;:[1,2,3,4],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:51,&amp;quot;slug&amp;quot;:&amp;quot;Kolosser&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:52,&amp;quot;name&amp;quot;:&amp;quot;1. Thessalonicher&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Thes&amp;quot;,&amp;quot;chapterCount&amp;quot;:5,&amp;quot;chapters&amp;quot;:[1,2,3,4,5],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:52,&amp;quot;slug&amp;quot;:&amp;quot;1.Thessalonicher&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:53,&amp;quot;name&amp;quot;:&amp;quot;2. Thessalonicher&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Thes&amp;quot;,&amp;quot;chapterCount&amp;quot;:3,&amp;quot;chapters&amp;quot;:[1,2,3],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:53,&amp;quot;slug&amp;quot;:&amp;quot;2.Thessalonicher&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:54,&amp;quot;name&amp;quot;:&amp;quot;1. Timotheus&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Tim&amp;quot;,&amp;quot;chapterCount&amp;quot;:6,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:54,&amp;quot;slug&amp;quot;:&amp;quot;1.Timotheus&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:55,&amp;quot;name&amp;quot;:&amp;quot;2. Timotheus&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Tim&amp;quot;,&amp;quot;chapterCount&amp;quot;:4,&amp;quot;chapters&amp;quot;:[1,2,3,4],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:55,&amp;quot;slug&amp;quot;:&amp;quot;2.Timotheus&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:56,&amp;quot;name&amp;quot;:&amp;quot;Titus&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Tit&amp;quot;,&amp;quot;chapterCount&amp;quot;:3,&amp;quot;chapters&amp;quot;:[1,2,3],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:56,&amp;quot;slug&amp;quot;:&amp;quot;Titus&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:57,&amp;quot;name&amp;quot;:&amp;quot;Philemon&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Phim&amp;quot;,&amp;quot;chapterCount&amp;quot;:1,&amp;quot;chapters&amp;quot;:[1],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:57,&amp;quot;slug&amp;quot;:&amp;quot;Philemon&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:58,&amp;quot;name&amp;quot;:&amp;quot;Hebr\u00e4er&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Heb&amp;quot;,&amp;quot;chapterCount&amp;quot;:13,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:58,&amp;quot;slug&amp;quot;:&amp;quot;Hebr\u00e4er&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:59,&amp;quot;name&amp;quot;:&amp;quot;Jakobus&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Jak&amp;quot;,&amp;quot;chapterCount&amp;quot;:5,&amp;quot;chapters&amp;quot;:[1,2,3,4,5],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:59,&amp;quot;slug&amp;quot;:&amp;quot;Jakobus&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:60,&amp;quot;name&amp;quot;:&amp;quot;1. Petrus&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Pet&amp;quot;,&amp;quot;chapterCount&amp;quot;:5,&amp;quot;chapters&amp;quot;:[1,2,3,4,5],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:60,&amp;quot;slug&amp;quot;:&amp;quot;1.Petrus&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:61,&amp;quot;name&amp;quot;:&amp;quot;2. Petrus&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Pet&amp;quot;,&amp;quot;chapterCount&amp;quot;:3,&amp;quot;chapters&amp;quot;:[1,2,3],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:61,&amp;quot;slug&amp;quot;:&amp;quot;2.Petrus&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:62,&amp;quot;name&amp;quot;:&amp;quot;1. Johannes&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Joh&amp;quot;,&amp;quot;chapterCount&amp;quot;:5,&amp;quot;chapters&amp;quot;:[1,2,3,4,5],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:62,&amp;quot;slug&amp;quot;:&amp;quot;1.Johannes&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:63,&amp;quot;name&amp;quot;:&amp;quot;2. Johannes&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Joh&amp;quot;,&amp;quot;chapterCount&amp;quot;:1,&amp;quot;chapters&amp;quot;:[1],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:63,&amp;quot;slug&amp;quot;:&amp;quot;2.Johannes&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:64,&amp;quot;name&amp;quot;:&amp;quot;3. Johannes&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;3Joh&amp;quot;,&amp;quot;chapterCount&amp;quot;:1,&amp;quot;chapters&amp;quot;:[1],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:64,&amp;quot;slug&amp;quot;:&amp;quot;3.Johannes&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:65,&amp;quot;name&amp;quot;:&amp;quot;Judas&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Jud&amp;quot;,&amp;quot;chapterCount&amp;quot;:1,&amp;quot;chapters&amp;quot;:[1],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:65,&amp;quot;slug&amp;quot;:&amp;quot;Judas&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:66,&amp;quot;name&amp;quot;:&amp;quot;Offenbarung&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Offb&amp;quot;,&amp;quot;chapterCount&amp;quot;:22,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;apocalypse&amp;quot;,&amp;quot;order&amp;quot;:66,&amp;quot;slug&amp;quot;:&amp;quot;Offenbarung&amp;quot;,&amp;quot;hasContent&amp;quot;:false}]}],&amp;quot;availableBooks&amp;quot;:[{&amp;quot;slug&amp;quot;:&amp;quot;Markus&amp;quot;,&amp;quot;order&amp;quot;:41},{&amp;quot;slug&amp;quot;:&amp;quot;Lukas&amp;quot;,&amp;quot;order&amp;quot;:42},{&amp;quot;slug&amp;quot;:&amp;quot;Johannes&amp;quot;,&amp;quot;order&amp;quot;:43}]},&amp;quot;env&amp;quot;:&amp;quot;local&amp;quot;,&amp;quot;auth&amp;quot;:{&amp;quot;user&amp;quot;:null},&amp;quot;requestedPath&amp;quot;:&amp;quot;api\/search&amp;quot;},&amp;quot;url&amp;quot;:&amp;quot;\/api\/search&amp;quot;,&amp;quot;version&amp;quot;:&amp;quot;18765f3fa436d07c5ef1cbbbc3fa3b37&amp;quot;,&amp;quot;clearHistory&amp;quot;:false,&amp;quot;encryptHistory&amp;quot;:false}&quot;&gt;&lt;/div&gt;&lt;/body&gt;

&lt;/html&gt;
</code>
 </pre>
    </span>
<span id="execution-results-GETapi-search" hidden>
    <blockquote>Received response<span
                id="execution-response-status-GETapi-search"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-GETapi-search"
      data-empty-response-text="<Empty response>" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-GETapi-search" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-GETapi-search">

Tip: Check that you&#039;re properly connected to the network.
If you&#039;re a maintainer of ths API, verify that your API is running and you&#039;ve enabled CORS.
You can check the Dev Tools console for debugging information.</code></pre>
</span>
<form id="form-GETapi-search" data-method="GET"
      data-path="api/search"
      data-authed="0"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('GETapi-search', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
                    <button type="button"
                    style="background-color: #8fbcd4; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-tryout-GETapi-search"
                    onclick="tryItOut('GETapi-search');">Try it out ⚡
            </button>
            <button type="button"
                    style="background-color: #c97a7e; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-canceltryout-GETapi-search"
                    onclick="cancelTryOut('GETapi-search');" hidden>Cancel 🛑
            </button>&nbsp;&nbsp;
            <button type="submit"
                    style="background-color: #6ac174; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-executetryout-GETapi-search"
                    data-initial-text="Send Request 💥"
                    data-loading-text="⏱ Sending..."
                    hidden>Send Request 💥
            </button>
            </h3>
            <p>
            <small class="badge badge-green">GET</small>
            <b><code>api/search</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Content-Type</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Content-Type"                data-endpoint="GETapi-search"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Accept</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Accept"                data-endpoint="GETapi-search"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                        </form>

                    <h2 id="endpoints-GETapi-bible--reference-">Get Bible text based on reference</h2>

<p>
</p>



<span id="example-requests-GETapi-bible--reference-">
<blockquote>Example request:</blockquote>


<div class="bash-example">
    <pre><code class="language-bash">curl --request GET \
    --get "http://esra-bibel.local/api/bible/dww" \
    --header "Content-Type: application/json" \
    --header "Accept: application/json"</code></pre></div>


<div class="javascript-example">
    <pre><code class="language-javascript">const url = new URL(
    "http://esra-bibel.local/api/bible/dww"
);

const headers = {
    "Content-Type": "application/json",
    "Accept": "application/json",
};

fetch(url, {
    method: "GET",
    headers,
}).then(response =&gt; response.json());</code></pre></div>

</span>

<span id="example-responses-GETapi-bible--reference-">
            <blockquote>
            <p>Example response (404):</p>
        </blockquote>
                <details class="annotation">
            <summary style="cursor: pointer;">
                <small onclick="textContent = parentElement.parentElement.open ? 'Show headers' : 'Hide headers'">Show headers</small>
            </summary>
            <pre><code class="language-http">cache-control: no-cache, private
content-type: application/json
vary: X-Inertia
access-control-allow-origin: *
set-cookie: XSRF-TOKEN=eyJpdiI6ImNSc0d1YjVobHBQbEprelhLQnhIdkE9PSIsInZhbHVlIjoicC90ODJ2bVBQVmlrQ1JEL1JOdE5KQUljN0ErbG1yUm42QU5GaGMvc000TUdmaThzVk1RNXJlUWZKb3pPcUNHSFA0TWhuUmk2azJZTGpnSWh0M0Fvckp1U2h0RW5KM3RmK1BrWGhHRmYvWHU3TmtDbmZEUHd4aU1rYXlobTVVdmoiLCJtYWMiOiJkZjg3NzJmMjQ4OGVhMDk5Y2E2MjI1YjE3YmQyYzlmMWIwYTI4NmY0MGQ0MzVhOGVhOTRlN2IxNzc3OWFiNzRmIiwidGFnIjoiIn0%3D; expires=Wed, 09 Apr 2025 13:17:49 GMT; Max-Age=7200; path=/; samesite=lax; esrabibel_session=eyJpdiI6InZaSUVzTktJWFhhb3NpQ2hRaEVieFE9PSIsInZhbHVlIjoiL3JDUU1uRG05YlRzTUlGM1h5Sms0cSt2YjVsUWFneWYzV0JPVjVjeGxaYmJBTDk3NEJicWFTVlI4MnlsT1p4Rm9NVHRMWEFYUFZTK2V2WUpUQ09mSUxVTUc3MVV4T1NRL2l0dzFYa0VBQ29kZ0UwQnJSYXlBd1Y5cHZmem10QkwiLCJtYWMiOiJjYmM0ZjBiNTQzZjUxNTc4OGJjNjg0YjgwOWUzOTcwNjZjZjY5NzY0ODk0ZGUzYmMxMGZlY2VjYjExYzVjNjM5IiwidGFnIjoiIn0%3D; expires=Wed, 09 Apr 2025 13:17:49 GMT; Max-Age=7200; path=/; httponly; samesite=lax
 </code></pre></details>         <pre>

<code class="language-json" style="max-height: 300px;">&lt;!DOCTYPE html&gt;
&lt;html lang=&quot;de&quot;&gt;

&lt;head&gt;
    &lt;meta charset=&quot;utf-8&quot;&gt;
    &lt;meta name=&quot;viewport&quot; content=&quot;width=device-width, initial-scale=1&quot;&gt;

    &lt;title inertia&gt;&lt;/title&gt;

    &lt;!-- Fonts --&gt;
    &lt;!-- Preconnect to font domains --&gt;
    &lt;link rel=&quot;preconnect&quot; href=&quot;https://fonts.bunny.net&quot; crossorigin&gt;
    &lt;link rel=&quot;preconnect&quot; href=&quot;https://use.typekit.net&quot; as=&quot;font&quot; type=&quot;font/woff2&quot; crossorigin&gt;

    &lt;!-- Preload critical fonts --&gt;
    &lt;link rel=&quot;preload&quot; href=&quot;/fonts/ThanatosText-Book.woff2&quot; as=&quot;font&quot; type=&quot;font/woff2&quot; crossorigin&gt;

    &lt;!-- Load fonts --&gt;
    &lt;link href=&quot;https://fonts.bunny.net/css?family=figtree:400,500,600&amp;display=swap&quot; rel=&quot;stylesheet&quot; /&gt;
    &lt;link rel=&quot;stylesheet&quot; href=&quot;https://use.typekit.net/kzb8yhl.css&quot; media=&quot;print&quot; onload=&quot;this.media=&#039;all&#039;&quot;&gt;

    &lt;!-- Fallback for typekit fonts --&gt;
    &lt;noscript&gt;
        &lt;link rel=&quot;stylesheet&quot; href=&quot;https://use.typekit.net/kzb8yhl.css&quot;&gt;
    &lt;/noscript&gt;

    &lt;!-- Local font definition --&gt;
    &lt;style&gt;
        @font-face {
            font-family: &#039;ThanatosText&#039;;
            src: url(&#039;/fonts/ThanatosText-Book.woff2&#039;) format(&#039;woff2&#039;);
            font-weight: normal;
            font-style: normal;
            font-display: swap;
        }
    &lt;/style&gt;

    &lt;!-- Scripts --&gt;
    &lt;script type=&quot;text/javascript&quot;&gt;Object.assign(Ziggy.routes,{&quot;search.index&quot;:{&quot;uri&quot;:&quot;search&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;search.query&quot;:{&quot;uri&quot;:&quot;search\/{query}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;wheres&quot;:{&quot;query&quot;:&quot;[^\/]+&quot;},&quot;parameters&quot;:[&quot;query&quot;]},&quot;search.paged&quot;:{&quot;uri&quot;:&quot;search\/{query}\/{page?}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;wheres&quot;:{&quot;query&quot;:&quot;[^\/]+&quot;,&quot;page&quot;:&quot;[0-9]+&quot;},&quot;parameters&quot;:[&quot;query&quot;,&quot;page&quot;]},&quot;search.settings&quot;:{&quot;uri&quot;:&quot;search\/settings&quot;,&quot;methods&quot;:[&quot;POST&quot;]},&quot;dashboard&quot;:{&quot;uri&quot;:&quot;dashboard&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;bible.import&quot;:{&quot;uri&quot;:&quot;import-bible&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;bible.import.store&quot;:{&quot;uri&quot;:&quot;import-bible&quot;,&quot;methods&quot;:[&quot;POST&quot;]},&quot;file.upload&quot;:{&quot;uri&quot;:&quot;api\/upload&quot;,&quot;methods&quot;:[&quot;POST&quot;]},&quot;profile.edit&quot;:{&quot;uri&quot;:&quot;profile&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;profile.update&quot;:{&quot;uri&quot;:&quot;profile&quot;,&quot;methods&quot;:[&quot;PATCH&quot;]},&quot;profile.destroy&quot;:{&quot;uri&quot;:&quot;profile&quot;,&quot;methods&quot;:[&quot;DELETE&quot;]},&quot;books.show&quot;:{&quot;uri&quot;:&quot;{reference}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;wheres&quot;:{&quot;reference&quot;:&quot;[\\w\\d\\.,\\-\\+]+&quot;},&quot;parameters&quot;:[&quot;reference&quot;]},&quot;register&quot;:{&quot;uri&quot;:&quot;register&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;login&quot;:{&quot;uri&quot;:&quot;login&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;password.request&quot;:{&quot;uri&quot;:&quot;forgot-password&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;password.email&quot;:{&quot;uri&quot;:&quot;forgot-password&quot;,&quot;methods&quot;:[&quot;POST&quot;]},&quot;password.reset&quot;:{&quot;uri&quot;:&quot;reset-password\/{token}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;parameters&quot;:[&quot;token&quot;]},&quot;password.store&quot;:{&quot;uri&quot;:&quot;reset-password&quot;,&quot;methods&quot;:[&quot;POST&quot;]},&quot;verification.notice&quot;:{&quot;uri&quot;:&quot;verify-email&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;verification.verify&quot;:{&quot;uri&quot;:&quot;verify-email\/{id}\/{hash}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;parameters&quot;:[&quot;id&quot;,&quot;hash&quot;]},&quot;verification.send&quot;:{&quot;uri&quot;:&quot;email\/verification-notification&quot;,&quot;methods&quot;:[&quot;POST&quot;]},&quot;password.confirm&quot;:{&quot;uri&quot;:&quot;confirm-password&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;password.update&quot;:{&quot;uri&quot;:&quot;password&quot;,&quot;methods&quot;:[&quot;PUT&quot;]},&quot;logout&quot;:{&quot;uri&quot;:&quot;logout&quot;,&quot;methods&quot;:[&quot;POST&quot;]},&quot;api.chapters.fetch&quot;:{&quot;uri&quot;:&quot;api\/chapters\/fetch&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;api.books&quot;:{&quot;uri&quot;:&quot;api\/books&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;api.books.content-status&quot;:{&quot;uri&quot;:&quot;api\/books\/content-status&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;api.books.show&quot;:{&quot;uri&quot;:&quot;api\/books\/{slug}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;parameters&quot;:[&quot;slug&quot;]},&quot;api.chapters.adjacent&quot;:{&quot;uri&quot;:&quot;api\/chapters\/{reference}\/adjacent&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;wheres&quot;:{&quot;reference&quot;:&quot;[\\w\\d,\\-]+&quot;},&quot;parameters&quot;:[&quot;reference&quot;]},&quot;api.search.books&quot;:{&quot;uri&quot;:&quot;api\/search\/books&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;api.search&quot;:{&quot;uri&quot;:&quot;api\/search&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;]},&quot;api.bible.text&quot;:{&quot;uri&quot;:&quot;api\/bible\/{reference}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;wheres&quot;:{&quot;reference&quot;:&quot;[\\w\\d,\\-]+&quot;},&quot;parameters&quot;:[&quot;reference&quot;]},&quot;storage.local&quot;:{&quot;uri&quot;:&quot;storage\/{path}&quot;,&quot;methods&quot;:[&quot;GET&quot;,&quot;HEAD&quot;],&quot;wheres&quot;:{&quot;path&quot;:&quot;.*&quot;},&quot;parameters&quot;:[&quot;path&quot;]}});&lt;/script&gt;    &lt;script type=&quot;module&quot; src=&quot;http://[::1]:5173/@vite/client&quot;&gt;&lt;/script&gt;&lt;script type=&quot;module&quot; src=&quot;http://[::1]:5173/resources/js/app.ts&quot;&gt;&lt;/script&gt;&lt;script type=&quot;module&quot; src=&quot;http://[::1]:5173/resources/js/Pages/NotFound.vue&quot;&gt;&lt;/script&gt;    &lt;/head&gt;

&lt;body class=&quot;font-sans antialiased&quot;&gt;
    &lt;div id=&quot;app&quot; data-page=&quot;{&amp;quot;component&amp;quot;:&amp;quot;NotFound&amp;quot;,&amp;quot;props&amp;quot;:{&amp;quot;errors&amp;quot;:{},&amp;quot;books&amp;quot;:{&amp;quot;sections&amp;quot;:[{&amp;quot;name&amp;quot;:&amp;quot;Altes Testament&amp;quot;,&amp;quot;books&amp;quot;:[{&amp;quot;id&amp;quot;:1,&amp;quot;name&amp;quot;:&amp;quot;1. Mose&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Mo&amp;quot;,&amp;quot;chapterCount&amp;quot;:50,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;law&amp;quot;,&amp;quot;order&amp;quot;:1,&amp;quot;slug&amp;quot;:&amp;quot;1.Mose&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:2,&amp;quot;name&amp;quot;:&amp;quot;2. Mose&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Mo&amp;quot;,&amp;quot;chapterCount&amp;quot;:40,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;law&amp;quot;,&amp;quot;order&amp;quot;:2,&amp;quot;slug&amp;quot;:&amp;quot;2.Mose&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:3,&amp;quot;name&amp;quot;:&amp;quot;3. Mose&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;3Mo&amp;quot;,&amp;quot;chapterCount&amp;quot;:27,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;law&amp;quot;,&amp;quot;order&amp;quot;:3,&amp;quot;slug&amp;quot;:&amp;quot;3.Mose&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:4,&amp;quot;name&amp;quot;:&amp;quot;4. Mose&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;4Mo&amp;quot;,&amp;quot;chapterCount&amp;quot;:36,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;law&amp;quot;,&amp;quot;order&amp;quot;:4,&amp;quot;slug&amp;quot;:&amp;quot;4.Mose&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:5,&amp;quot;name&amp;quot;:&amp;quot;5. Mose&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;5Mo&amp;quot;,&amp;quot;chapterCount&amp;quot;:34,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;law&amp;quot;,&amp;quot;order&amp;quot;:5,&amp;quot;slug&amp;quot;:&amp;quot;5.Mose&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:6,&amp;quot;name&amp;quot;:&amp;quot;Josua&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Jos&amp;quot;,&amp;quot;chapterCount&amp;quot;:24,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:6,&amp;quot;slug&amp;quot;:&amp;quot;Josua&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:7,&amp;quot;name&amp;quot;:&amp;quot;Richter&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Ri&amp;quot;,&amp;quot;chapterCount&amp;quot;:21,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:7,&amp;quot;slug&amp;quot;:&amp;quot;Richter&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:8,&amp;quot;name&amp;quot;:&amp;quot;Ruth&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Rt&amp;quot;,&amp;quot;chapterCount&amp;quot;:4,&amp;quot;chapters&amp;quot;:[1,2,3,4],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:8,&amp;quot;slug&amp;quot;:&amp;quot;Ruth&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:9,&amp;quot;name&amp;quot;:&amp;quot;1. Samuel&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Sam&amp;quot;,&amp;quot;chapterCount&amp;quot;:31,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:9,&amp;quot;slug&amp;quot;:&amp;quot;1.Samuel&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:10,&amp;quot;name&amp;quot;:&amp;quot;2. Samuel&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Sam&amp;quot;,&amp;quot;chapterCount&amp;quot;:24,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:10,&amp;quot;slug&amp;quot;:&amp;quot;2.Samuel&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:11,&amp;quot;name&amp;quot;:&amp;quot;1. K\u00f6nige&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1K\u00f6n&amp;quot;,&amp;quot;chapterCount&amp;quot;:22,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:11,&amp;quot;slug&amp;quot;:&amp;quot;1.K\u00f6nige&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:12,&amp;quot;name&amp;quot;:&amp;quot;2. K\u00f6nige&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2K\u00f6n&amp;quot;,&amp;quot;chapterCount&amp;quot;:25,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:12,&amp;quot;slug&amp;quot;:&amp;quot;2.K\u00f6nige&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:13,&amp;quot;name&amp;quot;:&amp;quot;1. Chronik&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Chr&amp;quot;,&amp;quot;chapterCount&amp;quot;:29,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:13,&amp;quot;slug&amp;quot;:&amp;quot;1.Chronik&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:14,&amp;quot;name&amp;quot;:&amp;quot;2. Chronik&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Chr&amp;quot;,&amp;quot;chapterCount&amp;quot;:36,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:14,&amp;quot;slug&amp;quot;:&amp;quot;2.Chronik&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:15,&amp;quot;name&amp;quot;:&amp;quot;Esra&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Esra&amp;quot;,&amp;quot;chapterCount&amp;quot;:10,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:15,&amp;quot;slug&amp;quot;:&amp;quot;Esra&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:16,&amp;quot;name&amp;quot;:&amp;quot;Nehemia&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Neh&amp;quot;,&amp;quot;chapterCount&amp;quot;:13,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:16,&amp;quot;slug&amp;quot;:&amp;quot;Nehemia&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:17,&amp;quot;name&amp;quot;:&amp;quot;Esther&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Est&amp;quot;,&amp;quot;chapterCount&amp;quot;:10,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:17,&amp;quot;slug&amp;quot;:&amp;quot;Esther&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:18,&amp;quot;name&amp;quot;:&amp;quot;Hiob&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Hi&amp;quot;,&amp;quot;chapterCount&amp;quot;:42,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;wisdom&amp;quot;,&amp;quot;order&amp;quot;:18,&amp;quot;slug&amp;quot;:&amp;quot;Hiob&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:19,&amp;quot;name&amp;quot;:&amp;quot;Psalmen&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Ps&amp;quot;,&amp;quot;chapterCount&amp;quot;:150,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;wisdom&amp;quot;,&amp;quot;order&amp;quot;:19,&amp;quot;slug&amp;quot;:&amp;quot;Psalmen&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:20,&amp;quot;name&amp;quot;:&amp;quot;Spr\u00fcche&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Spr&amp;quot;,&amp;quot;chapterCount&amp;quot;:31,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;wisdom&amp;quot;,&amp;quot;order&amp;quot;:20,&amp;quot;slug&amp;quot;:&amp;quot;Spr\u00fcche&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:21,&amp;quot;name&amp;quot;:&amp;quot;Prediger&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Pred&amp;quot;,&amp;quot;chapterCount&amp;quot;:12,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;wisdom&amp;quot;,&amp;quot;order&amp;quot;:21,&amp;quot;slug&amp;quot;:&amp;quot;Prediger&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:22,&amp;quot;name&amp;quot;:&amp;quot;Hohelied&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Hl&amp;quot;,&amp;quot;chapterCount&amp;quot;:8,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;wisdom&amp;quot;,&amp;quot;order&amp;quot;:22,&amp;quot;slug&amp;quot;:&amp;quot;Hohelied&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:23,&amp;quot;name&amp;quot;:&amp;quot;Jesaja&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Jes&amp;quot;,&amp;quot;chapterCount&amp;quot;:66,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:23,&amp;quot;slug&amp;quot;:&amp;quot;Jesaja&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:24,&amp;quot;name&amp;quot;:&amp;quot;Jeremia&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Jer&amp;quot;,&amp;quot;chapterCount&amp;quot;:52,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:24,&amp;quot;slug&amp;quot;:&amp;quot;Jeremia&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:25,&amp;quot;name&amp;quot;:&amp;quot;Klagelieder&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Kla&amp;quot;,&amp;quot;chapterCount&amp;quot;:5,&amp;quot;chapters&amp;quot;:[1,2,3,4,5],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;wisdom&amp;quot;,&amp;quot;order&amp;quot;:25,&amp;quot;slug&amp;quot;:&amp;quot;Klagelieder&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:26,&amp;quot;name&amp;quot;:&amp;quot;Hesekiel&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Hes&amp;quot;,&amp;quot;chapterCount&amp;quot;:48,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:26,&amp;quot;slug&amp;quot;:&amp;quot;Hesekiel&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:27,&amp;quot;name&amp;quot;:&amp;quot;Daniel&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Dan&amp;quot;,&amp;quot;chapterCount&amp;quot;:12,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:27,&amp;quot;slug&amp;quot;:&amp;quot;Daniel&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:28,&amp;quot;name&amp;quot;:&amp;quot;Hosea&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Hos&amp;quot;,&amp;quot;chapterCount&amp;quot;:14,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:28,&amp;quot;slug&amp;quot;:&amp;quot;Hosea&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:29,&amp;quot;name&amp;quot;:&amp;quot;Joel&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Joel&amp;quot;,&amp;quot;chapterCount&amp;quot;:3,&amp;quot;chapters&amp;quot;:[1,2,3],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:29,&amp;quot;slug&amp;quot;:&amp;quot;Joel&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:30,&amp;quot;name&amp;quot;:&amp;quot;Amos&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Am&amp;quot;,&amp;quot;chapterCount&amp;quot;:9,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:30,&amp;quot;slug&amp;quot;:&amp;quot;Amos&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:31,&amp;quot;name&amp;quot;:&amp;quot;Obadja&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Ob&amp;quot;,&amp;quot;chapterCount&amp;quot;:1,&amp;quot;chapters&amp;quot;:[1],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:31,&amp;quot;slug&amp;quot;:&amp;quot;Obadja&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:32,&amp;quot;name&amp;quot;:&amp;quot;Jona&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Jon&amp;quot;,&amp;quot;chapterCount&amp;quot;:4,&amp;quot;chapters&amp;quot;:[1,2,3,4],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:32,&amp;quot;slug&amp;quot;:&amp;quot;Jona&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:33,&amp;quot;name&amp;quot;:&amp;quot;Micha&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Mi&amp;quot;,&amp;quot;chapterCount&amp;quot;:7,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:33,&amp;quot;slug&amp;quot;:&amp;quot;Micha&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:34,&amp;quot;name&amp;quot;:&amp;quot;Nahum&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Nah&amp;quot;,&amp;quot;chapterCount&amp;quot;:3,&amp;quot;chapters&amp;quot;:[1,2,3],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:34,&amp;quot;slug&amp;quot;:&amp;quot;Nahum&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:35,&amp;quot;name&amp;quot;:&amp;quot;Habakuk&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Hab&amp;quot;,&amp;quot;chapterCount&amp;quot;:3,&amp;quot;chapters&amp;quot;:[1,2,3],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:35,&amp;quot;slug&amp;quot;:&amp;quot;Habakuk&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:36,&amp;quot;name&amp;quot;:&amp;quot;Zephanja&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Zeph&amp;quot;,&amp;quot;chapterCount&amp;quot;:3,&amp;quot;chapters&amp;quot;:[1,2,3],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:36,&amp;quot;slug&amp;quot;:&amp;quot;Zephanja&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:37,&amp;quot;name&amp;quot;:&amp;quot;Haggai&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Hag&amp;quot;,&amp;quot;chapterCount&amp;quot;:2,&amp;quot;chapters&amp;quot;:[1,2],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:37,&amp;quot;slug&amp;quot;:&amp;quot;Haggai&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:38,&amp;quot;name&amp;quot;:&amp;quot;Sacharja&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Sach&amp;quot;,&amp;quot;chapterCount&amp;quot;:14,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:38,&amp;quot;slug&amp;quot;:&amp;quot;Sacharja&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:39,&amp;quot;name&amp;quot;:&amp;quot;Maleachi&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Mal&amp;quot;,&amp;quot;chapterCount&amp;quot;:4,&amp;quot;chapters&amp;quot;:[1,2,3,4],&amp;quot;testament&amp;quot;:&amp;quot;ot&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;prophecy&amp;quot;,&amp;quot;order&amp;quot;:39,&amp;quot;slug&amp;quot;:&amp;quot;Maleachi&amp;quot;,&amp;quot;hasContent&amp;quot;:false}]},{&amp;quot;name&amp;quot;:&amp;quot;Neues Testament&amp;quot;,&amp;quot;books&amp;quot;:[{&amp;quot;id&amp;quot;:40,&amp;quot;name&amp;quot;:&amp;quot;Matth\u00e4us&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Mt&amp;quot;,&amp;quot;chapterCount&amp;quot;:28,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;gospel&amp;quot;,&amp;quot;order&amp;quot;:40,&amp;quot;slug&amp;quot;:&amp;quot;Matth\u00e4us&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:41,&amp;quot;name&amp;quot;:&amp;quot;Markus&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Mk&amp;quot;,&amp;quot;chapterCount&amp;quot;:16,&amp;quot;chapters&amp;quot;:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;gospel&amp;quot;,&amp;quot;order&amp;quot;:41,&amp;quot;slug&amp;quot;:&amp;quot;Markus&amp;quot;,&amp;quot;hasContent&amp;quot;:true},{&amp;quot;id&amp;quot;:42,&amp;quot;name&amp;quot;:&amp;quot;Lukas&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Lk&amp;quot;,&amp;quot;chapterCount&amp;quot;:24,&amp;quot;chapters&amp;quot;:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;gospel&amp;quot;,&amp;quot;order&amp;quot;:42,&amp;quot;slug&amp;quot;:&amp;quot;Lukas&amp;quot;,&amp;quot;hasContent&amp;quot;:true},{&amp;quot;id&amp;quot;:43,&amp;quot;name&amp;quot;:&amp;quot;Die Heilsbotschaft nach Johannes&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Joh&amp;quot;,&amp;quot;chapterCount&amp;quot;:21,&amp;quot;chapters&amp;quot;:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;gospel&amp;quot;,&amp;quot;order&amp;quot;:43,&amp;quot;slug&amp;quot;:&amp;quot;Johannes&amp;quot;,&amp;quot;hasContent&amp;quot;:true},{&amp;quot;id&amp;quot;:44,&amp;quot;name&amp;quot;:&amp;quot;Apostelgeschichte&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Apg&amp;quot;,&amp;quot;chapterCount&amp;quot;:28,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;history&amp;quot;,&amp;quot;order&amp;quot;:44,&amp;quot;slug&amp;quot;:&amp;quot;Apostelgeschichte&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:45,&amp;quot;name&amp;quot;:&amp;quot;R\u00f6mer&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;R\u00f6m&amp;quot;,&amp;quot;chapterCount&amp;quot;:16,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:45,&amp;quot;slug&amp;quot;:&amp;quot;R\u00f6mer&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:46,&amp;quot;name&amp;quot;:&amp;quot;1. Korinther&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Kor&amp;quot;,&amp;quot;chapterCount&amp;quot;:16,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:46,&amp;quot;slug&amp;quot;:&amp;quot;1.Korinther&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:47,&amp;quot;name&amp;quot;:&amp;quot;2. Korinther&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Kor&amp;quot;,&amp;quot;chapterCount&amp;quot;:13,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:47,&amp;quot;slug&amp;quot;:&amp;quot;2.Korinther&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:48,&amp;quot;name&amp;quot;:&amp;quot;Galater&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Gal&amp;quot;,&amp;quot;chapterCount&amp;quot;:6,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:48,&amp;quot;slug&amp;quot;:&amp;quot;Galater&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:49,&amp;quot;name&amp;quot;:&amp;quot;Epheser&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Eph&amp;quot;,&amp;quot;chapterCount&amp;quot;:6,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:49,&amp;quot;slug&amp;quot;:&amp;quot;Epheser&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:50,&amp;quot;name&amp;quot;:&amp;quot;Philipper&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Phil&amp;quot;,&amp;quot;chapterCount&amp;quot;:4,&amp;quot;chapters&amp;quot;:[1,2,3,4],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:50,&amp;quot;slug&amp;quot;:&amp;quot;Philipper&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:51,&amp;quot;name&amp;quot;:&amp;quot;Kolosser&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Kol&amp;quot;,&amp;quot;chapterCount&amp;quot;:4,&amp;quot;chapters&amp;quot;:[1,2,3,4],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:51,&amp;quot;slug&amp;quot;:&amp;quot;Kolosser&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:52,&amp;quot;name&amp;quot;:&amp;quot;1. Thessalonicher&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Thes&amp;quot;,&amp;quot;chapterCount&amp;quot;:5,&amp;quot;chapters&amp;quot;:[1,2,3,4,5],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:52,&amp;quot;slug&amp;quot;:&amp;quot;1.Thessalonicher&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:53,&amp;quot;name&amp;quot;:&amp;quot;2. Thessalonicher&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Thes&amp;quot;,&amp;quot;chapterCount&amp;quot;:3,&amp;quot;chapters&amp;quot;:[1,2,3],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:53,&amp;quot;slug&amp;quot;:&amp;quot;2.Thessalonicher&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:54,&amp;quot;name&amp;quot;:&amp;quot;1. Timotheus&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Tim&amp;quot;,&amp;quot;chapterCount&amp;quot;:6,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:54,&amp;quot;slug&amp;quot;:&amp;quot;1.Timotheus&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:55,&amp;quot;name&amp;quot;:&amp;quot;2. Timotheus&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Tim&amp;quot;,&amp;quot;chapterCount&amp;quot;:4,&amp;quot;chapters&amp;quot;:[1,2,3,4],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:55,&amp;quot;slug&amp;quot;:&amp;quot;2.Timotheus&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:56,&amp;quot;name&amp;quot;:&amp;quot;Titus&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Tit&amp;quot;,&amp;quot;chapterCount&amp;quot;:3,&amp;quot;chapters&amp;quot;:[1,2,3],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:56,&amp;quot;slug&amp;quot;:&amp;quot;Titus&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:57,&amp;quot;name&amp;quot;:&amp;quot;Philemon&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Phim&amp;quot;,&amp;quot;chapterCount&amp;quot;:1,&amp;quot;chapters&amp;quot;:[1],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:57,&amp;quot;slug&amp;quot;:&amp;quot;Philemon&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:58,&amp;quot;name&amp;quot;:&amp;quot;Hebr\u00e4er&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Heb&amp;quot;,&amp;quot;chapterCount&amp;quot;:13,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:58,&amp;quot;slug&amp;quot;:&amp;quot;Hebr\u00e4er&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:59,&amp;quot;name&amp;quot;:&amp;quot;Jakobus&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Jak&amp;quot;,&amp;quot;chapterCount&amp;quot;:5,&amp;quot;chapters&amp;quot;:[1,2,3,4,5],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:59,&amp;quot;slug&amp;quot;:&amp;quot;Jakobus&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:60,&amp;quot;name&amp;quot;:&amp;quot;1. Petrus&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Pet&amp;quot;,&amp;quot;chapterCount&amp;quot;:5,&amp;quot;chapters&amp;quot;:[1,2,3,4,5],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:60,&amp;quot;slug&amp;quot;:&amp;quot;1.Petrus&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:61,&amp;quot;name&amp;quot;:&amp;quot;2. Petrus&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Pet&amp;quot;,&amp;quot;chapterCount&amp;quot;:3,&amp;quot;chapters&amp;quot;:[1,2,3],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:61,&amp;quot;slug&amp;quot;:&amp;quot;2.Petrus&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:62,&amp;quot;name&amp;quot;:&amp;quot;1. Johannes&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;1Joh&amp;quot;,&amp;quot;chapterCount&amp;quot;:5,&amp;quot;chapters&amp;quot;:[1,2,3,4,5],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:62,&amp;quot;slug&amp;quot;:&amp;quot;1.Johannes&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:63,&amp;quot;name&amp;quot;:&amp;quot;2. Johannes&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;2Joh&amp;quot;,&amp;quot;chapterCount&amp;quot;:1,&amp;quot;chapters&amp;quot;:[1],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:63,&amp;quot;slug&amp;quot;:&amp;quot;2.Johannes&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:64,&amp;quot;name&amp;quot;:&amp;quot;3. Johannes&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;3Joh&amp;quot;,&amp;quot;chapterCount&amp;quot;:1,&amp;quot;chapters&amp;quot;:[1],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:64,&amp;quot;slug&amp;quot;:&amp;quot;3.Johannes&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:65,&amp;quot;name&amp;quot;:&amp;quot;Judas&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Jud&amp;quot;,&amp;quot;chapterCount&amp;quot;:1,&amp;quot;chapters&amp;quot;:[1],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;epistle&amp;quot;,&amp;quot;order&amp;quot;:65,&amp;quot;slug&amp;quot;:&amp;quot;Judas&amp;quot;,&amp;quot;hasContent&amp;quot;:false},{&amp;quot;id&amp;quot;:66,&amp;quot;name&amp;quot;:&amp;quot;Offenbarung&amp;quot;,&amp;quot;shortName&amp;quot;:&amp;quot;Offb&amp;quot;,&amp;quot;chapterCount&amp;quot;:22,&amp;quot;chapters&amp;quot;:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22],&amp;quot;testament&amp;quot;:&amp;quot;nt&amp;quot;,&amp;quot;category&amp;quot;:&amp;quot;apocalypse&amp;quot;,&amp;quot;order&amp;quot;:66,&amp;quot;slug&amp;quot;:&amp;quot;Offenbarung&amp;quot;,&amp;quot;hasContent&amp;quot;:false}]}],&amp;quot;availableBooks&amp;quot;:[{&amp;quot;slug&amp;quot;:&amp;quot;Markus&amp;quot;,&amp;quot;order&amp;quot;:41},{&amp;quot;slug&amp;quot;:&amp;quot;Lukas&amp;quot;,&amp;quot;order&amp;quot;:42},{&amp;quot;slug&amp;quot;:&amp;quot;Johannes&amp;quot;,&amp;quot;order&amp;quot;:43}]},&amp;quot;env&amp;quot;:&amp;quot;local&amp;quot;,&amp;quot;auth&amp;quot;:{&amp;quot;user&amp;quot;:null},&amp;quot;requestedPath&amp;quot;:&amp;quot;api\/bible\/dww&amp;quot;},&amp;quot;url&amp;quot;:&amp;quot;\/api\/bible\/dww&amp;quot;,&amp;quot;version&amp;quot;:&amp;quot;18765f3fa436d07c5ef1cbbbc3fa3b37&amp;quot;,&amp;quot;clearHistory&amp;quot;:false,&amp;quot;encryptHistory&amp;quot;:false}&quot;&gt;&lt;/div&gt;&lt;/body&gt;

&lt;/html&gt;
</code>
 </pre>
    </span>
<span id="execution-results-GETapi-bible--reference-" hidden>
    <blockquote>Received response<span
                id="execution-response-status-GETapi-bible--reference-"></span>:
    </blockquote>
    <pre class="json"><code id="execution-response-content-GETapi-bible--reference-"
      data-empty-response-text="<Empty response>" style="max-height: 400px;"></code></pre>
</span>
<span id="execution-error-GETapi-bible--reference-" hidden>
    <blockquote>Request failed with error:</blockquote>
    <pre><code id="execution-error-message-GETapi-bible--reference-">

Tip: Check that you&#039;re properly connected to the network.
If you&#039;re a maintainer of ths API, verify that your API is running and you&#039;ve enabled CORS.
You can check the Dev Tools console for debugging information.</code></pre>
</span>
<form id="form-GETapi-bible--reference-" data-method="GET"
      data-path="api/bible/{reference}"
      data-authed="0"
      data-hasfiles="0"
      data-isarraybody="0"
      autocomplete="off"
      onsubmit="event.preventDefault(); executeTryOut('GETapi-bible--reference-', this);">
    <h3>
        Request&nbsp;&nbsp;&nbsp;
                    <button type="button"
                    style="background-color: #8fbcd4; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-tryout-GETapi-bible--reference-"
                    onclick="tryItOut('GETapi-bible--reference-');">Try it out ⚡
            </button>
            <button type="button"
                    style="background-color: #c97a7e; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-canceltryout-GETapi-bible--reference-"
                    onclick="cancelTryOut('GETapi-bible--reference-');" hidden>Cancel 🛑
            </button>&nbsp;&nbsp;
            <button type="submit"
                    style="background-color: #6ac174; padding: 5px 10px; border-radius: 5px; border-width: thin;"
                    id="btn-executetryout-GETapi-bible--reference-"
                    data-initial-text="Send Request 💥"
                    data-loading-text="⏱ Sending..."
                    hidden>Send Request 💥
            </button>
            </h3>
            <p>
            <small class="badge badge-green">GET</small>
            <b><code>api/bible/{reference}</code></b>
        </p>
                <h4 class="fancy-heading-panel"><b>Headers</b></h4>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Content-Type</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Content-Type"                data-endpoint="GETapi-bible--reference-"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                                <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>Accept</code></b>&nbsp;&nbsp;
&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="Accept"                data-endpoint="GETapi-bible--reference-"
               value="application/json"
               data-component="header">
    <br>
<p>Example: <code>application/json</code></p>
            </div>
                        <h4 class="fancy-heading-panel"><b>URL Parameters</b></h4>
                    <div style="padding-left: 28px; clear: unset;">
                <b style="line-height: 2;"><code>reference</code></b>&nbsp;&nbsp;
<small>string</small>&nbsp;
 &nbsp;
                <input type="text" style="display: none"
                              name="reference"                data-endpoint="GETapi-bible--reference-"
               value="dww"
               data-component="url">
    <br>
<p>Example: <code>dww</code></p>
            </div>
                    </form>

            

        
    </div>
    <div class="dark-box">
                    <div class="lang-selector">
                                                        <button type="button" class="lang-button" data-language-name="bash">bash</button>
                                                        <button type="button" class="lang-button" data-language-name="javascript">javascript</button>
                            </div>
            </div>
</div>
</body>
</html>
