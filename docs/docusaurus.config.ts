import type * as Preset from '@docusaurus/preset-classic';
import type { Config } from '@docusaurus/types';
import { themes as prismThemes } from 'prism-react-renderer';

const config: Config = {
    title: 'ESB Online Dokumentation',
    tagline: 'Umfassende Dokumentation für die ESB Online Plattform',
    favicon: 'img/favicon.ico',

    // Set the production url of your site here
    url: 'https://esrabibel.gitlab.io',
    // Set the /<baseUrl>/ pathname under which your site is served
    baseUrl: '/',

    // GitHub pages deployment config.
    organizationName: 'ebtc',
    projectName: 'esrabibel',

    onBrokenLinks: 'warn',
    onBrokenMarkdownLinks: 'warn',

    i18n: {
        defaultLocale: 'de',
        locales: ['de', 'en'],
    },

    presets: [
        [
            'classic',
            {
                docs: {
                    sidebarPath: './sidebars.ts',
                    editUrl:
                        'https://gitlab.com/esra-bibel/esb-online/-/tree/main/docs',
                },
                theme: {
                    customCss: './src/css/custom.css',
                },
            } satisfies Preset.Options,
        ],
    ],

    plugins: [
        [
            '@docusaurus/plugin-content-docs',
            {
                id: 'web',
                path: 'web',
                routeBasePath: 'web',
                sidebarPath: './sidebars-web.ts',
            },
        ],
        [
            '@docusaurus/plugin-content-docs',
            {
                id: 'components',
                path: 'components',
                routeBasePath: 'components',
                sidebarPath: './sidebars-components.ts',
            },
        ],
        [
            '@docusaurus/plugin-content-docs',
            {
                id: 'libs',
                path: 'libs',
                routeBasePath: 'libs',
                sidebarPath: './sidebars-libs.ts',
            },
        ],
    ],

    themeConfig: {
        // Replace with your project's social card
        image: 'img/esb-social-card.jpg',
        navbar: {
            title: 'ESB Dokumentation',
            logo: {
                alt: 'ESB Logo',
                src: 'img/logo.svg',
            },
            items: [
                {
                    type: 'docSidebar',
                    sidebarId: 'tutorialSidebar',
                    position: 'left',
                    label: 'Anleitung',
                },
                {
                    to: '/web/intro',
                    label: 'Web App',
                    position: 'left',
                },
                {
                    to: '/components/intro',
                    label: 'Komponenten',
                    position: 'left',
                },
                {
                    to: '/libs/intro',
                    label: 'Bibliotheken',
                    position: 'left',
                },
                {
                    type: 'localeDropdown',
                    position: 'right',
                },
                {
                    href: 'https://gitlab.com/esra-bibel/esb-online',
                    label: 'GitLab',
                    position: 'right',
                },
            ],
        },
        footer: {
            style: 'dark',
            links: [
                {
                    title: 'Dokumentation',
                    items: [
                        {
                            label: 'Anleitung',
                            to: '/docs/intro',
                        },
                        {
                            label: 'Web App',
                            to: '/web/intro',
                        },
                        {
                            label: 'API',
                            to: '/web/api/intro',
                        },
                    ],
                },
                {
                    title: 'Community',
                    items: [
                        {
                            label: 'GitLab',
                            href: 'https://gitlab.com/esra-bibel/esb-online',
                        },
                    ],
                },
            ],
            copyright: `Copyright ${new Date().getFullYear()} ESB Online. Erstellt mit Docusaurus.`,
        },
        prism: {
            theme: prismThemes.github,
            darkTheme: prismThemes.dracula,
            additionalLanguages: ['php', 'bash', 'typescript', 'jsx', 'tsx'],
        },
    },
} satisfies Config;

export default config;
