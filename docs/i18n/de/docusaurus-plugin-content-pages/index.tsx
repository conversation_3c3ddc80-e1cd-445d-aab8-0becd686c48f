import React from 'react';
import clsx from 'clsx';
import Link from '@docusaurus/Link';
import useDocusaurusContext from '@docusaurus/useDocusaurusContext';
import Layout from '@theme/Layout';
import Heading from '@theme/Heading';

import styles from '@site/src/pages/index.module.css';

function HomepageHeader() {
  const {siteConfig} = useDocusaurusContext();
  return (
    <header className={clsx('hero hero--primary', styles.heroBanner)}>
      <div className="container">
        <Heading as="h1" className="hero__title">
          {siteConfig.title}
        </Heading>
        <p className="hero__subtitle">{siteConfig.tagline}</p>
        <div className={styles.buttons}>
          <Link
            className="button button--secondary button--lg"
            to="/docs/intro">
            Dokumentation anzeigen
          </Link>
        </div>
      </div>
    </header>
  );
}

export default function Home(): React.ReactElement {
  const {siteConfig} = useDocusaurusContext();
  return (
    <Layout
      title={`${siteConfig.title}`}
      description="Dokumentation für die ESB Online Plattform">
      <HomepageHeader />
      <main>
        <section className={styles.features}>
          <div className="container">
            <div className="row">
              <div className={clsx('col col--4')}>
                <div className="text--center padding-horiz--md">
                  <h3>Web App</h3>
                  <p>
                    Dokumentation für die Laravel + Vue 3 Web-Anwendung
                  </p>
                  <Link
                    className="button button--secondary button--sm"
                    to="/web/intro">
                    Mehr erfahren
                  </Link>
                </div>
              </div>
              <div className={clsx('col col--4')}>
                <div className="text--center padding-horiz--md">
                  <h3>API</h3>
                  <p>
                    API-Referenz für die ESB Online Plattform
                  </p>
                  <Link
                    className="button button--secondary button--sm"
                    to="/web/api/intro">
                    Mehr erfahren
                  </Link>
                </div>
              </div>
              <div className={clsx('col col--4')}>
                <div className="text--center padding-horiz--md">
                  <h3>Bibliotheken</h3>
                  <p>
                    Dokumentation für die gemeinsam genutzten Bibliotheken
                  </p>
                  <Link
                    className="button button--secondary button--sm"
                    to="/libs/intro">
                    Mehr erfahren
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
    </Layout>
  );
}
