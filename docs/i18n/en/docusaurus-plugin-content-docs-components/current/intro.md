---
id: intro
title: Vue Components
sidebar_position: 1
---

# Vue Components

This documentation describes the Vue 3 components used in the ESB Online platform. The components are written in TypeScript and use the Composition API.

## Component Structure

The components are divided into different categories:

### UI Components

Basic UI components used throughout the application:

- Buttons
- Forms
- Navigation
- Modals

### Bible Components

Specialized components for Bible display and navigation:

- BibleReader: Main component for displaying Bible text
- BibleNavigation: Component for navigating between books, chapters, and verses
- BibleSearch: Component for searching the Bible
