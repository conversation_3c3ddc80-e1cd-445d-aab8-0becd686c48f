---
id: intro
title: ESB Online Documentation
sidebar_position: 1
---

# ESB Online Documentation

Welcome to the comprehensive documentation for the ESB Online platform. This documentation covers all aspects of the platform, including the web application, API, components, and shared libraries.

## Documentation Structure

The documentation is organized into several sections:

- **Guide**: General information about the platform
- **Web App**: Documentation for the Laravel + Vue 3 web application
- **API**: API reference documentation
- **Components**: Vue component documentation
- **Libraries**: Documentation for shared libraries

## Getting Started

To get started with the ESB Online platform, please refer to the installation and configuration guides:

- [Installation](/docs/installation)
- [Configuration](/docs/configuration)

## Development

For development information, please refer to the following sections:

- [Web App Development](/web/intro)
- [API Development](/web/api/intro)
- [Component Development](/components/intro)
- [Library Development](/libs/intro)

## Contributing

We welcome contributions to the ESB Online platform. Please refer to the [contribution guide](/docs/intro#contributing) for more information.
