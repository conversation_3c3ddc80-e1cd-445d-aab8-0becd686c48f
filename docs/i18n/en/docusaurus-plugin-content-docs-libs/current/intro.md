---
id: intro
title: Shared Libraries
sidebar_position: 1
---

# Shared Libraries

This documentation describes the shared libraries (libs) of the ESB Online platform. These libraries provide reusable functions, types, and constants that are used by various applications within the monorepo.

## Type System

The core library's type system has been optimized for better tree-shakeability:

1. Types are organized in a modular structure with explicit named exports
2. Each category of types (Bible, Common, Display, Search, Text) is exported from its own module
3. Enums are exported separately from types

### Key Types

The type system includes the following main categories:

#### Bible

Types for Bible structures:

```typescript
// Example of Bible types
export interface BaseBook {
  id: string;
  name: string;
  testament: Testament;
  category: BookCategory;
  originalLanguage: OriginalLanguage;
}

export interface Book extends BaseBook {
  chapters: number[];
}

export interface Chapter {
  id: string;
  bookId: string;
  number: number;
  verses: Verse[];
}

export interface Verse {
  id: string;
  chapterId: string;
  number: number;
  text: string;
  words: Word[];
  has_text_variant: boolean;
}
```

#### Display

Types for display and navigation:

```typescript
export interface NavigationState {
  bookId: string;
  chapterNumber: number;
  verseNumber?: number;
  verseReference?: string;
}

export interface ChapterWindow {
  id: string;
  bookId: string;
  chapterNumber: number;
  content: string;
}
```

### Key Enums

```typescript
export enum Testament {
  OT = 'OT',
  NT = 'NT',
}

export enum BookCategory {
  LAW = 'LAW',
  HISTORY = 'HISTORY',
  WISDOM = 'WISDOM',
  PROPHETS_MAJOR = 'PROPHETS_MAJOR',
  PROPHETS_MINOR = 'PROPHETS_MINOR',
  GOSPELS = 'GOSPELS',
  ACTS = 'ACTS',
  PAULINE = 'PAULINE',
  GENERAL = 'GENERAL',
  REVELATION = 'REVELATION',
}

export enum OriginalLanguage {
  HEBREW = 'HEBREW',
  GREEK = 'GREEK',
  ARAMAIC = 'ARAMAIC',
  MIXED = 'MIXED',
}
```

## Variant Handler

The Bible text parser handles variants using the VariantHandler class:

1. In USX XML format, variants are marked with `<ms>` tags:
   - Start: `<ms sid="variant_id" type="va" />`
   - End: `<ms eid="variant_id" />`

2. The VariantHandler:
   - Processes these milestone elements to track variant sections
   - Generates a unique ID for each variant: `var_` + md5 hash of sid + verse ID
   - Marks verses with variants by setting `has_text_variant = true`
   - Supports nested variants using a stack-based approach

This structure ensures that bundlers can properly tree-shake unused types and enums.
