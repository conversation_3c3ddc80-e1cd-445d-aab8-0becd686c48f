---
id: intro
title: Web Application
sidebar_position: 1
---

# ESB Online Web Application

This documentation describes the ESB Online web application, developed with Laravel 11 and Vue 3.

## Architecture

The web application follows a modern architecture:

- **Backend**: Laravel 11 with PHP 8.3 and strict typing
- **Frontend**: Vue 3 with TypeScript and Composition API
- **State Management**: Pinia for reactive and type-safe state management
- **Routing**: Inertia.js for server-side rendering with SPA experience

## Main Components

The application consists of several main components:

### Bible Navigation

The Bible navigation allows users to navigate through the Bible. It uses the BibleNavigationStore, which manages the current state of navigation:

```typescript
// Example of using the BibleNavigationStore
const bibleStore = useBibleNavigationStore();

// Load chapter
bibleStore.loadChapter('GEN', 1);

// Scroll to a verse
bibleStore.scrollToVerse('GEN', 1, 1);
```

### Bible Display

The Bible display shows the Bible text and manages scrolling between chapters. It uses scroll management functions to determine the most visible section.

### Search

The search function allows users to search the Bible and display results.

## Development Guidelines

The following guidelines are followed in the development of the web application:

- **TDD approach**: Tests are written before implementation
- **Strict typing**: TypeScript and PHP 8.3 with strict typing
- **Documentation**: All components and functions are documented
- **Accessibility**: Semantic HTML and ARIA attributes for better accessibility
