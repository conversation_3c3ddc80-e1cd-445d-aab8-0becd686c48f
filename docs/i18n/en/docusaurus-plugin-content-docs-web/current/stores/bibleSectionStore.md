---
id: bibleSectionStore
title: bible-section Store
sidebar_position: 2
---

# bible-section Store

Bible sections and navigation

## Examples

## State

```typescript
interface State {
  chapters: Map<string, Section>;
  detailedBooks: Map<string, Book>;
  detailedBooksLoading: Map<string, boolean>;
  currentBook: Book | null;
  currentChapter: number | null;
  currentVerse: number | null;
  currentVerseEnd: number | null; // Add support for verse ranges
  verseRanges: Array<{ start: number; end: number }>; // Store complex verse ranges
  
  // Loading states
  isLoadingNext: boolean;
  isLoadingPrevious: boolean;
  scrollHandlingEnabled: boolean;
  isInitialized: boolean;
  isInitialLoad: boolean;
  currentBookHasContent: boolean;
  footnoteState: {
    footnote: Footnote;
    word: string;
    reference: string;
    x: number;
    y: number;
    isClickLocked: boolean;
  } | null;
}
```

### chapters

**Type:** `Map<string, Section>`

State property chapters

### detailedBooks

**Type:** `Map<string, Book>`

State property detailedBooks

### detailedBooksLoading

**Type:** `Map<string, boolean>`

State property detailedBooksLoading

### currentBook

**Type:** `Book | null`

State property currentBook

### currentChapter

**Type:** `number | null`

State property currentChapter

### currentVerse

**Type:** `number | null`

State property currentVerse

### currentVerseEnd

**Type:** `number | null`

State property currentVerseEnd

### verseRanges

**Type:** `Array<{ start: number; end: number }>`

State property verseRanges

### isLoadingNext

**Type:** `boolean`

State property isLoadingNext

### isLoadingPrevious

**Type:** `boolean`

State property isLoadingPrevious

### scrollHandlingEnabled

**Type:** `boolean`

State property scrollHandlingEnabled

### isInitialized

**Type:** `boolean`

State property isInitialized

### isInitialLoad

**Type:** `boolean`

State property isInitialLoad

### currentBookHasContent

**Type:** `boolean`

State property currentBookHasContent

### footnoteState

**Type:** `{ footnote: Footnote; word: string; reference: string; x: number; y: number; isClickLocked: boolean; } | null`

State property footnoteState

## Getters

### Get

Getter for Get

**Returns:** `unknown` - Returns the Get

## Actions
