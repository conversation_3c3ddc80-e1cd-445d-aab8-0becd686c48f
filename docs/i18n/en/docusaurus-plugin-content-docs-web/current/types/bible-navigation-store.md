---
id: bible-navigation-store
title: Bible Navigation Store
sidebar_position: 2
---

# Bible Navigation Store

The Bible Navigation Store is a Pinia store that manages the navigation state for the Bible application.

## Key Features

1. It uses TypeScript with strict typing
2. It handles chapter navigation, verse references, and scroll handling
3. It interacts with bibleDataStore and bibleUrlStore
4. Important methods:
   - setVerseReference: Stores the current verse reference string
   - enableScrollHandling/disableScrollHandling: Controls whether scroll events should be processed
   - updateCurrentViewportChapter: Updates the current chapter and loads adjacent chapters

## State

```typescript
interface BibleNavigationState {
  currentBook: Book | null;
  currentChapter: number | null;
  currentVerse: number | null;
  verseReference: string | null;
  scrollHandlingEnabled: boolean;
}
```

## Actions

```typescript
// Navigate to a specific book and chapter
navigateToChapter(bookSlug: string, chapterNumber: number): Promise<void>

// Navigate to a specific verse
navigateToVerse(bookSlug: string, chapterNumber: number, verseNumber: number): Promise<void>

// Set the current verse reference string
setVerseReference(reference: string): void

// Enable or disable scroll handling
enableScrollHandling(): void
disableScrollHandling(): void

// Update the current viewport chapter
updateCurrentViewportChapter(chapter: Chapter): void
```
