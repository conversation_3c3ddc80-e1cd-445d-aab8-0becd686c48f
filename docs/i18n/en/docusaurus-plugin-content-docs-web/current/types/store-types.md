---
id: store-types
title: Store Types
sidebar_position: 3
---

# Store Types

This module contains types for Pinia stores in the web application.

## BibleNavigationStore

```typescript
interface BibleNavigationStore {
  currentBook: Book | null;
  currentChapter: Chapter | null;
  currentVerse: Verse | null;
  verseReference: string | null;
  isScrollHandlingEnabled: boolean;
  
  // Methods
  setCurrentBook(book: Book): void;
  setCurrentChapter(chapter: Chapter): void;
  setCurrentVerse(verse: Verse): void;
  setVerseReference(reference: string): void;
  enableScrollHandling(): void;
  disableScrollHandling(): void;
  updateCurrentViewportChapter(chapter: Chapter): void;
}
```

## BibleDataStore

```typescript
interface BibleDataStore {
  books: Book[];
  currentBook: Book | null;
  isLoading: boolean;
  
  // Methods
  loadBooks(): Promise<void>;
  loadBook(slug: string): Promise<Book>;
  loadChapter(bookSlug: string, chapterNumber: number): Promise<Chapter>;
}
```
