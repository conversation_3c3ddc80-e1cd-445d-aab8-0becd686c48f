# Bibelbuch-Identifikationssystem

Dieses Dokument erläutert, wie Bücher im gesamten ESB-Online-System identifiziert werden.

## Datenbankstruktur

In der Datenbank hat jedes Buch drei eindeutige Kennungen:

1. **`id`**: Auto-Increment-Primärschlüssel (nur für interne Datenbanknutzung)
2. **`order`**: Numerische Position in der Bibel (1 für Genesis, 2 für Exodus, usw.)
3. **`slug`**: URL-freundliche Zeichenkettenkennung (z.B. "gen" für Genesis)

## Identifikationsstandards

Für die Konsistenz in der gesamten Anwendung verwenden wir die folgenden Standards:

### URLs und benutzerseitige Referenzen
- **Verwenden Sie `slug`** für alle URLs und benutzerseitigen Referenzen
- Beispiel: `/Johannes/3` für Johannes Kapitel 3

### Interne Buchidentifikation
- **Verwenden Sie `order`** für alle internen Buchidentifikationen
- Dies ist die numerische Position des Buches in der Bibel
- In TypeScript wird dies als `order`-Eigenschaft der `Book`-Schnittstelle gespeichert

### API-Kommunikation
- **Parameterbezeichnung**: Verwenden Sie `bookId`, um auf die `order` des Buches zu verweisen
- **Validierung**: Überprüfen Sie, dass `bookId` in der Spalte `books.order` existiert

## Typdefinitionen

In TypeScript definiert die `BaseBook`-Schnittstelle:

```typescript
interface BaseBook {
    /**
     * Die eindeutige Kennung des Buches (entspricht dem 'id'-Feld in der Datenbank)
     */
    id: number;
    /**
     * Die Reihenfolge des Buches in der Bibel (numerische Position)
     */
    order: number;
    slug: string;
    name: string;
    number: number;
    chapterCount: number;
}
```

## Navigation zwischen Büchern

Bei der Navigation zwischen Büchern:

1. Das Frontend verwendet die `order` des Buches, um das nächste/vorherige Buch anzufordern
2. Das Backend findet das Buch anhand seines `order`-Feldes
3. Die Antwort enthält den `slug` des Buches für die URL-Konstruktion

## Implementierungshinweise

- In der Datenbank ist das Feld `order` für effiziente Nachschlagungen indiziert
- Das Feld `slug` wird für menschenlesbare URLs verwendet
- Das Auto-Increment-Feld `id` wird nur für interne Datenbankbeziehungen verwendet

## Beispielablauf

1. Benutzer navigiert zu `/Johannes/3` (Johannes Kapitel 3)
2. Backend analysiert URL, um Buch-Slug "Johannes" und Kapitel 3 zu erhalten
3. Backend findet Buch mit Slug "Johannes" und lädt Kapitel 3
4. Wenn der Benutzer auf "Nächstes Kapitel" klickt, sendet das Frontend eine Anfrage mit `bookId: 43` (Johannes' Reihenfolge) und `chapter: 4`
5. Backend findet Buch mit Reihenfolge 43 und lädt Kapitel 4
6. Antwort enthält Buch-Slug "Johannes" für URL-Konstruktion
7. Frontend aktualisiert URL zu `/Johannes/4`
