import Link from '@docusaurus/Link';
import Translate, { translate } from '@docusaurus/Translate';
import useDocusaurusContext from '@docusaurus/useDocusaurusContext';
import Heading from '@theme/Heading';
import Layout from '@theme/Layout';
import clsx from 'clsx';
import React from 'react';

import styles from './index.module.css';

function HomepageHeader() {
    const { siteConfig } = useDocusaurusContext();
    return (
        <header className={clsx('hero hero--primary', styles.heroBanner)}>
            <div className="container">
                <Heading as="h1" className="hero__title">
                    {siteConfig.title}
                </Heading>
                <p className="hero__subtitle">
                    <Translate>
                        Dokumentation für die ESB Online Plattform
                    </Translate>
                </p>
                <div className={styles.buttons}>
                    <Link
                        className="button button--secondary button--lg"
                        to="/docs/intro"
                    >
                        <Translate><PERSON><PERSON><PERSON></Translate>
                    </Link>
                </div>
            </div>
        </header>
    );
}

export default function Home(): React.ReactElement {
    const { siteConfig } = useDocusaurusContext();
    return (
        <Layout
            title={`${siteConfig.title}`}
            description={translate({
                message: 'Dokumentation für die ESB Online Plattform',
            })}
        >
            <HomepageHeader />
            <main>
                <section className={styles.features}>
                    <div className="container">
                        <div className="row">
                            <div className={clsx('col col--4')}>
                                <div className="text--center padding-horiz--md">
                                    <h3>
                                        <Translate>Web App</Translate>
                                    </h3>
                                    <p>
                                        <Translate>
                                            Dokumentation für die Laravel + Vue
                                            3 Web-Anwendung
                                        </Translate>
                                    </p>
                                    <Link
                                        className="button button--secondary button--sm"
                                        to="/web/intro"
                                    >
                                        <Translate>Mehr erfahren</Translate>
                                    </Link>
                                </div>
                            </div>
                            <div className={clsx('col col--4')}>
                                <div className="text--center padding-horiz--md">
                                    <h3>
                                        <Translate>API</Translate>
                                    </h3>
                                    <p>
                                        <Translate>
                                            API-Referenz für die ESB Online
                                            Plattform
                                        </Translate>
                                    </p>
                                    <Link
                                        className="button button--secondary button--sm"
                                        to="/web/api/intro"
                                    >
                                        <Translate>Mehr erfahren</Translate>
                                    </Link>
                                </div>
                            </div>
                            <div className={clsx('col col--4')}>
                                <div className="text--center padding-horiz--md">
                                    <h3>
                                        <Translate>Bibliotheken</Translate>
                                    </h3>
                                    <p>
                                        <Translate>
                                            Dokumentation für die gemeinsam
                                            genutzten Bibliotheken
                                        </Translate>
                                    </p>
                                    <Link
                                        className="button button--secondary button--sm"
                                        to="/libs/intro"
                                    >
                                        <Translate>Mehr erfahren</Translate>
                                    </Link>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </main>
        </Layout>
    );
}
