import type { SidebarsConfig } from '@docusaurus/plugin-content-docs';

const sidebars: SidebarsConfig = {
  libsSidebar: [
    {
      type: 'category',
      label: 'Bibliotheken',
      items: ['intro'],
    },
    {
      type: 'category',
      label: 'Typen',
      items: [
        'README',
        {
          type: 'category',
          label: 'Bibel-Typen',
          items: ['bible-types'],
        },
        {
          type: 'category',
          label: 'Allgemeine Typen',
          items: ['common-types'],
        },
        {
          type: 'category',
          label: 'Anzeige-Typen',
          items: ['display-types'],
        },
        {
          type: 'category',
          label: 'Such-Typen',
          items: ['search-types'],
        },
        {
          type: 'category',
          label: 'Text-Typen',
          items: ['text-types'],
        },
        'enums',
      ],
    },
  ],
};

export default sidebars;
