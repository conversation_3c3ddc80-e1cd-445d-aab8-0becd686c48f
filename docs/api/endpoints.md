---
id: endpoints
title: API-Endpunkte
sidebar_position: 2
---

# API-Endpunkte

Die ESB Online-Plattform bietet mehrere API-Endpunkte für den Zugriff auf Bibeldaten und Benutzerinformationen. Nachfolgend finden Sie eine Zusammenfassung der verfügbaren Endpunkte.

## Bibeldaten

### Bücher abrufen

```
GET /api/books
```

Gibt eine Liste aller Bibelbücher zurück.

### Buch nach Slug abrufen

```
GET /api/books/{slug}
```

Gibt Details für ein bestimmtes Bibelbuch zurück, das durch seinen Slug identifiziert wird.

### Bücher durchsuchen

```
GET /api/books/search
```

Durchsucht Bibelbücher basierend auf Abfrageparametern.

### Kapitel abrufen

```
GET /api/chapters/fetch
```

Ruft Kapitel für ein bestimmtes Buch ab.

### Bibelreferenz abrufen

```
GET /api/bible/{reference}
```

Ruft Bibelinhalt basierend auf einer Referenzzeichenfolge ab (z.B. "GEN.1.1").

## Benutzerinformationen

### Benutzer abrufen

```
GET /api/user
```

Gibt Informationen über den authentifizierten Benutzer zurück.

## Datei-Upload

### Datei hochladen

```
POST /api/upload
```

Lädt eine Datei auf den Server hoch.

## Vollständige API-Dokumentation

Für die vollständige API-Dokumentation mit Anfrage-/Antwortbeispielen und interaktiven Tests, siehe die [vollständige API-Dokumentation](/api/full-documentation).
