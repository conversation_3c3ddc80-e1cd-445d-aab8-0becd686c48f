---
id: full-documentation
title: Full API Documentation
sidebar_position: 4
---

# Full API Documentation

The complete API documentation is generated using Scribe and provides detailed information about all endpoints, including:

- Request parameters
- Response formats
- Example requests and responses
- Interactive API testing

<div className="admonition admonition-info alert alert--info">
<div className="admonition-heading">
<h5>Interactive Documentation</h5>
</div>
<div className="admonition-content">
<p>
  The full API documentation is interactive and allows you to test API endpoints directly from your browser.
  Click the button below to access the complete documentation.
</p>
</div>
</div>

<div className="text--center margin-bottom--lg">
  <a href="/api-docs/index.html" className="button button--primary button--lg" target="_blank" rel="noopener noreferrer">
    View Full API Documentation
  </a>
</div>

## API Specification

The API is also available in OpenAPI format, which can be used with tools like Postman or Swagger UI:

<div className="text--center margin-bottom--lg">
  <a href="/api-docs/openapi.yaml" className="button button--secondary button--lg" target="_blank" rel="noopener noreferrer">
    Download OpenAPI Specification
  </a>
</div>

## Integrating with the API

### Example: Fetching Bible Books

```typescript
// Example using fetch API
async function fetchBibleBooks() {
  try {
    const response = await fetch('https://api.esrabibel.de/api/books');
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching Bible books:', error);
    return null;
  }
}
```

### Example: Fetching a Specific Chapter

```typescript
// Example using axios
import axios from 'axios';

async function fetchChapter(bookId: number, chapterNumber: number) {
  try {
    const response = await axios.get('https://api.esrabibel.de/api/chapters/fetch', {
      params: {
        bookId,
        chapterNumber
      }
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching chapter:', error);
    return null;
  }
}
```

## Keeping API Documentation Up-to-Date

The API documentation is automatically generated from the Laravel backend code using Scribe. When the API changes, the documentation is updated by running:

```bash
php artisan scribe:generate
```

This ensures that the documentation always reflects the current state of the API.
