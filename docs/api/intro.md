---
id: intro
title: API Dokumentation
sidebar_position: 1
---

# API Dokumentation

Willkommen zur API-Dokumentation für die ESB Online Plattform. Diese Dokumentation wird automatisch aus dem Laravel-Backend generiert und bietet eine umfassende Referenz für alle verfügbaren API-Endpunkte.

## Authentifizierung

Die API verwendet Laravel Sanctum für die Authentifizierung. Um auf geschützte Endpunkte zuzugreifen, müssen Sie einen gültigen API-Token in den Authorization-Header einfügen:

```
Authorization: Bearer {your-token}
```

## Verfügbare Endpunkte

Die API bietet Endpunkte für verschiedene Funktionen:

- Bibel-Daten (Bücher, Kapitel, Verse)
- Suche
- Benutzer und Authentifizierung
- Notizen und Markierungen

Die vollständige API-Dokumentation wird mit Scribe generiert und enthält detaillierte Informationen zu jedem Endpunkt, einsch<PERSON><PERSON><PERSON> Anforderungs- und Antwortbeispielen.
