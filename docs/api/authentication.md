---
id: authentication
title: Authentifizierung
sidebar_position: 3
---

# API Authentifizierung

Die ESB Online API verwendet Laravel Sanctum für die Authentifizierung, das ein einfaches tokenbasiertes API-Authentifizierungssystem bietet.

## API-Tokens erhalten

Um die API nutzen zu können, benötigen Sie einen API-Token. Dies kann über die Authentifizierungs-Endpunkte erfolgen:

```
POST /api/login
```

**Anfrage-Body:**
```json
{
  "email": "<EMAIL>",
  "password": "your-password"
}
```

**Antwort:**
```json
{
  "token": "your-api-token",
  "user": {
    "id": 1,
    "name": "User Name",
    "email": "<EMAIL>"
  }
}
```

## API-Tokens verwenden

Sobald Sie einen Token erhalten haben, können Sie ihn in Ihren API-Anfragen verwenden, indem Sie ihn im Authorization-Header einfügen:

```
Authorization: Bearer your-api-token
```

## Token-Ablauf

API-Tokens sind so konfiguriert, dass sie nach einer bestimmten Zeit ablaufen. Wenn ein Token abläuft, müssen Sie einen neuen anfordern.

## CSRF-Schutz

Für Webanwendungen, die Anfragen an die API von derselben Domain aus stellen, ist der CSRF-Schutz aktiviert. Bevor Sie Anfragen stellen, sollten Sie eine Anfrage an den CSRF-Endpunkt senden:

```
GET /sanctum/csrf-cookie
```

Dadurch wird ein CSRF-Cookie gesetzt, das automatisch in nachfolgenden Anfragen enthalten ist.
