import type { SidebarsConfig } from '@docusaurus/plugin-content-docs';

const sidebars: SidebarsConfig = {
  webSidebar: [
    'intro',
    'bible-navigation-flow',
    {
      type: 'category',
      label: 'Speicher',
      link: {
        type: 'doc',
        id: 'stores/index',
      },
      items: [
        'stores/index',
        {
          type: 'category',
          label: 'Bibel-Speicher',
          items: [
            'stores/bibleDataStore',
            'stores/bibleSectionStore',
            'stores/bibleHighlightStore',
            'stores/bibleMemoryStore',
          ],
        },
        'stores/searchStore',
        'stores/searchSettingsStore',
        'stores/textSettingsStore',
      ],
    },
    {
      type: 'category',
      label: 'Composables',
      link: {
        type: 'doc',
        id: 'composables/index',
      },
      items: [
        'composables/index',
        'composables/useDebounce',
        'composables/useDropdown',
        'composables/useScrollManager',
        'composables/useSearchResults',
        'composables/useTextSettings',
        'composables/useThrottle',
        'composables/useVerseReference',
      ],
    },
    {
      type: 'category',
      label: 'API',
      link: {
        type: 'doc',
        id: 'api/intro',
      },
      items: [
        'api/intro',
        'api/authentication',
        'api/endpoints',
        'api/full-documentation',
      ],
    },
    {
      type: 'category',
      label: 'Typen',
      link: {
        type: 'doc',
        id: 'types/readme',
      },
      items: [
        'types/readme',
        'types/store-types',
      ],
    },
    {
      type: 'category',
      label: 'Komponenten',
      link: {
        type: 'doc',
        id: 'components/index',
      },
      items: [
        'components/index',
        {
          type: 'category',
          label: 'Bibelanzeige',
          link: {
            type: 'doc',
            id: 'components/bibledisplay/index',
          },
          items: [
            'components/bibledisplay/index',
            'components/bibledisplay/ChapterContent',
            'components/bibledisplay/ChapterNumber',
            'components/bibledisplay/ChapterWrapper',
            'components/bibledisplay/FootnoteContent',
            'components/bibledisplay/FootnoteTooltip',
            'components/bibledisplay/FrontMatter',
            'components/bibledisplay/InfoItem',
            'components/bibledisplay/InfoSection',
            'components/bibledisplay/NumberSelector',
            'components/bibledisplay/ReferenceSelector',
            'components/bibledisplay/TextFormatDropdown',
            'components/bibledisplay/UnavailableBookNotice',
            'components/bibledisplay/VerseContent',
            'components/bibledisplay/VerseNumber',
            'components/bibledisplay/WordGroupContainer',
          ],
        },
        {
          type: 'category',
          label: 'Navigationsleiste',
          link: {
            type: 'doc',
            id: 'components/navigation/index',
          },
          items: [
            'components/navigation/index',
            'components/navigation/BibleBookDropdown',
            'components/navigation/BibleBookOffcanvas',
            'components/navigation/BibleBookSelector',
            'components/navigation/MobileSearchOverlay',
            'components/navigation/NavLink',
            'components/navigation/NavigationBar',
            'components/navigation/OffcanvasSidebar',
            'components/navigation/ResponsiveNavLink',
            'components/navigation/SubNavigationBar',
          ],
        },
        {
          type: 'category',
          label: 'Suche',
          link: {
            type: 'doc',
            id: 'components/search/index',
          },
          items: [
            'components/search/index',
            'components/search/BibleSearch',
            'components/search/SearchResultItem',
            'components/search/SearchTypeDropdown',
          ],
        },
        {
          type: 'category',
          label: 'Allgemein',
          link: {
            type: 'doc',
            id: 'components/common/index',
          },
          items: [
            'components/common/index',
            'components/common/ApplicationLogo',
            'components/common/Checkbox',
            'components/common/DangerButton',
            'components/common/Dropdown',
            'components/common/DropdownLink',
            'components/common/ErrorBoundary',
            'components/common/InputError',
            'components/common/InputLabel',
            'components/common/LoadingSpinner',
            'components/common/Modal',
            'components/common/Overlay',
            'components/common/PrimaryButton',
            'components/common/SecondaryButton',
            'components/common/TextInput',
          ],
        },
      ],
    },
  ],
};

export default sidebars;
