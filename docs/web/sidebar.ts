
module.exports = {
  webSidebar: [
    {
      type: 'category',
      label: 'Overview',
      items: ['intro'],
    },
    {
      type: 'category',
      label: 'Stores',
      items: [
        'stores/index',
        'stores/searchSettingsStore',
        'stores/searchStore',
        'stores/textSettingsStore',
        'stores/bibleDataStore',
        'stores/bibleHighlightStore',
        'stores/bibleMemoryStore',
        'stores/bibleSectionStore'
      ],
    },
    {
      type: 'category',
      label: 'Composables',
      items: [
        'composables/index',
        'composables/useDebounce',
        'composables/useDropdown',
        'composables/useLocalStorage',
        'composables/useScrollManager',
        'composables/useSearchResults',
        'composables/useSettingsAside',
        'composables/useTextSettings',
        'composables/useThrottle',
        'composables/useVerseReference'
      ],
    },
    {
      type: 'category',
      label: 'Types',
      items: ['types/README', 'types/store-types'],
    },
    {
      type: 'category',
      label: 'API',
      items: ['api/intro'],
    },
  ],
};
