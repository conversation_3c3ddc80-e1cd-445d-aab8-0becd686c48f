---
sidebar_position: 2
title: Bibelnavigationsablauf
---

# Bibelnavigationsablauf

Dieses Dokument beschreibt den Ausführungsablauf bei der Navigation zu einer Bibelkapitel-URL (z.B. `/Johannes1`) und beim Scrollen durch Kapitel.

## Inhaltsverzeichnis

1. [Initiale URL-Navigation](#initiale-url-navigation)
2. [Scrollen zwischen Kapiteln](#scrollen-zwischen-kapiteln)
3. [Schlüsselkomponenten und ihre Verantwortlichkeiten](#schlüsselkomponenten-und-ihre-verantwortlichkeiten)
4. [Store-Interaktionen](#store-interaktionen)
5. [API-Interaktionen](#api-interaktionen)
6. [Potenzielle Probleme und Debugging](#potenzielle-probleme-und-debugging)
7. [DOM-Aktualisierungsprozess für neue Kapitel](#dom-aktualisierungsprozess-für-neue-kapitel)

## Initiale URL-Navigation

Wenn eine URL wie `/Johannes1` aufger<PERSON>en wird, erfolgt die folgende Sequenz:

### 1. Route-Handling

```text
URL: /Johannes1
  ↓
Laravel Router
  ↓
Inertia.js Render
  ↓
Display.vue Component
```

### 2. Display.vue Komponenten-Initialisierung

Die Bibel-Leseerfahrung beginnt mit der `Display.vue`-Komponente, die als Haupteinstiegspunkt dient:

```typescript
// Display.vue
// Props und Store-Initialisierung
const props = defineProps<DisplayResponse>(); // Enthält anfängliche Abschnitte und Navigationszustand
const bibleStore = useBibleStore();
const textSettings = useTextSettingsStore();

// Scroll-Handling-Einrichtung
const {
    chapterRefs,
    setChapterRef,
    handleScroll,
    scrollToVerse,
    scrollToVerseRange,
    scrollToChapter,
    enableScrollHandling,
} = useScrollManager({
    onChapterVisible: (chapterId) => {
        bibleStore.setCurrentViewportChapterId(chapterId);
        bibleStore.loadAdjacentChapters(chapterId);
    }
});
```

### 3. Initiales Laden von Daten

```typescript
// Display.vue - onMounted
onMounted(() => {
    // Initialisiert den Bibel-Store mit den anfänglichen Daten vom Server
    bibleStore.initializeChapters(
        props.sections[0] as ChapterSection,
        props.chapterWindow
    );
    
    // Behandelt Versreferenzen in der URL, wenn vorhanden
    if (props.navigation.verseReference) {
        const { startVerse, endVerse } = parseVerseReference(props.navigation.verseReference);
        if (startVerse && endVerse) {
            scrollToVerseRange(startVerse, endVerse);
        } else if (startVerse) {
            scrollToVerse(startVerse);
        }
    }
    
    // Aktiviert Scroll-Handling nach anfänglicher Einrichtung
    nextTick(() => {
        enableScrollHandling();
    });
});
```

## Scrollen zwischen Kapiteln

Wenn ein Benutzer durch den Bibelinhalt scrollt, erfolgt folgender Prozess:

### 1. Scroll-Event-Handling

```typescript
// useScrollManager.ts
function handleScroll() {
    if (!scrollHandlingEnabled.value) return;
    
    // Findet das am besten sichtbare Kapitel im Viewport
    const mostVisibleChapterId = findMostVisibleChapter();
    
    if (mostVisibleChapterId && mostVisibleChapterId !== currentChapterId.value) {
        currentChapterId.value = mostVisibleChapterId;
        onChapterVisible(mostVisibleChapterId);
    }
}
```

### 2. Sichtbarkeitsberechnung

```typescript
// useScrollManager.ts
function findMostVisibleChapter() {
    let maxVisibleHeight = 0;
    let mostVisibleChapterId: string | null = null;

    chapterRefs.value.forEach((el, id) => {
        if (!el) return;
        
        const rect = el.getBoundingClientRect();
        const windowHeight = window.innerHeight;
        
        // Berechnet, wie viel des Elements im Viewport sichtbar ist
        const visibleTop = Math.max(0, rect.top);
        const visibleBottom = Math.min(windowHeight, rect.bottom);
        const visibleHeight = Math.max(0, visibleBottom - visibleTop);
        
        if (visibleHeight > maxVisibleHeight) {
            maxVisibleHeight = visibleHeight;
            mostVisibleChapterId = id;
        }
    });
    
    return mostVisibleChapterId;
}
```

### 3. Store-Aktualisierung und Laden angrenzender Kapitel

```typescript
// bibleSectionStore.ts
function setCurrentViewportChapterId(chapterId: string) {
    this.currentViewportChapterId = chapterId;
    
    // Aktualisiert die URL, um das aktuelle Kapitel widerzuspiegeln
    const section = this.chapters.get(chapterId);
    if (section && this.isChapterSection(section)) {
        const { book, chapter } = section;
        router.visit(`/${book.slug}${chapter}`, {
            preserveState: true,
            preserveScroll: true,
            replace: true,
        });
    }
}

function loadAdjacentChapters(chapterId: string) {
    // Lädt Kapitel, die an das aktuelle Kapitel angrenzen
    const section = this.chapters.get(chapterId);
    if (!section || !this.isChapterSection(section)) return;
    
    const { book, chapter } = section;
    
    // Überprüft, ob weitere Kapitel geladen werden müssen
    const hasNextChapter = this.chapters.has(`${book.slug}${chapter + 1}`);
    const hasPrevChapter = this.chapters.has(`${book.slug}${chapter - 1}`);
    
    if (!hasNextChapter) {
        this.fetchChapters({
            bookId: book.id,
            chapter: chapter,
            direction: 'next'
        });
    }
    
    if (!hasPrevChapter && chapter > 1) {
        this.fetchChapters({
            bookId: book.id,
            chapter: chapter,
            direction: 'previous'
        });
    }
}
```

## Schlüsselkomponenten und ihre Verantwortlichkeiten

### Display.vue
- Hauptcontainer für die Bibel-Leseerfahrung
- Initialisiert Stores und Composables
- Richtet Scroll-Handling ein
- Rendert Kapitelkomponenten

### ChapterWrapper.vue
- Container für ein einzelnes Kapitel
- Behandelt kapitelspezifisches Rendering und Interaktionen
- Registriert sich beim Scroll-Manager über Refs

### useScrollManager Composable
- Verfolgt die Sichtbarkeit von Kapiteln
- Behandelt Scroll-Events
- Bietet Funktionen zum programmatischen Scrollen
- Benachrichtigt Stores über Änderungen im Viewport

### useBibleStore (bibleSectionStore.ts)
- Verwaltet den Zustand aller geladenen Bibelabschnitte
- Aktualisiert die URL basierend auf Änderungen im Viewport
- Lädt Kapitel über API-Aufrufe
- Verwaltet die Kapitel-Datenstruktur

## Store-Interaktionen

Die Bibel-Navigations-Systematik basiert auf mehreren Stores, die zusammenarbeiten:

### bibleSectionStore
- Verwaltet den Zustand aller geladenen Bibelabschnitte
- Lädt Kapitel über API-Aufrufe
- Aktualisiert die URL basierend auf Änderungen im Viewport

### bibleDataStore
- Verwaltet Buch- und Kapitel-Daten
- Behandelt API-Aufrufe zum Laden von Kapiteln
- Stellt Kapitel-Inhalt für den Section-Store bereit

### bibleNavigationStore
- Verwaltet den Navigationszustand (aktuelles Buch, Kapitel, Vers)
- Koordiniert mit dem URL-Manager für URL-Updates
- Speichert Navigationshistorie

## API-Interaktionen

Kapitel-Daten werden über API-Aufrufe geladen:

```typescript
// bibleDataStore.ts
async function fetchChapters({
    bookId,
    chapter,
    direction
}: {
    bookId: number;
    chapter: number;
    direction: 'previous' | 'next' | 'exact';
}) {
    // Fügt zur Lade-Queue hinzu, um doppelte Anfragen zu vermeiden
    const queueId = `${bookId}-${chapter}-${direction}`;
    if (this.loadingQueue.includes(queueId)) return;
    
    this.loadingQueue.push(queueId);
    
    try {
        const response = await axios.get('/api/chapters/fetch', {
            params: {
                book_id: bookId,
                chapter: chapter,
                direction: direction
            }
        });
        
        // Verarbeitet neue Kapitel
        const newSections = this.filterNewChapters(response.data.sections);
        
        // Fügt Kapitel zum Store hinzu
        newSections.forEach(section => {
            this.addChapter(section);
        });
        
        // Entfernt aus der Lade-Queue
        const queueIndex = this.loadingQueue.indexOf(queueId);
        if (queueIndex !== -1) {
            this.loadingQueue.splice(queueIndex, 1);
        }
        
        return {
            sections: newSections,
            navigation: response.data.navigation,
            hasMore: response.data.has_more
        };
    } catch (error) {
        logger.error('Fehler beim Laden von Kapiteln:', error);
        
        // Entfernt aus der Lade-Queue
        const queueIndex = this.loadingQueue.indexOf(queueId);
        if (queueIndex !== -1) {
            this.loadingQueue.splice(queueIndex, 1);
        }
    }
}
```

## DOM-Aktualisierungsprozess für neue Kapitel

Wenn neue Kapitel geladen werden, wird der DOM wie folgt aktualisiert:

1. Der Bibel-Store fügt die neuen Kapitel-Daten hinzu
2. Die Display.vue-Komponente rendert reaktiv neue ChapterWrapper-Komponenten
3. Die ChapterWrapper-Komponenten registrieren sich beim Scroll-Manager
4. Der Scroll-Manager beginnt, die Sichtbarkeit der neuen Kapitel zu verfolgen

```vue
<!-- Display.vue -->
<template>
    <div 
        class="bible-content" 
        @scroll="handleScroll"
        :style="contentStyle"
    >
        <ChapterWrapper
            v-for="section in visibleSections"
            :key="section.id"
            :section="section"
            :ref="el => setChapterRef(getChapterId(section), el)"
        />
    </div>
</template>
```

## Potenzielle Probleme und Debugging

### 1. Scroll-Probleme

Wenn das Scrollen nicht richtig funktioniert:

- Überprüfen Sie, ob `scrollHandlingEnabled` auf `true` gesetzt ist
- Überprüfen Sie, ob die Kapitelreferenzen korrekt registriert sind
- Überprüfen Sie, ob die Sichtbarkeitsberechnungen korrekte Werte zurückgeben

### 2. Kapitel werden nicht geladen

Wenn Kapitel nicht richtig geladen werden:

- Überprüfen Sie den Netzwerk-Tab auf fehlgeschlagene API-Anfragen
- Überprüfen Sie, ob die Funktion `loadAdjacentChapters` aufgerufen wird
- Überprüfen Sie, ob die Kapitel-IDs korrekt generiert werden

### 3. URL-Updates

Wenn die URL nicht richtig aktualisiert wird:

- Überprüfen Sie, ob die Router-Navigation in `setCurrentViewportChapterId` funktioniert
- Überprüfen Sie, ob die aktuelle Kapitel-ID korrekt gesetzt ist
- Überprüfen Sie, ob Fehler im Zusammenhang mit der Navigation im Konsolen-Log vorhanden sind
