---
id: searchSettingsStore
title: search-settings Store
sidebar_position: 2
---

# search-settings Store

Search configuration and settings






## Examples



## State

```typescript
interface State {

}
```



## Getters

### getTypes

/** Available search types with their translations /

**Returns:** `(state) => state.types,

        /**
         * Available search types with their translations
         * @returns`




## Actions


