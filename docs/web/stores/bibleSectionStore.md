---
id: bibleSectionStore
title: bible-section Store
sidebar_position: 2
---

# bible-section Store

Bible sections and navigation






## Examples



## State

```typescript
interface State {
  chapters: new Map<string;
  detailedBooks: new Map<string;
  detailedBooksLoading: new Map<string;
  currentBook: Book | null;
  currentChapter: number | null;
  currentVerse: number | null;
  currentVerseEnd: number | null;
  verseRanges: Array<{ start: number; end: number }>;
  isLoadingNext: false;
  isLoadingPrevious: false;
  scrollHandlingEnabled: true;
  isInitialized: false;
  isInitialLoad: true;
  currentBookHasContent: true;
  footnoteState: {;
}
```

### chapters

**Type:** `new Map<string`

State property chapters


### detailedBooks

**Type:** `new Map<string`

State property detailedBooks


### detailedBooksLoading

**Type:** `new Map<string`

State property detailedBooksLoading


### currentBook

**Type:** `Book | null`

State property currentBook


### currentChapter

**Type:** `number | null`

State property currentChapter


### currentVerse

**Type:** `number | null`

State property currentVerse


### currentVerseEnd

**Type:** `number | null`

State property currentVerseEnd


### verseRanges

**Type:** `Array<{ start: number; end: number }>`

State property verseRanges


### isLoadingNext

**Type:** `false`

State property isLoadingNext


### isLoadingPrevious

**Type:** `false`

State property isLoadingPrevious


### scrollHandlingEnabled

**Type:** `true`

State property scrollHandlingEnabled


### isInitialized

**Type:** `false`

State property isInitialized


### isInitialLoad

**Type:** `true`

State property isInitialLoad


### currentBookHasContent

**Type:** `true`

State property currentBookHasContent


### footnoteState

**Type:** `{`

State property footnoteState



## Getters

### Get

Getter for Get

**Returns:** `unknown`




## Actions


