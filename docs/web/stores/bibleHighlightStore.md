---
id: bibleHighlightStore
title: bible-highlight Store
sidebar_position: 2
---

# bible-highlight Store

/** Store for managing word highlighting functionality /






## Examples



## State

```typescript
interface State {
  highlightedWords: new Set<string>();
  highlightedVerses: new Set<string>();
  highlightedChapters: new Set<string>();
  searchTerm: '';
  isHighlighting: false;
  highlightTimeout: number | null;
}
```

### highlightedWords

**Type:** `new Set<string>()`

/** highlighted words for the store /


### highlightedVerses

**Type:** `new Set<string>()`

/** highlighted words for the store /


### highlightedChapters

**Type:** `new Set<string>()`

/** highlighted verses for the store /


### searchTerm

**Type:** `''`

/** highlighted chapters for the store /


### isHighlighting

**Type:** `false`

/** search term for the store /


### highlightTimeout

**Type:** `number | null`

/** is highlighting for the store /



## Getters

### hasHighlights

Getter for hasHighlights

**Returns:** `boolean`




## Actions

### setSearchTerm

/** Set the search term and highlight matching words /

**Parameters:**

- `term`: `string` - Parameter term

**Returns:** `void` - 

