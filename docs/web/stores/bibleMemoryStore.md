---
id: bibleMemoryStore
title: bible-memory Store
sidebar_position: 2
---

# bible-memory Store

/** Store for managing memory usage and chapter cleanup /






## Examples



## State

```typescript
interface State {
  maxChaptersInMemory: 10;
  isCleaningUp: false;
  lastCleanupTime: 0;
  cleanupInterval: 60000;
}
```

### maxChaptersInMemory

**Type:** `10`

/** max chapters in memory for the store /


### isCleaningUp

**Type:** `false`

/** max chapters in memory for the store /


### lastCleanupTime

**Type:** `0`

/** is cleaning up for the store /


### cleanupInterval

**Type:** `60000`

/** last cleanup time for the store /



## Getters



## Actions


