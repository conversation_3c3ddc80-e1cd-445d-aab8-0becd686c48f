---
id: index
title: Stores Overview
sidebar_position: 1
---

# Stores Overview

This section contains documentation for all the Pinia stores used in the ESB Online web application.
Stores are used to manage the application state and provide a centralized place for data management.

## Available Stores

### [search-settings](searchSettingsStore)

Search configuration and settings

**State Properties:** 0  
**Getters:** 1  
**Actions:** 0


### [search](searchStore)

Bible search functionality and results

**State Properties:** 2  
**Getters:** 0  
**Actions:** 0


### [textSettings](textSettingsStore)

Text display settings and preferences

**State Properties:** 0  
**Getters:** 0  
**Actions:** 0


### [bible-data](bibleDataStore)

/** Store for managing Bible data (books, chapters) /

**State Properties:** 7  
**Getters:** 3  
**Actions:** 0


### [bible-highlight](bibleHighlightStore)

/** Store for managing word highlighting functionality /

**State Properties:** 6  
**Getters:** 1  
**Actions:** 1


### [bible-memory](bibleMemoryStore)

/** Store for managing memory usage and chapter cleanup /

**State Properties:** 4  
**Getters:** 0  
**Actions:** 0


### [bible-section](bibleSectionStore)

Bible sections and navigation

**State Properties:** 15  
**Getters:** 1  
**Actions:** 0

