---
id: bibleDataStore
title: bible-data Store
sidebar_position: 2
---

# bible-data Store

/** Store for managing Bible data (books, chapters) /






## Examples



## State

```typescript
interface State {
  string: any;
  chapters: new Map<string;
  string: any;
  books: new Map<number;
  bookOrder: string[];
  loadingQueue: string[];
  currentViewportChapterId: '';
}
```

### string

**Type:** `any`

State property string


### chapters

**Type:** `new Map<string`

/** Map of chapter sections indexed by chapter ID /


### string

**Type:** `any`

State property string


### books

**Type:** `new Map<number`

/** Map of chapter sections indexed by chapter ID /


### bookOrder

**Type:** `string[]`

/** Map of Bible books indexed by book ID /


### loadingQueue

**Type:** `string[]`

/** Array of book slugs in canonical order /


### currentViewportChapterId

**Type:** `''`

/** Queue of chapter IDs to be loaded /



## Getters

### getCurrentViewportChapter

Getter for getCurrentViewportChapter

**Returns:** `Section | null`



### getVisibleChapters

Getter for getVisibleChapters

**Returns:** `Section[]`



### getBookBySlug

Getter for getBookBySlug

**Returns:** `(slug: string) => Book | undefined`




## Actions


