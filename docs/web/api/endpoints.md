---
id: endpoints
title: API Endpoints
sidebar_position: 2
---

# API Endpoints

The ESB Online platform provides several API endpoints for accessing Bible data and user information. Below is a summary of the available endpoints.

## Bible Data

### Get Books

```
GET /api/books
```

Returns a list of all Bible books.

### Get Book by Slug

```
GET /api/books/{slug}
```

Returns details for a specific Bible book identified by its slug.

### Search Books

```
GET /api/books/search
```

Searches for Bible books based on query parameters.

### Get Chapters

```
GET /api/chapters/fetch
```

Fetches chapters for a specific book.

### Get Bible Reference

```
GET /api/bible/{reference}
```

Retrieves Bible content based on a reference string (e.g., "GEN.1.1").

## User Information

### Get User

```
GET /api/user
```

Returns information about the authenticated user.

## File Upload

### Upload File

```
POST /api/upload
```

Uploads a file to the server.

## Complete API Documentation

For complete API documentation with request/response examples and interactive testing, please refer to the [full API documentation](/web/api/full-documentation).
