---
id: authentication
title: Authentication
sidebar_position: 3
---

# API Authentication

The ESB Online API uses Laravel Sanctum for authentication, which provides a simple token-based API authentication system.

## Obtaining API Tokens

To use the API, you need to obtain an API token. This can be done through the authentication endpoints:

```
POST /api/login
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "your-password"
}
```

**Response:**
```json
{
  "token": "your-api-token",
  "user": {
    "id": 1,
    "name": "User Name",
    "email": "<EMAIL>"
  }
}
```

## Using API Tokens

Once you have obtained a token, you can use it in your API requests by including it in the Authorization header:

```
Authorization: Bearer your-api-token
```

## Token Expiration

API tokens are configured to expire after a certain period. When a token expires, you will need to request a new one.

## CSRF Protection

For web applications making requests to the API from the same domain, CSRF protection is enabled. Before making any requests, you should make a request to the CSRF endpoint:

```
GET /sanctum/csrf-cookie
```

This will set a CSRF cookie that will be automatically included in subsequent requests.
