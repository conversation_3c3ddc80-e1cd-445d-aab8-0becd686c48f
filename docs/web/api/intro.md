---
id: intro
title: Web App API
sidebar_position: 1
---

# Web App API Documentation

Willkommen zur API-Dokumentation für die ESB Online Web-Anwendung. Diese Dokumentation wird automatisch aus dem Laravel-Backend generiert und bietet eine umfassende Referenz für alle verfügbaren API-Endpunkte.

## Authentifizierung

Die API verwendet Laravel Sanctum für die Authentifizierung. Um auf geschützte Endpunkte zuzugreifen, müssen Sie einen gültigen API-Token in den Authorization-Header einfügen:

```
Authorization: Bearer {your-token}
```

## Verfügbare Endpunkte

Die API bietet Endpunkte für verschiedene Funktionen:

- Bibel-Daten (Bücher, Kapitel, Verse)
- Suche
- Benutzer und Authentifizierung
- Notizen und Markierungen

Die vollständige API-Dokumentation wird mit Scribe generiert und enthält detaillierte Informationen zu jedem Endpunkt, einsch<PERSON>ßlich Anforderungs- und Antwortbeispielen.

## Technische Details

Die API ist mit Laravel 11 und PHP 8.3 implementiert und verwendet:

- **Strict Types**: Alle PHP-Klassen verwenden strict_types=1
- **Ressourcen-Transformationen**: API-Ressourcen werden mit Laravel API Resources transformiert
- **Validierung**: Eingehende Anfragen werden mit Form Requests validiert
- **Authentifizierung**: Laravel Sanctum für Token-basierte API-Authentifizierung
- **Dokumentation**: Automatisch generiert mit Scribe

## API-Nutzung

Die API kann von verschiedenen Clients genutzt werden:

- **Web-Frontend**: Die Vue.js-Anwendung kommuniziert mit der API
- **Mobile App**: Die zukünftige mobile Anwendung wird die gleiche API verwenden
- **Externe Integrationen**: Drittanbieter können mit entsprechender Authentifizierung auf die API zugreifen
