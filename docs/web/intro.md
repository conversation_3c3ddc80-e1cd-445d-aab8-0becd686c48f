---
id: intro
title: Web-Anwendung
sidebar_position: 1
---

# ESB Online Web-Anwendung

Diese Dokumentation beschreibt die ESB Online Web-Anwendung, die mit Laravel 11 und Vue 3 entwickelt wurde.

## Architektur

Die Web-Anwendung folgt einer modernen Architektur:

- **Backend**: Laravel 11 mit PHP 8.3 und strikter Typisierung
- **Frontend**: Vue 3 mit TypeScript und Composition API
- **State Management**: Pinia für reaktive und typsichere Zustandsverwaltung
- **Routing**: Inertia.js für serverseitiges Rendering mit SPA-Erlebnis

## Hauptkomponenten

Die Anwendung besteht aus mehreren Hauptkomponenten:

### Bibelnavigation

Die Bibelnavigation ermöglicht es Benutzern, durch die Bibel zu navigieren. Sie verwendet den BibleNavigationStore, der den aktuellen Zustand der Navigation verwaltet:

```typescript
// Beispiel für die Verwendung des BibleNavigationStore
const bibleStore = useBibleNavigationStore();

// Kapitel laden
bibleStore.loadChapter('GEN', 1);

// Zu einem Vers scrollen
bibleStore.scrollToVerse('GEN', 1, 1);
```

### Bibelanzeige

Die Bibelanzeige zeigt den Bibeltext an und verwaltet das Scrolling zwischen Kapiteln. Sie verwendet Scroll-Management-Funktionen, um den am besten sichtbaren Abschnitt zu bestimmen.

### Suche

Die Suchfunktion ermöglicht es Benutzern, in der Bibel zu suchen und Ergebnisse anzuzeigen.

## Entwicklungsrichtlinien

Bei der Entwicklung der Web-Anwendung werden folgende Richtlinien befolgt:

- **TDD-Ansatz**: Tests werden vor der Implementierung geschrieben
- **Strikte Typisierung**: TypeScript und PHP 8.3 mit strikter Typisierung
- **Dokumentation**: Alle Komponenten und Funktionen werden dokumentiert
- **Barrierefreiheit**: Semantisches HTML und ARIA-Attribute für bessere Zugänglichkeit
