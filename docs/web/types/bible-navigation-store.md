# Bibel-Navigations-Store

Der Bibel-Navigations-Store ist ein Pinia-Store, der den Navigationszustand für die Bibelanwendung verwaltet.

## Hauptmerkmale

1. Er verwendet TypeScript mit strikter Typisierung
2. Er verwaltet die Kapitelnavigation, Versreferenzen und Scroll-Handling
3. Er interagiert mit bibleDataStore und bibleUrlStore
4. Wichtige Methoden:
   - setVerseReference: Speichert die aktuelle Versreferenz-Zeichenfolge
   - enableScrollHandling/disableScrollHandling: Steuert, ob Scroll-Events verarbeitet werden sollen
   - updateCurrentViewportChapter: Aktualisiert das aktuelle Kapitel und lädt angrenzende Kapitel

## Zustand

```typescript
interface BibleNavigationState {
  currentBook: Book | null;
  currentChapter: number | null;
  currentVerse: number | null;
  verseReference: string | null;
  scrollHandlingEnabled: boolean;
}
```

## Aktionen

```typescript
// Zu einem bestimmten Buch und Kapitel navigieren
navigateToChapter(bookSlug: string, chapterNumber: number): Promise<void>

// Zu einem bestimmten Vers navigieren
navigateToVerse(bookSlug: string, chapterNumber: number, verseNumber: number): Promise<void>

// Die aktuelle Versreferenz-Zeichenfolge setzen
setVerseReference(reference: string): void

// Scroll-Handling aktivieren oder deaktivieren
enableScrollHandling(): void
disableScrollHandling(): void

// Das aktuelle Viewport-Kapitel aktualisieren
updateCurrentViewportChapter(chapter: Chapter): void
```
