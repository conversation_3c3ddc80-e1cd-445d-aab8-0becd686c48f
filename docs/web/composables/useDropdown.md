---
id: useDropdown
title: useDropdown
sidebar_position: 2
---

# useDropdown

Manages dropdown UI component state and interactions






## Usage

```typescript
import { useDropdown } from '@/composables/useDropdown';

// Example usage
const dropdown = useDropdown();
```

## Examples



## API

### Parameters

This composable does not take any parameters.

### Returns

`` - The returned object with its properties


The composable returns an object with the following properties:

- `isOpen` - Returned value isOpen
- `toggleDropdown` - Returned value toggleDropdown
- `setDropdownState` - Returned value setDropdownState


## Helper Functions



## Dependencies


