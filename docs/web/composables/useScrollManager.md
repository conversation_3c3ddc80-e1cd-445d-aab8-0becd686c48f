---
id: useScrollManager
title: useScrollManager
sidebar_position: 2
---

# useScrollManager

Handles scroll events and position management






## Usage

```typescript
import { useScrollManager } from '@/composables/useScrollManager';

// Example usage
const scrollManager = useScrollManager(options);
```

## Examples



## API

### Parameters

- `options`: `ScrollManagerOptions` - Parameter options

### Returns

`unknown` - The returned object with its properties


The composable returns an object with the following properties:

- `chapterRefs` - Returned value chapterRefs
- `setChapterRef` - Returned value setChapterRef
- `handleScroll` - Returned value handleScroll
- `scrollToVerse` - Returned value scrollToVerse
- `scrollToVerseRange` - Returned value scrollToVerseRange
- `scrollToChapter` - Returned value scrollToChapter
- `enableScrollHandling` - Returned value enableScrollHandling
- `disableScrollHandling` - Returned value disableScrollHandling


## Helper Functions

### setChapterRef

/** set chapter ref /

**Parameters:**

- `el`: `Element | ComponentPublicInstance | null` - Parameter el
- `section`: `Section` - Parameter section

**Returns:** `any` - The result of the function




### scrollToVerse

Helper function scrollToVerse

**Parameters:**

- `verseNumber`: `number | null` - Parameter verseNumber
- `chapterNumber`: `number` - Parameter chapterNumber

**Returns:** `Promise<void>` - Return value of scrollToVerse




### scrollToVerseRange

Helper function scrollToVerseRange

**Parameters:**

- `startVerse`: `number | null` - Parameter startVerse
- `endVerse`: `number | null` - Parameter endVerse
- `chapterNumber`: `number` - Parameter chapterNumber

**Returns:** `Promise<void>` - Return value of scrollToVerseRange




### scrollToChapter

Helper function scrollToChapter

**Parameters:**

- `chapterId`: `string` - Parameter chapterId

**Returns:** `Promise<void>` - Return value of scrollToChapter





## Dependencies


### Stores

- `useBibleStore` - Reference to the Bible store
- `useBibleHighlightStore` - Reference to the BibleHighlight store

### Composables

- `useBibleStore` - Reference to the BibleStore composable
- `useBibleHighlightStore` - Reference to the BibleHighlightStore composable

