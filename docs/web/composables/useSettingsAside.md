---
id: useSettingsAside
title: useSettingsAside
sidebar_position: 2
---

# useSettingsAside

Composable for SettingsAside






## Usage

```typescript
import { useSettingsAside } from '@/composables/useSettingsAside';

// Example usage
const settingsAside = useSettingsAside();
```

## Examples



## API

### Parameters

This composable does not take any parameters.

### Returns

`unknown`


The composable returns an object with the following properties:

- `isSettingsOpen` - Returned value isSettingsOpen
- `openSettings` - Returned value openSettings
- `closeSettings` - Returned value closeSettings
- `toggleSettings` - Returned value toggleSettings


## Helper Functions



## Dependencies


