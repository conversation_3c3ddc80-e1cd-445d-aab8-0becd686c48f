---
id: useSearchResults
title: useSearchResults
sidebar_position: 2
---

# useSearchResults

Manages search results and search-related functionality






## Usage

```typescript
import { useSearchResults } from '@/composables/useSearchResults';

// Example usage
const searchResults = useSearchResults(initialResults);
```

## Examples



## API

### Parameters

- `initialResults`: `SearchResultsState` - Parameter initialResults

### Returns

`unknown` - The returned object with its properties


The composable returns an object with the following properties:

- `isLoading` - Returned value isLoading
- `hasError` - Returned value hasError
- `currentPage` - Returned value currentPage
- `allResults` - Returned value allResults
- `hasResults` - Returned value hasResults
- `highlightContent` - Returned value highlightContent
- `loadMoreResults` - Returned value loadMoreResults


## Helper Functions

### highlightContent

Helper function highlightContent

**Parameters:**

- `content`: `string` - Parameter content
- `query`: `string` - Parameter query

**Returns:** `string` - Return value of highlightContent




### loadMoreResults

/** load more results /

**Parameters:**

- `query`: `string` - Parameter query
- `types`: `string[]` - Parameter types

**Returns:** `any` - The result of the function





## Dependencies


