---
id: useTextSettings
title: useTextSettings
sidebar_position: 2
---

# useTextSettings

Manages text display settings and preferences






## Usage

```typescript
import { useTextSettings } from '@/composables/useTextSettings';

// Example usage
const textSettings = useTextSettings();
```

## Examples



## API

### Parameters

This composable does not take any parameters.

### Returns

`unknown` - The returned object with its properties


The composable returns an object with the following properties:

- `fontSize` - Returned value fontSize
- `showVerseNumbers` - Returned value showVerseNumbers
- `showChapterNumbers` - Returned value showChapterNumbers
- `showBookNames` - Returned value showBookNames
- `flowText` - Returned value flowText
- `showFootnotes` - Returned value showFootnotes
- `focusedMode` - Returned value focusedMode
- `showChapterHeadings` - Returned value showChapterHeadings
- `updateSettings` - Returned value updateSettings
- `toggleFocusedMode` - Returned value toggleFocusedMode


## Helper Functions



## Dependencies


### Stores

- `useTextSettingsStore` - Reference to the TextSettings store

### Composables

- `useTextSettingsStore` - Reference to the TextSettingsStore composable

