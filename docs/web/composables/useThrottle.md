---
id: useThrottle
title: useThrottle
sidebar_position: 2
---

# useThrottle

Provides throttle functionality to limit function execution frequency






## Usage

```typescript
import { useThrottle } from '@/composables/useThrottle';

// Example usage
const throttle = useThrottle();
```

## Examples



## API

### Parameters

This composable does not take any parameters.

### Returns

`unknown` - The returned object with its properties



## Helper Functions



## Dependencies


### Stores



### Composables

- `useThrottle` - Reference to the Throttle composable

