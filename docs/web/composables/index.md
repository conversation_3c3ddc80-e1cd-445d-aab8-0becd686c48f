---
id: index
title: Composables Overview
sidebar_position: 1
---

# Composables Overview

This section contains documentation for all the Vue composables used in the ESB Online web application.
Composables are reusable pieces of logic that can be shared between components.

## Available Composables

### [useDebounce](useDebounce)

Provides debounce functionality to delay function execution

**Parameters:** 0  
**Helper Functions:** 0  
**Returns:** `unknown`


### [useDropdown](useDropdown)

Manages dropdown UI component state and interactions

**Parameters:** 0  
**Helper Functions:** 0  
**Returns:** ``


### [isLocalStorageAvailable](useLocalStorage)

/** /

**Parameters:** 0  
**Helper Functions:** 0  
**Returns:** `boolean`


### [useScrollManager](useScrollManager)

Handles scroll events and position management

**Parameters:** 1  
**Helper Functions:** 4  
**Returns:** `unknown`


### [useSearchResults](useSearchResults)

Manages search results and search-related functionality

**Parameters:** 1  
**Helper Functions:** 2  
**Returns:** `unknown`


### [useSettingsAside](useSettingsAside)

Composable for SettingsAside

**Parameters:** 0  
**Helper Functions:** 0  
**Returns:** `unknown`


### [useTextSettings](useTextSettings)

Manages text display settings and preferences

**Parameters:** 0  
**Helper Functions:** 0  
**Returns:** `unknown`


### [useThrottle](useThrottle)

Provides throttle functionality to limit function execution frequency

**Parameters:** 0  
**Helper Functions:** 0  
**Returns:** `unknown`


### [useVerseReference](useVerseReference)

Handles Bible verse references and navigation

**Parameters:** 0  
**Helper Functions:** 4  
**Returns:** `unknown`

