---
id: useLocalStorage
title: isLocalStorageAvailable
sidebar_position: 2
---

# isLocalStorageAvailable

/** /






## Usage

```typescript
import { isLocalStorageAvailable } from '@/composables/useLocalStorage';

// Example usage
const isLocalStorageAvailable = isLocalStorageAvailable();
```

## Examples



## API

### Parameters

This composable does not take any parameters.

### Returns

`boolean` - Whether the save was successful



## Helper Functions



## Dependencies


