---
id: useDebounce
title: useDebounce
sidebar_position: 2
---

# useDebounce

Provides debounce functionality to delay function execution






## Usage

```typescript
import { useDebounce } from '@/composables/useDebounce';

// Example usage
const debounce = useDebounce();
```

## Examples



## API

### Parameters

This composable does not take any parameters.

### Returns

`unknown` - The returned object with its properties



## Helper Functions



## Dependencies


### Stores



### Composables

- `useDebounce` - Reference to the Debounce composable

