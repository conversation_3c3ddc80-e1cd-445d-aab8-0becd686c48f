---
id: useVerseReference
title: useVerseReference
sidebar_position: 2
---

# useVerseReference

Handles Bible verse references and navigation






## Usage

```typescript
import { useVerseReference } from '@/composables/useVerseReference';

// Example usage
const verseReference = useVerseReference();
```

## Examples



## API

### Parameters

This composable does not take any parameters.

### Returns

`unknown` - The returned object with its properties


The composable returns an object with the following properties:

- `startVerse` - Returned value startVerse
- `endVerse` - Returned value endVerse
- `raw` - Returned value raw
- `chapter` - Returned value chapter
- `book` - Returned value book


## Helper Functions

### parseVerseReference

Handles Bible verse references and navigation

**Parameters:**

- `reference`: `string` - Parameter reference

**Returns:** `VerseReference | null` - The returned object with its properties




### highlightSearchTerms

/** highlight search terms /

**Parameters:**

- `element`: `Element` - Parameter element

**Returns:** `any` - The result of the function




### navigateToReference

/** navigate to reference /

**Parameters:**

- `book`: `string` - Parameter book
- `chapter`: `number` - Parameter chapter
- `number`: `any` - Parameter number

**Returns:** `any` - The result of the function




### getAvailableVerses

Helper function getAvailableVerses

**Parameters:**

- `section`: `Section` - Parameter section

**Returns:** `number[]` - Return value of getAvailableVerses





## Dependencies


### Stores

- `useSearchStore` - Reference to the Search store
- `useBibleStore` - Reference to the Bible store
- `useBibleHighlightStore` - Reference to the BibleHighlight store

### Composables

- `useSearchStore` - Reference to the SearchStore composable
- `useBibleStore` - Reference to the BibleStore composable
- `useBibleHighlightStore` - Reference to the BibleHighlightStore composable

