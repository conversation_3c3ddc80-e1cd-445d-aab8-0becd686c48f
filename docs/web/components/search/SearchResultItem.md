---
id: SearchResultItem
title: SearchResultItem
---

# SearchResultItem

Keine Beschreibung verfügbar

## Komponentenstruktur

```
SearchResultItem
├── Props
```



## Verwendung

```vue
<template>
  <SearchResultItem />
</template>
```













## Template-Struktur

```html
<div
        class="hover:bg-theme-50 dark:hover:bg-theme-800 border-theme-300 rounded-lg border p-4 transition"
    >
        <div class="flex justify-between">
            <div
                class="result-type rounded-full px-2 py-0.5 text-xs uppercase"
                :class="getTypeColorClasses(result.type)"
            >
                {{ getTypeLabel(result.type) }}
            </div>
        </div>

        <h3 class="text-lg font-medium">
            <Link v-if="result.url" :href="result.url">
                {{ result.title }}
            </Link>
            <span v-else>{{ result.title }}</span>
        </h3>

        <!-- Special handling for footnote results -->
        <div
            v-if="result.type === 'footnote' && result.footnoteInfo"
            class="mt-2"
        >
            <div class="content">
                <!-- eslint-disable-next-line vue/no-v-html -->
                <span v-html="highlightedVerseContent"></span>
            </div>

            <!-- Add footnote content -->
            <div class="mt-2 rounded bg-gray-50 p-2 text-sm dark:bg-gray-800">
                <span class="font-medium">Fußnote: </span>
                <!-- eslint-disable-next-line vue/no-v-html -->
                <span v-html="footnoteContent"></span>
            </div>
        </div>

        <!-- eslint-disable-next-line vue/no-v-html -->
        <div v-else class="content mt-2" v-html="highlightedContent"></div>
        <div
            v-if="result.metadata && showMetadata"
            class="metadata mt-2 text-xs text-gray-500"
        >
            <div
                v-if="result.metadata.tags && result.metadata.tags.length"
                class="tags"
            >
                <span
                    v-for="tag in result.metadata.tags"
                    :key="tag"
                    class="tag mr-1"
                >
                    #{{ tag }}
                </span>
            </div>
        </div>
    </div>
```



## Stil

```css
.referenced-word {
    border: 2px solid var(--color-theme-500);
    border-radius: 0.25rem;
    padding: 0 0.25rem;
    margin: 0 0.1rem;
    background-color: var(--color-theme-50);
}

:global(.dark) .referenced-word {
    background-color: var(--color-theme-900);
    border-color: var(--color-theme-400);
}
```


