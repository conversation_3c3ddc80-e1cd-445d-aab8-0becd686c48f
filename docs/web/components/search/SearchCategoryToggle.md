---
id: SearchCategoryToggle
title: SearchCategoryToggle
---

# SearchCategoryToggle

Keine Beschreibung verfügbar

## Komponentenstruktur

```
SearchCategoryToggle
├── Props
```



## Verwendung

```vue
<template>
  <SearchCategoryToggle />
</template>
```













## Template-Struktur

```html
<div class="search-category-toggles">
        <h3 class="mb-2 text-sm font-medium">Search in:</h3>
        <div class="flex flex-wrap gap-2">
            <label
                v-for="category in categories"
                :key="category.value"
                class="inline-flex items-center"
            >
                <input
                    v-model="selectedCategories"
                    type="checkbox"
                    :value="category.value"
                    class="form-checkbox text-primary-600 h-4 w-4 transition duration-150 ease-in-out"
                    @change="emitChange"
                />
                <span class="ml-2 text-sm">{{ category.label }}</span>
            </label>
        </div>
    </div>
```




