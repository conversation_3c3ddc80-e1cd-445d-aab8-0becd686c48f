---
id: BibleSearch
title: BibleSearch
---

# BibleSearch

Keine Beschreibung verfügbar

## Komponentenstruktur

```
BibleSearch
├── Props
```



## Verwendung

```vue
<template>
  <BibleSearch />
</template>
```













## Template-Struktur

```html
<div class="search-container relative z-40">
        <div class="absolute inset-y-0 left-0 flex items-center pl-2">
            <BibleBookSelector
                v-model="selectedBook"
                :in-search-component="true"
                @book-select="handleBookSelect"
            />
        </div>
        <form @submit.prevent="handleSubmit">
            <TextInput
                v-model="searchQuery"
                type="text"
                :placeholder="placeholder"
                :aria-label="ariaLabel"
                class="dark:bg-theme-700 text-theme-900 dark:text-theme-100 dark:placeholder:text-theme-400 bg-theme/90 border-theme-300/50 w-full rounded-lg py-3 pr-24 pl-12 placeholder-gray-500 transition-all duration-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-500 dark:ring-gray-600"
                :class="{
                    'border-red-500 focus:border-red-500 focus:ring-red-500':
                        error,
                }"
                @input="handleInput"
                @keydown.down="handleArrowDown"
                @keydown.up="handleArrowUp"
                @keydown.esc="closeSuggestions"
                @blur="handleBlur"
            />

            <Icon
                v-if="searchQuery"
                name="X"
                class="absolute top-1/2 right-10 h-5 w-5 -translate-y-1/2 transform cursor-pointer"
                :aria-label="'Clear search'"
                @click="searchQuery = ''"
            />
        </form>
        <!-- Typeahead Suggestions -->
        <div
            v-show="showSuggestions && suggestions.length > 0"
            class="hide-scrollbar dark:bg-theme-800 bg-theme border-theme-300 dark:border-theme-600 absolute top-full right-0 left-0 z-50 mt-1 max-h-60 overflow-y-auto rounded-lg border shadow-lg"
        >
            <div
                v-for="(suggestion, index) in suggestions"
                :key="index"
                :class="[
                    'hover:bg-theme-100 dark:hover:bg-theme-700 cursor-pointer px-4 py-2',
                    {
                        'bg-theme-100 dark:bg-theme-700':
                            index === selectedIndex,
                    },
                ]"
                class="text-theme-900 dark:text-theme-100"
                @mousedown.prevent="selectSuggestion(suggestion)"
            >
                <div class="flex flex-col">
                    <span class="font-medium">{{
                        suggestion.split(' - ')[0]
                    }}</span>
                    <span class="text-theme-600 dark:text-theme-400 text-sm">{{
                        suggestion.split(' - ')[1]
                    }}</span>
                </div>
            </div>
        </div>
        <div class="absolute inset-y-0 right-0 flex items-center pr-2">
            <SearchTypeDropdown />
        </div>
    </div>
    <InputError :message="error" />
```



## Stil

```css
.hide-scrollbar {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

.hide-scrollbar::-webkit-scrollbar {
    display: none; /* Chrome, Safari and Opera */
}
```


