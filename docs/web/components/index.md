---
id: index
title: Web-Komponenten
sidebar_position: 1
---

# Web-Komponenten

Die ESB Online Webanwendung verwendet Vue.js-Komponenten, die in die folgenden Kategorien unterteilt sind:

## Bibelanzeige

Diese Kategorie enthält die folgenden Komponenten:

- [ChapterContent](./bibledisplay/ChapterContent.md)
- [ChapterNumber](./bibledisplay/ChapterNumber.md)
- [ChapterWrapper](./bibledisplay/ChapterWrapper.md)
- [FootnoteContent](./bibledisplay/FootnoteContent.md)
- [FootnoteTooltip](./bibledisplay/FootnoteTooltip.md)
- [FrontMatter](./bibledisplay/FrontMatter.md)
- [InfoItem](./bibledisplay/InfoItem.md)
- [InfoSection](./bibledisplay/InfoSection.md)
- [NumberSelector](./bibledisplay/NumberSelector.md)
- [UnavailableBookNotice](./bibledisplay/UnavailableBookNotice.md)
- [VerseContent](./bibledisplay/VerseContent.md)
- [VerseNumber](./bibledisplay/VerseNumber.md)
- [WordGroupContainer](./bibledisplay/WordGroupContainer.md)

## Navigation

Diese Kategorie enthält die folgenden Komponenten:

- [BibleBookDropdown](./navigation/BibleBookDropdown.md)
- [BibleBookOffcanvas](./navigation/BibleBookOffcanvas.md)
- [BibleBookSelector](./navigation/BibleBookSelector.md)
- [MobileSearchOverlay](./navigation/MobileSearchOverlay.md)
- [NavLink](./navigation/NavLink.md)
- [NavigationBar](./navigation/NavigationBar.md)
- [OffcanvasSidebar](./navigation/OffcanvasSidebar.md)
- [ReferenceSelector](./navigation/ReferenceSelector.md)
- [ResponsiveNavLink](./navigation/ResponsiveNavLink.md)
- [SubNavigationBar](./navigation/SubNavigationBar.md)

## Suche

Diese Kategorie enthält die folgenden Komponenten:

- [BibleSearch](./search/BibleSearch.md)
- [SearchCategoryToggle](./search/SearchCategoryToggle.md)
- [SearchResultItem](./search/SearchResultItem.md)
- [SearchTypeDropdown](./search/SearchTypeDropdown.md)

## Allgemein

Diese Kategorie enthält die folgenden Komponenten:

- [ApplicationLogo](./common/ApplicationLogo.md)
- [Checkbox](./common/Checkbox.md)
- [DangerButton](./common/DangerButton.md)
- [Dropdown](./common/Dropdown.md)
- [DropdownLink](./common/DropdownLink.md)
- [ErrorBoundary](./common/ErrorBoundary.md)
- [InputError](./common/InputError.md)
- [InputLabel](./common/InputLabel.md)
- [LoadingSpinner](./common/LoadingSpinner.md)
- [Modal](./common/Modal.md)
- [Overlay](./common/Overlay.md)
- [PrimaryButton](./common/PrimaryButton.md)
- [SecondaryButton](./common/SecondaryButton.md)
- [TextInput](./common/TextInput.md)

