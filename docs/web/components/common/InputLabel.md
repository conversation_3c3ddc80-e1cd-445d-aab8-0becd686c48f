---
id: InputLabel
title: InputLabel
---

# InputLabel

Keine Beschreibung verfügbar

## Komponentenstruktur

```
InputLabel
├── Props
```



## Verwendung

```vue
<template>
  <InputLabel />
</template>
```













## Template-Struktur

```html
<label class="text-theme-700 dark:text-theme-300 block text-sm font-medium">
        <span v-if="value">{{ value }}</span>
        <span v-else><slot /></span>
    </label>
```




