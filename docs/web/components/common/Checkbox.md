---
id: Checkbox
title: Checkbox
---

# Checkbox

Keine Beschreibung verfügbar

## Komponentenstruktur

```
Checkbox
├── Props
```



## Verwendung

```vue
<template>
  <Checkbox />
</template>
```













## Template-Struktur

```html
<input
        v-model="proxyChecked"
        type="checkbox"
        :value="value"
        class="dark:bg-theme-900 border-theme-300 dark:border-theme-700 rounded-sm text-indigo-600 shadow-xs focus:ring-indigo-500 dark:focus:ring-indigo-600 dark:focus:ring-offset-gray-800"
    />
```




