---
id: Overlay
title: Overlay
---

# Overlay

Keine Beschreibung verfügbar

## Komponentenstruktur

```
Overlay
├── Props
```



## Verwendung

```vue
<template>
  <Overlay />
</template>
```













## Template-Struktur

```html
<Teleport to="body">
        <Transition
            enter-active-class="transition-opacity duration-300 ease-out"
            enter-from-class="opacity-0"
            enter-to-class="opacity-100"
            leave-active-class="transition-opacity duration-200 ease-in"
            leave-from-class="opacity-100"
            leave-to-class="opacity-0"
        >
            <div
                v-if="show"
                class="fixed inset-0 z-30 bg-black/50 backdrop-blur-[1px]"
                @click="$emit('click')"
            ></div>
        </Transition>
    </Teleport>
```




