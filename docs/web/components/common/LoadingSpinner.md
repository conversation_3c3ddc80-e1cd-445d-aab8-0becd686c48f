---
id: LoadingSpinner
title: LoadingSpinner
---

# LoadingSpinner

Keine Beschreibung verfügbar

## Komponentenstruktur

```
LoadingSpinner
├── Props
```



## Verwendung

```vue
<template>
  <LoadingSpinner />
</template>
```













## Template-Struktur

```html
<div
        :class="[
            'flex items-center justify-center gap-2',
            center ? 'absolute inset-0' : 'py-4',
        ]"
    >
        <div
            v-if="variant === 'pulse'"
            class="flex space-x-1"
            role="status"
            aria-label="Loading"
        >
            <div
                v-for="i in 3"
                :key="i"
                :class="[
                    'animate-pulse rounded-full',
                    sizeClasses[size],
                    colorClasses[variant],
                ]"
                :style="{ animationDelay: `${(i - 1) * 0.15}s` }"
            />
        </div>
        <div
            v-else
            :class="[
                'animate-spin rounded-full border-t-2',
                sizeClasses[size],
                colorClasses[variant],
            ]"
            role="status"
            aria-label="Loading"
        >
            <span class="sr-only">Loading...</span>
        </div>
    </div>
```



## Stil

```css
.animate-spin {
    animation: spin 1s linear infinite;
}

.animate-pulse {
    animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

@keyframes pulse {
    0%,
    100% {
        opacity: 1;
    }
    50% {
        opacity: 0.3;
    }
}
```


