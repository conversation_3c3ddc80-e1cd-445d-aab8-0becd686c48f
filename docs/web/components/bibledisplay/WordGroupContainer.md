---
id: WordGroupContainer
title: WordGroupContainer
---

# WordGroupContainer

Returns the word type for data attribute

## Komponentenstruktur

```
WordGroupContainer
├── Props
```



## Verwendung

```vue
<template>
  <WordGroupContainer />
</template>
```













## Template-Struktur

```html
<span
        :class="groupClasses"
        :data-variant-type="group.isVariant ? group.variantType : undefined"
        :data-word-type="getGroupType()"
        :role="group.isOtQuote ? 'mark' : undefined"
        :aria-label="getAriaLabel()"
    >
        <template v-if="group.isVariant">&nbsp;
```



## Stil

```css
/* All groups should display inline by default */
.inline {
    display: inline;
}

/* Variant styling */
.variant {
    display: inline;
}

/* OT Quote styling */
.ot-quote {
    font-style: italic;
}

/* Variant type specific styling */
.variant-add {
    color: #4a5568;
}

.variant-va {
    font-weight: 500;
}

.variant-omission {
    text-decoration: line-through;
    opacity: 0.7;
}

.variant-alternative {
    color: #718096;
}
```


