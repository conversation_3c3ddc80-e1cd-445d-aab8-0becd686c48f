---
id: InfoItem
title: InfoItem
---

# InfoItem

Keine Beschreibung verfügbar

## Komponentenstruktur

```
InfoItem
├── Props
```



## Verwendung

```vue
<template>
  <InfoItem />
</template>
```













## Template-Struktur

```html
<div>
        <dt class="text-theme-600 dark:text-theme-400 text-sm">{{ label }}</dt>
        <dd class="text-theme-900 dark:text-theme-100" :class="className">
            {{ value }}
        </dd>
    </div>
```




