---
id: FootnoteContent
title: FootnoteContent
---

# FootnoteContent

Keine Beschreibung verfügbar

## Komponentenstruktur

```
FootnoteContent
├── Props
```



## Verwendung

```vue
<template>
  <FootnoteContent />
</template>
```













## Template-Struktur

```html
<template v-if="footnote.contentStructure">
        <template v-for="(element, index) in filteredElements" :key="index">
            <span :class="getElementClasses(element)">{{
                element.content
            }}</span
            >{{ index < filteredElements.length - 1 ? ' ' : '' }}
```




