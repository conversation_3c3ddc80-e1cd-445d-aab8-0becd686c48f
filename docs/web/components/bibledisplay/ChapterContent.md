---
id: ChapterContent
title: ChapterContent
---

# ChapterContent

Keine Beschreibung verfügbar

## Komponentenstruktur

```
ChapterContent
├── Props
```



## Verwendung

```vue
<template>
  <ChapterContent />
</template>
```













## Template-Struktur

```html
<article
        :id="chapter.number.toString()"
        :data-chapter-id="dataChapterId"
        :class="[
            articleClasses,
            textSettings.flowText ? 'flow-mode' : 'verse-mode',
        ]"
        class="prose prose-lg dark:prose-invert font-calluna max-w-full px-2"
    >
        <header>
            <h1
                v-if="showBookHeader"
                class="font-thanatos text-theme-900 dark:text-theme-100 mb-8 text-center text-4xl"
            >
                {{ chapter.book.name }}
            </h1>
        </header>
        <section
            v-for="(group, index) in paragraphGroups"
            :key="index"
            class="pericope"
            :class="{ 'pericope-start': group.isPericopeStart }"
            :aria-label="'Perikope ' + (index + 1)"
        >
            <!-- Render each verse as a div within the pericope section -->
            <div
                v-for="verse in group.verses"
                :id="`${chapter.book.slug}.${chapter.number}.${verse.number}`"
                :key="verse.number"
                class="verse-unit"
                :data-verse="verse.number"
                :data-reference="`${chapter.book.name} ${chapter.number}:${verse.number}`"
                :data-category="chapter.book.category"
            >
                <ChapterNumber
                    v-if="verse.number === 1"
                    :number="chapter.number"
                />
                <VerseContent
                    :verse="verse"
                    :flow-text="textSettings.flowText"
                    :show-verse-numbers="textSettings.showVerseNumbers"
                    :text-settings="textSettings"
                    @footnote-click="handleFootnoteClick($event, verse)"
                    @footnote-hover="handleFootnoteHover($event, verse)"
                />
            </div>
        </section>
    </article>
```



## Stil

```css
/* Base styles for both modes */
.pericope {
    margin-bottom: 1rem;
}

.highlighted-verse {
    text-decoration: underline;
    text-decoration-style: solid;
    text-decoration-thickness: 0.05em;
    text-underline-offset: 0.375em; /* Add some space between text and underline */
    border-radius: 0.25em;
    transition: text-decoration-color 0.2s ease;
}

/* Category-specific underline colors */
.highlighted-verse[data-category='history'] {
    text-decoration-color: var(--color-history-surface);
}

.highlighted-verse[data-category='wisdom'],
.highlighted-verse[data-category='law'] {
    text-decoration-color: var(--color-poetic-surface);
}

.highlighted-verse[data-category='prophecy'] {
    text-decoration-color: var(--color-prophecy-surface);
}

.highlighted-verse[data-category='gospel'] {
    text-decoration-color: var(--color-gospel-text);
}

.highlighted-verse[data-category='epistle'] {
    text-decoration-color: var(--color-epistle-surface);
}

.highlighted-verse[data-category='apocalypse'] {
    text-decoration-color: var(--color-revelation-surface);
}

/* Dark mode styles - slightly higher opacity for better visibility */
:global(.dark) .highlighted-verse[data-category='history'] {
    text-decoration-color: var(--color-history-text);
}

:global(.dark) .highlighted-verse[data-category='wisdom'],
:global(.dark) .highlighted-verse[data-category='law'] {
    text-decoration-color: var(--color-poetic-text);
}

:global(.dark) .highlighted-verse[data-category='prophecy'] {
    text-decoration-color: var(--color-prophecy-text);
}

:global(.dark) .highlighted-verse[data-category='gospel'] {
    text-decoration-color: var(--color-gospel-surface);
}

:global(.dark) .highlighted-verse[data-category='epistle'] {
    text-decoration-color: var(--color-epistle-text);
}

:global(.dark) .highlighted-verse[data-category='apocalypse'] {
    text-decoration-color: var(--color-revelation-text);
}

/* Flow text mode - pericope styling */
.flow-mode .pericope-title {
    display: block;
}
.flow-mode .verse-unit {
    display: inline;
}
/* Verse mode - pericope styling */
.verse-mode .pericope {
    margin-bottom: 0;
}
.verse-mode .pericope-title {
    display: none;
}
.verse-mode .verse-unit {
    display: block;
    margin-bottom: 1rem;
}
```


