---
id: FootnoteTooltip
title: FootnoteTooltip
---

# FootnoteTooltip

Keine Beschreibung verfügbar

## Komponentenstruktur

```
FootnoteTooltip
├── Props
```



## Verwendung

```vue
<template>
  <FootnoteTooltip />
</template>
```













## Template-Struktur

```html
<Teleport to="body">
        <Transition name="footnote">
            <div
                v-if="footnoteState"
                ref="tooltipRef"
                class="bg-theme-700 dark:bg-theme-700 fixed z-50 mb-12 max-w-[18.5em] min-w-[10.25em] rounded-lg shadow-lg"
                :class="tooltipClass"
                :style="tooltipStyle"
            >
                <div class="relative p-3">
                    <div
                        class="border-theme-600 dark:border-theme-600 mb-2 flex items-center justify-between border-b pb-2 text-sm font-medium"
                    >
                        <div>
                            <span
                                class="text-theme-100 dark:text-theme-100 mr-2 font-medium"
                                >»{{ footnoteState.word }}«</span
                            >
                            <span class="text-theme-400 dark:text-theme-400">{{
                                footnoteState.reference
                            }}</span>
                        </div>
                        <button
                            class="text-theme-300 hover:text-theme-100 dark:text-theme-400 dark:hover:text-theme-100 flex h-6 w-6 items-center justify-center rounded-full transition-colors duration-150"
                            @click="onClose"
                        >
                            <Icon
                                name="X"
                                class="h-4 w-4"
                                :aria-label="'Close footnote'"
                            />
                        </button>
                    </div>
                    <div class="text-theme-200 dark:text-theme-200 text-sm">
                        <FootnoteContent :footnote="footnoteState.footnote" />
                    </div>
                </div>
                <div
                    class="bg-theme-700 dark:bg-theme-700 absolute bottom-0 h-4 w-4 translate-y-1/2 rotate-45 transform"
                    :style="arrowStyle"
                    style="box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.1)"
                ></div>
            </div>
        </Transition>
    </Teleport>
```



## Stil

```css
.footnote-enter-active,
.footnote-leave-active {
    transition:
        opacity 0.2s ease,
        transform 0.2s ease;
}

.footnote-enter-from,
.footnote-leave-to {
    opacity: 0;
    transform: translateY(-10px);
}

.tooltip-align-left,
.tooltip-align-right,
.tooltip-align-center {
    transform-origin: center bottom;
}
```


