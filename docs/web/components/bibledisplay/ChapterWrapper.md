---
id: ChapterWrapper
title: ChapterWrapper
---

# ChapterWrapper

Keine Beschreibung verfügbar

## Komponentenstruktur

```
ChapterWrapper
├── Props
```



## Verwendung

```vue
<template>
  <ChapterWrapper />
</template>
```













## Template-Struktur

```html
<template v-if="isFrontmatterSection(section)">
        <Frontmatter
            :id="section.book.slug"
            :ref="(el) => setRef(el)"
            :initial-book="section.book"
            :data-chapter-id="section.book.slug"
        />
```




