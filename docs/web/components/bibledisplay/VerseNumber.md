---
id: VerseNumber
title: VerseNumber
---

# VerseNumber

Keine Beschreibung verfügbar

## Komponentenstruktur

```
VerseNumber
├── Props
```



## Verwendung

```vue
<template>
  <VerseNumber />
</template>
```













## Template-Struktur

```html
<span
        :class="[
            'mr-1 max-w-[2rem] cursor-pointer align-super text-[0.7em] select-none',
            versColor,
            flowText ? 'relative pr-1' : 'inline-block',
            isHighlighted ? 'font-bold' : '',
        ]"
        aria-hidden="false"
        role="button"
        :aria-pressed="isHighlighted"
        @click="handleClick"
    >
        {{ number }}
    </span>
```



## Stil

```css
sup {
    display: inline;
    bottom: 1.2em;
}
.flex sup {
    top: 0.8em;
}
```


