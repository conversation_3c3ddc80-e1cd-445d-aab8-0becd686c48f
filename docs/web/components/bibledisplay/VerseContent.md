---
id: VerseContent
title: VerseContent
---

# VerseContent

Keine Beschreibung verfügbar

## Komponentenstruktur

```
VerseContent
├── Props
```



## Verwendung

```vue
<template>
  <VerseContent />
</template>
```













## Template-Struktur

```html
<VerseNumber
        v-if="showVerseNumbers"
        :number="verse.number"
        :flow-text="flowText"
        :is-highlighted="isVerseHighlighted"
        class="font-thanatos verse-number"
    />
    <WordGroupContainer
        v-for="(group, index) in wordGroups"
        :key="index"
        :group="group"
        :show-footnotes="textSettings.showFootnotes"
        :class="[textContainerClasses, `text-${textSettings.fontSize}`]"
        @footnote-click="onFootnoteClick"
        @footnote-hover="onFootnoteHover"
    />
```



## Stil

```css
.variant-group {
    display: inline;
}

.verse-highlight {
    background-color: #ffffe0;
    padding: 0.25rem;
    margin: -0.25rem;
    border-radius: 0.25rem;
}

.verse.pericope-start {
    margin-left: 4em;
}

/* Font size classes */
.text-xs {
    font-size: 0.875rem;
}
.text-sm {
    font-size: 1rem;
}
.text-base {
    font-size: 1.125rem;
}
.text-lg {
    font-size: 1.3rem;
}
.text-xl {
    font-size: 1.5rem;
}
.text-2xl {
    font-size: 1.75rem;
}
.text-3xl {
    font-size: 2rem;
}
```


