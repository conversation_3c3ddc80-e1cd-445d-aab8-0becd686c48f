---
id: BibleBookDropdown
title: BibleBookDropdown
---

# BibleBookDropdown

Keine Beschreibung verfügbar

## Komponentenstruktur

```
BibleBookDropdown
├── Props
```



## Verwendung

```vue
<template>
  <BibleBookDropdown />
</template>
```













## Template-Struktur

```html
<div
        v-show="modelValue"
        ref="dropdownElement"
        class="bible-dropdown show-scrollbar-on-hover dark:bg-theme-800 bg-theme border-theme-300 dark:border-theme-600 absolute -left-2 z-60 mt-1 hidden rounded-b-lg border p-4 shadow-lg md:block dark:shadow-gray-900/30"
        :style="{ width: dropdownWidth, maxHeight: maxHeight }"
        role="menu"
        aria-orientation="vertical"
        aria-labelledby="menu-button"
        tabindex="-1"
    >
        <!-- Tabs -->
        <div class="dark:border-theme-700 mb-4 flex border-b">
            <button
                class="flex-1 pb-2 text-center text-sm transition-colors"
                :class="{
                    'border-b-2 border-indigo-500 text-indigo-600 dark:text-indigo-400':
                        activeTab === 'book',
                    'text-theme-500 dark:text-theme-400': activeTab !== 'book',
                }"
                @click="activeTab = 'book'"
            >
                Buch
            </button>
            <button
                class="flex-1 pb-2 text-center text-sm transition-colors"
                :class="{
                    'border-b-2 border-indigo-500 text-indigo-600 dark:text-indigo-400':
                        activeTab === 'chapter',
                    'text-theme-500 dark:text-theme-400':
                        activeTab !== 'chapter',
                    'cursor-not-allowed opacity-50': !selectedBook,
                }"
                :disabled="!selectedBook"
                @click="activeTab = 'chapter'"
            >
                Kapitel
            </button>
        </div>

        <!-- Search Input -->
        <div class="mb-4">
            <input
                v-model="searchQuery"
                type="text"
                :placeholder="
                    activeTab === 'book'
                        ? 'Bibelbuch suchen...'
                        : 'Kapitel suchen...'
                "
                class="dark:bg-theme-700 text-theme-900 dark:text-theme-100 bg-theme border-theme-300 dark:border-theme-600 w-full rounded-lg border px-3 py-2 text-sm placeholder-gray-500 transition-colors focus:border-indigo-500 focus:ring-2 focus:ring-indigo-500 dark:placeholder-gray-400 dark:focus:border-indigo-400 dark:focus:ring-indigo-400"
            />
        </div>

        <!-- Book Tab Content -->
        <div v-if="activeTab === 'book'" class="space-y-6 overflow-y-auto">
            <div
                v-for="testament in filteredCategories"
                :key="testament.name"
                class="space-y-4"
            >
                <h2
                    class="text-theme-900 dark:text-theme-100 text-lg font-semibold"
                >
                    {{ testament.name }}
                </h2>
                <div class="grid grid-cols-6 gap-2">
                    <button
                        v-for="book in testament.books"
                        :key="book.id"
                        class="hover:bg-opacity-90 dark:hover:bg-opacity-90 text-theme-900 dark:text-theme-100 block w-full rounded-lg px-3 py-2 text-center text-sm transition-colors focus:ring-2 focus:ring-indigo-500 focus:outline-none"
                        :class="[
                            getCategoryColorClasses(book.category).className,
                            selectedBook?.id === book.id
                                ? 'ring-2 ring-indigo-500 dark:ring-indigo-400'
                                : '',
                            !isBookAvailable(book.slug)
                                ? 'cursor-not-allowed opacity-50'
                                : '',
                        ]"
                        :disabled="!isBookAvailable(book.slug)"
                        :title="
                            !isBookAvailable(book.slug)
                                ? 'Dieses Buch steht bald zur Verfügung'
                                : book.name
                        "
                        @click.stop="
                            isBookAvailable(book.slug) && selectBook(book)
                        "
                    >
                        {{ book.shortName }}
                    </button>
                </div>
            </div>
        </div>

        <!-- Chapter Tab Content -->
        <div v-else-if="activeTab === 'chapter'" class="overflow-y-auto">
            <div class="grid grid-cols-4 gap-2 sm:grid-cols-7 md:grid-cols-8">
                <button
                    v-for="chapter in filteredChapters"
                    :key="chapter"
                    class="dropdown-item hover:bg-theme-100 dark:hover:bg-theme-700 text-theme-700 dark:text-theme-300 rounded-lg px-3 py-2 text-center text-sm transition-colors focus:ring-2 focus:ring-indigo-500 focus:outline-none dark:focus:ring-indigo-400"
                    :class="{
                        'bg-indigo-50 text-indigo-700 dark:bg-indigo-900/30 dark:text-indigo-300':
                            selectedChapter === chapter,
                    }"
                    @click="selectChapter(chapter)"
                >
                    {{ chapter }}
                </button>
            </div>
        </div>
    </div>
```



## Stil

```css
.overflow-y-auto {
    scrollbar-gutter: stable;
    scrollbar-width: thin;
}

.overflow-y-auto::-webkit-scrollbar {
    width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
    background: transparent !important;
    background: rgba(156, 163, 175, 0.2);
    border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
    background: transparent;
    border-radius: 3px;
}

.overflow-y-auto:hover::-webkit-scrollbar-thumb {
    background: rgba(156, 163, 175, 0.5); /* gray-400 with opacity */
}

/* Use overlay scrollbar to prevent layout shift */
@supports (overflow-y: overlay) {
    .overflow-y-auto {
        overflow-y: overlay;
    }
}

/* Dark mode adjustments */
.dark .overflow-y-auto:hover::-webkit-scrollbar-thumb {
    background: rgba(156, 163, 175, 0.5);
}

.show-scrollbar-on-hover {
    scrollbar-gutter: stable;
    scrollbar-width: thin;
}

.show-scrollbar-on-hover::-webkit-scrollbar {
    width: 6px;
}

.show-scrollbar-on-hover::-webkit-scrollbar-track {
    background: transparent !important;
    background: rgba(156, 163, 175, 0.2);
    border-radius: 3px;
}

.show-scrollbar-on-hover::-webkit-scrollbar-thumb {
    background: transparent;
    border-radius: 3px;
}

.show-scrollbar-on-hover:hover::-webkit-scrollbar-thumb {
    background: rgba(156, 163, 175, 0.5); /* gray-400 with opacity */
}

/* Use overlay scrollbar to prevent layout shift */
@supports (overflow-y: overlay) {
    .show-scrollbar-on-hover {
        overflow-y: overlay;
    }
}

/* Dark mode adjustments */
.dark .show-scrollbar-on-hover:hover::-webkit-scrollbar-thumb {
    background: rgba(156, 163, 175, 0.5);
}
```


