---
id: BibleBookSelector
title: BibleBookSelector
---

# BibleBookSelector

Keine Beschreibung verfügbar

## Komponentenstruktur

```
BibleBookSelector
├── Props
```



## Verwendung

```vue
<template>
  <BibleBookSelector />
</template>
```













## Template-Struktur

```html
<div class="bible-selector dropdown-container relative">
        <div class="flex items-center gap-2">
            <button
                type="button"
                class="hover:text-theme-900 hover:bg-theme flex items-center gap-1 rounded-lg p-1 transition-colors duration-200 focus:ring-2 focus:ring-blue-500 focus:outline-none"
                :class="{
                    'text-blue-600': selectedBook,
                    'bg-theme': isOpen,
                    'text-theme-600': inSearchComponent,
                    'text-theme-900 dark:text-theme-100':
                        !inSearchComponent && !selectedBook,
                }"
                @click="toggleDropdown"
            >
                <Icon
                    name="BookOpen"
                    class="h-5 w-5"
                    :aria-label="'Toggle book selection'"
                />
            </button>
        </div>

        <Overlay :show="isOpen" @click="isOpen = false" />

        <template v-if="isMobile">
            <Teleport to="body">
                <OffcanvasSidebar
                    :is-open="isOpen"
                    :sections="categories"
                    @close="isOpen = false"
                    @select-book="handleBookSelect"
                    @select-chapter="handleChapterSelect"
                />
            </Teleport>
```




