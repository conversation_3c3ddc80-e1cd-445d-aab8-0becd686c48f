---
id: OffcanvasSidebar
title: OffcanvasSidebar
---

# OffcanvasSidebar

Keine Beschreibung verfügbar

## Komponentenstruktur

```
OffcanvasSidebar
├── Props
```



## Verwendung

```vue
<template>
  <OffcanvasSidebar />
</template>
```













## Template-Struktur

```html
<aside>
        <!-- Backdrop -->
        <transition
            enter-active-class="transition-opacity ease-out duration-300"
            enter-from-class="opacity-0"
            enter-to-class="opacity-100"
            leave-active-class="transition-opacity ease-in duration-300"
            leave-from-class="opacity-100"
            leave-to-class="opacity-0"
        >
            <div
                v-show="isOpen"
                class="bg-theme-500 bg-opacity-75 dark:bg-theme-900 dark:bg-opacity-75 fixed inset-0 transition-opacity"
                @click="$emit('close')"
            ></div>
        </transition>

        <!-- Sidebar -->
        <transition
            enter-active-class="transition ease-out duration-300 transform"
            enter-from-class="-translate-x-full"
            enter-to-class="translate-x-0"
            leave-active-class="transition ease-in duration-300 transform"
            leave-from-class="translate-x-0"
            leave-to-class="-translate-x-full"
        >
            <div
                v-show="isOpen"
                class="dark:bg-theme-800 bg-theme fixed inset-0 z-50 w-full shadow-xl"
                @click.stop
            >
                <!-- Header with close button -->
                <div
                    class="dark:bg-theme-800 bg-theme dark:border-theme-700 sticky top-0 z-10 flex items-center justify-between border-b p-4"
                >
                    <h2
                        class="text-theme-900 dark:text-theme-100 text-lg font-medium"
                    >
                        Bibel Navigation
                    </h2>
                    <button
                        class="hover:bg-theme-100 dark:hover:bg-theme-700 text-theme-400 hover:text-theme-500 dark:hover:text-theme-300 rounded-lg p-2 focus:ring-2 focus:ring-blue-500 focus:outline-hidden focus:ring-inset"
                        @click="$emit('close')"
                    >
                        <span class="sr-only">Close sidebar</span>
                        <Icon
                            name="X"
                            class="h-5 w-5"
                            :aria-label="'Close navigation'"
                        />
                    </button>
                </div>

                <!-- Navigation content -->
                <div class="h-[calc(100vh-73px)] overflow-y-auto p-4">
                    <!-- Tabs -->
                    <div class="dark:border-theme-700 mb-4 flex border-b">
                        <button
                            class="flex-1 pb-2 text-center text-sm transition-colors"
                            :class="{
                                'border-b-2 border-indigo-500 text-indigo-600 dark:text-indigo-400':
                                    activeTab === 'book',
                                'text-theme-500 dark:text-theme-400':
                                    activeTab !== 'book',
                            }"
                            @click="activeTab = 'book'"
                        >
                            Buch
                        </button>
                        <button
                            class="flex-1 pb-2 text-center text-sm transition-colors"
                            :class="{
                                'border-b-2 border-indigo-500 text-indigo-600 dark:text-indigo-400':
                                    activeTab === 'chapter',
                                'text-theme-500 dark:text-theme-400':
                                    activeTab !== 'chapter',
                                'cursor-not-allowed opacity-50': !selectedBook,
                            }"
                            :disabled="!selectedBook"
                            @click="activeTab = 'chapter'"
                        >
                            Kapitel
                        </button>
                    </div>

                    <!-- Search Input -->
                    <div class="mb-4">
                        <input
                            v-model="searchQuery"
                            type="text"
                            :placeholder="
                                activeTab === 'book'
                                    ? 'Bibelbuch suchen...'
                                    : 'Kapitel suchen...'
                            "
                            class="dark:bg-theme-700 text-theme-900 dark:text-theme-100 bg-theme border-theme-300 dark:border-theme-600 w-full rounded-lg border px-3 py-2 text-sm placeholder-gray-500 transition-colors focus:border-indigo-500 focus:ring-2 focus:ring-indigo-500 dark:placeholder-gray-400 dark:focus:border-indigo-400 dark:focus:ring-indigo-400"
                        />
                    </div>

                    <!-- Book Tab Content -->
                    <div v-if="activeTab === 'book'" class="space-y-4">
                        <div
                            v-for="section in filteredSections"
                            :key="section.name"
                        >
                            <button
                                class="hover:bg-theme-100 dark:hover:bg-theme-700 text-theme-900 dark:text-theme-100 flex w-full items-center justify-between rounded-lg p-2 text-left"
                                @click.stop="toggleSection(section.name)"
                            >
                                <span class="font-medium">{{
                                    section.name
                                }}</span>
                                <Icon
                                    name="ChevronRight"
                                    class="h-5 w-5"
                                    :aria-label="'Select book'"
                                />
                            </button>
                            <div
                                v-show="isSectionExpanded(section.name)"
                                class="mt-2 grid grid-cols-5 gap-2 sm:grid-cols-6 md:grid-cols-8"
                            >
                                <button
                                    v-for="book in section.books"
                                    :key="book.id"
                                    class="rounded-lg px-3 py-2 text-center text-sm transition-colors"
                                    :class="[
                                        getCategoryColorClasses(book.category)
                                            .className,
                                        selectedBook?.id === book.id
                                            ? 'ring-2 ring-indigo-500 dark:ring-indigo-400'
                                            : '',
                                    ]"
                                    @click.stop="selectBook(book)"
                                >
                                    {{ book.shortName }}
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Chapter Tab Content -->
                    <div
                        v-else-if="activeTab === 'chapter' && selectedBook"
                        class="grid grid-cols-4 gap-2 sm:grid-cols-6 md:grid-cols-8"
                    >
                        <button
                            v-for="chapterNumber in filteredChapterNumbers"
                            :key="chapterNumber"
                            class="hover:bg-theme-100 dark:hover:bg-theme-700 text-theme-700 dark:text-theme-300 rounded-lg px-3 py-2 text-center text-sm"
                            @click.stop="selectChapter(chapterNumber)"
                        >
                            {{ chapterNumber }}
                        </button>
                    </div>
                </div>
            </div>
        </transition>
    </aside>
```



## Stil

```css
.scrollbar-hide {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}
```


