---
id: SubNavigationBar
title: SubNavigationBar
---

# SubNavigationBar

Toggles the bookmark state TODO: Implement bookmark functionality

## Komponentenstruktur

```
SubNavigationBar
├── Props
```



## Verwendung

```vue
<template>
  <SubNavigationBar />
</template>
```













## Template-Struktur

```html
<nav
        class="dark:bg-theme-800/90 bg-theme/90 relative z-10 shadow-sm backdrop-blur"
        role="navigation"
        aria-label="Chapter navigation"
    >
        <div
            class="mx-auto flex h-12 max-w-7xl items-center justify-between px-4 sm:px-6 lg:px-8"
        >
            <!-- Left side: Chapter and verse info with dropdowns -->
            <div class="flex items-center space-x-4">
                <div class="text-sm" style="z-index: 100">
                    <ReferenceSelector />
                </div>
            </div>

            <!-- Right side: Bookmark buttons -->
            <div class="flex items-center space-x-2">
                <button
                    class="hover:bg-theme-100 dark:hover:bg-theme-700 text-theme-600 hover:text-theme-900 dark:text-theme-400 dark:hover:text-theme-200 rounded-md p-1.5"
                    aria-label="Toggle bookmark"
                    :aria-pressed="isBookmarked ? 'true' : 'false'"
                    @click="toggleBookmark"
                >
                    <Icon
                        name="Bookmark"
                        class="h-5 w-5"
                        :class="{ 'fill-current': isBookmarked }"
                        :aria-label="'Toggle bookmark'"
                    />
                </button>
            </div>
        </div>
    </nav>
```



## Stil

```css
/* Scrollbar styling for dropdown menus */
.overflow-auto {
    scrollbar-width: thin;
    scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

.overflow-auto::-webkit-scrollbar {
    width: 4px;
}

.overflow-auto::-webkit-scrollbar-track {
    background: transparent;
}

.overflow-auto::-webkit-scrollbar-thumb {
    background-color: rgba(156, 163, 175, 0.5);
    border-radius: 20px;
}

/* Dark mode scrollbar */
.dark .overflow-auto {
    scrollbar-color: rgba(75, 85, 99, 0.5) transparent;
}

.dark .overflow-auto::-webkit-scrollbar-thumb {
    background-color: rgba(75, 85, 99, 0.5);
}

/* Ensure dropdowns appear above other elements */
.dropdown-container {
    position: relative;
}
```


