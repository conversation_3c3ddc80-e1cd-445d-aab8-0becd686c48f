---
id: MobileSearchOverlay
title: MobileSearchOverlay
---

# MobileSearchOverlay

MobileSearchOverlay component  Displays a search overlay for mobile devices with a search input and close button. This component is shown when the search is expanded in the NavigationBar.  @emits close - Emitted when the close button is clicked

## Komponentenstruktur

```
MobileSearchOverlay
├── Props
```



## Verwendung

```vue
<template>
  <MobileSearchOverlay />
</template>
```













## Template-Struktur

```html
<div
        class="dark:bg-theme-800 bg-theme fixed inset-x-0 top-0 z-50 flex h-16 items-center px-4"
        role="search"
        aria-label="Mobile search overlay"
    >
        <div class="flex w-full items-center">
            <BibleSearch
                placeholder="Suche nach Bibelversen..."
                aria-label="Search Bible verses"
            />
            <button
                class="text-theme-600 hover:text-theme-900 dark:text-theme-400 dark:hover:text-theme-200 ml-2 p-2"
                aria-label="Close search"
                @click="$emit('close')"
            >
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-6 w-6"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    aria-hidden="true"
                >
                    <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M6 18L18 6M6 6l12 12"
                    />
                </svg>
            </button>
        </div>
    </div>
```




