image: php:8.3

stages:
  - pages
  - build
  - test
  - deploy

variables:
  PHP_VERSION: "8.3"
  NODE_VERSION: "20"
  MYSQL_DATABASE: esra_bibel-test
  MYSQL_ROOT_PASSWORD: ${DB_PASSWORD}
  REDIS_HOST: redis
  REDIS_PORT: 6379
  MEILISEARCH_HOST: http://meilisearch:7700
  MEILISEARCH_KEY: ${MEILISEARCH_MASTER_KEY}
  COMPOSER_HOME: .composer
  COMPOSER_CACHE_DIR: .composer-cache
  TURBO_TOKEN: ${CI_JOB_TOKEN}
  TURBO_TEAM: ${CI_PROJECT_PATH_SLUG}
  PHP_MEMORY_LIMIT: "512M"
  COMPOSER_ALLOW_SUPERUSER: 1

workflow:
  rules:
    - if: $CI_COMMIT_MESSAGE =~ /\[skip-ci\]/
      when: never
    - if: $CI_COMMIT_BRANCH == "main"
      when: always
    - when: always

.web_changes: &web_changes
  changes:
    - apps/web/**/*
    - packages/**/*
    - .gitlab-ci.yml
    - package.json
    - yarn.lock

.docs_changes: &docs_changes
  changes:
    - docs/**/*
    - .gitlab-ci.yml
    - package.json
    - yarn.lock

.setup_deps: &setup_deps
  before_script:
    - apt-get update -yqq
    - apt-get install -yqq git unzip libpq-dev libcurl4-gnutls-dev libicu-dev libvpx-dev libjpeg-dev libpng-dev libxpm-dev zlib1g-dev libfreetype6-dev libxml2-dev libexpat1-dev libbz2-dev libgmp3-dev libldap2-dev unixodbc-dev libsqlite3-dev libaspell-dev libsnmp-dev libpcre3-dev libtidy-dev libonig-dev libzip-dev

    # Install PHP extensions
    - docker-php-ext-configure gd --with-freetype --with-jpeg
    - docker-php-ext-install pdo_mysql zip gd

    # Install Redis extension
    - pecl install redis
    - docker-php-ext-enable redis

    # Install Composer
    - curl -sS https://getcomposer.org/installer | php
    - mv composer.phar /usr/local/bin/composer

    # Install Node.js and Yarn
    - curl -sL https://deb.nodesource.com/setup_${NODE_VERSION}.x | bash -
    - apt-get install -yqq nodejs
    - npm install -g yarn
    - corepack enable
    - corepack prepare yarn@latest --activate

cache:
  key: ${CI_COMMIT_REF_SLUG}
  paths:
    - vendor/
    - .yarn/cache/
    - node_modules/
    - apps/*/node_modules/
    - packages/*/node_modules/
    - packages/*/vendor/
    - apps/*/vendor/
    - .turbo/

build-and-test-web:
  <<: *setup_deps
  stage: build
  services:
    - name: mysql:8.0
      alias: mysql
    - name: redis:alpine
      alias: redis
      command: ["redis-server", "--protected-mode", "no", "--requirepass", "secret"]
    - name: getmeili/meilisearch:v1.13
      alias: meilisearch
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
      changes:
        - apps/web/**/*
        - packages/**/*
        - .gitlab-ci.yml
        - package.json
        - yarn.lock
    - if: $CI_COMMIT_TAG =~ /^web-v.*/
  script:
    # Build steps
    - yarn install
    - yarn turbo run build --filter=@esbo/web...
    - cd apps/web
    - composer install --no-dev --optimize-autoloader

    # Set up test environment
    - cp .env.example .env
    - |
      cat >> .env << EOL
      APP_ENV=testing
      APP_DEBUG=true
      DB_HOST=mysql
      DB_DATABASE=${MYSQL_DATABASE}
      DB_USERNAME=root
      DB_PASSWORD=${MYSQL_ROOT_PASSWORD}
      REDIS_CLIENT=phpredis
      REDIS_HOST=redis
      REDIS_PASSWORD=secret
      REDIS_PORT=6379
      REDIS_PREFIX=test_
      REDIS_CACHE_DB=1
      REDIS_SESSION_DB=2
      REDIS_QUEUE_DB=3
      EOL

    # Install dev dependencies for testing
    - composer install --optimize-autoloader

    # Configure test environment variables
    - export DB_HOST=mysql
    - export DB_DATABASE=${MYSQL_DATABASE}
    - export DB_USERNAME=root
    - export DB_PASSWORD=${MYSQL_ROOT_PASSWORD}
    - export REDIS_HOST=redis
    - export REDIS_PORT=6379
    - export REDIS_PASSWORD=secret
    - export MEILISEARCH_HOST=${MEILISEARCH_HOST}

    # Clear config and cache
    - php artisan config:clear
    - php artisan cache:clear

    # Create test database
    - mysql -h mysql -u root -p${MYSQL_ROOT_PASSWORD} -e "CREATE DATABASE IF NOT EXISTS \`esra-bibel-test\`;"
    - export DB_DATABASE=esra-bibel-test

    # Setup database and test data
    - php artisan migrate:fresh --force
    - php artisan db:seed --force
    - php artisan bible:parse-usx bibletext/Johannes/Johannes.usx Johannes

    # Run tests with detailed output
    - php artisan test --env=testing -vvv
  artifacts:
    paths:
      - apps/web/vendor/
      - apps/web/public/build/
      - apps/web/bootstrap/cache/

deploy-web-production:
  <<: *setup_deps
  stage: deploy
  needs: ["build-and-test-web"]
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
      changes:
        - apps/web/**/*
        - packages/**/*
        - .gitlab-ci.yml
        - package.json
        - yarn.lock
  before_script:
    - apt-get update -yqq
    - apt-get install -yqq rsync openssh-client
    - eval $(ssh-agent -s)
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - echo "$SSH_KNOWN_HOSTS" >> ~/.ssh/known_hosts
    - chmod 644 ~/.ssh/known_hosts
  script:
    # Set up SSH key from secure file
    - mkdir -p ~/.ssh
    - cp $CI_PROJECT_DIR/gitlab-ci-esrabibel.pub ~/.ssh/id_ed25519.pub
    - chmod 600 ~/.ssh/id_ed25519.pub

    # Deploy using rsync
    - cd apps/web
    - |
      # Check if .env exists on server, if not copy from .env.example
      ssh $SSH_USER@$SSH_HOST "cd /var/www/vhosts/esrabibel.de && \
        if [ ! -f .env ]; then \
          cp .env.example .env && \
          php artisan key:generate; \
        fi"

    - rsync -avz --delete \
      --exclude=storage/logs \
      --exclude=storage/app \
      --exclude=storage/framework/sessions \
      --exclude=node_modules \
      --exclude=tests \
      ./ $SSH_USER@$SSH_HOST:/var/www/vhosts/esrabibel.de

    # Run post-deployment commands
    - ssh $SSH_USER@$SSH_HOST "cd /var/www/vhosts/esrabibel.de && \
      php artisan down && \
      php artisan migrate --force && \
      php artisan config:cache && \
      php artisan route:cache && \
      php artisan view:cache && \
      php artisan scout:sync-index-settings && \
      php artisan scout:import 'App\\Models\\Bible' && \
      chown -R www-data:psaserv . && \
      chmod -R 755 . && \
      chmod -R 775 storage && \
      chmod -R 775 bootstrap/cache && \
      php artisan up"
  environment:
    name: production
    url: https://app.esrabibel.de

pages:
  stage: pages
  image: node:lts
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
      changes:
        - docs/**/*
        - .gitlab-ci.yml
  before_script:
    # Setup Yarn
    - corepack enable
    - corepack prepare yarn@latest --activate
  script:
    # Install dependencies and build docs
    - cd docs
    - yarn install
    - yarn build

    # Move the built files to public directory for GitLab Pages
    - mkdir -p ../public
    - cp -r build/* ../public
  artifacts:
    paths:
      - public
  environment:
    name: docs
    url: https://esrabibel.gitlab.io
