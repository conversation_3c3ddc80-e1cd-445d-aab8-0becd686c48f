# Stage 1: build JS assets using Yarn
FROM node:23 AS builder
WORKDIR /workspace
COPY package.json yarn.lock turbo.json .yarnrc.yml ./
COPY apps/web ./apps/web
COPY libs ./libs
RUN corepack enable && corepack prepare yarn@4.8.1 --activate
RUN yarn install
RUN yarn workspace @esbo/web run build

# Stage 2: install PHP & Composer dependencies
FROM php:8.3-apache AS composer
ARG APP_DIR=apps/web
ARG APP_ENV=production
ENV APP_ENV=${APP_ENV}
RUN apt-get update && apt-get install -y \
    git curl libpng-dev libjpeg-dev libfreetype-dev libonig-dev libxml2-dev \
    zip unzip libzip-dev libpq-dev && rm -rf /var/lib/apt/lists/*
RUN docker-php-ext-install -j$(nproc) pdo_mysql pdo_pgsql mbstring exif pcntl bcmath gd zip
RUN pecl install redis && docker-php-ext-enable redis
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer
WORKDIR /var/www/html/${APP_DIR}
COPY ${APP_DIR}/ ./
COPY ${APP_DIR}/composer.json ${APP_DIR}/composer.lock ./
RUN composer install --no-dev --optimize-autoloader --no-interaction --no-scripts

# Stage 3: final image
FROM php:8.3-apache
ARG APP_DIR=apps/web
ARG APP_ENV=production
ENV APP_ENV=${APP_ENV}
RUN apt-get update && apt-get install -y netcat-openbsd && rm -rf /var/lib/apt/lists/*  # install nc for entrypoint
RUN a2enmod rewrite headers
RUN apt-get update && apt-get install -y \
    git curl libpng-dev libjpeg-dev libfreetype-dev libonig-dev libxml2-dev \
    zip unzip libzip-dev libpq-dev && rm -rf /var/lib/apt/lists/* \
    && docker-php-ext-install -j$(nproc) pdo_mysql pdo_pgsql mbstring exif pcntl bcmath gd zip \
    && pecl install redis && docker-php-ext-enable redis
WORKDIR /var/www/html
COPY --from=composer /var/www/html/${APP_DIR} ./apps/web
COPY --from=builder /workspace/apps/web/public ./apps/web/public
COPY docker/apache-config.conf /etc/apache2/sites-available/000-default.conf
COPY docker/entrypoint.sh /usr/local/bin/
RUN rm -f apps/web/bootstrap/cache/packages.php apps/web/bootstrap/cache/services.php || true
RUN chmod +x /usr/local/bin/entrypoint.sh \
    && chown -R www-data:www-data /var/www/html/apps \
    && chmod -R 775 /var/www/html/apps
ENTRYPOINT ["entrypoint.sh"]
CMD ["apache2-foreground"]
