#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

# Check if there are changes in the web app API files
API_CHANGES=$(git diff --cached --name-only | grep -E 'apps/web/(app|routes)/.*\.(php)$')

if [ -n "$API_CHANGES" ]; then
  echo "🔄 API changes detected. Regenerating API documentation..."
  cd apps/web && php artisan scribe:generate && cd ../..
  
  # Copy the generated docs to the Docusaurus static directory
  mkdir -p docs/static/api-docs
  cp -R apps/web/public/docs/* docs/static/api-docs/
  
  # Stage the updated documentation
  git add docs/static/api-docs
  
  echo "✅ API documentation updated successfully."
fi
