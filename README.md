# Bible Reader Application

A Laravel 12 application with Inertia.js (v2), Vue 3, and Pinia for reading and studying the Bible.

## Documentation

Documentation is available in the [Documentation](./docs) directory (which needs to be started with `yarn start`). Or at [https://esb-online-3dbe92.gitlab.io](https://esb-online-3dbe92.gitlab.io).

## Prerequisites

- PHP 8.2+
- Composer 2+
- Node.js 18+
- MySQL 8.0+
- Meilisearch 1.12+
- Yarn 4.5+ (or any other Node.js package manager)

## Installation of Prerequisites

```bash
# 1. Prerequsites Setup
npm install -g yarn
corepack enable
corepack prepare yarn@4.5.3 --activate
yarn set version 4.5.3
```

## Quick Setup Commands

```bash
# 1. Initial Setup
git clone <repository-url>
cd <project-directory>
composer install
yarn install
cp .env.example .env
php artisan key:generate

# 2. Database
php artisan migrate

# 3. Meilisearch Setup
php artisan meilisearch:setup
php artisan meilisearch:optimize-german

# Clear and rebuild indexes
php artisan scout:flush "App\Models\Book"
php artisan scout:flush "App\Models\Verse"
php artisan scout:flush "App\Models\Word"
php artisan scout:flush "App\Models\Footnote"
php artisan scout:flush "App\Models\SearchableText"

php artisan scout:import "App\Models\Book"
php artisan scout:import "App\Models\Verse"
php artisan scout:import "App\Models\Word"
php artisan scout:import "App\Models\Footnote"
php artisan scout:import "App\Models\SearchableText"

# 4. Start Development Servers
php artisan serve
yarn dev
meilisearch --db-path=/Applications/MAMP/htdocs/meilisearch
```

## Development Commands

```bash
php artisan bible:parse-usx bibletext/Johannes/Johannes.usx Johannes -v (or -vv or -vvv)
```

### Database Management

```bash
php artisan migrate:fresh        # Reset database
php artisan migrate:rollback     # Rollback last migration
php artisan migrate:fresh --seed # Reset and seed database
```

### Cache Management

```bash
php artisan optimize:clear    # Clear all caches
php artisan cache:clear      # Clear application cache
php artisan route:clear      # Clear route cache
php artisan config:clear     # Clear config cache
php artisan view:clear       # Clear view cache
```

### Search Management

```bash
# Rebuild specific index
php artisan scout:flush "App\Models\ModelName"
php artisan scout:import "App\Models\ModelName"

# Check Meilisearch
curl http://127.0.0.1:7700/health
curl http://127.0.0.1:7700/stats
```

### Testing & Code Style

```bash
php artisan test              # Run all tests
./vendor/bin/php-cs-fixer fix # Run PHP CS Fixer
yarn lint                  # Run ESLint
yarn lint:fix             # Run ESLint with auto-fix
```

### Production Build

```bash
yarn turbo build --filter=@esbo/web...
php artisan optimize
php artisan model:cache  # Optional
```

## Troubleshooting

If search isn't working:
1. Verify Meilisearch is running: `curl http://127.0.0.1:7700/health`
2. Check .env configuration
3. Rebuild indexes
4. Clear Laravel caches: `php artisan optimize:clear`
5. Check Meilisearch logs: `tail -f /var/log/meilisearch.log`

## Documentation Links

- [Laravel](https://laravel.com/docs)
- [Inertia.js](https://inertiajs.com/)
- [Vue 3](https://vuejs.org/)
- [Pinia](https://pinia.vuejs.org/)
- [Meilisearch](https://docs.meilisearch.com/)
