{"name": "@esbo/enums", "version": "0.1.0", "private": true, "type": "module", "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.js"}}, "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "eslint": "^9.10.0", "typescript": "^5.3.3"}}