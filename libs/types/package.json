{"name": "@esbo/types", "version": "1.0.0", "private": true, "type": "module", "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}}, "files": ["dist"], "scripts": {"build": "tsc", "dev": "tsc -w", "clean": "<PERSON><PERSON><PERSON> dist", "typecheck": "tsc --noEmit", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix"}, "keywords": ["bible", "types", "typescript"], "author": "", "license": "MIT", "devDependencies": {"@typescript-eslint/eslint-plugin": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "eslint": "^9.10.0", "rimraf": "^5.0.10", "typescript": "^5.8.2"}, "packageManager": "yarn@4.7.0"}