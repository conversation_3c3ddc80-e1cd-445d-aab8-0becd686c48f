export type MarginSize = 'margin-wide' | 'margin-normal' | 'margin-narrow';
export type FontSize =
    | 'xs'
    | 'sm'
    | 'base'
    | 'lg'
    | 'xl'
    | '2xl'
    | '3xl'
    | '4xl';
export type LineSpacing = 'normal' | 'relaxed' | 'loose';
export type ColorTheme = 'default' | 'papyrus' | 'gray' | 'high-contrast';
export type ThemeMode = 'light' | 'dark' | 'system';

export interface TextSettings {
    fontSize: FontSize | string;
    lineSpacing: LineSpacing | string;
    showVerseNumbers: boolean;
    flowText: boolean;
    showFootnotes: boolean;
    focusedMode: boolean;
    useInfiniteScroll: boolean;
    marginSize: MarginSize;
    showChapterNumbers: boolean;
    showBookNames: boolean;
    themeMode: ThemeMode;
    colorTheme: ColorTheme;
}
