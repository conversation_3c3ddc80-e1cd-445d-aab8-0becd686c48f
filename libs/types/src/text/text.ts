import type { Paragraph } from './paragraph';
import type { Style } from './style';

/**
 * Represents a text block
 */
export interface Text {
    /** The text ID */
    id: number;
    /** The text content */
    content: string;
    /** The text paragraphs */
    paragraphs: Paragraph[];
    /** The text styles */
    styles: Style[];
    /** The text metadata */
    metadata: {
        /** The text language */
        language: string;
        /** The text version */
        version: string;
        /** The text copyright */
        copyright: string;
    };
}
