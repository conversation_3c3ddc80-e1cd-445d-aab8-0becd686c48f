/**
 * Represents a text style
 */
export interface Style {
    /** The style ID */
    id: number;
    /** The style name */
    name: string;
    /** The style properties */
    properties: {
        /** The font family */
        fontFamily: string;
        /** The font size */
        fontSize: number;
        /** The font weight */
        fontWeight: number;
        /** The font style */
        fontStyle: 'normal' | 'italic';
        /** The text decoration */
        textDecoration: string;
        /** The text color */
        color: string;
        /** The background color */
        backgroundColor: string;
        /** The line height */
        lineHeight: number;
        /** The letter spacing */
        letterSpacing: number;
        /** The text transform */
        textTransform: 'none' | 'uppercase' | 'lowercase' | 'capitalize';
        /** The text align */
        textAlign: 'left' | 'center' | 'right' | 'justify';
        /** The margin */
        margin: {
            /** The top margin */
            top: number;
            /** The right margin */
            right: number;
            /** The bottom margin */
            bottom: number;
            /** The left margin */
            left: number;
        };
        /** The padding */
        padding: {
            /** The top padding */
            top: number;
            /** The right padding */
            right: number;
            /** The bottom padding */
            bottom: number;
            /** The left padding */
            left: number;
        };
    };
}

export type Mode = 'light' | 'dark';
export type Weight = 100 | 200 | 500;
export type StyleType = 'text' | 'bg' | 'border';
