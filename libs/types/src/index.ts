// Bible types
export type {
    BaseBook,
    Book,
    BookMetadata,
    BookView,
    Chapter,
    Footnote,
    FootnoteContentElement,
    FootnoteContentStructure,
    ParagraphGroup,
    ReadingProgress,
    SimpleBook,
    Verse,
    Word,
    WordGroup,
    WordParagraphGroup,
    FootnoteClickEvent,
    FootnoteHoverEvent,
    FootnoteState,
} from './bible';

// Common types
export type { FilterType } from './common/filters';
export type { User } from './common/user';

// Display types
export type {
    DisplayReference,
    NavigationSection,
    NavigationState,
} from './display/navigation';

export type {
    BookSection,
    ChapterSection,
    FrontmatterSection,
    Section,
} from './display/section';

export type {
    ChapterWindow,
    FrontmatterWindow,
    Window,
} from './display/window';

export type { Bookmark } from './display/bookmarks';

export type {
    BibleReaderState,
    BibleReference,
    DisplayResponse,
    LoadMoreOptions,
    LoadTarget,
    LoadingConfig,
    ParsedReference,
    ReferenceError,
    ScrollDirection,
    SectionRequest,
    SectionResponse,
} from './display/responses';

// Search types
export type {
    GeneralSearchResult,
    SearchMatch,
    SearchRequest,
    SearchResponse,
    SearchResult,
    SearchResultMetadata,
    SearchResultType,
    SearchResultsState,
    Suggestion,
} from './search/search';

// Text types
export type { Paragraph } from './text/paragraph';
export type {
    ColorTheme,
    FontSize,
    LineSpacing,
    MarginSize,
    TextSettings,
    ThemeMode,
} from './text/settings';
export type { Mode, Style, StyleType, Weight } from './text/style';
export type { Text } from './text/text';

// Enums and constants
export {
    BookCategory,
    OriginalLanguage,
    Testament,
    bookCategoryLabels,
} from './common/enums';
