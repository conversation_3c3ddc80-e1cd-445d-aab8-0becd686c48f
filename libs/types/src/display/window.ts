import type { NavigationState } from './navigation';
import type { ChapterSection, FrontmatterSection, Section } from './section';

/**
 * Represents a window in the application
 */
export interface Window {
    /** The window ID */
    id: number;
    /** The window title */
    title: string;
    /** The window content */
    content: string;
    /** The window sections */
    sections: Section[];
    /** The window navigation */
    navigation: NavigationState;
    /** The window metadata */
    metadata: {
        /** The window type */
        type: string;
        /** The window mode */
        mode: 'single' | 'parallel' | 'split';
        /** The window layout */
        layout: 'default' | 'compact' | 'wide';
        /** The window position */
        position: {
            /** The x position */
            x: number;
            /** The y position */
            y: number;
            /** The width */
            width: number;
            /** The height */
            height: number;
        };
    };
}

/**
 * Represents a chapter window with navigation sections
 */
export interface ChapterWindow {
    /** The current chapter section */
    current: ChapterSection;
    /** The previous chapter section, if any */
    previous?: ChapterSection;
    /** The next chapter section, if any */
    next?: ChapterSection;
    /** The next-next chapter section, if any */
    nextNext?: ChapterSection;
    /** The frontmatter section, if any */
    frontmatter?: FrontmatterSection;
}

/**
 * Represents a frontmatter window
 */
export interface FrontmatterWindow extends Window {
    /** The frontmatter sections */
    sections: FrontmatterSection[];
}
