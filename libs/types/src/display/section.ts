import type { BaseBook, Book } from '../bible/book';
import type { Verse } from '../bible/content';

/**
 * Base section interface
 */
export interface BaseSection {
    id: string;
    type:
        | 'frontmatter'
        | 'chapter-current'
        | 'chapter-previous'
        | 'chapter-next'
        | 'chapter-next-next';
    book: BaseBook;
}

/**
 * Frontmatter section
 */
export interface FrontmatterSection extends BaseSection {
    type: 'frontmatter';
    number: number;
    id: string;
    book: Book;
}

/**
 * Chapter section
 */
export interface ChapterSection extends BaseSection {
    type:
        | 'chapter-current'
        | 'chapter-previous'
        | 'chapter-next'
        | 'chapter-next-next';
    id: string;
    book: Book; // Book for chapter 1, BaseBook for others
    number: number;
    verses: Verse[];
}

/**
 * Section type (union of FrontmatterSection and ChapterSection)
 */
export type Section = FrontmatterSection | ChapterSection;

/**
 * Book section to describes the shape of book data coming from Inertia
 */
export interface BookSection {
    name: string;
    books: Book[];
}
