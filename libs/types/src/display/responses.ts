import type { BaseBook, Book } from '../bible/book';
import type { FrontmatterSection, ChapterSection } from './section';
import type { NavigationState, DisplayReference } from './navigation';

/**
 * Loading configuration
 */
export interface LoadingConfig {
    nextChapter: {
        book: BaseBook;
        number: number;
    } | null;
    hasMoreNext: boolean;
    hasMorePrevious: boolean;
}

/**
 * Display reference for navigation
 */
// Removed the DisplayReference interface

/**
 * Response type for display data
 */
export interface DisplayResponse {
    reference: DisplayReference;
    sections: (FrontmatterSection | ChapterSection)[];
    navigation: NavigationState;
    loadingConfig?: LoadingConfig;
}

/**
 * Bible reference
 */
export interface BibleReference {
    book?: string;
    chapter?: number;
    verseStart?: number;
    verseEnd?: number;
}

/**
 * Parsed reference
 */
export interface ParsedReference {
    type: 'book' | 'chapter' | 'verse' | 'verse-range';
    reference: BibleReference;
}

/**
 * Section response
 */
export interface SectionResponse {
    sections: {
        previous: FrontmatterSection | ChapterSection | null;
        current: FrontmatterSection | ChapterSection;
        next: FrontmatterSection | ChapterSection | null;
        nextNext: FrontmatterSection | ChapterSection | null;
    };
    navigation: NavigationState;
    loadingConfig: LoadingConfig;
}

/**
 * Section request
 */
export interface SectionRequest {
    book: string;
    type: 'frontmatter' | 'chapter';
    chapter?: number;
    loadDirection: 'previous' | 'next' | 'nextNext';
}

/**
 * Scroll direction
 */
export type ScrollDirection = 'forward' | 'backward';

/**
 * Load more options
 */
export interface LoadMoreOptions {
    preserveState: boolean;
    preserveScroll: boolean;
    only: string[];
}

/**
 * Load target
 */
export interface LoadTarget {
    book: string;
    chapter: number;
}

/**
 * Bible reader state
 */
export interface BibleReaderState {
    chapters: Map<string, ChapterSection>;
    books: Map<string, Book>;
    currentViewportChapterId: string | null;
    isLoadingNext: boolean;
    isLoadingPrev: boolean;
    maxLoadedChapters: number;
    bookOrder: string[];
}

/**
 * Reference error
 */
export class ReferenceError extends Error {
    public constructor(message: string) {
        super(message);
        this.name = 'ReferenceError';
    }
}
