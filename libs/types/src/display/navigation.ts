import type { BaseBook, Book, BookView } from '../bible/book';

/**
 * Display reference for navigation
 */
export interface DisplayReference {
    book: Book;
    chapter: number;
    frontmatter: boolean;
    verseStart: number | null;
    verseEnd: number | null;
    verseRanges?: { start: number; end: number }[];
    slug?: string;
}

/**
 * Navigation state
 */
export interface NavigationState {
    currentBook: BaseBook;
    previousBook: BaseBook | null;
    nextBook: BaseBook | null;
    url?: string;
    verseReference?: string;
    scrollToVerse?: DisplayReference;
    scrollToChapter?: DisplayReference;
    scrollToFrontmatter?: DisplayReference;
    scrollToVerseRange?: DisplayReference;
}

export interface NavigationSection {
    name: string;
    books: BookView[];
}
