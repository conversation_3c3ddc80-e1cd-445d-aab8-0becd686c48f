import type { Book, SimpleBook } from './book';
import type { Footnote } from './footnotes';

/**
 * Represents a single word in a verse
 */
export interface Word {
    verseNumber: number;
    text: string;
    textAfter: string;
    isOtQuote: boolean;
    otQuoteGroupId: string | null;
    isEmphasized: boolean;
    isAddition: boolean;
    isFootnote: boolean;
    isVariant: boolean;
    wordType: 'add' | 'om' | 'va' | 'xot' | null;
    variantGroupId: string | null;
    wordGroupId: string | null;
    footnoteGroupId: string | null;
    footnote: Footnote | null;
    paragraphGroupId: string | null;
    position: number;
    paragraphStyle: string | null;
}

/**
 * Represents a group of words that share variant or OT quote properties
 */
export type WordGroup = {
    words: Word[];
    isVariant: boolean;
    wordType: Word['wordType'];
    variantGroupId: Word['variantGroupId'];
    wordGroupId: Word['wordGroupId'];
    isOtQuote: boolean;
    isAddition: boolean;
    paragraphStyle: Word['paragraphStyle'];
};

export interface WordParagraphGroup {
    paragraphGroupId: string;
    paragraphStyle: string | null;
    words: Word[];
    verseNumbers: number[];
    isPericopeStart?: boolean;
}

/**
 * Represents a verse in a chapter
 */
export interface Verse {
    number: number;
    words?: Word[];
    wordGroups: WordGroup[];
    paragraphGroupId?: string;
    paragraphStyle?: string | null;
    isPericopeStart?: boolean;
    hasOtQuote?: boolean;
    hasTextVariant?: boolean;
}

/**
 * Represents a chapter in a book
 */
export interface Chapter {
    bookId: number;
    number: number;
    verses: Verse[];
    book?: Book | SimpleBook;
    verseCount: number;
}

export interface ParagraphGroup {
    isPericopeStart: boolean;
    pericopeTitle?: string;
    verses: Verse[];
    paragraphGroupId: string;
}
