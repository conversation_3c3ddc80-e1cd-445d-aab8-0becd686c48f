import type { Book<PERSON><PERSON>gor<PERSON>, Testament } from '../common/enums';
import type { Chapter } from './content';
import type { BookMetadata, ReadingProgress } from './metadata';

/**
 * Base interface for a book in the Bible
 */
export interface BaseBook {
    id: number;
    order: number;
    slug: string;
    name: string;
    chapterCount: number;
    category: BookCategory;
    hasContent: boolean;
}

/**
 * Interface for a book in the Bible
 */
export interface Book extends BaseBook {
    abbreviation: string;
    testament: Testament;
    testamentLabel: string;
    searchNames: string;
    chapters?: Chapter[];
    readingProgress?: ReadingProgress;
    metadata?: BookMetadata;
}

/**
 * Simple version of a book for basic display
 */
export type SimpleBook = BaseBook;

/**
 * Interface for a book view in the Bible
 */
export interface BookView extends BaseBook {
    abbreviation: string;
    chapters: Chapter[] | number[]; // Allow both types to be compatible
    hasContent: boolean;
}
