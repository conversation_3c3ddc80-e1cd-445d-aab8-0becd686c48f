/**
 * Unified types for footnote event emits (used in VerseContent, ChapterContent, etc.)
 */
export interface FootnoteClickEvent {
  event: MouseEvent;
  footnote: Footnote;
  word: string;
  referenceEl: HTMLElement | null;
}

export interface FootnoteHoverEvent {
  event: MouseEvent;
  footnote: Footnote | null;
  word?: string;
  referenceEl: HTMLElement | null;
}
/**
 * Represents a footnote content element
 */
export interface FootnoteContentElement {
    type: 'text' | 'caller' | 'styled' | string;
    content: string;
    style?: string;
    href?: string;
    title?: string;
}

/**
 * Represents a footnote content structure
 */
export interface FootnoteContentStructure {
    elements: FootnoteContentElement[];
    metadata: {
        has_references: boolean;
        total_elements: number;
    };
}

/**
 * Represents a footnote
 */
export interface Footnote {
    id: number;
    searchableText: string;
    referencedWord: string | null;
    verseId: number;
    hasItalics: boolean;
    position: number;
    isReference?: boolean;
    contentStructure?: FootnoteContentStructure;
}

export interface FootnoteState {
  footnote: Footnote;
  word: string;
  reference: string;
  x: number;
  y: number;
  isClickLocked: boolean;
  referenceEl: HTMLElement | null;
}
