import type { OriginalLanguage } from '../common/enums';

/**
 * Represents reading progress for a book
 */
export interface ReadingProgress {
    /** The book ID */
    bookId: number;
    /** The last read chapter number */
    lastReadChapter: number;
    /** The last read verse number */
    lastReadVerse: number;
    /** The last read timestamp */
    lastReadAt: string;
    /** The reading progress percentage */
    progress: number;
}

/**
 * Represents metadata for a book
 */
export interface BookMetadata {
    location: string | null;
    authors: string[];
    writtenYear: number | null;
    theme: string | null;
    keyPeople: string | null;
    keyWords: string | null;
    keyTeachings: string | null;
    keyVerses: string | null;
    covenants: string | null;
    attributesOfGod: string | null;
    historicalPeriod: string | null;
    originalLanguage: OriginalLanguage;
}
