export interface EnumOption {
    value: string;
    label: string;
}

export enum Testament {
    OT = 'ot',
    NT = 'nt',
}

export enum BookCategory {
    LAW = 'law',
    HISTORY = 'history',
    WISDOM = 'wisdom',
    PROPHECY = 'prophecy',
    GOSPEL = 'gospel',
    EPISTLE = 'epistle',
    APOCALYPTIC = 'apocalypse',
}

export enum OriginalLanguage {
    HEBREW = 'hebrew',
    GREEK = 'greek',
    ARAMAIC = 'aramaic',
    MIXED = 'greek-aramaic',
}

export const bookCategoryLabels: Record<BookCategory, string> = {
    [BookCategory.LAW]: 'Gesetz',
    [BookCategory.HISTORY]: 'Geschichte',
    [BookCategory.WISDOM]: 'Weisheit',
    [BookCategory.PROPHECY]: 'Propheten',
    [BookCategory.GOSPEL]: 'Evangelien',
    [BookCategory.EPISTLE]: 'Briefe',
    [BookCategory.APOCALYPTIC]: 'Apokalypse',
};
