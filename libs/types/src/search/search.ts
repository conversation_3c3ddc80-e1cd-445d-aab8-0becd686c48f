import type { BookView, Footnote } from '../bible';
import { BibleReference } from '../display';

/**
 * Represents a search match
 */
export interface SearchMatch {
    /** The matched text */
    text: string;
    /** Start position of the match */
    start: number;
    /** End position of the match */
    end: number;
}

/**
 * Represents a search result
 */
export interface SearchResult {
    /** The book containing the search result */
    book: BookView;
    /** The chapter number */
    chapter: number;
    /** The verse number */
    verse: number;
    /** The text content */
    text: string;
    /** The matched portions */
    matches: SearchMatch[];
}

/**
 * Represents metadata for a search result
 */
export interface SearchResultMetadata {
    /** Associated tags */
    tags: string[];
    /** End verse number if applicable */
    endVerse: number | null;
    /** Start verse number if applicable */
    startVerse: number | null;
    /** Whether contains Old Testament quote */
    hasOtQuote: boolean;
    /** Whether contains text variant */
    hasTextVariant: boolean;
    /** Book name if applicable */
    bookName: string | null;
    /** Chapter number if applicable */
    chapterNumber: number | null;
    /** Verse number if applicable */
    verseNumber: number | null;
}

/**
 * Represents a general search result
 */
export interface GeneralSearchResult {
    /** Unique identifier */
    id: number;
    /** Type of the result */
    type: string;
    /** Title of the result */
    title: string;
    /** Content of the result */
    content: string;
    /** URL to the result */
    url: string;
    /** Associated metadata */
    metadata: SearchResultMetadata;
    /** Footnote information if applicable */
    footnoteInfo?: Footnote;
}

/**
 * Represents the state of search results
 */
export interface SearchResultsState {
    /** Array of search results */
    data: GeneralSearchResult[];
    /** Metadata about the search results */
    metadata: {
        /** Total number of results */
        total: number;
        /** Results per page */
        perPage: number;
        /** Current page number */
        currentPage: number;
        /** Last page number */
        lastPage: number;
    };
    /** Error message if any */
    error?: string | null;
}

/**
 * Represents a search request
 */
export interface SearchRequest {
    /** Search query string */
    query: string;
    /** Optional book filter */
    book?: string;
    /** Optional chapter filter */
    chapter?: number;
    /** Type of search */
    type?: 'text' | 'reference';
}

/**
 * Represents a search response
 */
export interface SearchResponse {
    /** Array of search results */
    results: SearchResult[];
    /** Total number of results */
    total: number;
    /** Current page number */
    page: number;
    /** Results per page */
    perPage: number;
}

/**
 * Represents a search result type
 */
export interface SearchResultType {
    /** Unique identifier */
    id: number;
    /** Name of the result type */
    name: string;
}

export type Suggestion = {
    display?: string;
    name?: string;
    url?: string;
    isReference?: boolean;
    versePreview?: string;
    reference?: BibleReference;
    book?: BookView;
};
