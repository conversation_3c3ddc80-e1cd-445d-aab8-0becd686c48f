concurrent = 1

[[runners]]
  name = "local-docker-runner"
  url = "https://gitlab.com/"
  token = "local-testing"
  executor = "docker"
  [runners.docker]
    tls_verify = false
    image = "php:8.4"
    privileged = false
    disable_entrypoint_overwrite = false
    oom_kill_disable = false
    disable_cache = false
    volumes = ["/cache"]
    shm_size = 0
    network_mode = "esb-test-net"
  [runners.cache]
    Type = "local"
    Path = "/cache"
    Shared = true
