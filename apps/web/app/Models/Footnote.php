<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Lara<PERSON>\Scout\Searchable;

class Footnote extends Model
{
    use HasFactory, Searchable;

    protected $fillable = [
        'verse_id',
        'position',
        'caller',
        'content',
        'searchable_text',
        'content_structure',
        'referenced_word',
        'is_reference',
        'has_italics'
    ];

    protected $casts = [
        'content_structure' => 'array',
        'is_reference' => 'boolean',
        'has_italics' => 'boolean'
    ];

    /**
     * Get the indexable data array for the model.
     *
     * @return array
     */
    public function toSearchableArray()
    {
        return [
            'id' => $this->id,
            'searchable_text' => $this->searchable_text,
            'referenced_word' => $this->referenced_word,
            'verse_id' => $this->verse_id
        ];
    }

    /**
     * Get the verse that owns the footnote.
     */
    public function verse()
    {
        return $this->belongsTo(Verse::class);
    }

    /**
     * Get the words that reference this footnote.
     */
    public function words(): HasMany
    {
        return $this->hasMany(Word::class);
    }
}
