<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Chapter extends Model
{
    use HasFactory;

    protected $fillable = [
        'book_id',
        'number',
        'title',
        'summary',
    ];

    /**
     * Get the book that owns the chapter.
     */
    public function book(): BelongsTo
    {
        return $this->belongsTo(Book::class);
    }

    /**
     * Get the verses for the chapter.
     */
    public function verses(): HasMany
    {
        return $this->hasMany(Verse::class)->orderBy('number');
    }

    /**
     * Get the reading progress records for this chapter.
     */
    public function readingProgress(): Has<PERSON><PERSON>
    {
        return $this->hasMany(ChapterReadingProgress::class);
    }

    public function footnotes()
    {
        return $this->morphMany(Footnote::class, 'footnoteable');
    }

    /**
     * Helper method to get the next chapter in the book
     */
    public function next()
    {
        return static::where('book_id', $this->book_id)
            ->where('number', '>', $this->number)
            ->orderBy('number')
            ->first();
    }

    /**
     * Helper method to get the previous chapter in the book
     */
    public function previous()
    {
        return static::where('book_id', $this->book_id)
            ->where('number', '<', $this->number)
            ->orderBy('number', 'desc')
            ->first();
    }

    /**
     * Check if this is the first chapter in the book
     */
    public function isFirst(): bool
    {
        return $this->number === 1;
    }

    /**
     * Check if this is the last chapter in the book
     */
    public function isLast(): bool
    {
        return $this->number === $this->book->chapters_count;
    }

    /**
     * Get reading progress for a specific user
     */
    public function getReadingProgressForUser($userId): ?ChapterReadingProgress
    {
        return $this->readingProgress()
            ->where('user_id', $userId)
            ->first();
    }

    /**
     * Check if chapter is completed for a specific user
     */
    public function isCompleted($userId): bool
    {
        $progress = $this->getReadingProgressForUser($userId);
        return $progress?->completed_at !== null;
    }

    /**
     * Scope to get chapters by book
     */
    public function scopeByBook($query, $bookId)
    {
        return $query->where('book_id', $bookId);
    }

    /**
     * Get the chapter number with leading zero if needed
     */
    public function getFormattedNumberAttribute(): string
    {
        return str_pad($this->number, 2, '0', STR_PAD_LEFT);
    }

    /**
     * Get the full reference (e.g., "Genesis 1")
     */
    public function getFullReferenceAttribute(): string
    {
        return "{$this->book->name} {$this->number}";
    }
}
