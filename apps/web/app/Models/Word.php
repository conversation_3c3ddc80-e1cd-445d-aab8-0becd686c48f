<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Laravel\Scout\Searchable;

class Word extends Model
{
    use HasFactory, Searchable;

    protected $fillable = [
        'text',
        'text_after',
        'position',
        'verse_id',
        'has_footnote',
        'has_variant',
        'is_emphasized',
        'is_addition',
        'is_ot_quote',
        'footnote_id',
        'footnote_group_id',
        'paragraph_style_id',
        'word_type',
        'variant_group_id',
        'strongs_number',
        'word_group_id',
        'paragraph_group_id'
    ];

    protected $casts = [
        'is_ot_quote' => 'boolean',
        'is_addition' => 'boolean',
        'has_footnote' => 'boolean',
        'has_variant' => 'boolean',
        'is_emphasized' => 'boolean',
    ];

    /**
     * Get the footnote associated with this word.
     */
    public function footnote(): BelongsTo
    {
        return $this->belongsTo(Footnote::class);
    }

    /**
     * Get the verse this word belongs to.
     */
    public function verse(): BelongsTo
    {
        return $this->belongsTo(Verse::class);
    }

     /**
     * Get the paragraph style group this word belongs to.
     */
    public function paragraphStyle(): BelongsTo
    {
        return $this->belongsTo(ParagraphStyle::class);
    }

    /**
     * Get the chapter through the verse relationship.
     */
    public function chapter(): BelongsTo
    {
        return $this->belongsTo(Chapter::class)->through('verse');
    }

    /**
     * Get the book through the verse relationship.
     */
    public function book(): BelongsTo
    {
        return $this->belongsTo(Book::class)->through('verse.chapter');
    }

    /**
     * Get the next word in the verse
     */
    public function next()
    {
        return static::where('verse_id', $this->verse_id)
            ->where('position', '>', $this->position)
            ->orderBy('position')
            ->first();
    }

    /**
     * Get the previous word in the verse
     */
    public function previous()
    {
        return static::where('verse_id', $this->verse_id)
            ->where('position', '<', $this->position)
            ->orderBy('position', 'desc')
            ->first();
    }


    /**
     * Scope to find words by transliteration
     */
    public function scopeByTransliteration($query, string $transliteration)
    {
        return $query->where('transliteration', 'LIKE', "%{$transliteration}%");
    }

    /**
     * Scope to find words in a specific verse
     */
    public function scopeInVerse($query, $verseId)
    {
        return $query->where('verse_id', $verseId)->orderBy('position');
    }

    /**
     * Scope to find words in a specific chapter
     */
    public function scopeInChapter($query, $chapterId)
    {
        return $query->whereHas('verse', function ($query) use ($chapterId) {
            $query->where('chapter_id', $chapterId);
        })->orderBy('verse_id')->orderBy('position');
    }

    /**
     * Get the full reference including the word (e.g., "Genesis 1:1 word[3]")
     */
    public function getFullReferenceAttribute(): string
    {
        return "{$this->verse->full_reference} word[{$this->position}]";
    }

    /**
     * Get words that share the same Strong's number
     */
    public function getSimilarWords()
    {
        if (!$this->strongs_number) {
            return collect();
        }

        return static::where('strongs_number', $this->strongs_number)
            ->where('id', '!=', $this->id)
            ->get();
    }

    /**
     * Get the context of the word (surrounding words)
     */
    public function getContext(int $wordsBefore = 3, int $wordsAfter = 3)
    {
        return static::where('verse_id', $this->verse_id)
            ->whereBetween('position', [
                $this->position - $wordsBefore,
                $this->position + $wordsAfter
            ])
            ->orderBy('position')
            ->get();
    }

    public function getEffectiveParagraphStyle()
    {
        // Return word-level style if exists, otherwise check verse-level
        if ($this->paragraph_style_id) {
            return $this->paragraphStyle;
        }
    }

    /**
     * Get the name of the index associated with the model.
     */
    public function searchableAs(): string
    {
        return 'words';
    }

    /**
     * Get the searchable array for Scout.
     *
     * @return array
     */
    public function toSearchableArray()
    {
        return [
            'id' => $this->id,
            'text' => $this->text,
            'text_after' => $this->text_after,
            'position' => $this->position,
            'is_ot_quote' => $this->is_ot_quote,
            'is_emphasized' => $this->is_emphasized,
            'is_addition' => $this->is_addition,
            'is_footnote' => $this->is_footnote,
            'is_variant' => $this->is_variant,
            'word_type' => $this->word_type,
        ];
    }
}
