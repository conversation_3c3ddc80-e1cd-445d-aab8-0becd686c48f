<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use <PERSON>vel\Scout\Searchable;

class SearchableText extends Model
{
    use Searchable;

    protected $fillable = [
        'type',
        'content',
        'metadata',
        'verse_id',
        'chapter_id',
        'book_id'
    ];

    protected $casts = [
        'metadata' => 'array'
    ];

    public function book()
    {
        return $this->belongsTo(Book::class);
    }

    public function chapter()
    {
        return $this->belongsTo(Chapter::class);
    }

    public function verse()
    {
        return $this->belongsTo(Verse::class);
    }

    public function toSearchableArray()
    {
        $this->loadMissing(['book', 'chapter', 'verse']);

        $array = [
            'id' => $this->id,
            'type' => $this->type,
            'content' => $this->content,
            'metadata' => $this->metadata,
            'searchable_content' => $this->content // Add a dedicated field for searching
        ];

        // Add book information if available
        if ($this->book) {
            $array['book_id'] = $this->book->id;
            $array['book_name'] = $this->book->name;
            $array['book_slug'] = $this->book->slug;
            $array['testament'] = $this->book->testament;
            $array['category'] = $this->book->category;
        }

        // Add chapter information if available
        if ($this->chapter) {
            $array['chapter_id'] = $this->chapter->id;
            $array['chapter_number'] = $this->chapter->number;
        }

        // Add verse information if available
        if ($this->verse) {
            $array['verse_id'] = $this->verse->id;
            $array['verse_number'] = $this->verse->number;
            $array['reference_full'] = $this->getReferenceFormats()['full'];
        }

        return $array;
    }

    /**
     * Get different formats of the verse reference
     */
    protected function getReferenceFormats(): array
    {
        if (!$this->book || !$this->chapter || !$this->verse) {
            return [
                'full' => null,
                'short' => null,
                'numeric' => null
            ];
        }

        $base = [
            'full' => "{$this->book->name} {$this->chapter->number}",
            'short' => "{$this->book->abbreviation} {$this->chapter->number}",
            'numeric' => "{$this->book->id}.{$this->chapter->number}"
        ];

        if ($this->verse->start_verse === $this->verse->end_verse) {
            $verseRef = $this->verse->number;
        } else {
            $verseRef = "{$this->verse->start_verse}-{$this->verse->end_verse}";
        }

        $base['full'] .= ",$verseRef";
        $base['short'] .= ",$verseRef";
        $base['numeric'] .= ",$verseRef";

        return $base;
    }

    /**
     * Find verses by reference (e.g., "1.Mose 1,1" or "Gen 1,1-3")
     */
    public static function findByReference(string $reference)
    {
        // Handle verse ranges (e.g., "1.Mose 1,1-3")
        if (preg_match('/^(.+?)\s+(\d+),(\d+)-(\d+)$/', $reference, $matches)) {
            [, $bookName, $chapterNumber, $startVerse, $endVerse] = $matches;
            
            return static::whereHas('book', function ($query) use ($bookName) {
                $query->where('name', $bookName)
                    ->orWhere('abbreviation', $bookName)
                    ->orWhereRaw("FIND_IN_SET(?, search_names) > 0", [$bookName]);
            })
            ->whereHas('chapter', function ($query) use ($chapterNumber) {
                $query->where('number', $chapterNumber);
            })
            ->whereHas('verse', function ($query) use ($startVerse, $endVerse) {
                $query->where('start_verse', '<=', $endVerse)
                    ->where('end_verse', '>=', $startVerse);
            })
            ->where('type', 'verse')
            ->orderBy('verse.number')
            ->get();
        }
        
        // Handle single verse (e.g., "1.Mose 1,1")
        if (preg_match('/^(.+?)\s+(\d+),(\d+)$/', $reference, $matches)) {
            [, $bookName, $chapterNumber, $verseNumber] = $matches;
            
            return static::whereHas('book', function ($query) use ($bookName) {
                $query->where('name', $bookName)
                    ->orWhere('abbreviation', $bookName)
                    ->orWhereRaw("FIND_IN_SET(?, search_names) > 0", [$bookName]);
            })
            ->whereHas('chapter', function ($query) use ($chapterNumber) {
                $query->where('number', $chapterNumber);
            })
            ->whereHas('verse', function ($query) use ($verseNumber) {
                $query->where('number', $verseNumber);
            })
            ->where('type', 'verse')
            ->first();
        }

        return null;
    }
}
