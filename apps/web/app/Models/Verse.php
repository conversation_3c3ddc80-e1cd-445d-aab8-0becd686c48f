<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Lara<PERSON>\Scout\Searchable;

class Verse extends Model
{
    use HasFactory, Searchable;

    protected $fillable = [
        'chapter_id',
        'number',
        'start_verse',
        'end_verse',
        'text',
        'tags',
        'is_pericope_start',
        'has_ot_quote',
        'has_text_variant',
        'paragraph_style_id',
        'paragraph_group_id'
    ];

    protected $casts = [
        'number' => 'integer',
        'start_verse' => 'integer',
        'end_verse' => 'integer',
        'tags' => 'json',
        'is_pericope_start' => 'boolean',
        'has_ot_quote' => 'boolean',
        'has_text_variant' => 'boolean',
    ];

    /**
     * Get the book that owns the verse.
     */
    public function book(): BelongsTo
    {
        return $this->belongsTo(Book::class);
    }

    /**
     * Get the chapter that owns the verse.
     */
    public function chapter(): BelongsTo
    {
        return $this->belongsTo(Chapter::class);
    }

    /**
     * Get the words for the verse.
     */
    public function words(): HasMany
    {
        return $this->hasMany(Word::class)->orderBy('position');
    }

    /**
     * Get the footnotes for the verse.
     */
    public function footnotes(): HasMany
    {
        return $this->hasMany(Footnote::class);
    }

    /**
     * Get the paragraph style for the verse.
     */
    public function paragraphStyle(): BelongsTo
    {
        return $this->belongsTo(ParagraphStyle::class);
    }

    /**
     * Get the display text for the verse.
     */
    public function getDisplayTextAttribute(): string
    {
        $text = '';
        $words = $this->words()->with('footnote')->get();

        foreach ($words as $word) {
            $text .= $word->text;
            if ($word->footnote) {
                $text .= '[' . $word->footnote->id . ']';
            }
            $text .= ' ';
        }

        return trim($text);
    }

    /**
     * Get the verse range as a string.
     */
    public function getVerseRangeAttribute(): string
    {
        if (!$this->start_verse || $this->start_verse === $this->end_verse) {
            return (string)$this->number;
        }
        return "{$this->start_verse}-{$this->end_verse}";
    }

    public function getEffectiveParagraphStyles()
    {
        // Get both verse-level and word-level styles
        $styles = collect();

        // Add unique word-level styles
        $styles = $styles->merge(
            $this->words()
                ->with('paragraphStyle')
                ->get()
                ->pluck('paragraphStyle')
                ->unique('id')
        );

        return $styles->unique('id');
    }

    public function getWordsWithStyle($styleCode)
    {
        return $this->words()
            ->whereHas('paragraphStyle', function($query) use ($styleCode) {
                $query->where('style_code', $styleCode);
            })
            ->orderBy('position')
            ->get();
    }

    /**
     * Get the name of the index associated with the model.
     */
    public function searchableAs(): string
    {
        return 'verses';
    }

    /**
     * Get the indexable data array for the model.
     *
     * @return array
     */
    public function toSearchableArray(): array
    {
        return [
            'id' => $this->id,
            'chapter_id' => $this->chapter_id,
            'number' => $this->number,
            'text' => $this->text,
            'start_verse' => $this->start_verse,
            'end_verse' => $this->end_verse,
            'has_ot_quote' => $this->has_ot_quote,
            'has_text_variant' => $this->has_text_variant,
            'paragraph_style_id' => $this->paragraph_style_id,
            'paragraph_group_id' => $this->paragraph_group_id
        ];
    }
}
