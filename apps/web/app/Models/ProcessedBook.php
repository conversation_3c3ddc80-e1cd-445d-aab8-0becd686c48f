<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ProcessedBook extends Model
{
    protected $table = 'processed_books';

    protected $fillable = [
        'filename',
        'book_name',
        'status',
        'extra_data'
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'extra_data' => 'json'
    ];

    /**
     * Get the book associated with the processed book.
     */
    public function book(): BelongsTo
    {
        return $this->belongsTo(Book::class, 'book_name', 'slug');
    }
}
