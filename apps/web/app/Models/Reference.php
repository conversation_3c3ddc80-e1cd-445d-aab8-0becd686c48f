<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Reference extends Model
{
    use HasFactory;

    protected $fillable = [
        'footnote_id',
        'type',
        'target_book',
        'target_chapter',
        'target_verse',
        'display_text',
        'attributes'
    ];

    protected $casts = [
        'target_chapter' => 'integer',
        'attributes' => 'json'
    ];

    public function footnote()
    {
        return $this->belongsTo(Footnote::class);
    }
}
