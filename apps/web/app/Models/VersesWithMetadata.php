<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class VersesWithMetadata extends Model
{
    protected $table = 'verses_with_metadata';

    protected $fillable = [
        'verse_id',
        'verse_text',
        'verse_number',
        'chapter_number',
        'book_name',
        'book_abbreviation',
        'words',
        'original_words',
        'tags',
        'footnotes'
    ];

    protected $casts = [
        'words' => 'array',
        'original_words' => 'array',
        'tags' => 'array',
        'footnotes' => 'array'
    ];

    public function verse()
    {
        return $this->belongsTo(Verse::class);
    }
}
