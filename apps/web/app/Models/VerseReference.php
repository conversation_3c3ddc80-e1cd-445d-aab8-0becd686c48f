<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class VerseReference extends Model
{
    protected $fillable = [
        'footnote_id',
        'referenced_chapter',
        'referenced_verse'
    ];

    protected $casts = [
        'referenced_chapter' => 'integer',
        'referenced_verse' => 'integer'
    ];

    /**
     * Get the footnote that owns the verse reference.
     */
    public function footnote()
    {
        return $this->belongsTo(Footnote::class);
    }

    /**
     * Get the referenced verse (if it exists in the database).
     */
    public function referencedVerse()
    {
        return $this->belongsTo(Verse::class, 'referenced_verse', 'verse_number')
            ->where('chapter_number', $this->referenced_chapter);
    }
}
