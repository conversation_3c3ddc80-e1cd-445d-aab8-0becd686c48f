<?php

namespace App\Models;

use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;
use App\Enums\BookCategory;
use App\Enums\OriginalLanguage;
use App\Enums\Testament;
use App\Models\Chapter;
use App\Models\Verse;
use App\Models\BookSummary;
use App\Models\ReadingProgress;
use App\Models\BookNameVariation;
use Illuminate\Support\Collection;
use Laravel\Scout\Searchable;

class Book extends Model
{
    use HasFactory, Searchable;

    protected $uniqueFields = ['abbreviation', 'order'];

    protected $fillable = [
        'name',
        'slug',
        'abbreviation',
        'order',
        'testament',
        'category',
        'chapters_count',
        'original_language',
        'location',
        'historical_period',
        'authors',
        'written_year',
        'theme',
        'key_people',
        'attributes_of_god',
        'key_words',
        'covenants',
        'key_teachings',
        'key_verses',
        'search_names'
    ];

    protected $casts = [
        'testament' => Testament::class,
        'category' => BookCategory::class,
        'original_language' => OriginalLanguage::class,
        'written_year' => 'integer',
        'chapters_count' => 'integer',
        'order' => 'integer',
        'key_people' => 'array',
        'attributes_of_god' => 'array',
        'key_words' => 'array',
        'covenants' => 'array',
        'key_teachings' => 'array',
        'key_verses' => 'array',
        'authors' => 'array',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($book) {
            if (!$book->slug) {
                $book->slug = static::createSlug($book->abbreviation);
            }
        });

        static::updating(function ($book) {
            if ($book->isDirty('abbreviation') && !$book->isDirty('slug')) {
                $book->slug = static::createSlug($book->abbreviation);
            }
        });
    }

    protected static function createSlug($text)
    {
        // Convert to lowercase while preserving umlauts
        $slug = mb_strtolower($text, 'UTF-8');

        // Replace spaces and special characters (except umlauts) with hyphens
        $slug = preg_replace('/[^a-zäöüß0-9-]+/u', '-', $slug);

        // Remove multiple consecutive hyphens
        $slug = preg_replace('/-+/', '-', $slug);

        // Remove leading and trailing hyphens
        $slug = trim($slug, '-');

        // Capitalize first letter (including umlauts)
        return mb_convert_case(mb_substr($slug, 0, 1, 'UTF-8'), MB_CASE_TITLE, 'UTF-8') .
               mb_substr($slug, 1, null, 'UTF-8');
    }

    public function chapters(): HasMany
    {
        return $this->hasMany(Chapter::class)->orderBy('number');
    }

    public function verses(): HasManyThrough
    {
        return $this->hasManyThrough(Verse::class, Chapter::class);
    }

    public function summary(): HasOne
    {
        return $this->hasOne(BookSummary::class);
    }

    public function readingProgress(): HasMany
    {
        return $this->hasMany(ReadingProgress::class);
    }

    public function nameVariations(): HasMany
    {
        return $this->hasMany(BookNameVariation::class);
    }

    /**
     * Relationships
     */

    /**
     * Scopes
     */
    public function scopeOldTestament($query)
    {
        return $query->where('testament', Testament::OT);
    }

    public function scopeNewTestament($query)
    {
        return $query->where('testament', Testament::NT);
    }

    public function scopeByCategory($query, BookCategory $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Helper methods
     */
    public function getReadingProgressForUser($userId): ?ReadingProgress
    {
        return $this->readingProgress()
            ->where('user_id', $userId)
            ->first();
    }

    public function isCompleted($userId): bool
    {
        $progress = $this->getReadingProgressForUser($userId);
        return $progress?->completed_at !== null;
    }

    /**
     * Get the indexable data array for the model.
     *
     * @return array
     */
    public function toSearchableArray(): array
    {
        $variations = $this->nameVariations()->pluck('variation')->toArray();
        $chapters = $this->chapters()->pluck('number')->toArray();
        
        // Get all verse references using a direct query to avoid relationship issues
        $verses = [];
        if ($this->chapters_count > 0) {
            $verses = \DB::table('verses')
                ->join('chapters', 'verses.chapter_id', '=', 'chapters.id')
                ->where('chapters.book_id', $this->id)
                ->select('verses.number', 'chapters.number as chapter_number')
                ->get()
                ->map(fn($verse) => "{$verse->chapter_number}:{$verse->number}")
                ->toArray();
        }

        // Combine all search terms
        $searchTerms = array_merge(
            [$this->name, $this->abbreviation, $this->slug],
            $variations,
            array_map(fn($c) => "{$this->name} {$c}", $chapters),
            array_map(fn($v) => "{$this->name} {$v}", $verses),
            // Add dot-less versions for numbered books
            $this->name ? [str_replace('. ', '', $this->name)] : []
        );

        return array_filter([
            'id' => $this->id,
            'name' => $this->name,
            'slug' => $this->slug,
            'abbreviation' => $this->abbreviation,
            'search_terms' => array_unique(array_filter($searchTerms)),
            'variations' => $variations,
            'chapters' => $chapters,
            'verses' => $verses,
            'testament' => $this->testament?->value,
            'category' => $this->category?->value,
            'chapters_count' => $this->chapters_count,
            'order' => $this->order,
            'has_content' => $this->has_content,
        ], function ($value) {
            return !is_null($value);
        });
    }

    /**
     * Get the name of the index associated with the model.
     */
    public function searchableAs(): string
    {
        return 'books';
    }

    public static function findByReference($bookPart)
    {
        // Clean up the book part
        $bookPart = trim($bookPart);

        // Try exact match first
        $book = static::where('name', $bookPart)
            ->orWhere('abbreviation', $bookPart)
            ->first();

        if ($book) {
            return $book;
        }

        // Then try Meilisearch for fuzzy matching
        return static::search($bookPart)
            ->within('books')
            ->take(1)
            ->get()
            ->first();
    }

    /**
     * Search for books by name, slug, abbreviation or name variations
     * @param string $term The search term
     * @return Collection
     */
    public static function searchByNameOrVariation(string $term): Collection
    {
        $term = trim($term);

        return static::query()
            ->where('name', 'like', "%{$term}%")
            ->orWhere('slug', 'like', "%{$term}%")
            ->orWhere('abbreviation', 'like', "%{$term}%")
            ->orWhereHas('nameVariations', function ($query) use ($term) {
                $query->where('name', 'like', "%{$term}%");
            })
            ->with('nameVariations')
            ->get()
            ->map(function ($book) {
                return [
                    'id' => $book->id,
                    'order' => $book->order,
                    'name' => $book->name,
                    'slug' => $book->slug,
                    'hasContent' => $book->has_content,
                    'chapterCount' => $book->chapters_count,
                    'category' => $book->category
                ];
            });
    }


    /**
     * Check if the book has any content (verses)
     */
    public function getHasContentAttribute(): bool
    {
        return $this->chapters()->whereHas('verses')->exists();
    }
}
