<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ParagraphSpan extends Model
{
    protected $fillable = [
        'paragraph_style_id',
        'start_chapter_id',
        'start_verse_id',
        'end_chapter_id',
        'end_verse_id',
        'style_code',
        'attributes'
    ];

    protected $casts = [
        'attributes' => 'array'
    ];

    /**
     * Get the paragraph style associated with this span
     */
    public function paragraphStyle(): BelongsTo
    {
        return $this->belongsTo(ParagraphStyle::class);
    }

    /**
     * Get the starting chapter
     */
    public function startChapter(): BelongsTo
    {
        return $this->belongsTo(Chapter::class, 'start_chapter_id');
    }

    /**
     * Get the ending chapter
     */
    public function endChapter(): BelongsTo
    {
        return $this->belongsTo(Chapter::class, 'end_chapter_id');
    }

    /**
     * Get the starting verse
     */
    public function startVerse(): BelongsTo
    {
        return $this->belongsTo(Verse::class, 'start_verse_id');
    }

    /**
     * Get the ending verse
     */
    public function endVerse(): BelongsTo
    {
        return $this->belongsTo(Verse::class, 'end_verse_id');
    }

    /**
     * Check if this span contains a specific verse
     */
    public function containsVerse(Verse $verse): bool
    {
        $verseInStartChapter = $verse->chapter_id >= $this->start_chapter_id;
        $verseInEndChapter = $verse->chapter_id <= $this->end_chapter_id;

        if (!$verseInStartChapter || !$verseInEndChapter) {
            return false;
        }

        if ($verse->chapter_id === $this->start_chapter_id) {
            if ($this->start_verse_id && $verse->number < $this->startVerse->number) {
                return false;
            }
        }

        if ($verse->chapter_id === $this->end_chapter_id) {
            if ($this->end_verse_id && $verse->number > $this->endVerse->number) {
                return false;
            }
        }

        return true;
    }

    /**
     * Get all verses within this span
     */
    public function getVerses()
    {
        return Verse::whereHas('chapter', function ($query) {
            $query->where('id', '>=', $this->start_chapter_id)
                  ->where('id', '<=', $this->end_chapter_id);
        })->where(function ($query) {
            if ($this->start_verse_id && $this->end_verse_id) {
                $query->whereBetween('id', [$this->start_verse_id, $this->end_verse_id]);
            } elseif ($this->start_verse_id) {
                $query->where('id', '>=', $this->start_verse_id);
            } elseif ($this->end_verse_id) {
                $query->where('id', '<=', $this->end_verse_id);
            }
        })->orderBy('chapter_id')
          ->orderBy('number')
          ->get();
    }

    /**
     * Get all words within this span
     */
    public function getWords()
    {
        return Word::whereHas('verse', function ($query) {
            $query->whereHas('chapter', function ($q) {
                $q->where('id', '>=', $this->start_chapter_id)
                  ->where('id', '<=', $this->end_chapter_id);
            });
        })->where(function ($query) {
            if ($this->start_verse_id && $this->end_verse_id) {
                $query->whereHas('verse', function ($q) {
                    $q->whereBetween('id', [$this->start_verse_id, $this->end_verse_id]);
                });
            } elseif ($this->start_verse_id) {
                $query->whereHas('verse', function ($q) {
                    $q->where('id', '>=', $this->start_verse_id);
                });
            } elseif ($this->end_verse_id) {
                $query->whereHas('verse', function ($q) {
                    $q->where('id', '<=', $this->end_verse_id);
                });
            }
        })->orderBy('verse_id')
          ->orderBy('position')
          ->get();
    }

    /**
     * Scope a query to find spans that contain a specific verse
     */
    public function scopeContainingVerse($query, Verse $verse)
    {
        return $query->where('start_chapter_id', '<=', $verse->chapter_id)
                    ->where('end_chapter_id', '>=', $verse->chapter_id)
                    ->where(function ($q) use ($verse) {
                        $q->whereNull('start_verse_id')
                          ->orWhereHas('startVerse', function ($sq) use ($verse) {
                              $sq->where('number', '<=', $verse->number);
                          });
                    })
                    ->where(function ($q) use ($verse) {
                        $q->whereNull('end_verse_id')
                          ->orWhereHas('endVerse', function ($sq) use ($verse) {
                              $sq->where('number', '>=', $verse->number);
                          });
                    });
    }

    /**
     * Get a readable representation of the span range
     */
    public function getRange(): string
    {
        $start = $this->start_chapter_id;
        if ($this->start_verse_id) {
            $start .= ':' . $this->startVerse->number;
        }

        $end = $this->end_chapter_id;
        if ($this->end_verse_id) {
            $end .= ':' . $this->endVerse->number;
        }

        return "{$start}-{$end}";
    }
}
