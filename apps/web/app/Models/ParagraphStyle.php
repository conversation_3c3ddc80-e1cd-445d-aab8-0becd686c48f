<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ParagraphStyle extends Model
{
    use HasFactory;

    protected $fillable = [
        'style_code',
        'description',
        'attributes'
    ];

    protected $casts = [
        'attributes' => 'json'
    ];

    public function verses()
    {
        return $this->hasMany(Verse::class);
    }
}
