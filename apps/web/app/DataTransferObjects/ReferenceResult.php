<?php

namespace App\DataTransferObjects;

use App\Models\Book;

readonly class ReferenceResult
{
    public function __construct(
        public Book $book,
        public int $chapter,
        public ?int $verse,
        public ?int $verseEnd,
        public string $url
    ) {}

    public function toArray(): array
    {
        return [
            'book' => $this->book,
            'chapter' => $this->chapter,
            'verse' => $this->verse,
            'verseEnd' => $this->verseEnd,
            'url' => $this->url,
        ];
    }
}
