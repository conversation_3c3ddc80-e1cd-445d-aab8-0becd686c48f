<?php

namespace App\DataTransferObjects;

use App\Services\BibleReference;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Pagination\LengthAwarePaginator;

class SearchResult implements Arrayable
{
    public function __construct(
        public readonly array $data,
        public readonly array $metadata = [],
        public readonly ?string $error = null
    ) {}

    public static function fromSearch($results, array $metadata = []): self
    {
        if ($results instanceof LengthAwarePaginator) {
            return new self(
                data: $results->items(),
                metadata: array_merge([
                    'total' => $results->total(),
                    'perPage' => $results->perPage(),
                    'currentPage' => $results->currentPage(),
                    'lastPage' => $results->lastPage(),
                ], $metadata)
            );
        }

        if (is_array($results)) {
            return new self(
                data: $results,
                metadata: $metadata
            );
        }

        return new self(
            data: $results->toArray(),
            metadata: $metadata
        );
    }

    public static function fromReference(BibleReference $reference): self
    {
        return new self(
            data: [],
            metadata: [
                'reference' => $reference->toString(),
                'book' => $reference->getBook(),
                'chapter' => $reference->getChapter(),
                'verse' => $reference->getVerse(),
            ]
        );
    }

    public static function error(string $message): self
    {
        return new self(
            data: [],
            metadata: [],
            error: $message
        );
    }

    public function toArray(): array
    {
        return [
            'data' => $this->data,
            'metadata' => $this->metadata,
            'error' => $this->error,
        ];
    }
}
