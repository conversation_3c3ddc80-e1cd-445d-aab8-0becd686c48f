<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class SearchRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'q' => ['nullable', 'string'],
            'types' => ['nullable', 'array'],
            'types.*' => ['string', 'in:books,verses,words,footnotes'],
            'page' => ['nullable', 'integer', 'min:1'],
            'per_page' => ['nullable', 'integer', 'min:1', 'max:100'],
            'book' => ['nullable', 'string'],
            'include_metadata' => ['nullable', 'boolean'],
        ];
    }

    public function searchTypes(): array
    {
        return $this->input('types', ['books', 'verses', 'words', 'footnotes']);
    }

    public function perPage(): int
    {
        return $this->input('per_page', 20);
    }

    public function includeMetadata(): bool
    {
        return $this->boolean('include_metadata');
    }
}
