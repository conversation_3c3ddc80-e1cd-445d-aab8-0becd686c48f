<?php

namespace App\Http\Requests\Book;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class FetchAdditionalChaptersRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'bookId' => ['required', 'integer', 'exists:books,order'],
            'chapter' => ['required', 'integer', 'min:1'],
            'direction' => ['required', Rule::in(['next', 'previous'])],
        ];
    }
}
