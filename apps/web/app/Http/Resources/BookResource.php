<?php

namespace App\Http\Resources;

use App\Enums\Testament;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin \App\Models\Book */

class BookResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'slug' => $this->slug,
            'order' => $this->order,
            'chapterCount' => $this->chapters_count,
            'abbreviation' => $this->abbreviation,
            'testament' => $this->testament?->value ?? 'ot',
            'testamentLabel' => $this->testament === Testament::OT ? 'AT' : 'NT',
            'category' => $this->category?->value ?? 'law',
            'searchNames' => $this->search_names,
            'metadata' => [
                'location' => $this->location,
                'authors' => $this->authors,
                'writtenYear' => $this->written_year,
                'theme' => $this->theme,
                'keyPeople' => $this->key_people,
                'keyWords' => $this->key_words,
                'keyTeachings' => $this->key_teachings,
                'keyVerses' => $this->key_verses,
                'covenants' => $this->covenants,
                'attributesOfGod' => $this->attributes_of_god,
                'historicalPeriod' => $this->historical_period,
                'originalLanguage' => $this->original_language?->value ?? 'hebrew',
            ]
        ];
    }
}
