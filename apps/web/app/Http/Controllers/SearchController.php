<?php

namespace App\Http\Controllers;

use App\Http\Requests\SearchRequest;
use App\Services\Search\BibleSearchService;
use App\Services\BibleReferenceService;
use App\Models\Book;
use Illuminate\Http\RedirectResponse;
use Inertia\Inertia;
use Inertia\Response;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class SearchController extends Controller
{
    private int $perPage = 25;

    public function __construct(
        private readonly BibleSearchService $searchService,
        private readonly BibleReferenceService $referenceService
    ) {}

    public function __invoke(Request $request, ?string $query = null, ?string $page = null): Response
    {
        // Get search types from request (POST or GET), then session, then default
        $types = $request->input('types', $request->session()->get('searchSettings.types', ['books', 'footnotes', 'metadata']));

        // Ensure types is always an array
        if (!is_array($types)) {
            $types = [$types];
        }

        $page = !$page ? $request->input('page') : $page;

        // Store the types in session for future requests
        $request->session()->put('searchSettings.types', $types);

        Log::info('Search request', [
            'query' => $query,
            'page' => $page,
            'request_params' => $request->all(),
            'session_types' => $types
        ]);

        // If no search criteria, return the search interface
        if (!$query) {
            return Inertia::render('SearchResults', [
                'query' => '',
                'results' => [
                    'data' => [],
                    'metadata' => [
                        'total' => 0,
                        'per_page' => $this->perPage,
                        'current_page' => 1,
                        'last_page' => 1
                    ],
                    'error' => null
                ],
                'filters' => [
                    'types' => $types
                ]
            ]);
        }

        $bookId = $request->input('book');

        // If book is selected but no query, show first chapter of that book
        if ($bookId && !$query) {
            $book = Book::find($bookId);
            if ($book) {
                return redirect('/' . $book->slug . '1');
            }
        }

        Log::info('Performing search with types', [
            'types' => $types,
            'bookId' => $request->input('bookId')
        ]);

        // Perform the search
        $result = $this->searchService->search(
            $query,
            $types,
            $request->input('bookId'),
            false,
            $this->perPage,
            (int)$page ?: 1
        );

        Log::info('Search completed', [
            'data' => $result->data,
            'metadata' => $result->metadata,
            'error' => $result->error
        ]);

        // If we have metadata but no data, try to fetch the data directly
        if (empty($result->data) && isset($result->metadata['total']) && $result->metadata['total'] > 0) {
            Log::info('No data but metadata shows results exist, fetching directly');

            // Map frontend types to database types
            $typeMap = [
                'books' => 'verse',
                'verses' => 'verse',
                'footnotes' => 'footnote',
                'words' => 'word',
                'metadata' => 'metadata'
            ];

            // Convert frontend types to database types
            $searchTypes = empty($types)
                ? array_values($typeMap)
                : array_map(fn($type) => $typeMap[$type] ?? $type, $types);

            // Directly query the database
            $searchQuery = \App\Models\SearchableText::search($query)
                ->when($request->input('bookId'), function ($query) use ($request) {
                    $query->where('book_id', $request->input('bookId'));
                })
                ->when(!empty($searchTypes), function ($query) use ($searchTypes) {
                    $query->whereIn('type', $searchTypes);
                });

            $results = $searchQuery->take($this->perPage)->get();

            if ($results->isNotEmpty()) {
                $results->load(['book', 'chapter', 'verse']);
                $transformedResults = $results->map(function($item) {
                    $reference = $item->book
                        ? sprintf('%s %d,%d', $item->book->name, $item->chapter->number, $item->verse->number)
                        : null;

                    return [
                        'id' => $item->id,
                        'type' => $item->type,
                        'title' => $reference,
                        'subtitle' => $item->book ? $item->book->name : null,
                        'content' => $item->content,
                        'url' => $item->book
                            ? $item->book->slug . $item->chapter->number . ',' . $item->verse->number
                            : '/',
                        'relevance' => 1,
                        'reference' => $reference,
                        'metadata' => [
                            'tags' => $item->metadata['tags'] ?? [],
                            'endVerse' => $item->metadata['end_verse'] ?? null,
                            'startVerse' => $item->metadata['start_verse'] ?? null,
                            'hasOtQuote' => $item->metadata['has_ot_quote'] ?? false,
                            'hasTextVariant' => $item->metadata['has_text_variant'] ?? false,
                            'bookName' => $item->book ? $item->book->name : null,
                            'chapterNumber' => $item->chapter ? $item->chapter->number : null,
                            'verseNumber' => $item->verse ? $item->verse->number : null,
                        ]
                    ];
                })->toArray();

                Log::info('Retrieved data directly', ['count' => count($transformedResults)]);

                // Create a new result with the transformed data
                $result = new \App\DataTransferObjects\SearchResult(
                    data: $transformedResults,
                    metadata: $result->metadata,
                    error: null
                );
            }
        }

        // Always render SearchResults when a search is performed
        return Inertia::render('SearchResults', [
            'query' => $query,
            'results' => $result->toArray(),
            'filters' => [
                'types' => $types
            ]
        ]);
    }

    public function saveSettings(Request $request): Response
    {
        if ($request->has('types')) {
            $request->session()->put('searchSettings.types', $request->input('types'));
        }

        return Inertia::render('SearchResults', [
            'query' => $request->input('query', ''),
            'types' => $request->input('types', ['books']),
            'filters' => [
                'types' => $request->input('types', ['books'])
            ],
        ]);
    }

    public function search(Request $request)
    {
        $query = $request->input('query');
        $page = (int) $request->input('page', 1);
        $types = $request->input('types', ['verse', 'footnote', 'book', 'metadata']);

        Log::info('API Search request', [
            'query' => $query,
            'page' => $page,
            'types' => $types
        ]);

        if (!$query) {
            return response()->json([
                'data' => [],
                'metadata' => [
                    'total' => 0,
                    'perPage' => $this->perPage,
                    'currentPage' => 1,
                    'lastPage' => 1
                ]
            ]);
        }

        $result = $this->searchService->search(
            $query,
            $types,
            null,
            true,
            $this->perPage,
            $page
        );

        return response()->json($result);
    }
}
