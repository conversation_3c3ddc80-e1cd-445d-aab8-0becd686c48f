<?php

namespace App\Http\Controllers;

use App\Models\Book;
use App\Services\BibleReferenceParser;
use App\Services\ChapterService;
use App\Services\Transformers\BookTransformer;
use App\Services\Transformers\ChapterTransformer;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\Log;
use App\Http\Requests\Book\FetchAdditionalChaptersRequest;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use App\Models\ProcessedBook;

class BookController extends Controller
{
    private array $sections = [];
    public $includeFootnotes = true;

    public function __construct(
        private BibleReferenceParser $referenceParser,
        private ChapterService $chapterService,
        private BookTransformer $bookTransformer,
        private ChapterTransformer $chapterTransformer,
    ) {}

    /**
     * Build a chapter section array for response
     *
     * @param string $type The section type (chapter-current, chapter-next, chapter-previous)
     * @param mixed $chapter The chapter object
     * @param Book $currentBook The current book
     * @param Book|null $alternativeBook The alternative book (previous or next)
     * @param array $options Transformation options
     * @return array The formatted chapter section
     */
    private function buildChapterSection(string $type, $chapter, Book $currentBook, ?Book $alternativeBook = null, array $options = []): array
    {
        $isCurrentBook = $chapter->book_id === $currentBook->id;
        $bookToUse = $isCurrentBook ? $currentBook : $alternativeBook;

        // Default options
        $options = array_merge([
            'includeWords' => false,
            'includeWordGroups' => true,
            'includeFootnotes' => $this->includeFootnotes,
        ], $options);

        // If we have an array instead of an Eloquent model, we need to convert it to a collection
        if (is_array($chapter) || $chapter instanceof \ArrayAccess) {
            $verses = collect($chapter['verses'] ?? []);
            // Add transformed words if they exist
            if (isset($chapter['verses'])) {
                foreach ($chapter['verses'] as &$verse) {
                    $verse['words'] = $verse['words'] ?? [];
                    $verse['footnotes'] = $verse['footnotes'] ?? [];
                }
            }
        } else {
            $verses = $chapter->verses;
        }

        return [
            'type' => $type,
            'id' => $chapter->number > 0 ? sprintf('%s%d',
                $bookToUse->slug,
                $chapter->number
            ) : $bookToUse->slug,
            'book' => $this->bookTransformer->transformCore($bookToUse),
            'number' => $chapter->number,
            'verses' => $this->chapterTransformer->transformVerses($verses, [
                'includeWords' => $options['includeWords'],
                'includeWordGroups' => $options['includeWordGroups'],
                'includeFootnotes' => $options['includeFootnotes'],
            ])
        ];
    }

    /**
     * Build a frontmatter section array for response
     *
     * @param Book $book The book
     * @return array The formatted frontmatter section
     */
    private function buildFrontmatterSection(Book $book): array
    {
        return [
            'type' => 'frontmatter',
            'id' => $book->slug,
            'book' => $this->bookTransformer->transform($book),
        ];
    }

    /**
     * Main entry point for displaying chapters with infinite scroll support
     */
    public function show(Request $request, string $reference)
    {
        try {
            $parsedReference = $this->parseAndValidateReference($request, $reference);
            if ($parsedReference instanceof \Symfony\Component\HttpFoundation\Response) {
                return $parsedReference; // Return error response if validation failed
            }

            $book = $parsedReference['book'];
            $chapterNumber = $parsedReference['chapter'];
            $isFrontmatter = $parsedReference['is_frontmatter'] ?? false;

            // Get request parameters with defaults
            $includeWords = $request->boolean('words', false);
            $includeWordGroups = $request->boolean('wordGroups', true);
            $includeFootnotes = $request->boolean('footnotes', $this->includeFootnotes);

            // Get chapter window with context
            $chapterWindow = $this->chapterService->getChapterWindow(
                $book,
                $chapterNumber,
                [
                    'includeWords' => $includeWords,
                    'includeWordGroups' => $includeWordGroups,
                    'includeFootnotes' => $includeFootnotes,
                ]
            );

            // Check if chapter window is null (no data available)
            if ($chapterWindow === null) {
                return Inertia::render('NotFound', [
                    'requestedPath' => $reference,
                    'customMessage' => 'Keine Daten vorhanden'
                ])->toResponse($request)->setStatusCode(404);
            }

            // Build sections array based on the chapter window
            $sections = $this->buildSectionsArray($book, $chapterWindow, $isFrontmatter, [
                'includeWords' => $includeWords,
                'includeWordGroups' => $includeWordGroups,
                'includeFootnotes' => $includeFootnotes,
            ]);

            // Build the response data
            $response = $this->buildResponseData($parsedReference, $book, $chapterWindow, $sections);

            return Inertia::render('Display', $response);
        } catch (\Exception $e) {
            Log::error('Error displaying bible reference:', [
                'reference' => $reference,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return Inertia::render('NotFound', [
                'requestedPath' => $reference
            ])->toResponse($request)->setStatusCode(404);
        }
    }

    /**
     * Parse and validate the reference
     */
    private function parseAndValidateReference(Request $request, string $reference)
    {
        try {
            $parsedReference = $this->referenceParser->parse($reference);
        } catch (\Exception $e) {
            Log::warning('Invalid reference', [
                'reference' => $reference,
                'error' => $e->getMessage()
            ]);
            return Inertia::render('NotFound', [
                'requestedPath' => $reference
            ])->toResponse($request)->setStatusCode(404);
        }

        // Validate book exists
        $book = Book::where('slug', $parsedReference['book']->slug)->firstOrFail();
        $parsedReference['book'] = $book;

        // Check if book has content
        $hasContent = $book->chapters()->whereHas('verses')->exists();
        $parsedReference['hasContent'] = $hasContent;

        // Handle frontmatter case
        if (!$parsedReference['chapter']) {
            $parsedReference['chapter'] = 0;
            $parsedReference['is_frontmatter'] = true;
        }

        // Validate chapter exists
        if ($parsedReference['chapter'] && !$parsedReference['is_frontmatter']) {
            if ($parsedReference['chapter'] < 1 || $parsedReference['chapter'] > $book->chapters_count) {
                Log::error('Invalid chapter number', [
                    'book' => $book->name,
                    'chapter' => $parsedReference['chapter'],
                    'max_chapters' => $book->chapters_count
                ]);
                return Inertia::render('NotFound', [
                    'requestedPath' => $reference
                ])->toResponse($request)->setStatusCode(404);
            }
        }

        // Validate verse exists if specified
        if ($parsedReference['verse_start']) {
            $chapter = $book->chapters()->where('number', $parsedReference['chapter'])->first();
            if (!$chapter || !$chapter->verses()->where('number', $parsedReference['verse_start'])->exists()) {
                Log::error('Invalid verse number', [
                    'book' => $book->name,
                    'chapter' => $parsedReference['chapter'],
                    'verse' => $parsedReference['verse_start']
                ]);
                return Inertia::render('NotFound', [
                    'requestedPath' => $reference
                ])->toResponse($request)->setStatusCode(404);
            }
        }

        return $parsedReference;
    }

    /**
     * Build sections array based on chapter window
     *
     * @param Book $book The book model
     * @param array $chapterWindow The chapter window data
     * @param bool $isFrontmatter Whether to show frontmatter
     * @param array $options Options for including word data
     *   - bool $includeWords Whether to include the words array (default: false)
     *   - bool $includeWordGroups Whether to include word groups (default: true)
     *   - bool $includeFootnotes Whether to include footnotes (default: false)
     * @return array The formatted sections
     */
    private function buildSectionsArray(Book $book, array $chapterWindow, bool $isFrontmatter, array $options = []): array
    {
        // Set default options
        $options = array_merge([
            'includeWords' => false,
            'includeWordGroups' => true,
            'includeFootnotes' => false,
        ], $options);

        $this->sections = [];

        // Add previous chapters
        $this->addPreviousChapters($book, $chapterWindow, $options);


        // Add current chapter or frontmatter
        $this->addCurrentSection($book, $chapterWindow, $isFrontmatter, $options);

        // Add next chapters
        $this->addNextChapters($book, $chapterWindow, $options);

        return $this->sections;
    }

    /**
     * Add previous chapters to sections array
     *
     * @param Book $book The book model
     * @param array $chapterWindow The chapter window data
     * @param array $options Options for including word data
     */
    private function addPreviousChapters(Book $book, array $chapterWindow, array $options = []): void
    {
        // Previous previous chapter if available
        if ($chapterWindow['previousPrevious']) {
            $this->sections[] = $this->buildChapterSection(
                'chapter-previous',
                $chapterWindow['previousPrevious'],
                $book,
                $chapterWindow['previousBook'],
                $options
            );
        }

        // Previous chapter
        if ($chapterWindow['previous']) {
            $this->sections[] = $this->buildChapterSection(
                'chapter-previous',
                $chapterWindow['previous'],
                $book,
                $chapterWindow['previousBook'],
                $options
            );
        }
    }

    /**
     * Add current section (frontmatter or chapter) to sections array
     *
     * @param Book $book The book model
     * @param array $chapterWindow The chapter window data
     * @param bool $isFrontmatter Whether to show frontmatter
     * @param array $options Options for including word data
     */
    private function addCurrentSection(Book $book, array $chapterWindow, bool $isFrontmatter, array $options = []): void
    {
        if ($isFrontmatter) {
            // For frontmatter requests, add the frontmatter section
            $this->sections[] = $this->buildFrontmatterSection($book);

            // Add chapter 1 as the next section
            $chapterOne = $book->chapters()->where('number', 1)->first();
            if ($chapterOne) {
                $this->sections[] = $this->buildChapterSection(
                    'chapter-current',
                    $chapterOne,
                    $book,
                    null,
                    $options
                );
            }
        } else if ($chapterWindow['current']->number < 3) {
            // For early chapters, include frontmatter
            $this->sections[] = $this->buildFrontmatterSection($book);

            // Add the current chapter
            $this->sections[] = $this->buildChapterSection(
                'chapter-current',
                $chapterWindow['current'],
                $book,
                null,
                $options
            );
        } else {
            // Normal case - just add the current chapter
            $this->sections[] = $this->buildChapterSection(
                'chapter-current',
                $chapterWindow['current'],
                $book,
                null,
                $options
            );
        }
    }

    /**
     * Add next chapters to sections array
     *
     * @param Book $book The book model
     * @param array $chapterWindow The chapter window data
     * @param array $options Options for including word data
     */
    private function addNextChapters(Book $book, array $chapterWindow, array $options = []): void
    {
        // Next chapter
        if (isset($chapterWindow['next']) && $chapterWindow['next']) {
            // Include frontmatter when crossing book boundaries
            if (isset($chapterWindow['nextBook']) && $chapterWindow['next']->book_id !== $book->id) {
                $this->sections[] = $this->buildFrontmatterSection($chapterWindow['nextBook']);
            }

            $this->sections[] = $this->buildChapterSection(
                'chapter-next',
                $chapterWindow['next'],
                $book,
                $chapterWindow['nextBook'] ?? null,
                $options
            );
        }

        // Next next chapter if available
        if (isset($chapterWindow['nextNext']) && $chapterWindow['nextNext']) {
            $this->sections[] = $this->buildChapterSection(
                'chapter-next',
                $chapterWindow['nextNext'],
                $book,
                $chapterWindow['nextBook'] ?? null,
                $options
            );
        }
    }
    private function buildResponseData(array $parsedReference, Book $book, array $chapterWindow, array $sections): array
    {
        return [
            'reference' => [
                'book' => $parsedReference['book'],
                'chapter' => $parsedReference['chapter'],
                'frontmatter' => boolval($parsedReference['is_frontmatter'] ?? false),
                'verseStart' => $parsedReference['verse_start'] ?? null,
                'verseEnd' => $parsedReference['verse_end'] ?? null,
                'verseRanges' => $parsedReference['verse_ranges'] ?? [],
            ],

            'chapter' => [
                'number' => $parsedReference['chapter'],
                'title' => $chapterWindow['current']?->title,
                'verses' => $this->chapterTransformer->transformVerses($chapterWindow['current']?->verses ?? []),
            ],

            'sections' => $sections,

            'navigation' => $this->buildNavigationData($book, $chapterWindow, $parsedReference),

            'loadingConfig' => [
                'nextChapter' => $chapterWindow['nextNext'] ? [
                    'book' => $this->bookTransformer->transformCore($chapterWindow['nextNext']->book),
                    'number' => $chapterWindow['nextNext']->number,
                ] : null,
                'hasMoreNext' => $chapterWindow['hasMoreNext'],
                'hasMorePrevious' => $chapterWindow['hasMorePrevious'],
            ],

            'hasContent' => $parsedReference['hasContent'],
        ];
    }

    /**
     * Build navigation data for response
     */
    private function buildNavigationData(Book $book, array $chapterWindow, array $parsedReference): array
    {
        $isFrontmatter = $parsedReference['is_frontmatter'] ?? false;
        Log::info("Parsed Reference is", $parsedReference);

        return [
            'currentBook' => $this->bookTransformer->transformForNavigation($book),
            'previousBook' => $chapterWindow['previousBook'] ?
                $this->bookTransformer->transformForNavigation($chapterWindow['previousBook']) : null,
            'nextBook' => $chapterWindow['nextBook'] ?
                $this->bookTransformer->transformForNavigation($chapterWindow['nextBook']) : null,

            // Add scroll targets based on reference type
            'scrollToChapter' => !$isFrontmatter && !($parsedReference['verse_start'] ?? null) ? [
                'book' => $parsedReference['book']->slug,
                'chapter' => $parsedReference['chapter']
            ] : null,
            'scrollToFrontmatter' => $isFrontmatter ? [
                'slug' => $parsedReference['book']->slug
            ] : null,
            'scrollToVerse' => ($parsedReference['verse_start'] ?? null) &&
                          ($parsedReference['verse_end'] ?? null) &&
                          ($parsedReference['verse_start'] === $parsedReference['verse_end']) ? [
                'book' => $parsedReference['book']->slug,
                'chapter' => $parsedReference['chapter'],
                'verseStart' => $parsedReference['verse_start']
            ] : null,
            'scrollToVerseRange' => ($parsedReference['verse_start'] ?? null) &&
                               ($parsedReference['verse_end'] ?? null) &&
                               ($parsedReference['verse_start'] !== $parsedReference['verse_end']) ? [
                'book' => $parsedReference['book']->slug,
                'chapter' => $parsedReference['chapter'],
                'verseStart' => $parsedReference['verse_start'],
                'verseEnd' => $parsedReference['verse_end'],
                'verseRanges' => $parsedReference['verse_ranges'] ?? []
            ] : null
        ];
    }

    /**
     * API endpoint for fetching additional chapters
     */
    /**
     * @OA\Get(
     *     path="/api/chapters/fetch",
     *     summary="Fetch additional chapters",
     *     tags={"Chapters"},
     *     @OA\Parameter(
     *         name="bookId",
     *         in="query",
     *         required=true,
     *         description="The ID of the book",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Parameter(
     *         name="chapter",
     *         in="query",
     *         required=true,
     *         description="The chapter number",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Parameter(
     *         name="direction",
     *         in="query",
     *         required=true,
     *         description="Direction to fetch chapters (next or previous)",
     *         @OA\Schema(type="string", enum={"next", "previous"})
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             @OA\Property(property="sections", type="array", @OA\Items(type="object")),
     *             @OA\Property(property="navigation", type="object"),
     *             @OA\Property(property="hasMore", type="boolean")
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error"
     *     )
     */
    public function fetchAdditionalChapters(FetchAdditionalChaptersRequest $request)
    {
        $direction = $request->input('direction');
        $bookId = $request->input('bookId');
        $chapterNumber = $request->input('chapter');

        // Get request parameters with defaults
        $includeWords = $request->boolean('words', false);
        $includeWordGroups = $request->boolean('wordGroups', true);
        $includeFootnotes = $request->boolean('footnotes', true);

        // Get the book by order instead of id
        $book = Book::where('order', $bookId)->firstOrFail();

        // Get the chapter window with options
        $chapterWindow = $this->chapterService->getChapterWindow(
            $book,
            $chapterNumber,
            [
                'includeWords' => $includeWords,
                'includeWordGroups' => $includeWordGroups,
                'includeFootnotes' => $includeFootnotes,
            ]
        );

        // Check if chapter window is null (chapter not found)
        if ($chapterWindow === null) {
            return response()->json([
                'error' => 'Chapter not found',
                'book' => $book->slug,
                'chapter' => $chapterNumber
            ], 404);
        }

        $sections = [];

        if ($direction === 'next') {
            // Next chapter
            if ($chapterWindow['next']) {
                // Include frontmatter when crossing book boundaries
                if ($chapterWindow['next']->book_id !== $book->id) {
                    $sections[] = $this->buildFrontmatterSection($chapterWindow['nextBook']);
                }

                $sections[] = $this->buildChapterSection(
                    'chapter-next',
                    $chapterWindow['next'],
                    $book,
                    $chapterWindow['nextBook'],
                    [
                        'includeWords' => $includeWords,
                        'includeWordGroups' => $includeWordGroups,
                        'includeFootnotes' => $includeFootnotes,
                    ]
                );
            }

            // Next next chapter
            if ($chapterWindow['nextNext']) {
                $sections[] = $this->buildChapterSection(
                    'chapter-next',
                    $chapterWindow['nextNext'],
                    $book,
                    $chapterWindow['nextBook'],
                    [
                        'includeWords' => $includeWords,
                        'includeWordGroups' => $includeWordGroups,
                        'includeFootnotes' => $includeFootnotes,
                    ]
                );
            }
        } else {
            // Previous previous chapter
            if ($chapterWindow['previousPrevious']) {
                $sections[] = $this->buildChapterSection(
                    'chapter-previous',
                    $chapterWindow['previousPrevious'],
                    $book,
                    $chapterWindow['previousBook'],
                    [
                        'includeWords' => $includeWords,
                        'includeWordGroups' => $includeWordGroups,
                        'includeFootnotes' => $includeFootnotes,
                    ]
                );
            }

            // Previous chapter
            if ($chapterWindow['previous']) {
                $sections[] = $this->buildChapterSection(
                    'chapter-previous',
                    $chapterWindow['previous'],
                    $book,
                    $chapterWindow['previousBook'],
                    [
                        'includeWords' => $includeWords,
                        'includeWordGroups' => $includeWordGroups,
                        'includeFootnotes' => $includeFootnotes,
                    ]
                );
            }

            // Include frontmatter when crossing book boundaries
            if ($direction === 'previous' &&
                $chapterWindow['previous'] &&
                $chapterWindow['previous']->book_id !== $book->id) {
                $sections[] = $this->buildFrontmatterSection($book);
            }
        }

        return response()->json([
            'sections' => $sections,
            'navigation' => [
                'previousBook' => $chapterWindow['previousBook'] ? [
                    'slug' => $chapterWindow['previousBook']->slug,
                    'order' => $chapterWindow['previousBook']->order,
                    'chapterCount' => $chapterWindow['previousBook']->chapters_count,
                ] : null,
                'nextBook' => $chapterWindow['nextBook'] ? [
                    'slug' => $chapterWindow['nextBook']->slug,
                    'order' => $chapterWindow['nextBook']->order,
                    'chapterCount' => $chapterWindow['nextBook']->chapters_count,
                ] : null,
            ],
            'hasMore' => $direction === 'next' ?
                $chapterWindow['hasMoreNext'] :
                $chapterWindow['hasMorePrevious'],
        ]);
    }

    public function fetchAllBooks(Request $request): JsonResponse
    {
        $books = Book::orderBy('order')->get();
        return response()->json([
            'data' => $books->map(fn ($book) => $this->bookTransformer->transformForNavigation($book))
        ]);
    }

    /**
     * Get a single book by slug with all its metadata
     */
    public function getBook(string $slug): JsonResponse
    {
        $book = Book::where('slug', $slug)->firstOrFail();
        return response()->json([
            'data' => $this->bookTransformer->transform($book)
        ]);
    }

    /**
     * Get content status for all books
     */
    public function getContentStatus(): JsonResponse
    {
        $books = Book::all();
        $contentStatus = [];

        foreach ($books as $book) {
            // A book has content if it has at least one chapter with verses
            $hasContent = $book->chapters()
                ->whereHas('verses')
                ->exists();

            $contentStatus[$book->slug] = $hasContent;
        }

        return response()->json(['data' => $contentStatus]);
    }

    /**
     * Get recently processed books (last 30 days)
     */
    public function getRecentBooks(): JsonResponse
    {
        $thirtyDaysAgo = now()->subDays(14);

        $processedBooks = ProcessedBook::where('created_at', '>=', $thirtyDaysAgo)
            ->where('status', 'successful')
            ->with('book')
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'data' => $processedBooks->map(function ($processedBook) {
                // Only include books that have a valid book relationship
                if ($processedBook->book) {
                    $book = $processedBook->book;

                    return [
                        'slug' => $book->slug,
                        'name' => $book->name,
                        'updated_at' => $processedBook->updated_at->toISOString(),
                        'testament' => $book->testament?->value,
                        //'testamentLabel' => $book->testament?->label(),
                        'category' => $book->category?->value,
                        //'categoryLabel' => $book->category?->label()
                    ];
                }
                return null;
            })->filter() // Remove null values
        ]);
    }
}
