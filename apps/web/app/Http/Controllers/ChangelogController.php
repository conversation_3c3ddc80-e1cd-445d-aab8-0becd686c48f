<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Services\ChangelogService;
use Illuminate\Http\JsonResponse;
use Inertia\Inertia;
use Inertia\Response;

class ChangelogController extends Controller
{
    public function __construct(
        private readonly ChangelogService $changelogService,
    ) {
    }

    public function index(): Response
    {
        return Inertia::render('Changelog/Index', [
            'releases' => $this->changelogService->getChangelog(),
        ]);
    }

    public function data(): JsonResponse
    {
        return response()->json([
            'releases' => $this->changelogService->getChangelog(),
        ]);
    }
}
