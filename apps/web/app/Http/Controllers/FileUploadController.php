<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class FileUploadController extends Controller
{
    public function upload(Request $request)
    {
        $request->validate([
            'file' => 'required|file|mimes:docx',
        ]);

        if ($request->file('file')->isValid()) {
            $path = $request->file('file')->store('uploads');

            // Hier kannst du den Importprozess starten oder die Datei für später speichern
            return response()->json(['message' => 'Datei erfolgreich hochgeladen!', 'path' => $path], 200);
        }

        return response()->json(['error' => 'Ungültige Datei.'], 400);
    }
}
