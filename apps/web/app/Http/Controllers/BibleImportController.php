<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use PhpOffice\PhpWord\IOFactory;
use App\Services\TextFormatter;

class BibleImportController extends Controller
{
    protected $formatter;

    public function __construct(TextFormatter $formatter)
    {
        $this->formatter = $formatter;
    }

    public function index()
    {
        // Retrieve uploaded files and their statuses from the database
        $uploads = DB::table('uploads')->get(); // Assuming you have an 'uploads' table

        return Inertia::render('Bible/Import', [
            'uploads' => $uploads
        ]);
    }

    public function import(Request $request)
    {
        $request->validate([
            'file' => 'required|file|mimes:docx',
            'book_name' => 'required|string', // Validate the book name
        ]);

        $file = $request->file('file');
        $filename = $file->getClientOriginalName();
        $bookName = $request->input('book_name');

        // Log the upload in the database
        $uploadId = DB::table('uploads')->insertGetId([
            'filename' => $filename,
            'book_name' => $bookName, // Store the book name
            'status' => 'pending',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        DB::beginTransaction();

        try {
            $wordFilesPath = storage_path('app/bible_files');
            $files = glob($wordFilesPath . '/*.docx');

            foreach ($files as $file) {
                $this->processWordFile($file);
            }

            DB::commit();
            DB::table('uploads')->where('id', $uploadId)->update([
                'status' => 'successful',
                'updated_at' => now()
            ]);

            return response()->json(['message' => 'Import abgeschlossen.'], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            DB::table('uploads')->where('id', $uploadId)->update([
                'status' => 'failed',
                'updated_at' => now()
            ]);
            return response()->json(['error' => 'Import fehlgeschlagen: ' . $e->getMessage()], 500);
        }
    }

    private function processWordFile($file)
    {
        $phpWord = IOFactory::load($file);
        $sections = $phpWord->getSections();

        $bookId = null;
        $chapterId = null;

        foreach ($sections as $section) {
            $elements = $section->getElements();

            foreach ($elements as $element) {
                if (method_exists($element, 'getText')) {
                    $text = $element->getText();

                    if ($this->isBookTitle($text)) {
                        $bookId = $this->createOrUpdateBook($text);
                    } elseif ($this->isChapterTitle($text)) {
                        $chapterId = $this->createOrUpdateChapter($bookId, $text);
                    } elseif ($this->isVerse($text)) {
                        $this->createOrUpdateVerse($chapterId, $text, $element);
                    }
                }
            }
        }
    }

    private function isBookTitle(string $text): bool
    {
        return preg_match('/^\w+$/', $text);
    }

    private function createOrUpdateBook(string $title)
    {
        $book = DB::table('books')->where('name', $title)->first();

        if ($book) {
            DB::table('books')->where('id', $book->id)->update(['updated_at' => now()]);
            return $book->id;
        } else {
            return DB::table('books')->insertGetId([
                'name' => $title,
                'created_at' => now(),
                'updated_at' => now()
            ]);
        }
    }

    private function isChapterTitle(string $text): bool
    {
        return preg_match('/^Kapitel\s\d+$/i', $text);
    }

    private function createOrUpdateChapter($bookId, string $text)
    {
        preg_match('/\d+/', $text, $matches);
        $chapterNumber = $matches[0];

        $chapter = DB::table('chapters')->where('book_id', $bookId)->where('chapter_number', $chapterNumber)->first();

        if ($chapter) {
            DB::table('chapters')->where('id', $chapter->id)->update(['updated_at' => now()]);
            return $chapter->id;
        } else {
            return DB::table('chapters')->insertGetId([
                'book_id' => $bookId,
                'chapter_number' => $chapterNumber,
                'created_at' => now(),
                'updated_at' => now()
            ]);
        }
    }

    private function isVerse(string $text): bool
    {
        return preg_match('/^#\d+/', $text);
    }

    private function createOrUpdateVerse($chapterId, string $text, $element)
    {
        preg_match('/^#(\d+)\s(.*)$/', $text, $matches);
        $verseNumber = $matches[1];
        $verseText = $matches[2];

        // Check for special formatting
        if ($this->formatter->isItalic($element)) {
            $verseText = '<i>' . $verseText . '</i>';
        }

        if ($this->formatter->isBold($element)) {
            // Pericope handling
            $verseText = '<b>' . $verseText . '</b>';
        }

        if ($this->formatter->isAllCaps($element)) {
            $verseText = '<span class="uppercase">' . $verseText . '</span>';
        }

        if ($this->formatter->hasEllipsis($verseText)) {
            $verseText = str_replace('–', '<span class="ellipsis">...</span>', $verseText);
        }

        $nonOriginalWords = $this->formatter->extractNonOriginalWords($verseText);
        foreach ($nonOriginalWords as $word) {
            // Store non-original words as metadata
            DB::table('verse_metadata')->insert([
                'verse_id' => $verseId,
                'metadata' => $word,
                'created_at' => now(),
                'updated_at' => now()
            ]);
        }

        if ($this->formatter->hasFootnoteMarker($verseText)) {
            $footnoteContent = $this->formatter->extractFootnoteContent($verseText);
            $this->importFootnote($chapterId, $verseNumber, $footnoteContent);
        }

        if ($this->formatter->isDirectQuote($verseText)) {
            $verseText = '<span class="italic">' . $verseText . '</span>';
        }

        $verse = DB::table('verses')->where('chapter_id', $chapterId)->where('verse_number', $verseNumber)->first();

        if ($verse) {
            if ($verse->text !== $verseText) {
                DB::table('verses')->where('id', $verse->id)->update([
                    'text' => $verseText,
                    'updated_at' => now()
                ]);
            }
        } else {
            $verseId = DB::table('verses')->insertGetId([
                'chapter_id' => $chapterId,
                'verse_number' => $verseNumber,
                'text' => $verseText,
                'created_at' => now(),
                'updated_at' => now()
            ]);
        }
    }

    private function importFootnote($chapterId, $verseNumber, string $content)
    {
        $verseId = DB::table('verses')
            ->where('chapter_id', $chapterId)
            ->where('verse_number', $verseNumber)
            ->value('id');

        DB::table('footnotes')->insert([
            'verse_id' => $verseId,
            'content' => $content,
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }
}
