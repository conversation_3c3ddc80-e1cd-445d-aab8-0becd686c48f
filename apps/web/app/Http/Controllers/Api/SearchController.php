<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\Search\BibleSearchService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class SearchController extends Controller
{
    public function __construct(
        private readonly BibleSearchService $searchService
    ) {}

    public function search(Request $request): JsonResponse
    {
        $query = $request->query('query');
        $types = $request->query('types', ['books']);
        $page = (int)$request->query('page', 1);

        if (!$query) {
            return response()->json([
                'data' => [],
                'metadata' => [
                    'total' => 0,
                    'per_page' => 25,
                    'current_page' => 1,
                    'last_page' => 1
                ],
                'error' => null
            ]);
        }

        $result = $this->searchService->search(
            $query,
            $types,
            $request->query('bookId'),
            false,
            25,
            $page
        );

        return response()->json($result->toArray());
    }
}
