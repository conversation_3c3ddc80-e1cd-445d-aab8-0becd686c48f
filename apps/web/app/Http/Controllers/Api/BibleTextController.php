<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Book;
use App\Services\BibleReferenceParser;
use App\Services\Transformers\VerseTransformer;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class BibleTextController extends Controller
{
    public function __construct(
        private BibleReferenceParser $referenceParser,
        private VerseTransformer $verseTransformer,
    ) {}

    /**
     * Get Bible text based on reference
     * 
     * @param Request $request
     * @param string $reference Book slug with optional chapter and verse references:
     *                         - Book only: "Johannes"
     *                         - Book and chapter: "Johannes1"
     *                         - Single verse: "Johannes1,1"
     *                         - Verse range: "Johannes1,1-9"
     * @return JsonResponse
     */
    public function getText(Request $request, string $reference): JsonResponse
    {
        try {
            // Parse the reference
            $parsedReference = $this->referenceParser->parse($reference);
            
            // Get the book
            $book = Book::where('slug', $parsedReference['book']->slug)->firstOrFail();
            
            // Initialize query to get verses
            $query = $book->verses();
            
            // If chapter is specified, filter by chapter
            if ($parsedReference['chapter']) {
                $chapter = $book->chapters()
                    ->where('number', $parsedReference['chapter'])
                    ->firstOrFail();
                    
                $query = $chapter->verses();
            }
            
            // If verse range is specified, filter by verses
            if ($parsedReference['verse_start']) {
                $query->where('number', '>=', $parsedReference['verse_start']);
                
                if ($parsedReference['verse_end']) {
                    $query->where('number', '<=', $parsedReference['verse_end']);
                } else {
                    $query->where('number', $parsedReference['verse_start']);
                }
            }

            // Get request parameters with defaults
            $includeFootnotes = $request->boolean('footnotes', false);
            $includeWords = $request->boolean('words', false); // Default to false to reduce payload
            $includeWordGroups = $request->boolean('wordGroups', true); // Default to true for backward compatibility
            
            // Eager load necessary relations
            $withClauses = [];
            
            // Always load words if either words or wordGroups are requested
            if ($includeWords || $includeWordGroups) {
                $withClauses['words'] = function($q) {
                    $q->orderBy('position');
                };
            }
            
            // Load footnotes if requested
            if ($includeFootnotes) {
                $withClauses['footnotes'] = function($q) {
                    $q->orderBy('position');
                };
            }
            
            // Get verses with necessary relations
            $verses = $query->with($withClauses)
                ->orderBy('number')
                ->get();

            // Transform verses with the specified options
            $transformedVerses = $verses->map(function ($verse) use ($includeWords, $includeWordGroups, $includeFootnotes) {
                return $this->verseTransformer->transform($verse, [
                    'includeWords' => $includeWords,
                    'includeWordGroups' => $includeWordGroups,
                    'includeFootnotes' => $includeFootnotes,
                ]);
            });

            // Build response data
            $response = [
                'book' => [
                    'slug' => $book->slug,
                    'name' => $book->name,
                    'canonicalOrder' => $book->order, // Position in the biblical canon
                    'testament' => $book->testament, // 'ot' or 'nt'
                    'category' => $book->category, // e.g., 'law', 'history', 'prophecy', etc.
                ],
                'reference' => [
                    'chapter' => $parsedReference['chapter'],
                    'verseStart' => $parsedReference['verse_start'] ?? null,
                    'verseEnd' => $parsedReference['verse_end'] ?? null,
                ],
                'verses' => $transformedVerses,
                'metadata' => [
                    'includesFootnotes' => $includeFootnotes,
                    'includesWords' => $includeWords,
                    'includesWordGroups' => $includeWordGroups,
                    'totalVerses' => $verses->count(),
                    'totalChapters' => $book->chapters_count,
                ]
            ];

            return response()->json($response);

        } catch (\Exception $e) {
            Log::error('API Error fetching Bible text:', [
                'reference' => $reference,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'error' => 'Invalid reference or content not found',
                'message' => $e->getMessage()
            ], 404);
        }
    }
}
