<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Book;
use App\Services\BibleReferenceService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class BookSearchController extends Controller
{
    protected $bibleReferenceService;

    public function __construct(BibleReferenceService $bibleReferenceService)
    {
        $this->bibleReferenceService = $bibleReferenceService;
    }

   protected function findMatchingTerms($book, $term)
    {
        $matches = [];
        $term = strtolower(trim($term));

        // Check name and abbreviation
        if (stripos(strtolower($book->name), $term) === 0) {
            $matches[] = $book->name;
        }
        if (stripos(strtolower($book->abbreviation ?? ''), $term) === 0) {
            $matches[] = $book->abbreviation;
        }

        // Check variations
        foreach ($book->variations ?? [] as $variation) {
            if (stripos(strtolower($variation), $term) === 0) {
                $matches[] = $variation;
            }
        }

        // Check chapter numbers
        foreach ($book->chapters ?? [] as $chapter) {
            if (strpos($chapter, $term) !== false) {
                $matches[] = "Kapitel {$chapter}";
            }
        }

        return array_slice(array_unique($matches), 0, 3);
    }

    /**
     * Format a reference suggestion with verse preview
     */
    protected function formatReferenceSuggestion($reference): array
    {
        $displayText = $reference->book->slug . ' ' . $reference->chapter;
        $slug = $reference->book->slug . $reference->chapter;
        $versePreview = '';
        $verseNumber = $reference->verse ?? 1; // Default to verse 1 if not specified

        // Build the display text and slug
        if ($reference->verse) {
            $displayText .= ',' . $reference->verse;
            $slug .= ',' . $reference->verse;

            if ($reference->verseEnd && $reference->verseEnd !== $reference->verse) {
                $displayText .= '-' . $reference->verseEnd;
                $slug .= '-' . $reference->verseEnd;
            }
        }

        // Get verse preview text
        try {
            $verse = \App\Models\Verse::whereHas('chapter', function($query) use ($reference) {
                    $query->where('book_id', $reference->book->id)
                          ->where('number', $reference->chapter);
                })
                ->where('number', $verseNumber)
                ->first();

            if ($verse) {
                // Clean up the verse text by removing any HTML tags and extra whitespace
                $versePreview = trim(strip_tags($verse->text));

                // Truncate if too long
                if (mb_strlen($versePreview) > 80) {
                    $versePreview = mb_substr($versePreview, 0, 80) . '...';
                }
            }
        } catch (\Exception $e) {
            // Silently fail if there's an error fetching the verse
            \Log::debug('Error fetching verse preview: ' . $e->getMessage());
        }

        return [
            'display' => $displayText,
            'book' => [
                'id' => $reference->book->id,
                'name' => $reference->book->name,
                'slug' => $reference->book->slug,
                'order' => $reference->book->order,
                'hasContent' => $reference->book->has_content,
                'chapterCount' => $reference->book->chapters_count,
                'category' => $reference->book->category
            ],
            'isReference' => true,
            'reference' => [
                'chapter' => $reference->chapter,
                'verseStart' => $reference->verse,
                'verseEnd' => $reference->verseEnd,
            ],
            'versePreview' => $versePreview ? $versePreview : ''
        ];
    }

    public function __invoke(Request $request)
    {
        $term = $request->query('q');

        if (empty($term)) {
            return response()->json([]);
        }

        // First try to parse as a Bible reference
        $reference = $this->bibleReferenceService->parseReference($term);

        if ($reference) {
            return response()->json([$this->formatReferenceSuggestion($reference)]);
        }

        // Search using Meilisearch
        $books = Book::search($term, function ($meilisearch, $query, $options) {
            $options['limit'] = 10;
            $options['attributesToRetrieve'] = [
                'id', 'name', 'slug', 'abbreviation', 'variations', 'chapters', 'chapters_count', 'has_content', 'category'
            ];
            return $meilisearch->search($query, $options);
        })->get();

        $results = $books->map(function ($book) use ($term) {
            $matches = $this->findMatchingTerms($book, strtolower($term));
            return [
                'display' => $book->name,
                'book' => [
                    'id' => $book->id,
                    'name' => $book->name,
                    'slug' => $book->slug,
                    'abbreviation' => $book->abbreviation,
                    'hasContent' => $book->has_content ?? true,
                    'chapterCount' => $book->chapters_count,
                    'category' => $book->category ?? null,
                    'matches' => $matches
                ],
                'isReference' => false,
                'versePreview' => ''
            ];
        });

        return response()->json($results);
    }
}
