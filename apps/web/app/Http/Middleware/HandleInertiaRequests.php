<?php

namespace App\Http\Middleware;

use App\Services\BookOrganizationService;
use Illuminate\Http\Request;
use Inertia\Middleware;

class HandleInertiaRequests extends Middleware
{
    protected $bookOrganizationService;

    public function __construct(BookOrganizationService $bookOrganizationService)
    {
        $this->bookOrganizationService = $bookOrganizationService;
    }

    /**
     * The root template that is loaded on the first page visit.
     *
     * @var string
     */
    protected $rootView = 'app';

    /**
     * Determine the current asset version.
     */
    public function version(Request $request): ?string
    {
        return parent::version($request);
    }

    /**
     * Define the props that are shared by default.
     *
     * @return array<string, mixed>
     */
    /**
     * Get the application version from .env.VERSION or package.json
     * 
     * @return string
     */
    protected function getAppVersion(): string
    {
        // First try to read from .env.VERSION file
        $envVersionPath = base_path('.env.VERSION');
        if (file_exists($envVersionPath)) {
            $content = file_get_contents($envVersionPath);
            if (preg_match('/VERSION=(.+)/i', $content, $matches)) {
                return $matches[1];
            }
        }
        
        // Fallback to package.json
        $packageJsonPath = base_path('package.json');
        if (file_exists($packageJsonPath)) {
            $packageJson = json_decode(file_get_contents($packageJsonPath), true);
            if (isset($packageJson['version'])) {
                return $packageJson['version'];
            }
        }
        
        // Default fallback version
        return '0.0.0';
    }
    
    public function share(Request $request): array
    {
        return [
            ...parent::share($request),
            'env' => env('APP_ENV', 'production'),
            'auth' => [
                'user' => $request->user(),
            ],
            'books' => $this->bookOrganizationService->getOrganizedBooks(),
            'appVersion' => $this->getAppVersion(),
        ];
    }
}
