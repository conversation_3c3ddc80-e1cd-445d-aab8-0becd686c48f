<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class SentryCors
{
    public function handle(Request $request, Closure $next)
    {
        // Handle preflight OPTIONS request
        if ($request->isMethod('OPTIONS')) {
            $response = response('', 204);
        } else {
            $response = $next($request);
        }

        // Get the origin
        $origin = $request->header('Origin');
        
        // Allow Sentry domains and local development
        $allowedOrigins = [
            'https://o4508808448245760.ingest.de.sentry.io',
            'http://localhost',
            'http://localhost:8000',
            'http://127.0.0.1:8000'
        ];

        if (in_array($origin, $allowedOrigins)) {
            $response->headers->set('Access-Control-Allow-Origin', $origin);
        }

        $response->headers->set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
        $response->headers->set('Access-Control-Allow-Headers', 'Origin, Content-Type, Accept, Authorization, X-Requested-With, sentry-trace, baggage');
        $response->headers->set('Access-Control-Allow-Credentials', 'true');
        $response->headers->set('Access-Control-Max-Age', '86400'); // 24 hours
        $response->headers->set('Vary', 'Origin');

        // Add specific headers for Sentry
        $response->headers->set('Access-Control-Expose-Headers', 'sentry-trace, baggage');
        
        return $response;
    }
}
