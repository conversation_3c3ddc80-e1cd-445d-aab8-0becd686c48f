<?php

namespace App\Enums;

enum OriginalLanguage: string
{
    case HEBREW = 'hebrew';
    case GREEK = 'greek';
    case ARAMAIC = 'aramaic';

    public function label(): string
    {
        return match($this) {
            self::HEBREW => 'Heb<PERSON>äisch',
            self::GREEK => 'Griechisch',
            self::ARAMAIC => 'Aramäisch',
        };
    }

    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }
}
