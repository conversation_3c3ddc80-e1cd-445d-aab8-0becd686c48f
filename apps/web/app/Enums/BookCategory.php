<?php

namespace App\Enums;

enum BookCategory: string
{
    case LAW = 'law';
    case HISTORY = 'history';
    case WISDOM = 'wisdom';
    case PROPHECY = 'prophecy';
    case GOSPEL = 'gospel';
    case EPISTLE = 'epistle';
    case APOCALYPTIC = 'apocalypse';

    public function label(): string
    {
        return match($this) {
            self::LAW => 'Gesetz',
            self::HISTORY => 'Geschichte',
            self::WISDOM => 'Weisheit',
            self::PROPHECY => 'Propheten',
            self::GOSPEL => 'Evangelien',
            self::EPISTLE => 'Briefe',
            self::APOCALYPTIC => 'Apokalypse',
        };
    }

    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }
}
