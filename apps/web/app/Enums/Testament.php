<?php

namespace App\Enums;

enum Testament: string
{
    case OT = 'ot';
    case NT = 'nt';

    public function label(): string
    {
        return match($this) {
            self::OT => 'Altes Testament',
            self::NT => 'Neues Testament',
        };
    }

    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    public static function fromAbbreviation(string $abbr): self
    {
        return match (strtolower($abbr)) {
            'ot' => self::OT,
            'nt' => self::NT,
            default => throw new RuntimeException("Invalid testament value: {$abbr}")
        };
    }
}
