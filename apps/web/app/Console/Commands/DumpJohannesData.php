<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Support\Facades\File;

class DumpJohannesData extends Command
{
    protected $signature = 'db:dump-johannes {--keep-old : Keep old dump files}';
    protected $description = 'Create a SQL dump of Johannes book data (INSERT statements only)';

    private $dumpDir;

    public function __construct()
    {
        parent::__construct();
        $this->dumpDir = database_path('seeders/test-data');
    }

    public function handle()
    {
        // Ensure directory exists
        if (!file_exists($this->dumpDir)) {
            mkdir($this->dumpDir, 0755, true);
        }

        // Delete old dumps unless --keep-old flag is used
        if (!$this->option('keep-old')) {
            $this->deleteOldDumps();
        }

        // Create new dump
        $filename = $this->createDump();
        
        $this->info("Database dump completed: $filename");
        $this->info("This file should be committed to the repository.");
        
        return 0;
    }

    private function deleteOldDumps(): void
    {
        $pattern = $this->dumpDir . '/esb_johannes_*.sql';
        $files = glob($pattern);
        
        foreach ($files as $file) {
            unlink($file);
            $this->info("Deleted old dump: $file");
        }
    }

    private function createDump(): string
    {
        // Get Johannes book data
        $johannes = DB::table('books')->where('slug', 'Johannes')->first();
        if (!$johannes) {
            throw new \RuntimeException('Johannes book not found in database');
        }

        // Get related chapters
        $chapters = DB::table('chapters')
            ->where('book_id', $johannes->id)
            ->get();

        // Get related verses
        $verses = DB::table('verses')
            ->whereIn('chapter_id', $chapters->pluck('id'))
            ->get();

        $timestamp = Carbon::now()->format('Ymd_His');
        $filename = $this->dumpDir . "/esb_johannes_{$timestamp}.sql";

        // Start building SQL file with only INSERT statements
        $sql = "-- ESB Online Database Dump - Johannes Book Data\n";
        $sql .= "-- Version: 1.0\n";
        $sql .= "-- Generation Time: " . Carbon::now()->format('Y-m-d H:i:s') . "\n\n";

        // Add book data
        $sql .= "INSERT INTO `books` (`id`, `name`, `slug`, `abbreviation`, `order`, `testament`, `category`, `chapters_count`, `original_language`, `location`, `historical_period`, `authors`, `written_year`, `theme`, `key_people`, `attributes_of_god`, `key_words`, `covenants`, `key_teachings`, `key_verses`, `search_names`, `created_at`, `updated_at`) VALUES (";
        $sql .= implode(',', array_map(function ($value) {
            return is_null($value) ? 'NULL' : DB::connection()->getPdo()->quote($value);
        }, [
            $johannes->id,
            $johannes->name,
            $johannes->slug,
            $johannes->abbreviation,
            $johannes->order,
            $johannes->testament,
            $johannes->category,
            $johannes->chapters_count,
            $johannes->original_language,
            $johannes->location,
            $johannes->historical_period,
            $johannes->authors,
            $johannes->written_year,
            $johannes->theme,
            $johannes->key_people,
            $johannes->attributes_of_god,
            $johannes->key_words,
            $johannes->covenants,
            $johannes->key_teachings,
            $johannes->key_verses,
            $johannes->search_names,
            $johannes->created_at,
            $johannes->updated_at
        ]));
        $sql .= ");\n\n";

        // Add chapter data
        foreach ($chapters as $chapter) {
            $sql .= "INSERT INTO `chapters` (`id`, `book_id`, `number`, `title`, `summary`, `created_at`, `updated_at`) VALUES (";
            $sql .= implode(',', array_map(function ($value) {
                return is_null($value) ? 'NULL' : DB::connection()->getPdo()->quote($value);
            }, [
                $chapter->id,
                $chapter->book_id,
                $chapter->number,
                $chapter->title,
                $chapter->summary,
                $chapter->created_at,
                $chapter->updated_at
            ]));
            $sql .= ");\n";
        }
        $sql .= "\n";

        // Add verse data
        foreach ($verses as $verse) {
            $sql .= "INSERT INTO `verses` (`id`, `chapter_id`, `number`, `start_verse`, `end_verse`, `text`, `created_at`, `updated_at`) VALUES (";
            $sql .= implode(',', array_map(function ($value) {
                return is_null($value) ? 'NULL' : DB::connection()->getPdo()->quote($value);
            }, [
                $verse->id,
                $verse->chapter_id,
                $verse->number,
                $verse->start_verse,
                $verse->end_verse,
                $verse->text,
                $verse->created_at,
                $verse->updated_at
            ]));
            $sql .= ");\n";
        }

        file_put_contents($filename, $sql);
        return $filename;
    }
}
