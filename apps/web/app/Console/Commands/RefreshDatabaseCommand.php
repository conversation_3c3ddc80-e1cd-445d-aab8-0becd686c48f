<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Artisan;

class RefreshDatabaseCommand extends Command
{
    protected $signature = 'db:refresh-all';
    protected $description = 'Run migrate:fresh, reset IDs, and db:seed in sequence';

    public function handle()
    {
        $this->info('Starting complete database refresh...');

        // Run migrate:reset
        $this->info('Resetting the DB...');
        Artisan::call('migrate:reset');
        $this->info('Resetting the DB completed.');

        // Run migrate:fresh
        $this->info('Running migrations...');
        Artisan::call('migrate:fresh');
        $this->info('Migrations completed.');

        // Reset auto-increment values
        $this->info('Resetting auto-increment values...');
        $tables = DB::select('SHOW TABLES');
        $dbName = config('database.connections.mysql.database');

        foreach ($tables as $table) {
            $tableName = $table->{'Tables_in_' . $dbName};
            $this->info("Resetting auto-increment for table: {$tableName}");

            try {
                DB::statement("ALTER TABLE `{$tableName}` AUTO_INCREMENT = 1");
            } catch (\Exception $e) {
                $this->error("Failed to reset {$tableName}: " . $e->getMessage());
            }
        }
        $this->info('Auto-increment values reset completed.');

        // Run database seeder
        $this->info('Running database seeder...');
        Artisan::call('db:seed');
        $this->info('Database seeding completed.');

        $this->info('Database refresh completed successfully!');
    }
}
