<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class RefreshVersesWithMetadata extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:refresh-verses-with-metadata';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Aktualisiert die verses_with_metadata Tabelle mit aggregierten Daten';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Löschen Sie die aktuelle Tabelle
        DB::table('verses_with_metadata')->truncate();

        // Fügen Sie die aggregierten Daten ein
        DB::table('verses_with_metadata')->insert(
            DB::select("
                SELECT
                    verses.id AS verse_id,
                    verses.text AS verse_text,
                    verses.verse_number,
                    chapters.chapter_number,
                    books.name AS book_name,
                    books.abbreviation AS book_abbreviation,
                    JSON_ARRAYAGG(words.word) AS words,
                    JSON_ARRAYAGG(words.original_word) AS original_words,
                    JSON_ARRAYAGG(tags.name) AS tags,
                    JSON_ARRAYAGG(footnotes.content) AS footnotes
                FROM verses
                JOIN chapters ON chapters.id = verses.chapter_id
                JOIN books ON books.id = chapters.book_id
                LEFT JOIN words ON words.verse_id = verses.id
                LEFT JOIN word_tag ON word_tag.word_id = words.id
                LEFT JOIN tags ON tags.id = word_tag.tag_id
                LEFT JOIN footnotes ON footnotes.verse_id = verses.id
                GROUP BY verses.id, chapters.chapter_number, books.name, books.abbreviation
            ")
        );

        $this->info('Die Tabelle verses_with_metadata wurde aktualisiert.');
    }
}
