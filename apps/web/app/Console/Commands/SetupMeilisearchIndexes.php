<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use MeiliSearch\Client;
use Illuminate\Support\Facades\Config;

class SetupMeilisearchIndexes extends Command
{
    protected $signature = 'meilisearch:setup';
    protected $description = 'Set up Meilisearch indexes with proper settings';

    public function handle()
    {
        $host = config('scout.meilisearch.host');
        $key = config('scout.meilisearch.key');
        $client = new Client($host, $key);

        $indexSettings = config('scout.meilisearch.index-settings', []);

        foreach ($indexSettings as $indexName => $settings) {
            $this->info("Setting up index: {$indexName}");

            try {
                // Create or get the index
                $index = $client->createIndex($indexName, ['primaryKey' => 'id']);
                
                // Update settings
                if (isset($settings['searchableAttributes'])) {
                    $index->updateSearchableAttributes($settings['searchableAttributes']);
                }
                if (isset($settings['filterableAttributes'])) {
                    $index->updateFilterableAttributes($settings['filterableAttributes']);
                }
                if (isset($settings['sortableAttributes'])) {
                    $index->updateSortableAttributes($settings['sortableAttributes']);
                }
                if (isset($settings['rankingRules'])) {
                    $index->updateRankingRules($settings['rankingRules']);
                }
                if (isset($settings['stopWords'])) {
                    $index->updateStopWords($settings['stopWords']);
                }
                if (isset($settings['typoTolerance'])) {
                    $index->updateTypoTolerance($settings['typoTolerance']);
                }
                if (isset($settings['pagination'])) {
                    $index->updatePagination($settings['pagination']);
                }
                if (isset($settings['displayedAttributes'])) {
                    $index->updateDisplayedAttributes($settings['displayedAttributes']);
                }

                $this->info("Successfully configured index: {$indexName}");
            } catch (\Exception $e) {
                $this->error("Error setting up index {$indexName}: " . $e->getMessage());
            }
        }

        $this->info('All indexes have been set up!');
    }
}
