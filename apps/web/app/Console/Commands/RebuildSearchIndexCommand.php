<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Models\Book;
use App\Models\Chapter;
use App\Models\Verse;
use App\Models\Word;
use App\Models\Footnote;
use App\Models\SearchableText;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use MeiliSearch\Client;
use Illuminate\Support\Facades\Log;

class RebuildSearchIndexCommand extends Command
{
    protected $signature = 'bible:rebuild-search
                           {--book= : Optional slug of a specific book to rebuild index for}';
    protected $description = 'Rebuilds the search index for Bible content';

    public function handle(): int
    {
        if (!$this->option('quiet')) {
            $this->info('Rebuilding search index...');
        }

        try {
            $bookSlug = $this->option('book');
            
            if ($bookSlug) {
                // Rebuild index for a single book
                return $this->rebuildIndexForBook($bookSlug);
            } else {
                // Rebuild entire index
                return $this->rebuildEntireIndex();
            }
        } catch (\Exception $e) {
            $this->error('Error rebuilding search index: ' . $e->getMessage());
            Log::error('Failed to rebuild search index: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }
    
    /**
     * Rebuild the search index for a specific book
     *
     * @param string $bookSlug Slug of the book to reindex
     * @return int Command result code
     */
    protected function rebuildIndexForBook(string $bookSlug): int
    {
        $book = Book::where('slug', $bookSlug)->first();
        
        if (!$book) {
            $this->error("Book with slug '{$bookSlug}' not found");
            return Command::FAILURE;
        }
        
        if (!$this->option('quiet')) {
            $this->info("Rebuilding search index for {$book->name}...");
        }
        
        // Remove existing entries for this book
        SearchableText::where('book_id', $book->id)->delete();
        
        if (!$this->option('quiet')) {
            $this->info("Cleared existing index for {$book->name}");
        }
        
        // Index chapters for this book
        $this->indexBook($book);
        
        // Update Meilisearch settings
        $this->updateMeilisearchSettings();
        
        if (!$this->option('quiet')) {
            $this->info("Search index for {$book->name} rebuilt successfully!");
        }
        
        return Command::SUCCESS;
    }
    
    /**
     * Rebuild the entire search index
     *
     * @return int Command result code
     */
    protected function rebuildEntireIndex(): int
    {
        // Clear existing index
        SearchableText::truncate();
        
        if (!$this->option('quiet')) {
            $this->info('Cleared existing search index');
            $this->info('Indexing all books and chapters...');
        }

        // Index books and chapters
        $books = Book::with('chapters')->get();
        
        foreach ($books as $book) {
            $this->indexBook($book);
        }

        // Update Meilisearch settings
        $this->updateMeilisearchSettings();
        
        if (!$this->option('quiet')) {
            $this->info('Search index rebuilt successfully!');
        }
        
        return Command::SUCCESS;
    }
    
    /**
     * Index a single book and its chapters, verses, etc.
     *
     * @param \App\Models\Book $book The book to index
     * @return void
     */
    protected function indexBook(Book $book): void
    {
        foreach ($book->chapters as $chapter) {
            // Index chapter heading if exists
            if ($chapter->heading) {
                SearchableText::create([
                    'type' => 'heading',
                    'book_id' => $book->id,
                    'chapter_id' => $chapter->id,
                    'content' => $chapter->heading,
                    'metadata' => [
                        'level' => 'chapter'
                    ]
                ]);
            }

            // Index verses with their words
            $verses = Verse::with(['chapter', 'words'])->where('chapter_id', $chapter->id)->get();

            foreach ($verses as $verse) {
                // Get verse words ordered by position
                $verseContent = $verse->words->sortBy('position')->pluck('text')->join(' ');

                SearchableText::create([
                    'type' => 'verse',
                    'book_id' => $book->id,
                    'chapter_id' => $chapter->id,
                    'verse_id' => $verse->id,
                    'content' => $verseContent,
                    'metadata' => [
                        'has_ot_quote' => $verse->has_ot_quote,
                        'has_text_variant' => $verse->has_text_variant,
                        'start_verse' => $verse->start_verse,
                        'end_verse' => $verse->end_verse,
                        'tags' => $verse->tags
                    ]
                ]);

                // Get footnotes through words
                $footnotes = Footnote::whereIn('id', $verse->words->pluck('footnote_id'))
                    ->whereNotNull('id')
                    ->orderBy('position')
                    ->get();

                // Index footnotes
                foreach ($footnotes as $footnote) {
                    // Find the word that references this footnote
                    $referencedWord = Word::where('footnote_id', $footnote->id)->first();

                    SearchableText::create([
                        'type' => 'footnote',
                        'book_id' => $book->id,
                        'chapter_id' => $chapter->id,
                        'verse_id' => $verse->id,
                        'content' => $footnote->content,
                        'metadata' => [
                            'position' => $footnote->position,
                            'verse_id' => $verse->id,
                            'footnote_id' => $footnote->id,
                            // Store only IDs, not the actual word text
                        ]
                    ]);
                }
            }
        }

        if (!$this->option('quiet')) {
            $this->info("Indexed book: {$book->name}");
        }
    }

    /**
     * Update Meilisearch settings for optimal search performance
     *
     * @return void
     */
    protected function updateMeilisearchSettings(): void
    {
        if (!$this->option('quiet')) {
            $this->info('Configuring Meilisearch settings...');
        }
        
        try {
            $searchableText = new SearchableText();
            $index = $searchableText->searchableAs();

            // Get Meilisearch client
            $client = new Client(config('scout.meilisearch.host'), config('scout.meilisearch.key'));

            // Configure searchable attributes
            $client->index($index)->updateSettings([
                'searchableAttributes' => [
                    'content',
                    'metadata.tags',
                    'book.name',
                    'book.abbreviation',
                    'book.search_names',
                    'references.full',
                    'references.short',
                    'references.numeric'
                ],
                'filterableAttributes' => [
                    'type',
                    'book.id',
                    'book.testament',
                    'book.category',
                    'book.order',
                    'chapter.id',
                    'chapter.number',
                    'verse.id',
                    'verse.number',
                    'verse.start_verse',
                    'verse.end_verse',
                    'metadata.has_ot_quote',
                    'metadata.has_text_variant',
                    'metadata.is_reference',
                    'metadata.has_italics'
                ],
                'sortableAttributes' => [
                    'book.order',
                    'chapter.number',
                    'verse.number',
                    'verse.start_verse',
                    'verse.end_verse'
                ],
                'rankingRules' => [
                    'words',
                    'typo',
                    'proximity',
                    'attribute',
                    'sort',
                    'exactness'
                ]
            ]);

            if (!$this->option('quiet')) {
                $this->info('Meilisearch settings updated');
            }
        } catch (\Exception $e) {
            $this->warn('Failed to update Meilisearch settings: ' . $e->getMessage());
            Log::warning('Failed to update Meilisearch settings: ' . $e->getMessage());
        }
    }
}
