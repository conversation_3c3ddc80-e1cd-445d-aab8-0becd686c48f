<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use MeiliSearch\Client;

class InitializeMeilisearchIndexes extends Command
{
    protected $signature = 'meilisearch:init';
    protected $description = 'Initialize Meilisearch indexes';

    public function handle()
    {
        $host = config('scout.meilisearch.host');
        $key = config('scout.meilisearch.key');
        $client = new Client($host, $key);

        $indexes = ['books', 'verses', 'words', 'footnotes'];

        foreach ($indexes as $indexName) {
            $this->info("Creating index: {$indexName}");
            try {
                $client->createIndex($indexName, ['primaryKey' => 'id']);
                $this->info("Successfully created index: {$indexName}");
            } catch (\Exception $e) {
                // If index already exists, that's fine
                if (strpos($e->getMessage(), 'already exists') === false) {
                    $this->error("Error creating index {$indexName}: " . $e->getMessage());
                } else {
                    $this->info("Index {$indexName} already exists");
                }
            }
        }

        $this->info('All indexes have been initialized!');
    }
}
