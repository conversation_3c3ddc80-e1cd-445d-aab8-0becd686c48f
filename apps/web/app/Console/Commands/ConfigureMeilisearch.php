<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Meilisearch\Client;
use App\Models\Book;
use App\Models\Verse;
use App\Models\Word;
use App\Models\Footnote;

class ConfigureMeilisearch extends Command
{
    protected $signature = 'meilisearch:configure';
    protected $description = 'Configure Meilisearch indexes and settings';

    public function handle()
    {
        $client = new Client(config('scout.meilisearch.host'), config('scout.meilisearch.key'));

        // Configure Books index
        $client->createIndex('books', ['primaryKey' => 'id']);
        $client->index('books')->updateSearchableAttributes([
            'name',
            'abbreviation',
            'search_names',
            'authors',
            'theme',
            'key_people',
            'attributes_of_god',
            'key_words',
            'covenants',
            'key_teachings',
            'key_verses'
        ]);

        // Configure Verses index
        $client->createIndex('verses', ['primaryKey' => 'id']);
        $client->index('verses')->updateSearchableAttributes([
            'text',
            'reference'
        ]);

        // Configure Words index
        $client->createIndex('words', ['primaryKey' => 'id']);
        $client->index('words')->updateSearchableAttributes([
            'text',
            'strongs_number',
            'transliteration',
            'definition'
        ]);

        // Configure Footnotes index
        $client->createIndex('footnotes', ['primaryKey' => 'id']);
        $client->index('footnotes')->updateSearchableAttributes([
            'content',
            'reference'
        ]);

        $this->info('Meilisearch indexes configured successfully!');
    }
}
