<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Book;
use App\Models\Word;
use App\Models\Verse;
use App\Models\Footnote;

class ReindexScoutCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'scout:reindex {--model= : Specific model to reindex}';

    /**
     * The console command description.
     */
    protected $description = 'Reindex all or specific Scout models';

    /**
     * Array of models that use Scout
     */
    protected array $models = [
        Book::class,
        Word::class,
        Verse::class,
        Footnote::class,
    ];

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $specificModel = $this->option('model');

        if ($specificModel) {
            $this->reindexModel($specificModel);
            return;
        }

        $this->info('Starting reindexing of all Scout models...');
        $bar = $this->output->createProgressBar(count($this->models));
        $bar->start();

        foreach ($this->models as $modelClass) {
            $this->reindexModel($modelClass);
            $bar->advance();
        }

        $bar->finish();
        $this->newLine();
        $this->info('All models have been reindexed!');
    }

    /**
     * Reindex a specific model
     */
    protected function reindexModel(string $modelClass): void
    {
        $modelName = class_basename($modelClass);
        
        try {
            // First flush the model
            $this->call('scout:flush', [
                'model' => $modelClass
            ]);

            // Then import it
            $this->call('scout:import', [
                'model' => $modelClass
            ]);

            $this->info("Successfully reindexed {$modelName}");
        } catch (\Exception $e) {
            $this->error("Failed to reindex {$modelName}: " . $e->getMessage());
        }
    }
}
