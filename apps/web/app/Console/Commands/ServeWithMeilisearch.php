<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Symfony\Component\Process\Process;

class ServeWithMeilisearch extends Command
{
    protected $signature = 'dev';

    protected $description = 'Start Meilisearch and frontend development server';

    protected function isMeilisearchRunning(): bool
    {
        try {
            $response = file_get_contents('http://localhost:7700/health');
            return $response !== false;
        } catch (\Exception $e) {
            return false;
        }
    }

    public function handle(): void
    {
        $this->info('Starting development environment...');

        // Check if Meilisearch is already running
        if ($this->isMeilisearchRunning()) {
            $this->info('Meilisearch is already running at http://localhost:7700');
        } else {
            // Get Meilisearch DB path from .env or use default
            $dbPath = env('MEILISEARCH_DB_PATH', '/Applications/MAMP/htdocs/meilisearch');

            // Ensure the database directory exists
            if (!file_exists($dbPath)) {
                mkdir($dbPath, 0755, true);
            }

            // Start Meilisearch as a background process
            $this->info('Starting Meilisearch...');
            $meilisearch = new Process(['/opt/homebrew/bin/meilisearch', '--db-path=' . $dbPath]);
            $meilisearch->setTimeout(null);
            $meilisearch->start(function ($type, $buffer) {
                if (Process::ERR === $type) {
                    $this->error($buffer);
                } else {
                    $this->info($buffer);
                }
            });

            // Wait a moment to check if Meilisearch started successfully
            sleep(2);
            
            if (!$this->isMeilisearchRunning()) {
                $this->error('Failed to start Meilisearch. Make sure it is installed and the path is writable.');
                return;
            }

            // Register shutdown function to ensure Meilisearch is stopped
            register_shutdown_function(function() use ($meilisearch) {
                if ($meilisearch->isRunning()) {
                    $meilisearch->stop();
                }
            });
        }

        // Reindex Scout models
        $this->info('Reindexing Scout models...');
        $this->call('scout:reindex');

        // Start frontend development server
        $this->info('Starting frontend development server...');
        $frontendServer = new Process(['yarn', 'dev']);
        $frontendServer->setWorkingDirectory(base_path());
        $frontendServer->setTimeout(null);
        $frontendServer->run(function ($type, $buffer) {
            $this->output->write($buffer);
        });
    }
}
