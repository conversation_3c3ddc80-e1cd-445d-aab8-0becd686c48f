<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ResetAutoIncrementCommand extends Command
{
    protected $signature = 'db:reset-ids';
    protected $description = 'Reset auto-increment IDs for all tables to 1';

    public function handle()
    {
        // Get all tables from the current database
        $tables = DB::select('SHOW TABLES');
        $dbName = config('database.connections.mysql.database');
        
        foreach ($tables as $table) {
            $tableName = $table->{'Tables_in_' . $dbName};
            $this->info("Resetting auto-increment for table: {$tableName}");
            
            try {
                DB::statement("ALTER TABLE `{$tableName}` AUTO_INCREMENT = 1");
            } catch (\Exception $e) {
                $this->error("Failed to reset {$tableName}: " . $e->getMessage());
            }
        }

        $this->info('All auto-increment values have been reset to 1');
    }
}
