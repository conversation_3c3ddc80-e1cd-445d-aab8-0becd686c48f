<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Models\Book;
use App\Models\ProcessedBook;
use App\Services\BibleData\UsxParser;
use App\Services\BibleData\UsxParserState;
use App\Services\BibleData\UsxParserConfig;
use App\Services\BibleData\UsxParserLogger;
use App\Services\BibleData\TextProcessor;
use App\Services\BibleData\Handlers\ElementHandler;
use App\Services\BibleData\ModelFactory;
use App\Services\SitemapGenerator;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;
use Symfony\Component\Console\Output\OutputInterface;

class ParseBibleUsxCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'bible:parse
                            {book : Name of the book (e.g., <PERSON>)}
                            {--chapters= : Comma-separated list of chapters to parse (e.g., 1,2,3)}
                            {--debug : Enable debug output}
                            {--output : Enable standard output messages}
                            {--optimize-tables : Optimize database tables after parsing}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Parse USX Bible text files';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $startTime = microtime(true);
        $startMemory = memory_get_usage(true);

        // Check if output should be shown based on flag
        $showOutput = $this->option('output') || $this->option('debug');
        $showDebug = $this->option('debug');

        try {
            $bookName = $this->argument('book');

            $this->info("Parsing {$bookName}...");

            // Construct the path to the USX file
            $usxDirectory = base_path('bibeltext/' . $bookName);
            $usxFilePath = $usxDirectory . '/' . $bookName . '.usx';

            // First check if the file and directory exist
            if (!file_exists($usxDirectory)) {
                $this->error("Directory not found: {$usxDirectory}");
                $this->updateProcessedBookStatus($bookName, $usxFilePath, 'failed', 'Directory not found');
                return Command::FAILURE;
            }

            // Check if file exists
            if (!file_exists($usxFilePath)) {
                $this->error("USX file not found: {$usxFilePath}");
                $this->updateProcessedBookStatus($bookName, $usxFilePath, 'failed', 'USX file not found');
                return Command::FAILURE;
            }

            // Create or update ProcessedBook record
            $processedBook = $this->updateProcessedBookStatus($bookName, $usxFilePath, 'pending', 'Parsing in progress');

            // Parse chapters option if provided
            $chapterFilter = null;
            if ($this->option('chapters')) {
                $chapterFilter = array_map('intval', explode(',', $this->option('chapters')));
                if ($showDebug) {
                    $this->info("Processing only chapters: " . implode(', ', $chapterFilter));
                }
            }

            // Find the book in the database
            $bookSlug = $this->getBookSlugFromName($bookName);
            $book = Book::where('slug', $bookSlug)->first();

            if (!$book) {
                $this->error("Book with slug '{$bookSlug}' not found. Please create the book first.");
                $this->updateProcessedBookStatus($bookName, $usxFilePath, 'failed', 'Book not found in database');
                return Command::FAILURE;
            }

            // --- Extract book title from USX file ---
            $usxXml = simplexml_load_file($usxFilePath);
            $bookTitle = null;
            if ($usxXml !== false) {
                $foundBook = false;
                foreach ($usxXml->children() as $child) {
                    if (!$foundBook && $child->getName() === 'book') {
                        $foundBook = true;
                        continue;
                    }
                    if ($foundBook && $child->getName() === 'para' && ((string)$child['style'] === 'mt')) {
                        $bookTitle = trim((string)$child);
                        break;
                    }
                }
            }
            if ($bookTitle) {
                $book->name = $bookTitle;
                $book->save();
                if ($showDebug) {
                    $this->info("Updated book name to: '{$bookTitle}'");
                }
            } else {
                $this->warn('Could not extract book title from USX file.');
            }

            // Configure the parser
            $config = new UsxParserConfig([
                'debug' => $showDebug
            ]);

            // Count total chapters for progress indication
            $totalChapters = $book->chapters_count; //$this->countChaptersInUsxFile($usxFilePath);

            // Create a logger
            $logger = new UsxParserLogger($this, $showDebug, !$showOutput);

            // Create a state object
            $state = new UsxParserState($logger, $config);

            // Create a progress tracker for showing progress
            $progressBar = null;

            // Use different progress display based on debug mode
            if ($showDebug) {
                // In debug mode, just log chapter progress
                $config->setProgressCallback(function($currentChapter, $totalChapters) {
                    $this->info("Processing chapter {$currentChapter}/{$totalChapters}");
                });
            } else {
                // Create progress bar for non-debug output (always show progress indicator)
                $progressBar = $this->output->createProgressBar($totalChapters);
                $progressBar->setFormat(' %current%/%max% chapters (%percent%%) %bar% %message%');
                $progressBar->setMessage('');
                $progressBar->start();

                $config->setProgressCallback(function($currentChapter, $totalChapters) use ($progressBar) {
                    $progressBar->setProgress($currentChapter);
                });
            }

            // Create a model factory
            $modelFactory = new ModelFactory($state, $logger);

            // Create a text processor
            $textProcessor = new TextProcessor($state, $logger, $modelFactory);

            // Create an element handler
            $elementHandler = new ElementHandler($state, $logger, $textProcessor, $modelFactory);

            // Create a parser
            $parser = new UsxParser(
                $state,
                $config,
                $logger,
                $textProcessor,
                $elementHandler,
                $modelFactory,
                $totalChapters
            );

            // Parse the USX file
            $options = [
                'chapters' => $chapterFilter,
                'max_retries' => 3,
                'retry_delay' => 1
            ];

            $stats = $parser->parseFile($usxFilePath, $book, $options);

            // Finish progress display
            if ($progressBar) {
                $progressBar->setMessage(' ✓');
                $progressBar->finish();
                $this->output->newLine();
            }

            // Calculate elapsed time
            $elapsedTime = microtime(true) - $startTime;
            $memoryUsed = memory_get_usage(true) - $startMemory;

            // Prepare processing results for storage and display
            $processingResults = [
                'stats' => [
                    'verses_created' => $stats['verses_created'] ?? 0,
                    'footnotes_created' => $stats['footnotes_created'] ?? 0,
                    'words_created' => $stats['words_created'] ?? 0,
                    'batch_flushes' => $stats['batch_flushes'] ?? 0,
                ],
                'performance' => [
                    'time_elapsed' => round($elapsedTime, 2),
                    'memory_usage_mb' => round($memoryUsed / 1024 / 1024, 2),
                    'peak_memory_mb' => round(memory_get_peak_usage(true) / 1024 / 1024, 2),
                ],
                'book_slug' => $this->getBookSlugFromName($bookName),
            ];

            // Display stats if output is enabled
            if ($showOutput || $this->option('optimize-tables')) {
                $this->info("\nProcessing complete!");
                $this->info("Verses created: " . ($stats['verses_created'] ?? 0));
                $this->info("Footnotes created: " . ($stats['footnotes_created'] ?? 0));
                $this->info("Words created: " . ($stats['words_created'] ?? 0));

                if (isset($stats['batch_flushes'])) {
                    $this->info("Batch flushes: " . $stats['batch_flushes']);
                }

                // Display memory usage
                $this->info("\nPerformance statistics:");
                $this->info(sprintf("Time elapsed: %.2f seconds", $processingResults['performance']['time_elapsed']));
                $this->info(sprintf("Memory usage: %.2f MB", $processingResults['performance']['memory_usage_mb']));
                $this->info(sprintf("Peak memory usage: %.2f MB", $processingResults['performance']['peak_memory_mb']));
            }

            // Display errors and warnings (always show errors regardless of output setting)
            $errors = $logger->getErrors();
            if (!empty($errors)) {
                $this->error("\nErrors encountered:");
                foreach ($errors as $error) {
                    $this->error("- " . $error);
                }
                $processingResults['errors'] = $errors;
                $this->updateProcessedBookStatus($bookName, $usxFilePath, 'failed', $processingResults);
                return Command::FAILURE;
            }

            $warnings = $logger->getWarnings();
            if (!empty($warnings) && $showOutput) {
                $this->warn("\nWarnings:");
                foreach ($warnings as $warning) {
                    $this->warn("- " . $warning);
                }
            }

            // Update the ProcessedBook with success status
            $this->updateProcessedBookStatus($bookName, $usxFilePath, 'successful', $processingResults);

            // Perform post-parsing optimizations if requested
            if ($this->option('optimize-tables')) {
                $this->performPostParsingOptimizations($bookName);
            }
            
            // Update sitemap with the newly parsed book
            $this->updateSitemap($book);

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('An error occurred: ' . $e->getMessage());

            if ($showDebug) {
                $this->error('Stack trace:');
                $this->error($e->getTraceAsString());
            }

            // Update the ProcessedBook with failure status
            $this->updateProcessedBookStatus($this->argument('book'), $usxFilePath ?? '', 'failed', [
                'error' => $e->getMessage(),
                'trace' => $showDebug ? $e->getTraceAsString() : null,
            ]);

            return Command::FAILURE;
        }
    }

    /**
     * Get a ProcessedBook instance for the book.
     *
     * @param string $bookName The name of the book
     * @param string $usxFilePath Path to the USX file
     * @return \App\Models\ProcessedBook The processed book instance
     */
    private function getProcessedBook(string $bookName, string $usxFilePath): ProcessedBook
    {
        // Find existing record or create a new one
        $processedBook = ProcessedBook::firstOrNew([
            'filename' => $usxFilePath,
        ]);

        // Set or update values
        $processedBook->book_name = $bookName;

        // Save the record
        $processedBook->save();

        return $processedBook;
    }

    /**
     * Update the status of a ProcessedBook.
     *
     * @param string $bookName The name of the book
     * @param string $usxFilePath Path to the USX file
     * @param string $status The new status (e.g., 'pending', 'successful', 'failed')
     * @param string|array $extraData Additional data to store (stats, errors, etc.)
     * @return \App\Models\ProcessedBook The updated processed book instance
     */
    private function updateProcessedBookStatus(string $bookName, string $usxFilePath, string $status, $extraData = null): ProcessedBook
    {
        $processedBook = $this->getProcessedBook($bookName, $usxFilePath);

        // Set or update values
        $processedBook->status = $status;

        // Store any extra data (stats, errors, etc.)
        if ($extraData) {
            $processedBook->extra_data = is_array($extraData) ? $extraData : ['message' => $extraData];
        }

        // Save the record
        $processedBook->save();

        return $processedBook;
    }

    /**
     * Convert a book name to a slug for database lookup
     *
     * @param string $bookName The name of the book (e.g., "Matthäus")
     * @return string The slug form of the book name (e.g., "matthaus")
     */
    private function getBookSlugFromName(string $bookName): string
    {
        // Convert to lowercase
        $slug = strtolower($bookName);

        // Replace umlauts and special characters
        $replacements = [
            'ä' => 'a',
            'ö' => 'o',
            'ü' => 'u',
            'ß' => 'ss',
            'Ä' => 'a',
            'Ö' => 'o',
            'Ü' => 'u',
            ' ' => '-',
            '_' => '-'
        ];

        $slug = str_replace(array_keys($replacements), array_values($replacements), $slug);

        // Handle numbered books (e.g., 1.Timotheus, 2.Petrus)
        $slug = preg_replace('/(\d+)\.?\s*(\w)/', '$1.$2', $slug);

        // Capitalize first letter after number-dot pattern
        $slug = preg_replace_callback('/(\d+\.)(\w)/', function($matches) {
            return $matches[1] . strtoupper($matches[2]);
        }, $slug);

        // Remove any remaining non-alphanumeric characters except dots
        $slug = preg_replace('/[^a-zA-Z0-9\.\-]/', '', $slug);

        return $slug;
    }

    /**
     * Count the number of chapters in a USX file for progress calculation
     *
     * @param string $usxFilePath Path to the USX file
     * @return int Number of chapters in the file
     */
    private function countChaptersInUsxFile(string $usxFilePath): int
    {
        try {
            $xml = simplexml_load_file($usxFilePath);
            if (!$xml) {
                return 0;
            }

            // Only count chapter elements that have a number attribute and are opening tags
            // (not chapter end tags with eid attribute)
            $chapterCount = $xml->xpath('//chapter[@number and not(@eid)]');
            return count($chapterCount);
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Perform post-parsing optimizations for better performance
     *
     * @param string $bookName The name of the book that was parsed
     * @return void
     */
    private function performPostParsingOptimizations(string $bookName): void
    {
        $bookSlug = $this->getBookSlugFromName($bookName);

        try {
            // 1. Optimize database tables affected by parsing
            $this->optimizeDatabaseTables(['words', 'footnotes', 'verses', 'chapters']);

            // 2. Clear Redis cache for this book
            $this->clearRedisCache($bookSlug);

            // 3. Trigger Meilisearch re-indexing for this book
            $this->reindexMeilisearch($bookSlug);
        } catch (\Exception $e) {
            $this->warn("Post-parsing optimizations failed: {$e->getMessage()}");
            // Log but don't fail - these are nice-to-have optimizations
            Log::warning("Post-parsing optimizations failed for $bookName: {$e->getMessage()}");
        }
    }

    /**
     * Optimize database tables to reclaim space after deletions
     *
     * @param array $tables List of tables to optimize
     * @return void
     */
    private function optimizeDatabaseTables(array $tables): void
    {
        if ($this->option('debug') || $this->option('output') || $this->option('optimize-tables')) {
            $this->line('Optimizing database tables...');
        }

        foreach ($tables as $table) {
            try {
                DB::statement("OPTIMIZE TABLE $table");
                if ($this->option('debug') || $this->option('optimize-tables')) {
                    $this->line("Optimized table: $table");
                }
            } catch (\Exception $e) {
                if ($this->option('debug') || $this->option('optimize-tables')) {
                    $this->warn("Could not optimize table $table: {$e->getMessage()}");
                }
            }
        }
    }

    /**
     * Clear Redis cache for a specific book
     *
     * @param string $bookSlug The book slug to clear cache for
     * @return void
     */
    private function clearRedisCache(string $bookSlug): void
    {
        if ($this->option('debug') || $this->option('output') || $this->option('optimize-tables')) {
            $this->line('Clearing Redis cache...');
        }

        try {
            // Clear specific book-related keys from Redis
            $keys = [
                "book:$bookSlug*",
                "chapter:$bookSlug*",
                "verse:$bookSlug*",
                "navigation:$bookSlug*"
            ];

            foreach ($keys as $pattern) {
                $redisKeys = Redis::keys($pattern);
                if (!empty($redisKeys)) {
                    Redis::del($redisKeys);

                    if ($this->option('debug') || $this->option('optimize-tables')) {
                        $this->line("Cleared Redis keys matching: $pattern");
                    }
                }
            }
        } catch (\Exception $e) {
            if ($this->option('debug') || $this->option('optimize-tables')) {
                $this->warn("Redis cache clearing failed: {$e->getMessage()}");
            }
        }
    }

    /**
     * Re-index the book in Meilisearch
     */
    private function reindexMeilisearch(string $bookSlug): void
    {
        if ($this->option('debug') || $this->option('output') || $this->option('optimize-tables')) {
            $this->line('Re-indexing search data...');
        }

        try {
            // Use a simple approach that works with Laravel
            if (!$this->option('debug') && !$this->option('output') && !$this->option('optimize-tables')) {
                // Run quietly using shell redirection to suppress output
                $command = base_path('artisan') . ' bible:rebuild-search --book=' . escapeshellarg($bookSlug) . ' > /dev/null 2>&1';
                $exitCode = 0;
                passthru($command, $exitCode);
            } else {
                // Show command output by using normal Artisan call with only the book parameter
                $exitCode = $this->call('bible:rebuild-search', [
                    '--book' => $bookSlug
                ]);
            }

            if ($exitCode === 0) {
                if ($this->option('debug') || $this->option('optimize-tables')) {
                    $this->line("Re-indexed book: $bookSlug");
                }
            } else {
                if ($this->option('debug') || $this->option('optimize-tables')) {
                    $this->warn("Meilisearch re-indexing failed with exit code: $exitCode");
                }
            }
        } catch (\Exception $e) {
            if ($this->option('debug') || $this->option('optimize-tables')) {
                $this->warn("Meilisearch re-indexing failed: {$e->getMessage()}");
            }
            // Log but don't fail - this is a nice-to-have optimization
            Log::warning("Meilisearch re-indexing failed for $bookSlug: {$e->getMessage()}");
        }
    }

    /**
     * Update the sitemap with the newly parsed book
     *
     * @param Book $book The book that was parsed
     * @return void
     */
    private function updateSitemap(Book $book): void
    {
        if ($this->option('debug') || $this->option('output')) {
            $this->line('Updating sitemap.xml with book chapters...');
        }

        try {
            $sitemapGenerator = new SitemapGenerator();
            $result = $sitemapGenerator->updateSitemapWithBook($book);

            if ($result) {
                if ($this->option('debug') || $this->option('output')) {
                    $this->line("Updated sitemap.xml with chapters from {$book->name}");
                }
            } else {
                if ($this->option('debug') || $this->option('output')) {
                    $this->warn("Failed to update sitemap.xml with chapters from {$book->name}");
                }
            }
        } catch (\Exception $e) {
            if ($this->option('debug') || $this->option('output')) {
                $this->warn("Sitemap update failed: {$e->getMessage()}");
            }
            // Log but don't fail - this is a nice-to-have SEO feature
            Log::warning("Sitemap update failed for {$book->name}: {$e->getMessage()}");
        }
    }
}
