<?php

namespace App\Console\Commands;

use App\Enums\BookCategory;
use App\Enums\Testament;
use App\Models\Book;
use Illuminate\Console\Command;

class CreateTestBookCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'bible:create-test-book {slug} {name?} {--testament=NT} {--category=GOSPEL} {--abbreviation=} {--order=0} {--chapters-count=0}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create a test book for parsing';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $slug = $this->argument('slug');
        $name = $this->argument('name') ?? ucfirst($slug);
        $testament = $this->option('testament');
        $category = $this->option('category');
        $abbreviation = $this->option('abbreviation') ?? substr($name, 0, 3);
        $order = (int) $this->option('order');
        $chapterCount = (int) $this->option('chapters-count');

        // Check if the book already exists
        $book = Book::where('slug', $slug)->first();
        if ($book) {
            $this->info("Book with slug '{$slug}' already exists.");
            return Command::SUCCESS;
        }

        // Create the book
        $book = Book::create([
            'slug' => $slug,
            'name' => $name,
            'testament' => Testament::from($testament),
            'category' => BookCategory::from($category),
            'abbreviation' => $abbreviation,
            'order' => $order,
            'chapters_count' => $chapterCount,
        ]);

        $this->info("Book '{$name}' created successfully with slug '{$slug}'.");
        return Command::SUCCESS;
    }
}
