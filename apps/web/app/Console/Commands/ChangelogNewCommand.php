<?php

declare(strict_types=1);

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\File;

class ChangelogNewCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'changelog:new {--message= : Optional message to include in the changelog entry}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create a new changelog entry for the current version';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $version = $this->getAppVersion();
        $date = Carbon::now()->format('Y-m-d');
        $message = $this->option('message') ?: $this->ask('Enter a brief description for this version (optional)');
        
        $this->info("Creating changelog entry for version {$version}");
        
        // Get the path to the CHANGELOG.md file
        $changelogPath = base_path('CHANGELOG.md');
        
        // Create the file if it doesn't exist
        if (!File::exists($changelogPath)) {
            $this->createNewChangelog($changelogPath, $version, $date, $message);
            $this->info('Created new CHANGELOG.md file');
            return 0;
        }
        
        // Read the existing changelog
        $content = File::get($changelogPath);
        
        // Check if this version already exists in the changelog
        if (str_contains($content, "## [{$version}]")) {
            if (!$this->confirm("Version {$version} already exists in the changelog. Do you want to update it?")) {
                $this->warn('Operation cancelled');
                return 1;
            }
            
            // Update the existing version entry
            $content = $this->updateExistingVersion($content, $version, $date, $message);
            $this->info("Updated existing entry for version {$version}");
        } else {
            // Add a new version entry
            $content = $this->addNewVersion($content, $version, $date, $message);
            $this->info("Added new entry for version {$version}");
        }
        
        // Write the updated content back to the file
        File::put($changelogPath, $content);
        
        return 0;
    }
    
    /**
     * Get the application version from .env.VERSION or package.json
     */
    protected function getAppVersion(): string
    {
        // First try to read from .env.VERSION file
        $envVersionPath = base_path('.env.VERSION');
        if (File::exists($envVersionPath)) {
            $content = File::get($envVersionPath);
            if (preg_match('/VERSION=(.+)/i', $content, $matches)) {
                return $matches[1];
            }
        }
        
        // Fallback to package.json
        $packageJsonPath = base_path('package.json');
        if (File::exists($packageJsonPath)) {
            $packageJson = json_decode(File::get($packageJsonPath), true);
            if (isset($packageJson['version'])) {
                return $packageJson['version'];
            }
        }
        
        // Default fallback version
        return '0.0.0';
    }
    
    /**
     * Create a new changelog file with the initial version
     */
    protected function createNewChangelog(string $path, string $version, string $date, ?string $message): void
    {
        $content = <<<EOT
# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [{$version}] - {$date}

EOT;

        if ($message) {
            $content .= "\n### Added\n\n- {$message}\n";
        } else {
            $content .= "\n### Added\n\n- Initial release\n";
        }
        
        File::put($path, $content);
    }
    
    /**
     * Update an existing version entry in the changelog
     */
    protected function updateExistingVersion(string $content, string $version, string $date, ?string $message): string
    {
        if (!$message) {
            return $content; // No message to add, return unchanged
        }
        
        // Find the version section
        $pattern = "/## \[{$version}\].*?\n(.*?)(?=\n## \[|$)/s";
        if (preg_match($pattern, $content, $matches)) {
            $versionContent = $matches[1];
            
            // Check if there's an "Added" section
            if (str_contains($versionContent, '### Added')) {
                // Add to existing Added section
                $updatedVersionContent = preg_replace(
                    '/### Added\n\n(.*?)(?=\n###|$)/s',
                    "### Added\n\n$1- {$message}\n",
                    $versionContent
                );
            } else {
                // Create a new Added section
                $updatedVersionContent = $versionContent . "\n### Added\n\n- {$message}\n";
            }
            
            // Replace the old version content with the updated one
            return str_replace($matches[0], "## [{$version}] - {$date}\n" . $updatedVersionContent, $content);
        }
        
        return $content;
    }
    
    /**
     * Add a new version entry to the changelog
     */
    protected function addNewVersion(string $content, string $version, string $date, ?string $message): string
    {
        // Find the position after the header section
        $headerEndPos = strpos($content, '## [');
        if ($headerEndPos === false) {
            // No existing versions, add after the header
            $headerEndPos = strlen($content);
        }
        
        $newVersionEntry = "\n## [{$version}] - {$date}\n\n";
        if ($message) {
            $newVersionEntry .= "### Added\n\n- {$message}\n";
        } else {
            $newVersionEntry .= "### Added\n\n- New version\n";
        }
        
        // Insert the new version after the header but before any existing versions
        return substr_replace($content, $newVersionEntry, $headerEndPos, 0);
    }
}
