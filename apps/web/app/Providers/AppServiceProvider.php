<?php

namespace App\Providers;

use App\Models\Book;
use Illuminate\Support\Facades\Vite;
use Illuminate\Support\ServiceProvider;
use Inertia\Inertia;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->app->singleton(TextFormatter::class);

        // Register MySQL schema grammar
        $this->app->bind('db.schema.grammar.mysql', function () {
            return new \Illuminate\Database\Schema\Grammars\MySqlGrammar;
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Vite::prefetch(concurrency: 3);

        Inertia::share([
            'books' => fn () => Book::with(['chapters' => function ($query) {
                    $query->select('id', 'book_id', 'number')->orderBy('number');
                }])
                ->select('id', 'name', 'testament', 'category', 'order', 'abbreviation')
                ->orderBy('order')
                ->get()
                ->groupBy(function ($book) {
                    return $book->testament === 'old' ? 'ot' : 'nt';
                })
                ->map(function ($testamentBooks) {
                    return $testamentBooks->groupBy('category')->map(function ($categoryBooks) {
                        return $categoryBooks->values()->map(function ($book) {
                            return [
                                'id' => $book->id,
                                'name' => $book->name,
                                'abbreviation' => $book->abbreviation,
                                'chapters' => range(1, $book->chapters->count()),
                                'testament' => $book->testament === 'old' ? 'ot' : 'nt',
                                'category' => $book->category,
                                'order' => $book->order
                            ];
                        });
                    });
                })
        ]);
    }
}
