<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Esbo\Core\Models\Book;
use Esbo\Core\Models\Chapter;
use Esbo\Core\Models\Verse;

class EsboServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        // Register any application bindings
    }

    public function boot(): void
    {
        // Bind core models to the container if needed
        $this->app->bind('esbo.book', function ($app) {
            return new Book();
        });

        $this->app->bind('esbo.chapter', function ($app) {
            return new Chapter();
        });

        $this->app->bind('esbo.verse', function ($app) {
            return new Verse();
        });
    }
}
