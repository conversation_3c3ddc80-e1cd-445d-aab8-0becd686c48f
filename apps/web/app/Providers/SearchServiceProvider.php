<?php

namespace App\Providers;

use App\Services\Search\BibleSearchService;
use App\Services\Search\BookSearchService;
use App\Services\Search\VerseSearchService;
use App\Services\BibleReferenceParser;
use Illuminate\Support\ServiceProvider;

class SearchServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->singleton(BookSearchService::class);
        $this->app->singleton(VerseSearchService::class);
        
        $this->app->singleton(BibleSearchService::class, function ($app) {
            return new BibleSearchService(
                $app->make(BibleReferenceParser::class),
                $app->make(BookSearchService::class),
                $app->make(VerseSearchService::class)
            );
        });
    }
}
