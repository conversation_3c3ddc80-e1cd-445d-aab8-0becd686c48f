<?php

namespace App\Providers;

use App\Services\BibleReferenceParser;
use App\Services\BibleReferenceService;
use App\Services\FootnoteReferenceService;
use Illuminate\Support\ServiceProvider;

class BibleServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton(FootnoteReferenceService::class, function ($app) {
            return new FootnoteReferenceService(
                $app->make(BibleReferenceService::class),
                $app->make(BibleReferenceParser::class)
            );
        });
    }
    
    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
