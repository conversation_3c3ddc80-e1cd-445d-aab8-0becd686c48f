<?php

namespace App\Services;

use App\Models\Book;

class BibleReference
{
    public function __construct(
        private readonly ?Book $book,
        private readonly ?int $chapter,
        private readonly ?int $verseStart = null,
        private readonly ?int $verseEnd = null
    ) {}

    public function isValid(): bool
    {
        return $this->book !== null && $this->chapter !== null;
    }

    public function getBook(): ?Book
    {
        return $this->book;
    }

    public function getChapter(): ?int
    {
        return $this->chapter;
    }

    public function getVerseStart(): ?int
    {
        return $this->verseStart;
    }

    public function getVerseEnd(): ?int
    {
        return $this->verseEnd;
    }

    public function getVerse(): ?int
    {
        return $this->verseStart;
    }

    public function toString(): string
    {
        $reference = $this->book?->name . ' ' . $this->chapter;
        if ($this->verseStart !== null) {
            $reference .= ',' . $this->verseStart;
            if ($this->verseEnd !== null && $this->verseEnd !== $this->verseStart) {
                $reference .= '-' . $this->verseEnd;
            }
        }
        return $reference;
    }
}
