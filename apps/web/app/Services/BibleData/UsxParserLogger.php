<?php

namespace App\Services\BibleData;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Symfony\Component\Console\Output\OutputInterface;

class UsxParserLogger
{
    private ?Command $command;
    private ?OutputInterface $output;
    private bool $isDebug;
    private bool $isQuiet;
    private array $errors = [];
    private array $warnings = [];

    /**
     * Create a new UsxParserLogger instance
     *
     * @param Command|OutputInterface|null $output The command or output instance
     * @param bool $isDebug Whether to enable debug logging
     * @param bool $isQuiet Whether to suppress debug output to console (still logs to file)
     */
    public function __construct($output = null, bool $isDebug = false, bool $isQuiet = false)
    {
        if ($output instanceof Command) {
            $this->command = $output;
            $this->output = null;
        } elseif ($output instanceof OutputInterface) {
            $this->command = null;
            $this->output = $output;
        } else {
            $this->command = null;
            $this->output = null;
        }

        $this->isDebug = $isDebug;
        $this->isQuiet = $isQuiet;
    }

    /**
     * Log a debug message
     *
     * @param string $message The message to log
     * @return void
     */
    public function debug(string $message): void
    {
        if ($this->isDebug) {
            // Always log to file
            try {
                // Try to use the parser channel specifically
                Log::channel('parser')->debug($message);
            } catch (\Exception $e) {
                // Fall back to default debug logging if parser channel fails
                Log::debug($message);
            }

            // Only output to console if not in quiet mode
            if (!$this->isQuiet) {
                $this->writeOutput("DEBUG: $message");
            }
        }
    }

    /**
     * Log an error message
     *
     * @param string $message The message to log
     * @param array $context Additional context
     * @return void
     */
    public function error(string $message, array $context = []): void
    {
        $this->errors[] = $message;
        Log::error($message, $context);
        $this->writeError($message);
    }

    /**
     * Log a warning message
     *
     * @param string $message The message to log
     * @param array $context Additional context
     * @return void
     */
    public function warning(string $message, array $context = []): void
    {
        $this->warnings[] = $message;
        Log::warning($message, $context);
        $this->writeWarning($message);
    }

    /**
     * Get all logged errors
     *
     * @return array The logged errors
     */
    public function getErrors(): array
    {
        return $this->errors;
    }

    /**
     * Get all logged warnings
     *
     * @return array The logged warnings
     */
    public function getWarnings(): array
    {
        return $this->warnings;
    }

    /**
     * Set debug mode
     *
     * @param bool $isDebug Whether to enable debug logging
     * @return void
     */
    public function setDebugMode(bool $isDebug): void
    {
        $this->isDebug = $isDebug;
    }

    /**
     * Set quiet mode
     *
     * @param bool $isQuiet Whether to suppress debug output to console
     * @return void
     */
    public function setQuietMode(bool $isQuiet): void
    {
        $this->isQuiet = $isQuiet;
    }

    /**
     * Write a normal output message
     *
     * @param string $message The message to write
     * @return void
     */
    private function writeOutput(string $message): void
    {
        if ($this->command) {
            $this->command->line($message);
        } elseif ($this->output) {
            $this->output->writeln($message);
        }
    }

    /**
     * Write an error message
     *
     * @param string $message The message to write
     * @return void
     */
    private function writeError(string $message): void
    {
        if ($this->command) {
            $this->command->error($message);
        } elseif ($this->output) {
            $this->output->writeln("<fg=red>$message</fg=red>");
        }
    }

    /**
     * Write a warning message
     *
     * @param string $message The message to write
     * @return void
     */
    private function writeWarning(string $message): void
    {
        if ($this->command) {
            $this->command->warn($message);
        } elseif ($this->output) {
            $this->output->writeln("<comment>$message</comment>");
        }
    }
}
