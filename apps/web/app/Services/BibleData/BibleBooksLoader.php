<?php

namespace App\Services\BibleData;

use Illuminate\Support\Facades\File;
use InvalidArgumentException;

class BibleBooksLoader
{
    public function __construct(
        private readonly BibleBooksValidator $validator
    ) {}

   public function load(): array
    {
        $path = database_path('data/bible_books.json');

        if (!File::exists($path)) {
            throw new InvalidArgumentException("Bible books data file not found at {$path}");
        }

        $data = json_decode(File::get($path), true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new InvalidArgumentException('Invalid JSON in Bible books data file');
        }

        $this->validator->validate($data);
        return $data;
    }
}
