<?php

namespace App\Services\BibleData;

class ContentProcessor
{
    public function processContent(string $content): array
    {
        return $this->pipe($content, [
            [$this, 'normalizeSpaces'],
            [$this, 'splitWords'],
            [$this, 'filterEmptyWords'],
        ]);
    }

    protected function pipe(string $content, array $callbacks): array
    {
        $result = $content;
        foreach ($callbacks as $callback) {
            $result = call_user_func($callback, $result);
        }
        return $result;
    }

    protected function normalizeSpaces(string $text): string
    {
        return preg_replace('/\s+/', ' ', trim($text));
    }

    protected function splitWords(string $text): array
    {
        $words = [];
        $currentWord = '';
        $len = mb_strlen($text);

        for ($i = 0; $i < $len; $i++) {
            $char = mb_substr($text, $i, 1);

            if ($char === ' ') {
                if (!empty($currentWord)) {
                    $words[] = $currentWord;
                    $currentWord = '';
                }
            } elseif (preg_match('/[,.;:!?]/', $char)) {
                if (!empty($currentWord)) {
                    $currentWord .= $char;
                    $words[] = $currentWord;
                    $currentWord = '';
                } else {
                    $words[] = $char;
                }
            } else {
                $currentWord .= $char;
            }
        }

        if (!empty($currentWord)) {
            $words[] = $currentWord;
        }

        return $words;
    }

    protected function filterEmptyWords(array $words): array
    {
        return array_values(array_filter($words, 'trim'));
    }
}
