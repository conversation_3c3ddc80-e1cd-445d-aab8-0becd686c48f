<?php

namespace App\Services\BibleData;

use Illuminate\Support\Facades\Validator;
use InvalidArgumentException;
use Illuminate\Validation\Rule;
use App\Enums\BookCategory;

class BibleBooksValidator
{
    public function validate(array $data): void
    {
        $validator = Validator::make($data, [
            '*' => 'required|array',
            '*.abbreviations' => 'required|array',
            '*.abbreviations.*' => 'required|string',
            '*.alternate_names' => 'required|array',
            '*.alternate_names.*' => 'required|string',
            '*.common_names' => 'required|array',
            '*.common_names.*' => 'required|string',
            '*.metadata' => 'required|array',
            '*.metadata.order' => 'required|integer',
            '*.metadata.testament' => [
                'required',
                Rule::in(['ot', 'nt', 'old', 'new']),
            ],
            '*.metadata.category' => [
                'required',
                Rule::in(BookCategory::values()),
            ],
            '*.metadata.chapters_count' => 'required|integer',
            '*.metadata.original_language' => 'required|string',
            '*.metadata.historical_period' => 'nullable|string',
            '*.metadata.location' => 'nullable|string',
            '*.metadata.authors' => 'required|array',
            '*.metadata.authors.*' => 'required|string',
            '*.metadata.written_year' => 'required|integer',
            '*.metadata.theme' => 'required|string',
            '*.metadata.key_people' => 'required|array',
            '*.metadata.key_people.*' => 'required|string',
            '*.metadata.attributes_of_god' => 'required|array',
            '*.metadata.attributes_of_god.*' => 'required|string',
            '*.metadata.key_words' => 'required|array',
            '*.metadata.key_words.*' => 'required|string',
            '*.metadata.covenants' => 'required|array',
            '*.metadata.covenants.*' => 'required|string',
            '*.metadata.key_teachings' => 'required|array',
            '*.metadata.key_teachings.*' => 'required|string',
            '*.metadata.key_verses' => 'required|array',
            '*.metadata.key_verses.*' => 'required|string',
        ]);

        if ($validator->fails()) {
            throw new InvalidArgumentException(
                'Invalid Bible books data: ' . json_encode($validator->errors()->all())
            );
        }
    }
}
