<?php

declare(strict_types=1);

namespace App\Services\BibleData;

use App\Models\Book;
use App\Models\Chapter;
use App\Models\Verse;
use App\Models\Word;
use App\Models\Footnote;
use App\Models\Reference;
use App\Models\ParagraphStyle;
use App\Models\ParagraphSpan;
use DOMElement;
use DOMNode;
use SimpleXMLElement;

class ModelFactory
{
    protected UsxParserState $state;
    protected UsxParserLogger $logger;

    public function __construct(UsxParserState $state, UsxParserLogger $logger)
    {
        $this->state = $state;
        $this->logger = $logger;
    }

    /**
     * Create or update a chapter
     *
     * @param int $chapterNumber The chapter number
     * @return Chapter The created or updated chapter
     */
    public function createChapter(int $chapterNumber): Chapter
    {
        // Use state management from UsxParserState
        $this->state->resetForChapter();
        $this->state->chapterNumber = $chapterNumber;

        // Create or update chapter
        $this->state->chapter = Chapter::updateOrCreate(
            [
                'book_id' => $this->state->book->id,
                'number' => $this->state->chapterNumber
            ]
        );

        $this->logger->debug("Created/Updated chapter {$chapterNumber}");

        return $this->state->chapter;
    }

    /**
     * Create a verse
     *
     * @param string $verseNumber The verse number (can be a range like "1-3")
     * @return Verse The created verse
     */
    public function createVerse(string $verseNumber): Verse
    {
        $this->logger->debug("Creating verse: {$verseNumber}");

        // Ensure we have a valid chapter
        if (!$this->state->chapter) {
            $this->logger->debug("No valid chapter found for verse {$verseNumber}. Using current chapter number: {$this->state->chapterNumber}");

            // Try to find the chapter by number if it exists
            if ($this->state->chapterNumber && $this->state->book) {
                $this->state->chapter = Chapter::where('book_id', $this->state->book->id)
                    ->where('number', $this->state->chapterNumber)
                    ->first();

                if ($this->state->chapter) {
                    $this->logger->debug("Found chapter by number {$this->state->chapterNumber} with ID: {$this->state->chapter->id}");
                } else {
                    $this->logger->debug("Failed to find chapter by number {$this->state->chapterNumber}");
                    // Create the chapter if it doesn't exist
                    $this->createChapter($this->state->chapterNumber);
                }
            } else {
                $this->logger->debug("Cannot create verse: no valid chapter number or book");
                throw new \Exception("Cannot create verse: no valid chapter");
            }
        }

        // Handle verse ranges (e.g., "1-3")
        $startVerse = $endVerse = intval($verseNumber);
        if (strpos($verseNumber, '-') !== false) {
            list($startVerse, $endVerse) = array_map('intval', explode('-', $verseNumber));
        }

        // If there is a pending paragraph style/group id (set by ParagraphHandler before verse exists), apply it now
        if (!empty($this->state->pendingParagraphStyle) && !empty($this->state->pendingParagraphGroupId)) {
            $this->logger->debug("Applying pending paragraph style/group id to new verse: style={$this->state->pendingParagraphStyle->style_code}, group_id={$this->state->pendingParagraphGroupId}");
            $this->state->currentVerseParagraphStyle = $this->state->pendingParagraphStyle;
            $this->state->currentParagraphGroupId = $this->state->pendingParagraphGroupId;
            // Clear pending state
            unset($this->state->pendingParagraphStyle);
            unset($this->state->pendingParagraphGroupId);
        }
        // Ensure we have a paragraph group ID
        if (empty($this->state->currentParagraphGroupId)) {
            $this->state->currentParagraphGroupId = $this->generateParagraphGroupId();
            $this->state->isPericopeStart = true;
            $this->logger->debug("Generated new paragraph group ID in createVerse: {$this->state->currentParagraphGroupId}");
        } else {
            $this->logger->debug("Using existing paragraph group ID: {$this->state->currentParagraphGroupId}");
        }

        $this->logger->debug("Setting is_pericope_start to: " . ($this->state->isPericopeStart ? 'true' : 'false'));
        $this->logger->debug("Creating verse with chapter_id: {$this->state->chapter->id}");

        $this->state->verse = Verse::updateOrCreate(
            [
                'chapter_id' => $this->state->chapter->id,
                'number' => $startVerse,
            ],
            [
                'text' => '',
                'start_verse' => $startVerse,
                'end_verse' => $endVerse,
                'paragraph_style_id' => $this->state->currentVerseParagraphStyle?->id,
                'paragraph_group_id' => $this->state->currentParagraphGroupId,
                'tags' => [],
                'is_pericope_start' => $this->state->isPericopeStart,
                'has_ot_quote' => false,
                'has_text_variant' => false
            ]
        );

        // Verify the value was set correctly
        $this->logger->debug("After save, verse ID: {$this->state->verse->id}, chapter_id: {$this->state->verse->chapter_id}");
        $this->logger->debug("After save, is_pericope_start = " . ($this->state->verse->is_pericope_start ? 'true' : 'false'));

        // Clean existing related data for this verse
        Word::where('verse_id', $this->state->verse->id)->delete();
        Footnote::where('verse_id', $this->state->verse->id)->delete();

        $this->state->isPericopeStart = false; // new para tags create new pericopes, after verse set to false again
        $this->state->incrementVerseCount();

        return $this->state->verse;
    }

    /**
     * Create a word
     *
     * @param array $wordData The word data
     * @return Word|null The created word or null if no verse exists
     */
    public function createWord(array $wordData): ?Word
    {
        try {
            if (!isset($wordData['verse_id']) || !$wordData['verse_id']) {
                $this->logger->debug("Cannot create word: no valid verse_id provided");
                return null;
            }

            if (!isset($wordData['text']) || !isset($wordData['position'])) {
                $this->logger->debug("Cannot create word: missing required fields");
                return null;
            }

            // Ensure text is properly sanitized
            $wordData['text'] = $this->sanitizeWordText($wordData['text']);

            $this->logger->debug("Creating word: " . json_encode($wordData));

            // Set is_ot_quote flag if word_type is 'xot'
            if (($wordData['word_type'] ?? '') === 'xot' && !isset($wordData['is_ot_quote'])) {
                $wordData['is_ot_quote'] = true;
                $this->logger->debug("Setting is_ot_quote to true because word_type is 'xot'");
            }

            // Set is_addition flag if word_type is 'add'
            if (($wordData['word_type'] ?? '') === 'add' && !isset($wordData['is_addition'])) {
                $wordData['is_addition'] = true;
                $this->logger->debug("Setting is_addition to true because word_type is 'add'");
            }

            // Set has_variant flag if variant_group_id is present
            if (!empty($wordData['variant_group_id']) && !isset($wordData['has_variant'])) {
                $wordData['has_variant'] = true;
                if (!isset($wordData['variant_type'])) {
                    $wordData['variant_type'] = 'text';
                }
                $this->logger->debug("Setting has_variant to true because variant_group_id is present");
            }

            // Update verse flags if needed
            if (($wordData['is_ot_quote'] ?? false) || ($wordData['word_type'] ?? '') === 'xot') {
                $this->updateVerseFlag($wordData['verse_id'], 'has_ot_quote');
            }

            if (($wordData['has_variant'] ?? false) || !empty($wordData['variant_group_id'])) {
                $this->updateVerseFlag($wordData['verse_id'], 'has_text_variant');
            }

            // Add the word to the buffer
            $this->logger->debug("Adding word '{$wordData['text']}' at position {$wordData['position']} to buffer");
            $this->state->addToWordBuffer($wordData);
            $this->state->incrementWordCount();

            // Create and return a new Word instance
            return new Word($wordData);

        } catch (\Throwable $e) {
            $this->logger->error("Error in ModelFactory::createWord: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Update a verse flag
     *
     * @param int|string $verseId The verse ID
     * @param string $flagName The flag name to update
     * @return bool Whether the update was successful
     */
    public function updateVerseFlag($verseId, string $flagName): bool
    {
        try {
            $verse = Verse::find($verseId);
            if (!$verse) {
                $this->logger->debug("Cannot update verse flag: verse not found with ID {$verseId}");
                return false;
            }

            // Only update if the flag exists on the verse model and it's not already set to true
            if (in_array($flagName, $verse->getFillable()) && !$verse->$flagName) {
                $verse->$flagName = true;
                $verse->save([$flagName]);
                $this->logger->debug("Updated verse {$verse->number} flag {$flagName} to true");
                return true;
            }

            if ($verse->$flagName) {
                $this->logger->debug("Flag {$flagName} is already set to true on verse {$verse->number}");
            } else {
                $this->logger->debug("Flag {$flagName} is not fillable on verse model");
            }

            return false;
        } catch (\Exception $e) {
            $this->logger->error("Error updating verse flag: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Sanitize word text to handle special characters
     *
     * @param string $text The text to sanitize
     * @return string The sanitized text
     */
    protected function sanitizeWordText(string $text): string
    {
        // Special characters that need careful handling
        $specialChars = [
            '[' => '&#91;',  // Left square bracket
            ']' => '&#93;',  // Right square bracket
            '»' => '&#187;', // Right-pointing double angle quotation mark
            '«' => '&#171;', // Left-pointing double angle quotation mark
            '"' => '&#8220;', // Left double quotation mark
            '"' => '&#8221;', // Right double quotation mark
            '—' => '&#8212;', // Em dash
            '–' => '&#8211;', // En dash
        ];

        // Encode problematic characters while preserving their semantic meaning
        /*foreach ($specialChars as $char => $encoded) {
            if (strpos($text, $char) !== false) {
                $this->logger->debug("Sanitizing special character '{$char}' in word: {$text}");
                $text = str_replace($char, $encoded, $text);
            }
        }*/

        return $text;
    }

    /**
     * Create a footnote
     *
     * @param DOMNode $note The footnote node
     * @param int $position The footnote position
     * @return Footnote|null The created footnote or null if no verse exists
     */
    public function createFootnote(DOMNode $note, int $position): ?Footnote
    {
        if (!$this->state->verse) {
            return null;
        }

        // Get note attributes
        $caller = '';
        $content = '';
        $searchableText = '';
        $contentStructure = [
            'elements' => [],
            'metadata' => [
                'total_elements' => 0,
                'has_references' => false
            ]
        ];

        // Process caller
        if ($note instanceof \DOMElement) {
            $callerNode = $note->getElementsByTagName('char')->item(0);
            if ($callerNode) {
                $caller = trim($callerNode->textContent);
            }
        }

        // Process content - collect all text content and build structure
        $contentNodes = $note->childNodes;
        foreach ($contentNodes as $node) {
            if ($node->nodeName === '#text') {
                $text = trim($node->textContent);
                if (!empty($text)) {
                    $contentStructure['elements'][] = [
                        'type' => 'text',
                        'content' => $text
                    ];
                    $content .= $text;
                }
            } elseif ($node->nodeName === 'char') {
                $style = ($node instanceof \DOMElement) ? $node->getAttribute('style') : '';
                $text = trim($node->textContent);
                if (!empty($text)) {
                    if ($style === 'fr') {
                        // Skip the caller in content but add to structure
                        $contentStructure['elements'][] = [
                            'type' => 'caller',
                            'style' => $style,
                            'content' => $text
                        ];
                    } else {
                        $contentStructure['elements'][] = [
                            'type' => 'styled',
                            'style' => $style,
                            'content' => $text
                        ];
                        $content .= $text;
                    }
                }
            }
        }

        $contentStructure['metadata']['total_elements'] = count($contentStructure['elements']);
        $searchableText = trim(strip_tags($content));

        // Create the footnote
        $footnote = new Footnote([
            'verse_id' => $this->state->verse->id,
            'position' => $position,
            'caller' => $caller,
            'content' => $content,
            'searchable_text' => $searchableText,
            'content_structure' => $contentStructure,
            'is_reference' => false,
            'has_italics' => false
        ]);
        $footnote->save();

        // Increment footnote count
        $this->state->incrementFootnoteCount();

        // Flush the current word buffer to ensure all previous words are saved
        $this->state->flushWordBuffer();

        // Now get the actual last word and attach the footnote to it
        $lastWord = Word::where('verse_id', $this->state->verse->id)
                       ->orderBy('position', 'desc')
                       ->first();

        if ($lastWord) {
            $lastWord->has_footnote = true;
            $lastWord->footnote_id = $footnote->id;
            $lastWord->save();
        }

        return $footnote;
    }

    /**
     * Create a reference
     *
     * @param DOMNode|SimpleXMLElement $element The reference element
     * @return Reference|null The created reference or null if no verse exists or no location is specified
     */
    public function createReference($element): ?Reference
    {
        $attributes = $this->getElementAttributes($element);
        $text = $element instanceof SimpleXMLElement ? $element->__toString() : $element->textContent;
        $loc = $attributes['loc'] ?? null;

        if (!$loc || !$this->state->verse) {
            return null;
        }

        // Parse the reference location
        preg_match('/^(?:(\w+)\s+)?(\d+),(\d+(?:[a-z]|-\d+)?)$/', $loc, $matches);

        $reference = new Reference([
            'type' => $attributes['type'] ?? 'verse_ref',
            'subtype' => $attributes['subtype'] ?? 'verse',
            'text' => $text,
            'target_book' => $matches[1] ?? $this->state->book->slug,
            'target_chapter' => $matches[2] ?? null,
            'target_verse' => $matches[3] ?? null,
            'display_text' => $text,
            'attributes' => $attributes
        ]);

        if ($this->state->footnote) {
            $reference->footnote()->associate($this->state->footnote);
        }

        $reference->save();
        return $reference;
    }

    /**
     * Find or create a paragraph style
     *
     * @param DOMNode|SimpleXMLElement $element The paragraph element
     * @return ParagraphStyle|null The created paragraph style or null if no style code is specified
     */
    public function createParagraphStyle($element): ?ParagraphStyle
    {
        $attributes = $this->getElementAttributes($element);
        $styleCode = $attributes['style'] ?? null;

        if (!$styleCode) {
            return null;
        }

        // Find or create the paragraph style
        return ParagraphStyle::firstOrCreate(
            ['style_code' => $styleCode],
            [
                'name' => $this->getParagraphStyleName($styleCode),
                'description' => $this->getParagraphStyleDescription($styleCode),
                'attributes' => $attributes
            ]
        );
    }

    /**
     * Generate a paragraph group ID
     *
     * @return string The generated paragraph group ID
     */
    public function generateParagraphGroupId(): string
    {
        return 'p_' . substr(md5(uniqid('', true)), 0, 4);
    }

    /**
     * Get attributes from an element
     *
     * @param DOMNode|SimpleXMLElement $element The element
     * @return array The element attributes
     */
    public function getElementAttributes($element): array
    {
        $attributes = [];

        if ($element instanceof SimpleXMLElement) {
            foreach ($element->attributes() as $name => $value) {
                $attributes[$name] = (string)$value;
            }
        } elseif ($element instanceof DOMElement) {
            if ($element->hasAttributes()) {
                foreach ($element->attributes as $attr) {
                    $attributes[$attr->name] = $attr->value;
                }
            }
        }

        return $attributes;
    }

    /**
     * Get a paragraph style name from its code
     *
     * @param string $styleCode The paragraph style code
     * @return string The paragraph style name
     */
    protected function getParagraphStyleName(string $styleCode): string
    {
        $styleNames = [
            'p' => 'Normal Paragraph',
            'q' => 'Poetry',
            'q1' => 'Poetry Level 1',
            'q2' => 'Poetry Level 2',
            'q3' => 'Poetry Level 3',
            'b' => 'Blank Line',
            'h' => 'Header',
            'mt' => 'Main Title',
            'mt1' => 'Main Title 1',
            'mt2' => 'Main Title 2',
            'mt3' => 'Main Title 3',
            's' => 'Section Heading',
            's1' => 'Section Heading 1',
            's2' => 'Section Heading 2',
            's3' => 'Section Heading 3',
            'r' => 'Parallel Reference',
            'li' => 'List Item',
            'li1' => 'List Item Level 1',
            'li2' => 'List Item Level 2',
            'li3' => 'List Item Level 3',
            'm' => 'Margin Paragraph',
            'pi' => 'Indented Paragraph',
            'pc' => 'Centered Paragraph',
            'nb' => 'No Break',
            'cls' => 'Closure',
            'sp' => 'Speaker',
        ];

        return $styleNames[$styleCode] ?? 'Unknown Style: ' . $styleCode;
    }

    /**
     * Get a paragraph style description from its code
     *
     * @param string $styleCode The paragraph style code
     * @return string The paragraph style description
     */
    protected function getParagraphStyleDescription(string $styleCode): string
    {
        $styleDescriptions = [
            'p' => 'A normal paragraph.',
            'q' => 'A poetry stanza.',
            'q1' => 'A poetry stanza, level 1 indent.',
            'q2' => 'A poetry stanza, level 2 indent.',
            'q3' => 'A poetry stanza, level 3 indent.',
            'b' => 'A blank line.',
            'h' => 'A header.',
            'mt' => 'The main title of the book.',
            'mt1' => 'The main title of the book, level 1.',
            'mt2' => 'The main title of the book, level 2.',
            'mt3' => 'The main title of the book, level 3.',
            's' => 'A section heading.',
            's1' => 'A section heading, level 1.',
            's2' => 'A section heading, level 2.',
            's3' => 'A section heading, level 3.',
            'r' => 'A parallel reference.',
            'li' => 'A list item.',
            'li1' => 'A list item, level 1.',
            'li2' => 'A list item, level 2.',
            'li3' => 'A list item, level 3.',
            'm' => 'A paragraph with no first line indent.',
            'pi' => 'An indented paragraph.',
            'pc' => 'A centered paragraph.',
            'nb' => 'A paragraph with no break with the previous paragraph.',
            'cls' => 'A closure.',
            'sp' => 'A speaker identification.',
        ];

        return $styleDescriptions[$styleCode] ?? 'No description available for this style.';
    }
}
