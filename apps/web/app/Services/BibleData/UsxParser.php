<?php

declare(strict_types=1);

namespace App\Services\BibleData;

use App\Models\Book;
use App\Models\Chapter;
use App\Models\Verse;
use App\Models\Word;
use App\Models\Footnote;
use App\Models\Reference;
use App\Models\ParagraphStyle;
use App\Models\ParagraphSpan;
use Illuminate\Support\Facades\DB;
use App\Services\BibleData\Handlers\ElementHandler;

use XMLReader;
use DOMDocument;
use DOMXPath;
use DOMElement;
use SimpleXMLElement;
use Throwable;

class UsxParser
{
    protected UsxParserState $state;
    protected UsxParserConfig $config;
    protected UsxParserLogger $logger;
    protected TextProcessor $textProcessor;
    protected ElementHandler $elementHandler;
    protected ModelFactory $modelFactory;
    protected int $chapterCount;

    public function __construct(
        UsxParserState $state,
        UsxParserConfig $config,
        UsxParserLogger $logger,
        TextProcessor $textProcessor,
        ElementHandler $elementHandler,
        ModelFactory $modelFactory,
        int $chapterCount
    ) {
        $this->state = $state;
        $this->config = $config;
        $this->logger = $logger;
        $this->textProcessor = $textProcessor;
        $this->elementHandler = $elementHandler;
        $this->modelFactory = $modelFactory;
        $this->chapterCount = $chapterCount;
    }

    /**
     * Parse a USX file for a specific book
     *
     * @param string $usxFilePath Path to the USX file
     * @param Book $book The book model
     * @param array|null $options Optional parsing options
     * @return array Statistics about the parsing process
     * @throws \Exception
     */
    public function parseFile(string $usxFilePath, Book $book, ?array $options = null): array
    {
        $this->logger->debug("Starting to parse file: {$usxFilePath}");
        $this->state->book = $book;

        // Set default options
        $options = $options ?? [];
        $chapterFilter = $options['chapters'] ?? null;
        $maxRetries = $options['max_retries'] ?? 3;
        $retryDelay = $options['retry_delay'] ?? 1; // seconds

        if ($chapterFilter) {
            $this->logger->debug("Filtering chapters: " . implode(', ', (array)$chapterFilter));
        }

        DB::beginTransaction();
        try {
            // Delete existing words and footnotes for this book before re-parsing
            // This prevents ID growth over time with repeated parsing
            $chapterIds = $book->chapters()->pluck('id')->toArray();
            if (!empty($chapterIds)) {
                // First get the verse IDs that belong to this book's chapters
                $verseIds = DB::table('verses')
                    ->whereIn('chapter_id', $chapterIds)
                    ->pluck('id')
                    ->toArray();

                if (!empty($verseIds)) {
                    // Delete all words for these verses
                    $deletedWords = DB::table('words')
                        ->whereIn('verse_id', $verseIds)
                        ->delete();

                    // Delete all footnotes for these verses
                    $deletedFootnotes = DB::table('footnotes')
                        ->whereIn('verse_id', $verseIds)
                        ->delete();

                    $this->logger->debug("Deleted {$deletedWords} existing words and {$deletedFootnotes} footnotes for book {$book->name}");
                }
            }

            // Extract book name from USX
            $dom = new DOMDocument();
            $dom->load($usxFilePath);
            $xpath = new DOMXPath($dom);
            $headerNode = $xpath->query("//para[@style='h']")->item(0);
            if ($headerNode) {
                $bookNameFromUsx = trim($headerNode->textContent);
                $this->logger->debug("Found book name in USX: {$bookNameFromUsx}");
            }

            // Process the book content
            $this->processBookWithXMLReader($usxFilePath, $chapterFilter, $maxRetries, $retryDelay);

            // Ensure any remaining words are processed
            $this->logger->debug("Flushing word buffers at end of parsing...");
            $this->state->flushBuffers();
            $this->logger->debug("Word buffers flushed");

            DB::commit();
            return $this->state->getStats();
        } catch (\Exception $e) {
            DB::rollBack();
            $this->logger->debug("Error during parsing: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Parse a USX string for testing or other purposes
     *
     * @param string $xmlString The USX XML string
     * @param Book $book The book model
     * @return array Statistics about the parsing process
     */
    public function parseString(string $xmlString, Book $book): array
    {
        $this->logger->debug("Starting to parse XML string");
        $this->state->book = $book;

        DB::beginTransaction();
        try {
            // Create a temporary file
            $tempFile = tempnam(sys_get_temp_dir(), 'usx_');
            file_put_contents($tempFile, $xmlString);

            // Process the book content
            $this->processBookWithXMLReader($tempFile);

            // Clean up
            unlink($tempFile);

            // Ensure any remaining words are processed
            $this->logger->debug("Flushing word buffers at end of parsing...");
            $this->state->flushBuffers();
            $this->logger->debug("Word buffers flushed");

            DB::commit();
            return $this->state->getStats();
        } catch (\Exception $e) {
            DB::rollBack();
            $this->logger->debug("Error during parsing: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Process the book content using XMLReader for efficient parsing
     *
     * @param string $usxPath Path to the USX file
     * @param array|int|null $chapterFilter Optional chapter filter
     * @param int $maxRetries Maximum number of retries for error recovery
     * @param int $retryDelay Delay between retries in seconds
     * @return void
     */
    protected function processBookWithXMLReader(
        string $usxPath,
        $chapterFilter = null,
        int $maxRetries = 3,
        int $retryDelay = 1
    ): void {
        $this->logger->debug("Processing book with XMLReader: {$usxPath}");

        $currentXml = new DOMDocument();
        $reader = new XMLReader();
        if (!$reader->open($usxPath)) {
            throw new \Exception("Could not open USX file: {$usxPath}");
        }

        // First scan to find the book name from the header
        $bookName = null;
        $foundBookName = false;
        while ($reader->read() && !$foundBookName) {
            if ($reader->nodeType === XMLReader::ELEMENT && $reader->name === 'para') {
                if ($reader->getAttribute('style') === 'h') {
                    $node = $reader->expand();
                    if ($node) {
                        $bookName = trim($node->textContent);
                        $foundBookName = true;
                    }
                }
            }
        }

        $currentChapterNumber = 0;

        // Reset reader to start of file
        $reader->close();
        $reader->open($usxPath);

        // Convert chapter filter to array for easier checking
        $chapterFilterArray = $chapterFilter ? (array)$chapterFilter : null;
        $currentChapter = null;
        $skipCurrentChapter = false;
        $retryCount = 0;
        $lastPosition = 0;

        // Get the progress callback if available
        $progressCallback = $this->config->getProgressCallback();

        while ($reader->read()) {
            try {
                // Check if we're entering a new chapter
                if ($reader->nodeType === XMLReader::ELEMENT && $reader->name === 'chapter'
                    && $reader->getAttribute('number') && !$reader->getAttribute('eid')) {
                    $chapterNumber = (int)$reader->getAttribute('number');

                    // If we're changing chapters, flush any pending data
                    if ($currentChapter !== null && $currentChapter !== $chapterNumber) {
                        $this->logger->debug("Changing from chapter {$currentChapter} to {$chapterNumber}");
                        $this->state->flushBuffers();
                    }

                    $currentChapter = $chapterNumber;
                    $currentChapterNumber++;

                    // Call progress callback if set
                    if ($progressCallback !== null && is_callable($progressCallback)) {
                        $progressCallback($currentChapterNumber, $this->chapterCount);
                    }

                    // Check if we should skip this chapter based on filter
                    $skipCurrentChapter = $chapterFilterArray && !in_array($chapterNumber, $chapterFilterArray);

                    if ($skipCurrentChapter) {
                        $this->logger->debug("Skipping chapter {$chapterNumber} (not in filter)");
                    } else {
                        $this->logger->debug("Processing chapter {$chapterNumber}");
                    }
                }

                // Skip processing if we're in a filtered-out chapter
                if ($skipCurrentChapter) {
                    continue;
                }

                if ($reader->nodeType === XMLReader::ELEMENT) {
                    $nodeName = $reader->name;
                    $node = $reader->expand();

                    $this->elementHandler->handleElement($nodeName, $node, $reader);
                } elseif ($reader->nodeType === XMLReader::END_ELEMENT) {
                    $this->elementHandler->handleEndElement($reader->name);
                } elseif ($reader->nodeType === XMLReader::TEXT) {
                    // Handle text nodes directly - this is critical for text after footnotes
                    $textNode = $currentXml->createTextNode($reader->value);
                    $this->elementHandler->handleElement('#text', $textNode, $reader);
                    $this->logger->debug("Processed text node: " . substr($reader->value, 0, 30) . (strlen($reader->value) > 30 ? '...' : ''));
                }

                // Reset retry count on successful processing
                $retryCount = 0;
                $lastPosition = $reader->nodeType === XMLReader::ELEMENT ? $reader->depth : $lastPosition;

            } catch (Throwable $e) {
                $retryCount++;
                $this->logger->debug("Error processing node: " . $e->getMessage());
                $this->logger->debug("Retry count: {$retryCount}/{$maxRetries}");

                if ($retryCount <= $maxRetries) {
                    $this->logger->debug("Retrying after {$retryDelay} second(s)...");
                    sleep($retryDelay);
                    continue;
                } else {
                    $this->logger->debug("Max retries exceeded. Giving up.");
                    throw $e;
                }
            }
        }

        $reader->close();
    }
}
