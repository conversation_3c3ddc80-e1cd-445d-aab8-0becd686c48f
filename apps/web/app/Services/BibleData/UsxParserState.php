<?php

namespace App\Services\BibleData;

use App\Models\Book;
use App\Models\Chapter;
use App\Models\Verse;
use App\Models\Footnote;
use App\Models\Word;
use App\Models\ParagraphStyle;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UsxParserState
{
    /**
     * The last fully processed word (persisted or finalized).
     * @var null|\App\Models\Word
     */
    public $lastWord = null;

    /**
     * The word currently being processed.
     * @var null|\App\Models\Word
     */
    public $currentWord = null;

    public ?Book $book = null;
    public ?Chapter $chapter = null;
    public ?Verse $verse = null;
    public ?Footnote $footnote = null;
    public int $position = 0;
    public bool $inVerse = false;
    public bool $inNote = false;
    public bool $justFinishedFootnote = false;
    public bool $justFinishedVariant = false;
    public ?int $lastWordBeforeFootnotePosition = null;
    public ?int $lastWordBeforeVariantPosition = null;
    public ?int $lastPositionBeforeVariant = null;
    public bool $variantStartedInFootnote = false;
    public ?string $pendingPunctuation = null;
    public string $text = '';
    public string $verseText = '';
    public bool $textProcessed = false;
    public ?array $currentStyle = null;

    // Variant-related state
    public ?string $currentVariantGroupId = null;  // For 'va' type variants
    public array $variantGroupIdStack = [];        // Stack for nested 'va' variants

    // Word group related state
    public ?string $currentWordType = null;     // 'va', 'xot', 'add', etc.
    public ?string $currentWordGroupId = null;     // For 'xot' and 'add' types
    public ?string $currentCharacterGroupId = null;

    // OT quote related state (now using word_group_id)
    public ?string $currentOTQuoteGroupId = null;  // Legacy - to be replaced by currentWordGroupId for 'xot'

    public ?int $chapterNumber = null;
    public ?int $verseNumber = null;
    public ?Footnote $pendingFootnote = null;
    public ?ParagraphStyle $currentVerseParagraphStyle = null;
    public ?ParagraphStyle $currentWordParagraphStyle = null;
    public ?int $paragraphStartVerse = null;
    public ?int $paragraphEndVerse = null;
    public bool $inWordLevelParagraph = false;
    public ?string $currentParagraphGroupId = null;
    public bool $isPericopeStart = false;

    // Stats tracking
    public array $stats = [
        'verses_created' => 0,
        'footnotes_created' => 0,
        'words_created' => 0,
        'memory_peak' => 0,
        'batch_flushes' => 0,
        'transaction_count' => 0,
        'retry_count' => 0,
        'skipped_elements' => 0
    ];

    // Batch processing buffers
    public array $wordBuffer = [];
    private int $bufferSize;

    // Memory management
    private int $memoryCheckInterval;
    private int $memoryThreshold;
    private int $operationCount = 0;

    // Configuration
    private UsxParserConfig $config;

    private $logger;

    /** @var array */
    public array $words = [];

    /**
     * Create a new UsxParserState instance
     *
     * @param UsxParserConfig|null $config Optional parser configuration
     */
    public function __construct(UsxParserLogger $logger, ?UsxParserConfig $config = null)
    {
        $this->config = $config ?? new UsxParserConfig();
        $this->bufferSize = $this->config->getBatchSize();
        $this->memoryCheckInterval = $this->config->getMemoryCheckInterval();
        $this->memoryThreshold = $this->config->getMemoryThreshold();
        $this->logger = $logger;
    }

    public function resetForChapter(bool $preserveChapter = false): void
    {
        // Log the current state before resetting
        if ($this->chapter) {
            $this->logger->debug("Resetting state for chapter. Current chapter ID: {$this->chapter->id}, number: {$this->chapterNumber}");
        } else {
            $this->logger->debug("Resetting state for chapter. No current chapter.");
        }

        // Preserve chapter information if requested
        if (!$preserveChapter) {
            $this->chapterNumber = null;
            $this->chapter = null;
        } else {
            $this->logger->debug("Preserving chapter: " . ($this->chapter ? "ID: {$this->chapter->id}, number: {$this->chapterNumber}" : "No chapter to preserve"));
        }

        $this->verse = null;
        $this->verseNumber = null;
        $this->text = '';
        $this->verseText = '';
        $this->position = 0;
        $this->inVerse = false;
        $this->inNote = false;
        $this->textProcessed = false;
        $this->currentStyle = null;

        // Reset all variant and word group related state
        $this->currentVariantGroupId = null;
        $this->currentWordType = null;
        $this->variantGroupIdStack = [];
        $this->currentWordGroupId = null;
        $this->currentCharacterGroupId = null;
        $this->currentOTQuoteGroupId = null;

        $this->pendingFootnote = null;
        $this->currentVerseParagraphStyle = null;
        $this->currentWordParagraphStyle = null;
        $this->currentParagraphGroupId = null;
        $this->paragraphStartVerse = null;
        $this->paragraphEndVerse = null;
        $this->inWordLevelParagraph = false;
        $this->isPericopeStart = false;
        $this->justFinishedFootnote = false;
        $this->justFinishedVariant = false;
        $this->lastWordBeforeFootnotePosition = null;
        $this->lastWordBeforeVariantPosition = null;
        $this->lastPositionBeforeVariant = null;
        $this->variantStartedInFootnote = false;
        $this->pendingPunctuation = null;
        $this->flushBuffers();
        $this->words = [];

        // Force garbage collection
        $this->checkMemoryUsage(true);
    }

    public function resetForVerse(bool $preserveStyles = true): void
    {
        $currentVerseStyle = $preserveStyles ? $this->currentVerseParagraphStyle : null;

        // Ensure we update the current verse's text before resetting
        if ($this->verse && !empty(trim($this->verseText))) {
            // Get the text from the words table to ensure completeness
            $words = $this->verse->words()->orderBy('position')->get();
            $completeText = '';

            foreach ($words as $word) {
                $completeText .= $word->text . ' ';
            }

            $completeText = trim($completeText);

            if (!empty($completeText)) {
                $this->logger->debug("Updating verse text from words: " . substr($completeText, 0, 30) . (strlen($completeText) > 30 ? '...' : ''));
                $this->verse->update([
                    'text' => $completeText,
                    'paragraph_style_id' => $this->currentVerseParagraphStyle?->id
                ]);
            } else {
                // Fallback to using verseText if no words were found
                $this->logger->debug("Updating verse text from verseText: " . substr($this->verseText, 0, 30) . (strlen($this->verseText) > 30 ? '...' : ''));
                $this->verse->update([
                    'text' => trim($this->verseText),
                    'paragraph_style_id' => $this->currentVerseParagraphStyle?->id
                ]);
            }
        }

        $this->verse = null;
        $this->verseNumber = null;
        $this->text = '';
        $this->verseText = '';
        $this->position = 0;
        $this->inVerse = false;
        $this->textProcessed = false;
        $this->currentVerseParagraphStyle = $currentVerseStyle;

        // Keep paragraph_group_id to maintain continuity
        // $this->currentParagraphGroupId = null;

        $this->pendingFootnote = null;

        // DO NOT reset variant-related state when starting a new verse
        // Variants can span across verses and should only be reset when explicitly ended
        $this->logger->debug("Preserving variant state across verse boundary: " .
            ($this->currentVariantGroupId ? "currentVariantGroupId: {$this->currentVariantGroupId}" : "No active variant") . ", " .
            ($this->currentWordGroupId ? "currentWordGroupId: {$this->currentWordGroupId}" : "No active word group"));

        // Only reset character style when changing verses
        $this->currentCharacterGroupId = null;
        $this->isPericopeStart = false; // should only be true for the first verse

        // Reset footnote-related flags when starting a new verse
        $this->inNote = false;
        $this->justFinishedFootnote = false;
        $this->footnote = null;
        $this->lastWordBeforeFootnotePosition = null;

        // Reset variant-related flags but preserve group IDs
        $this->justFinishedVariant = false;
        $this->lastWordBeforeVariantPosition = null;
        $this->lastPositionBeforeVariant = null;
        $this->variantStartedInFootnote = false;
        $this->pendingPunctuation = null;

        $this->flushBuffers();
        $this->words = [];
    }

    public function addToWordBuffer(array $wordData): void
    {
        $this->wordBuffer[] = $wordData;
        $this->operationCount++;

        $bufferCount = count($this->wordBuffer);
        $this->logger->debug("Added word to buffer. Current buffer size: {$bufferCount}/{$this->bufferSize}");

        if ($bufferCount >= $this->bufferSize) {
            $this->logger->debug("Buffer size threshold reached ({$bufferCount}). Flushing word buffer...");
            $this->flushWordBuffer();
        }

        // Check memory usage periodically
        if ($this->operationCount % $this->memoryCheckInterval === 0) {
            $this->checkMemoryUsage();
        }
    }

    public function flushWordBuffer(): void
    {
        $bufferCount = count($this->wordBuffer);
        if (empty($this->wordBuffer)) {
            $this->logger->debug("Word buffer is empty. Nothing to flush.");
            return;
        }

        $this->logger->debug("Flushing word buffer with {$bufferCount} words...");

        // Use a transaction for better performance
        DB::beginTransaction();
        try {
            $createdCount = 0;
            foreach ($this->wordBuffer as $wordData) {
                Word::create($wordData);
                $createdCount++;
            }

            DB::commit();
            $this->stats['words_created'] += $createdCount;
            $this->stats['batch_flushes']++;
            $this->stats['transaction_count']++;

            $this->logger->debug("Successfully flushed word buffer. Created {$createdCount} words.");
        } catch (\Exception $e) {
            DB::rollBack();
            $this->logger->debug("Error flushing word buffer: " . $e->getMessage());
            throw $e;
        }

        $this->wordBuffer = [];
    }

    public function flushBuffers(): void
    {
        $this->flushWordBuffer();
    }

    public function incrementFootnoteCount(): void
    {
        $this->stats['footnotes_created']++;
    }

    public function incrementVerseCount(): void
    {
        $this->stats['verses_created']++;
    }

    public function incrementWordCount(): void
    {
        $this->stats['words_created']++;
    }

    public function incrementRetryCount(): void
    {
        $this->stats['retry_count']++;
    }

    public function incrementSkippedElementsCount(): void
    {
        $this->stats['skipped_elements']++;
    }

    public function getStats(): array
    {
        // Update peak memory usage before returning stats
        $this->stats['memory_peak'] = $this->getPeakMemoryUsage();
        return $this->stats;
    }

    /**
     * Check memory usage and perform garbage collection if needed
     *
     * @param bool $force Force garbage collection regardless of threshold
     * @return void
     */
    private function checkMemoryUsage(bool $force = false): void
    {
        $currentUsage = memory_get_usage(true);
        $peakUsage = memory_get_peak_usage(true);

        // Update peak memory usage stat
        if ($peakUsage > ($this->stats['memory_peak'] ?? 0)) {
            $this->stats['memory_peak'] = $peakUsage;
        }

        // If memory usage is high or forced, perform garbage collection
        if ($force || $currentUsage > $this->memoryThreshold) {
            // Flush any pending buffers
            $this->flushBuffers();

            // Force garbage collection
            if (gc_enabled()) {
                gc_collect_cycles();
            }
        }
    }

    /**
     * Get peak memory usage
     *
     * @return int Peak memory usage in bytes
     */
    public function getPeakMemoryUsage(): int
    {
        return memory_get_peak_usage(true);
    }

    public function startParagraph(ParagraphStyle $style, ?int $verseNumber = null, bool $isWordLevel = false): void
    {
        $this->isPericopeStart = true; // should set the first vers of the paragraph to is_pericope_start
        if ($isWordLevel) {
            $this->currentWordParagraphStyle = $style;
            $this->inWordLevelParagraph = true;
        } else {
            $this->currentVerseParagraphStyle = $style;
            $this->paragraphStartVerse = $verseNumber ?? $this->verse?->number;
        }
    }

    public function endParagraph(bool $isWordLevel = false): void
    {
        if ($isWordLevel) {
            $this->currentWordParagraphStyle = null;
            $this->inWordLevelParagraph = false;
            $this->currentParagraphGroupId = null;
        } else if ($this->currentVerseParagraphStyle && $this->chapter && $this->paragraphStartVerse) {
            // Update all verses in this paragraph range
            $this->paragraphEndVerse = $this->verse?->number ?? $this->paragraphStartVerse;

            // Update all verses in this range
            Verse::where('chapter_id', $this->chapter->id)
                ->whereBetween('number', [$this->paragraphStartVerse, $this->paragraphEndVerse])
                ->update(['paragraph_style_id' => $this->currentVerseParagraphStyle->id]);

            $this->currentVerseParagraphStyle = null;
            $this->paragraphStartVerse = null;
            $this->paragraphEndVerse = null;
            $this->currentParagraphGroupId = null;
        }
    }

    /**
     * Get the parser configuration
     *
     * @return UsxParserConfig The parser configuration
     */
    public function getConfig(): UsxParserConfig
    {
        return $this->config;
    }
}
