<?php

namespace App\Services\BibleData;

class UsxParserConfig
{
    private array $config;

    /**
     * Create a new UsxParserConfig instance
     *
     * @param array $customConfig Optional custom configuration to override defaults
     */
    public function __construct(array $customConfig = [])
    {
        $defaultConfig = [
            'batch_size' => 5000,
            'retry_attempts' => 3,
            'retry_delay' => 1,
            'memory_limit' => '1G',
            'memory_threshold' => 100 * 1024 * 1024, // 100 MB
            'memory_check_interval' => 1000,
            'allowed_styles' => ['p', 'q', 'q2', 'pi'],
            'debug' => false,
            'transaction_size' => 500, // Number of operations per transaction
            'progress_callback' => null, // Progress callback function
            'char_styles' => [
                // Special text
                'add' => ['type' => 'editorial', 'subtype' => 'addition'], // Wort nicht im Grundtext
                'bk' => ['type' => 'reference', 'subtype' => 'book'],
                'dc' => ['type' => 'special', 'subtype' => 'deuterocanonical'],
                'ior' => ['type' => 'reference', 'subtype' => 'published'],
                'iqt' => ['type' => 'quote', 'subtype' => 'introduction'],
                'k' => ['type' => 'keyword', 'subtype' => 'keyword'],
                'nd' => ['type' => 'name', 'subtype' => 'divine'],
                'ord' => ['type' => 'number', 'subtype' => 'ordinal'],
                'pn' => ['type' => 'name', 'subtype' => 'proper'],
                'png' => ['type' => 'name', 'subtype' => 'geographic'],
                'qac' => ['type' => 'special', 'subtype' => 'acrostic'],
                'qs' => ['type' => 'quote', 'subtype' => 'selah'],
                'qt' => ['type' => 'quote', 'subtype' => 'quoted'],
                'rq' => ['type' => 'reference', 'subtype' => 'inline'],
                'sig' => ['type' => 'special', 'subtype' => 'signature'],
                'sls' => ['type' => 'special', 'subtype' => 'selah'],
                'tl' => ['type' => 'foreign', 'subtype' => 'transliterated'],
                'wj' => ['type' => 'quote', 'subtype' => 'words-of-jesus'],

                // Character formatting
                'em' => ['type' => 'format', 'subtype' => 'emphasis'],
                'bd' => ['type' => 'format', 'subtype' => 'bold'],
                'it' => ['type' => 'format', 'subtype' => 'italic'],
                'bdit' => ['type' => 'format', 'subtype' => 'bold-italic'],
                'no' => ['type' => 'format', 'subtype' => 'normal'],
                'sc' => ['type' => 'format', 'subtype' => 'small-caps'],
                'sup' => ['type' => 'format', 'subtype' => 'superscript'],

                // Special features
                'rb' => ['type' => 'ruby', 'subtype' => 'base'],
                'rt' => ['type' => 'ruby', 'subtype' => 'gloss'],
                'cat' => ['type' => 'category', 'subtype' => 'category'],

                // Variants
                'va' => ['type' => 'variant', 'subtype' => 'alternate'], // Text Varianten
                'vp' => ['type' => 'variant', 'subtype' => 'primary'], // Primary variant

                // Notes
                'fm' => ['type' => 'note', 'subtype' => 'footnote-marker'],
                'xm' => ['type' => 'note', 'subtype' => 'cross-reference-marker'],

                // Special text
                'fig' => ['type' => 'figure', 'subtype' => 'figure'],
                'ndx' => ['type' => 'index', 'subtype' => 'subject-index'],
                'pro' => ['type' => 'pronunciation', 'subtype' => 'pronunciation'],
                'w' => ['type' => 'word', 'subtype' => 'wordlist'],
                'wg' => ['type' => 'word', 'subtype' => 'greek'],
                'wh' => ['type' => 'word', 'subtype' => 'hebrew'],
                'wa' => ['type' => 'word', 'subtype' => 'aramaic'],

                // Cross References
                'xt' => ['type' => 'reference', 'subtype' => 'target'],
                'xo' => ['type' => 'reference', 'subtype' => 'origin'],
                'xk' => ['type' => 'reference', 'subtype' => 'keyword'],
                'xq' => ['type' => 'reference', 'subtype' => 'quotation'],
                'xot' => ['type' => 'reference', 'subtype' => 'ot-quote'],
                'xnt' => ['type' => 'reference', 'subtype' => 'nt-quote'],
                'xdc' => ['type' => 'reference', 'subtype' => 'deuterocanonical'],
            ]
        ];

        // Try to load config from the application if available
        if (function_exists('config') && app()->bound('config')) {
            $configFromApp = config('parser.parsing', []);
            $defaultConfig = array_merge($defaultConfig, $configFromApp);
        }

        // Merge custom config with defaults
        $this->config = array_merge($defaultConfig, $customConfig);
    }

    /**
     * Get a configuration value
     *
     * @param string $key The configuration key
     * @param mixed $default The default value if key doesn't exist
     * @return mixed The configuration value
     */
    public function get(string $key, $default = null)
    {
        return $this->config[$key] ?? $default;
    }

    /**
     * Get all character styles
     *
     * @return array The character styles configuration
     */
    public function getCharStyles(): array
    {
        return $this->config['char_styles'];
    }

    /**
     * Get batch size for database operations
     *
     * @return int The batch size
     */
    public function getBatchSize(): int
    {
        return (int)$this->get('batch_size', 500);
    }

    /**
     * Get memory threshold for garbage collection
     *
     * @return int The memory threshold in bytes
     */
    public function getMemoryThreshold(): int
    {
        return (int)$this->get('memory_threshold', 100 * 1024 * 1024);
    }

    /**
     * Get memory check interval
     *
     * @return int The number of operations between memory checks
     */
    public function getMemoryCheckInterval(): int
    {
        return (int)$this->get('memory_check_interval', 1000);
    }

    /**
     * Get maximum retry attempts for error recovery
     *
     * @return int The maximum number of retry attempts
     */
    public function getMaxRetries(): int
    {
        return (int)$this->get('retry_attempts', 3);
    }

    /**
     * Get retry delay in seconds
     *
     * @return int The delay between retry attempts in seconds
     */
    public function getRetryDelay(): int
    {
        return (int)$this->get('retry_delay', 1);
    }

    /**
     * Get transaction size
     *
     * @return int The number of operations per transaction
     */
    public function getTransactionSize(): int
    {
        return (int)$this->get('transaction_size', 100);
    }

    /**
     * Get the progress callback function
     *
     * @return callable|null The progress callback function
     */
    public function getProgressCallback(): ?callable
    {
        return $this->get('progress_callback');
    }

    /**
     * Set the progress callback function
     *
     * @param callable|null $callback The progress callback function
     * @return void
     */
    public function setProgressCallback(?callable $callback): void
    {
        $this->set('progress_callback', $callback);
    }

    /**
     * Set a configuration value
     *
     * @param string $key The configuration key
     * @param mixed $value The configuration value
     * @return void
     */
    public function set(string $key, $value): void
    {
        $this->config[$key] = $value;
    }
}
