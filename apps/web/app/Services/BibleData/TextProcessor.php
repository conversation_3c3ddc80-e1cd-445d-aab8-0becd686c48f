<?php

declare(strict_types=1);

namespace App\Services\BibleData;

use App\Models\Word;
use App\Models\Verse;

class TextProcessor
{
    protected UsxParserState $state;
    protected UsxParserLogger $logger;
    protected ModelFactory $modelFactory;

    // Text processing buffers
    protected string $textBuffer = '';
    protected array $wordBuffer = [];

    public function __construct(
        UsxParserState $state,
        UsxParserLogger $logger,
        ModelFactory $modelFactory
    ) {
        $this->state = $state;
        $this->logger = $logger;
        $this->modelFactory = $modelFactory;
    }

    /**
     * Process accumulated text and create word models
     *
     * @param string $text The text to process
     * @param int $position The starting position for words
     * @return void
     */
    public function processAccumulatedText(string $text, int $position): void
    {
        //$text = $this->state->accumulatedText;
        //$this->state->accumulatedText = '';

        if (empty(trim($text))) {
            $this->logger->debug("Empty text, skipping processing");
            return;
        }

        if (!$this->state->verse) {
            $this->logger->debug("No active verse, skipping text processing");
            return;
        }

        $this->logger->debug("Processing text: " . substr($text, 0, 30) . (strlen($text) > 30 ? '...' : ''));

        // --- Handle punctuation after footnote ---
        // If justFinishedFootnote is true and the text starts with punctuation,
        // extract and attach all leading punctuation to the last word before the footnote
        if (
            $this->state->justFinishedFootnote
            && preg_match('/^([.,;:!?]+)/u', $text, $matches)
        ) {
            $punct = $matches[1];
            $this->logger->debug("Detected leading punctuation '{$punct}' immediately after footnote; will attach to last word before footnote. Wordbuffer: " . json_encode($this->state->wordBuffer));
            $this->state->pendingPunctuation = $punct;
            // Use lastWord from parser state
            $wordToUpdate = $this->state->currentWord;
            if ($wordToUpdate) {
                $this->logger->debug("Attaching punctuation '{$punct}' to word '{$wordToUpdate->text}' at position {$wordToUpdate->position}");
                $textAfter = ($wordToUpdate->text_after ?? '') . $punct;
                // Use updateOrCreate with search and update attributes
                $updatedWord = $wordToUpdate->updateOrCreate(
                    [
                        'verse_id' => $this->state->verse->id,
                        'position' => $wordToUpdate->position
                    ], // Search by position (or another unique field like 'id')
                    [
                        'text' => $wordToUpdate->text,
                        'text_after' => $textAfter,
                        'verse_id' => $this->state->verse->id,
                        'position' => $wordToUpdate->position,
                        'is_ot_quote' => $wordToUpdate->is_ot_quote,
                        'is_addition' => $wordToUpdate->is_addition,
                        'has_variant' => $wordToUpdate->has_variant,
                        'variant_group_id' => $wordToUpdate->variant_group_id,
                        'word_group_id' => $wordToUpdate->word_group_id,
                        'word_type' => $wordToUpdate->word_type,
                    ] // Values to update or create
                );
                //$wordToUpdate->text_after = ($wordToUpdate->text_after ?? '') . $punct;
                //$wordToUpdate->updateOrCreate();
                $this->state->currentWord = $updatedWord;
                $this->logger->debug("Wordbuffer: " . json_encode($this->state->wordBuffer));
            } else {
                $this->logger->warning("No lastWord found in parser state to attach punctuation after footnote");
            }

            // Remove the punctuation from the start of $text
            $text = mb_substr($text, mb_strlen($punct));
            // Reset state flags
            $this->state->justFinishedFootnote = false;
            $this->state->lastWordBeforeFootnotePosition = null;
        }

        // Split text into words, preserving punctuation
        $words = $this->splitTextIntoWords($text);
        $this->logger->debug("Split into " . count($words) . " words");
        $this->logger->debug("State in processAccumulatedText: " . json_encode($this->state));

        // Create word models
        $currentPosition = $position;
        foreach ($words as $wordEntry) {
            if (is_array($wordEntry)) {
                if (empty($wordEntry['text'])) {
                    continue;
                }
                $this->createWord($wordEntry, $currentPosition);
            } else {
                $trimmedWord = trim($wordEntry);
                if (empty($trimmedWord)) {
                    continue;
                }
                $this->createWord($wordEntry, $currentPosition);
            }
            $currentPosition++;
        }

        // Update state position
        $this->state->position = $currentPosition;
    }

    /**
     * Split text into words and punctuation, storing standalone punctuation in text_after.
     *
     * Each returned array entry has:
     *   - 'text': the word itself (empty if standalone punctuation)
     *   - 'text_after': punctuation to be stored in the text_after field (empty if none)
     *
     * @param string $text The text to split
     * @return array<int, array{text: string, text_after: string}>
     */
    protected function splitTextIntoWords(string $text): array
    {
        // Normalize whitespace
        $text = preg_replace('/\s+/', ' ', $text);
        $text = trim($text);
        if ($text === '') {
            return [];
        }

        $words = [];
        $wordGroups = explode(' ', $text);

        $lastWordIndex = -1;

        // Check if we have pending punctuation from a previous call
        $pendingPunctuation = $this->state->pendingPunctuation;
        if ($pendingPunctuation) {
            $this->logger->debug("Found pending punctuation: {$pendingPunctuation}");
            $this->state->pendingPunctuation = null; // Clear it since we're handling it now
            if ($pendingPunctuation) {
                //$this->state->pendingPunctuation = $pendingPunctuation;
                //$this->logger->debug("Stored pending punctuation: {$pendingPunctuation}");
            }

            // Don't return here - continue processing any existing words
            if (empty($wordGroups)) {
                return $words;
            }
        }

        foreach ($wordGroups as $group) {
            $trimmedGroup = trim($group);
            if (empty($trimmedGroup)) {
                continue;
            }

            $this->logger->debug("Processing group: {$trimmedGroup}");

            // Check if this is standalone punctuation
            if (preg_match('/^[.,;:!?]+$/u', $trimmedGroup)) {
                // If we just came out of a footnote, attach to the word before the footnote
                if ($this->state->justFinishedFootnote) {
                    $wordToUpdate = null;

                    if ($this->state->lastWordBeforeFootnotePosition !== null && $this->state->verse) {
                        $wordToUpdate = $this->state->verse->words()
                            ->where('position', $this->state->lastWordBeforeFootnotePosition)
                            ->first();
                    }

                    if ($wordToUpdate) {
                        try {
                            // Check if the word already ends with this punctuation
                            if (substr($wordToUpdate->text, -strlen($trimmedGroup)) === $trimmedGroup) {
                                $this->logger->debug("Word '{$wordToUpdate->text}' already ends with '{$trimmedGroup}', skipping");
                            } else {
                                $this->logger->debug("Attaching post-footnote punctuation '{$trimmedGroup}' to word before footnote: {$wordToUpdate->text}");

                                // Update the word text directly in the database
                                $wordToUpdate->update([
                                    'text' => $wordToUpdate->text . $trimmedGroup
                                ]);
                            }

                            // Reset the justFinishedFootnote flag since we've attached the punctuation
                            $this->state->justFinishedFootnote = false;
                            $this->state->lastWordBeforeFootnotePosition = null;

                            // Clear any pending punctuation
                            $this->state->pendingPunctuation = null;

                            // Skip adding this as a separate word
                            continue;
                        } catch (\Exception $e) {
                            $this->logger->error("Error updating word with punctuation: " . $e->getMessage());
                            // Fall back to normal handling if update fails
                        }
                    }

                    // If we couldn't update the word before the footnote and we have a current word,
                    // attach the colon to that instead
                    if ($lastWordIndex >= 0) {
                        // Check if the word already ends with this punctuation
                        if (substr($words[$lastWordIndex], -strlen($trimmedGroup)) === $trimmedGroup) {
                            $this->logger->debug("Word '{$words[$lastWordIndex]}' already ends with '{$trimmedGroup}', skipping");
                        } else {
                            $words[$lastWordIndex] .= $trimmedGroup;
                            $this->logger->debug("Attaching post-footnote punctuation '{$trimmedGroup}' to current word: {$words[$lastWordIndex]}");
                        }
                        $this->state->pendingPunctuation = null; // Clear any pending punctuation
                    } else {
                        // Store for later attachment
                        $this->state->pendingPunctuation = $trimmedGroup;
                        $this->logger->debug("Storing post-footnote punctuation for later: {$trimmedGroup}");
                    }
                }
                // If we just came out of a variant, attach to the word before/after the variant based on context
                else if ($this->state->justFinishedVariant && $lastWordIndex >= 0) {
                    // Check if the word already ends with this punctuation
                    if (substr($words[$lastWordIndex], -strlen($trimmedGroup)) === $trimmedGroup) {
                        $this->logger->debug("Word '{$words[$lastWordIndex]}' already ends with '{$trimmedGroup}', skipping");
                    } else {
                        $words[$lastWordIndex] .= $trimmedGroup;
                        $this->logger->debug("Attaching post-variant punctuation '{$trimmedGroup}' to word: {$words[$lastWordIndex]}");
                    }

                    $this->state->justFinishedVariant = false;
                    $this->state->pendingPunctuation = null; // Clear any pending punctuation
                }
                // Normal case - attach to the previous word if available
                else if ($lastWordIndex >= 0) {
                    // Check if the word already ends with this punctuation
                    if (substr($words[$lastWordIndex], -strlen($trimmedGroup)) === $trimmedGroup) {
                        $this->logger->debug("Word '{$words[$lastWordIndex]}' already ends with '{$trimmedGroup}', skipping");
                    } else {
                        $words[$lastWordIndex] .= $trimmedGroup;
                        $this->logger->debug("Attaching punctuation '{$trimmedGroup}' to word: {$words[$lastWordIndex]}");
                    }
                    $this->state->pendingPunctuation = null; // Clear any pending punctuation
                }
                // No previous word to attach to
                else {
                    $this->state->pendingPunctuation = $trimmedGroup;
                    $this->logger->debug("No previous word to attach punctuation to, storing for later: {$trimmedGroup}");
                }
                continue;
            }

            // Special handling for colon at the beginning of a group (like ": word")
            if (preg_match('/^:(.+)$/u', $trimmedGroup, $matches)) {
                // If we just came out of a footnote, attach the colon to the word before the footnote
                if ($this->state->justFinishedFootnote) {
                    $wordToUpdate = null;

                    if ($this->state->lastWordBeforeFootnotePosition !== null && $this->state->verse) {
                        $wordToUpdate = $this->state->verse->words()
                            ->where('position', $this->state->lastWordBeforeFootnotePosition)
                            ->first();
                    }

                    if ($wordToUpdate) {
                        try {
                            $this->logger->debug("Attaching colon to word before footnote: {$wordToUpdate->text}");

                            // Update the word text directly in the database
                            $wordToUpdate->update([
                                'text' => $wordToUpdate->text . ':'
                            ]);

                            // Reset the justFinishedFootnote flag
                            $this->state->justFinishedFootnote = false;
                            $this->state->lastWordBeforeFootnotePosition = null;
                        } catch (\Exception $e) {
                            $this->logger->error("Error updating word with colon: " . $e->getMessage());
                            // Fall back to normal handling if update fails
                        }
                    } else if ($lastWordIndex >= 0) {
                        // If we couldn't find the word before the footnote but have a current word,
                        // attach the colon to that instead
                        // Check if the word already ends with a colon
                        if (substr($words[$lastWordIndex], -1) === ':') {
                            $this->logger->debug("Word '{$words[$lastWordIndex]}' already ends with colon, skipping");
                        } else {
                            $words[$lastWordIndex] .= ':';
                            $this->logger->debug("Attaching colon to current word instead: {$words[$lastWordIndex]}");
                        }
                        $this->state->pendingPunctuation = null; // Clear any pending punctuation
                    }
                }
                // If we have a previous word but are not in a post-footnote state,
                // attach the colon to the previous word
                else if ($lastWordIndex >= 0) {
                    // Check if the word already ends with a colon
                    if (substr($words[$lastWordIndex], -1) === ':') {
                        $this->logger->debug("Word '{$words[$lastWordIndex]}' already ends with colon, skipping");
                    } else {
                        $words[$lastWordIndex] .= ':';
                        $this->logger->debug("Attaching colon to previous word: {$words[$lastWordIndex]}");
                    }
                    $this->state->pendingPunctuation = null; // Clear any pending punctuation
                }
                // Otherwise store the colon for later
                else {
                    $this->state->pendingPunctuation = ':';
                    $this->logger->debug("No word to attach colon to, storing for later");
                }

                // Process the rest of the text normally
                $restOfText = $matches[1];
                if (!empty(trim($restOfText))) {
                    if (preg_match('/^(.+?)([.,;:!?]*)$/u', $restOfText, $subMatches)) {
                        $word = $subMatches[1];
                        $punctuation = $subMatches[2];

                        $words[] = $word . $punctuation;
                        $lastWordIndex = count($words) - 1;
                        $this->logger->debug("Added word with punctuation after colon: {$word}{$punctuation}");
                    } else {
                        $words[] = $restOfText;
                        $lastWordIndex = count($words) - 1;
                        $this->logger->debug("Added word after colon: {$restOfText}");
                    }
                }
                continue;
            }

            // Handle normal words with potential punctuation
            if (preg_match('/^(.+?)([.,;:!?]*)$/u', $trimmedGroup, $matches)) {
                $word = $matches[1];
                $punctuation = $matches[2];

                $words[] = $word . $punctuation;
                $lastWordIndex = count($words) - 1;
                $this->logger->debug("Added word with punctuation: {$word}{$punctuation}");
            } else {
                // Add as-is if it doesn't match our patterns
                $words[] = $trimmedGroup;
                $lastWordIndex = count($words) - 1;
                $this->logger->debug("Added word as-is: {$trimmedGroup}");
            }
        }

        // Handle pending punctuation in different scenarios
        if ($this->state->pendingPunctuation) {
            $pendingPunctuation = $this->state->pendingPunctuation;

            // If we just finished a footnote, apply to the word before the footnote
            if ($this->state->justFinishedFootnote) {
                $wordToUpdate = null;

                if ($this->state->lastWordBeforeFootnotePosition !== null && $this->state->verse) {
                    $wordToUpdate = $this->state->verse->words()
                        ->where('position', $this->state->lastWordBeforeFootnotePosition)
                        ->first();
                }

                if ($wordToUpdate) {
                    try {
                        $this->logger->debug("Attaching pending punctuation '{$pendingPunctuation}' to word before footnote: {$wordToUpdate->text}");

                        // Update the word text directly
                        $wordToUpdate->update([
                            'text' => $wordToUpdate->text . $pendingPunctuation
                        ]);

                        // Reset the pending punctuation and footnote flags
                        $this->state->pendingPunctuation = null;
                        $this->state->justFinishedFootnote = false;
                        $this->state->lastWordBeforeFootnotePosition = null;
                    } catch (\Exception $e) {
                        $this->logger->error("Error updating word with punctuation: " . $e->getMessage());
                    }
                }
                // If we couldn't find the word before the footnote, try the last word in the current array
                else if ($lastWordIndex >= 0) {
                    // Check if the word already ends with this punctuation
                    if (substr($words[$lastWordIndex], -strlen($pendingPunctuation)) === $pendingPunctuation) {
                        $this->logger->debug("Word '{$words[$lastWordIndex]}' already ends with '{$pendingPunctuation}', skipping");
                    } else {
                        $words[$lastWordIndex] .= $pendingPunctuation;
                        $this->logger->debug("Attaching pending punctuation '{$pendingPunctuation}' to current word: {$words[$lastWordIndex]}");
                    }
                    $this->state->pendingPunctuation = null;
                }
            }
            // Not in post-footnote state, try to attach to the last word in the current words array
            else if ($lastWordIndex >= 0) {
                // Check if the word already ends with this punctuation
                if (substr($words[$lastWordIndex], -strlen($pendingPunctuation)) === $pendingPunctuation) {
                    $this->logger->debug("Word '{$words[$lastWordIndex]}' already ends with '{$pendingPunctuation}', skipping");
                } else {
                    $words[$lastWordIndex] .= $pendingPunctuation;
                    $this->logger->debug("Attaching stored pending punctuation '{$pendingPunctuation}' to word: {$words[$lastWordIndex]}");
                }
                $this->state->pendingPunctuation = null;
            }
            // Otherwise leave it in the state for later use
        }

        // Reset footnote and variant-related flags after all processing is done
        $this->state->justFinishedFootnote = false;
        $this->state->lastWordBeforeFootnotePosition = null;
        $this->state->justFinishedVariant = false;
        $this->state->lastWordBeforeVariantPosition = null;
        $this->state->pendingPunctuation = null;

        return array_values(array_filter($words, function($word) {
            return !empty(trim($word));
        }));
    }

    /**
     * Create a word model
     *
     * @param string $text The word text
     * @param int $position The position in the verse
     * @param bool $isPunctuation Whether this is punctuation
     * @return Word|null
     */
    protected function createWord(string $text, int $position, bool $isPunctuation = false): ?Word
    {
        if (empty(trim($text))) {
            return null;
        }

        $verseId = $this->state->verse?->id;
        if (!$verseId) {
            $this->logger->error("Cannot create word without a verse ID");
            return null;
        }

        // Get current style attributes
        $variantGroupId = $this->state->currentVariantGroupId;
        $wordGroupId = $this->state->currentWordGroupId;
        $characterGroupId = $this->state->currentCharacterGroupId;
        $wordType = $this->state->currentWordType ?? null;

        $isOTQuote = $wordType === 'xot';
        $isAddition = ($wordType === 'add');
        $hasVariant = !empty($this->state->currentVariantGroupId);

        // If we have pending punctuation from a footnote and this is the first word being created
        // after the footnote, apply it to the previous word
        /*if ($this->state->pendingPunctuation !== null && $this->state->currentWord !== null) {
            $this->state->currentWord->text_after = ($this->state->currentWord->text_after ?? '') . $this->state->pendingPunctuation;
            $this->state->pendingPunctuation = null;
        }*/

        // Log the current state for debugging
        $this->logger->debug("Creating word with current state: " .
            "isOTQuote: " . ($isOTQuote ? 'true' : 'false') . ", " .
            "isAddition: " . ($isAddition ? 'true' : 'false') . ", " .
            "hasVariant: " . ($hasVariant ? 'true' : 'false') . ", " .
            "wordType: " . ($wordType ?? 'null') . ", " .
            "wordGroupId: " . ($wordGroupId ?? 'null') . ", " .
            "variantGroupId: " . ($variantGroupId ?? 'null'));
        $this->logger->debug("Word current style: " . json_encode($this->state->currentStyle));

        // Create the word model
        try {
            $wordData = [
                'verse_id' => $verseId,
                'text' => $text,
                'position' => $position,
                'is_punctuation' => $isPunctuation,
                'character_group_id' => $characterGroupId,

                // Set paragraph_style_id for the word (prefer word-level, fallback to verse-level)
                'paragraph_style_id' => $this->state->currentWordParagraphStyle?->id ?? $this->state->currentVerseParagraphStyle?->id ?? null,

                // Handle OT quotes
                'is_ot_quote' => $isOTQuote,

                // Handle additions
                'is_addition' => $isAddition,

                // Handle variants
                'has_variant' => $hasVariant,
                'variant_group_id' => $hasVariant ? $variantGroupId : null,

                // Set word_group_id for 'xot' and 'add' types
                'word_group_id' => $wordGroupId,

                // Set word_type based on current style
                'word_type' => $wordType,

                // Always set paragraph_group_id for grouping
                'paragraph_group_id' => $this->state->currentParagraphGroupId ?? null,

            ];

            $word = $this->modelFactory->createWord($wordData);

            // Log the created word for debugging
            if ($word) {
                $this->logger->debug("Created word: '{$text}' with ID: {$word->id}, " .
                    "is_ot_quote: " . ($word->is_ot_quote ? 'true' : 'false') . ", " .
                    "is_addition: " . ($word->is_addition ? 'true' : 'false') . ", " .
                    "word_type: " . ($word->word_type ?? 'null') . ", " .
                    "word_group_id: " . ($word->word_group_id ?? 'null'));
            }

            // Maintain lastWord and currentWord in parser state
            $this->state->lastWord = $this->state->currentWord;
            $this->state->currentWord = $word;

            return $word;
        } catch (\Exception $e) {
            $this->logger->error("Error creating word: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Sanitize special characters that might cause issues
     *
     * @param string $text Text to sanitize
     * @return string Sanitized text
     */
    protected function sanitizeSpecialCharacters(string $text): string
    {
        // Handle special characters that might cause issues
        $problematicChars = [
            '[' => '&#91;',  // Left square bracket
            ']' => '&#93;',  // Right square bracket
            '»' => '&#187;', // Right-pointing double angle quotation mark
            '«' => '&#171;', // Left-pointing double angle quotation mark
            '"' => '&#8220;', // Left double quotation mark
            '"' => '&#8221;', // Right double quotation mark
            '—' => '&#8212;', // Em dash
            '–' => '&#8211;', // En dash
        ];

        // Encode problematic characters while preserving their semantic meaning
        /*foreach ($problematicChars as $char => $encoded) {
            if (strpos($text, $char) !== false) {
                $this->logger->debug("Sanitizing special character '{$char}' in text: {$text}");
                $text = str_replace($char, $encoded, $text);
            }
        }*/

        return $text;
    }

    /**
     * Flush any pending words
     *
     * @return void
     */
    public function flushWordBuffer(): void
    {
        if (!empty($this->wordBuffer)) {
            $this->logger->debug("Flushing " . count($this->wordBuffer) . " words from buffer");

            foreach ($this->wordBuffer as $wordData) {
                $this->createWord($wordData['text'], $wordData['position']);
            }

            $this->wordBuffer = [];
        }
    }

    /**
     * Get complete verse text from words
     *
     * @param Verse $verse The verse to get text for
     * @return string The complete verse text
     */
    public function getCompleteVerseText(Verse $verse): string
    {
        $words = $verse->words()->orderBy('position')->get();
        $text = '';

        foreach ($words as $word) {
            $text .= $word->text . ' ';
        }

        return trim($text);
    }
}
