<?php

declare(strict_types=1);

namespace App\Services\BibleData\Handlers;

use App\Services\BibleData\UsxParserState;
use App\Services\BibleData\UsxParserLogger;
use App\Services\BibleData\ModelFactory;
use DOMNode;
use XMLReader;

class ReferenceHandler extends AbstractElementHandler
{
    protected ModelFactory $modelFactory;

    public function __construct(
        UsxParserState $state,
        UsxParserLogger $logger,
        ModelFactory $modelFactory
    ) {
        parent::__construct($state, $logger);
        $this->modelFactory = $modelFactory;
    }

    /**
     * Handle a reference element
     *
     * @param DOMNode $node The DOM node
     * @param XMLReader $reader The XML reader
     * @return void
     */
    public function handle(DOMNode $node, XMLReader $reader): void
    {
        if (!$this->state->verse) {
            $this->logger->debug("Skipping reference - no active verse");
            return;
        }

        // Get reference attributes
        $attributes = $this->getAttributes($node);
        $loc = $attributes['loc'] ?? null;

        if (!$loc) {
            $this->logger->debug("Skipping reference - no location specified");
            return;
        }

        $this->logger->debug("Processing reference to location: {$loc}");

        // Create the reference
        $reference = $this->modelFactory->createReference($node);

        if ($reference) {
            $this->logger->debug("Created reference with ID: {$reference->id}");

            // If we're in a footnote, associate the reference with it
            if ($this->state->footnote) {
                // The content structure is now handled by NoteHandler
                $this->logger->debug("Associated reference with footnote ID: {$this->state->footnote->id}");
            }
        }
    }

    /**
     * Handle the end of a reference element
     *
     * @return void
     */
    public function handleEnd(): void
    {
        // No specific end handling needed for references
    }
}
