<?php

declare(strict_types=1);

namespace App\Services\BibleData\Handlers;

use App\Services\BibleData\UsxParserState;
use App\Services\BibleData\UsxParserLogger;
use App\Services\BibleData\ModelFactory;
use DOMNode;
use XMLReader;

class <PERSON>H<PERSON><PERSON> extends AbstractElementHandler
{
    protected ModelFactory $modelFactory;
    
    public function __construct(
        UsxParserState $state, 
        UsxParserLogger $logger,
        ModelFactory $modelFactory
    ) {
        parent::__construct($state, $logger);
        $this->modelFactory = $modelFactory;
    }
    
    /**
     * Handle a chapter element
     *
     * @param DOMNode $node The DOM node
     * @param XMLReader $reader The XML reader
     * @return void
     */
    public function handle(DOMNode $node, XMLReader $reader): void
    {
        if ($reader->hasAttributes) {
            $chapterNum = (int)$reader->getAttribute('number');
            $this->logger->debug("Processing chapter {$chapterNum}");
            
            // Create or update the chapter
            $chapter = $this->modelFactory->createChapter($chapterNum);
            $this->state->chapterNumber = $chapterNum;
            $this->state->chapter = $chapter;
            
            $this->logger->debug("Created/Updated chapter {$chapterNum} with ID: {$chapter->id}");
            
            // Reset verse-related state
            $this->state->verse = null;
            $this->state->text = '';
            $this->state->verseText = '';
            $this->state->position = 0;
            $this->state->inVerse = false;
            $this->state->inNote = false;
            $this->state->currentStyle = null;
        }
    }
}
