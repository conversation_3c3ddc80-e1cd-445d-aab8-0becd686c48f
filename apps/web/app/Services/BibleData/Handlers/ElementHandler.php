<?php

declare(strict_types=1);

namespace App\Services\BibleData\Handlers;

use App\Models\Verse;
use App\Services\BibleData\Handlers\ChapterHandler;
use App\Services\BibleData\Handlers\VerseHandler;
use App\Services\BibleData\Handlers\ParagraphHandler;
use App\Services\BibleData\Handlers\CharacterHandler;
use App\Services\BibleData\Handlers\NoteHandler;
use App\Services\BibleData\Handlers\ReferenceHandler;
use App\Services\BibleData\Handlers\VariantHandler;
use App\Services\BibleData\UsxParserState;
use App\Services\BibleData\UsxParserLogger;
use App\Services\BibleData\TextProcessor;
use App\Services\BibleData\ModelFactory;
use DOMElement;
use DOMNode;
use XMLReader;

class ElementHandler
{
    protected UsxParserState $state;
    protected UsxParserLogger $logger;
    protected TextProcessor $textProcessor;
    protected ModelFactory $modelFactory;

    // Element handlers using Strategy pattern
    protected array $handlers = [];

    public function __construct(
        UsxParserState $state,
        UsxParserLogger $logger,
        TextProcessor $textProcessor,
        ModelFactory $modelFactory
    ) {
        $this->state = $state;
        $this->logger = $logger;
        $this->textProcessor = $textProcessor;
        $this->modelFactory = $modelFactory;

        // Initialize handlers
        $this->initializeHandlers();
    }

    /**
     * Initialize all element handlers
     */
    protected function initializeHandlers(): void
    {
        $this->handlers = [
            'chapter' => new ChapterHandler($this->state, $this->logger, $this->modelFactory),
            'verse' => new VerseHandler($this->state, $this->logger, $this->modelFactory, $this->textProcessor),
            'para' => new ParagraphHandler($this->state, $this->logger, $this->modelFactory),
            'char' => new CharacterHandler($this->state, $this->logger),
            'note' => new NoteHandler($this->state, $this->logger, $this->modelFactory),
            'ref' => new ReferenceHandler($this->state, $this->logger, $this->modelFactory),
            'ms' => new VariantHandler($this->state, $this->logger)
        ];
    }

    /**
     * Handle an XML element
     *
     * @param string $nodeName The name of the node
     * @param DOMNode|null $node The expanded node
     * @param XMLReader $reader The XML reader
     * @return void
     */
    public function handleElement(string $nodeName, ?DOMNode $node, XMLReader $reader): void
    {
        if (!$node) {
            return;
        }

        // Handle text content
        if ($nodeName === '#text' && $node->nodeType === XML_TEXT_NODE) {
            $text = $node->textContent;
            $trimmedText = trim($text);

            $this->logger->debug("Found text node: '" . substr($text, 0, 30) . (strlen($text) > 30 ? '...' : '') . "'");
            $this->logger->debug("inVerse: " . ($this->state->inVerse ? 'true' : 'false') . ", inNote: " . ($this->state->inNote ? 'true' : 'false'));

            if ($this->state->inVerse && !empty($trimmedText)) {
                $this->logger->debug("Processing text node in verse " . ($this->state->verse ? $this->state->verse->number : 'unknown'));
                $this->logger->debug("Text content: " . substr($trimmedText, 0, 50) . (strlen($trimmedText) > 50 ? '...' : ''));

                // Add to state text
                $this->state->text .= $text;

                // If we're in a note, we'll handle this text differently
                if ($this->state->inNote) {
                    $this->logger->debug("Text is within a footnote, not adding to verse text directly");
                } else {
                    // Always add text to verse text and process it, regardless of textProcessed flag
                    $this->state->verseText .= $text;
                    $this->textProcessor->processAccumulatedText($trimmedText, $this->state->position);
                    $this->logger->debug("Text processed and added to verse text");
                }
            }
            return;
        }

        // Use appropriate handler for the element type
        if (isset($this->handlers[$nodeName])) {
            $this->handlers[$nodeName]->handle($node, $reader);

            // Reset textProcessed flag after handling a note element to ensure text after footnotes is processed
            if ($nodeName === 'note') {
                $this->state->textProcessed = false;
                $this->logger->debug("Text processed flag reset after note element");
            }

            // Reset justFinishedFootnote flag if we're starting a new verse
            if ($nodeName === 'verse') {
                $this->state->justFinishedFootnote = false;
                $this->state->footnote = null;
                $this->logger->debug("Reset footnote and justFinishedFootnote flags for new verse");
            }
        } else {
            $this->logger->debug("No handler for element type: {$nodeName}");
        }
    }

    /**
     * Handle an XML end element
     *
     * @param string $nodeName The name of the node
     * @return void
     */
    public function handleEndElement(string $nodeName): void
    {
        switch ($nodeName) {
            case 'char':
                $this->state->currentStyle = null;
                $this->state->textProcessed = false;
                $this->logger->debug("Text processed flag reset at end of char");
                break;

            case 'note':
                $this->state->inNote = false;
                $this->state->textProcessed = false; // Reset flag to ensure text after footnotes is processed
                $this->logger->debug("Text processed flag reset at end of note");
                break;

            case 'verse':
                // Update verse text when we reach the end of verse
                if ($this->state->verse) {
                    // Get the text from the words table to ensure completeness
                    $words = $this->state->verse->words()->orderBy('position')->get();
                    $completeText = '';

                    foreach ($words as $word) {
                        $completeText .= $word->text . ' ';
                    }

                    $completeText = trim($completeText);

                    if (!empty($completeText)) {
                        $this->logger->debug("Updating verse text from words: " . substr($completeText, 0, 30) . (strlen($completeText) > 30 ? '...' : ''));
                        $this->state->verse->update(['text' => $completeText]);
                    } else {
                        // Fallback to using verseText if no words were found
                        $this->logger->debug("Updating verse text from verseText: " . substr($this->state->verseText, 0, 30) . (strlen($this->state->verseText) > 30 ? '...' : ''));
                        $this->state->verse->update(['text' => trim($this->state->verseText)]);
                    }
                }
                $this->state->inVerse = false;
                $this->state->textProcessed = false; // Reset the text processed flag
                $this->state->footnote = null; // Reset the footnote property
                $this->state->justFinishedFootnote = false; // Reset the justFinishedFootnote flag
                $this->logger->debug("Reset footnote and justFinishedFootnote flags at end of verse");
                $this->logger->debug("Text processed flag reset in ElementHandler at end of verse");
                break;

            case 'para':
                // Check if we're ending a word-level paragraph
                $this->state->currentParagraphGroupId = null;
                if ($this->state->currentWordParagraphStyle !== null) {
                    $this->state->currentWordParagraphStyle = null;
                } else {
                    $this->state->currentVerseParagraphStyle = null;
                }
                // Reset text processed flag to ensure text after paragraphs is processed
                $this->state->textProcessed = false;
                $this->logger->debug("Text processed flag reset at end of para");
                break;
        }

        // Use appropriate handler for the element type if it exists
        if (isset($this->handlers[$nodeName])) {
            $this->handlers[$nodeName]->handleEnd();
        }
    }
}
