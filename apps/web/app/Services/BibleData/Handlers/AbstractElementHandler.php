<?php

declare(strict_types=1);

namespace App\Services\BibleData\Handlers;

use App\Services\BibleData\UsxParserState;
use App\Services\BibleData\UsxParserLogger;
use DOMNode;
use XMLReader;

abstract class AbstractElementHandler implements ElementHandlerInterface
{
    protected UsxParserState $state;
    protected UsxParserLogger $logger;
    
    public function __construct(UsxParserState $state, UsxParserLogger $logger)
    {
        $this->state = $state;
        $this->logger = $logger;
    }
    
    /**
     * Get attributes from a node
     *
     * @param DOMNode $node The DOM node
     * @return array The node attributes
     */
    protected function getAttributes(DOMNode $node): array
    {
        $attributes = [];
        
        if ($node->hasAttributes()) {
            foreach ($node->attributes as $attribute) {
                $attributes[$attribute->name] = $attribute->value;
            }
        }
        
        return $attributes;
    }
    
    /**
     * Handle the end of an XML element (default implementation)
     *
     * @return void
     */
    public function handleEnd(): void
    {
        // Default implementation does nothing
    }
}
