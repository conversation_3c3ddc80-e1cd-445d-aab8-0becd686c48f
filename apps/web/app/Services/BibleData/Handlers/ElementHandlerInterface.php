<?php

declare(strict_types=1);

namespace App\Services\BibleData\Handlers;

use DOMNode;
use XMLReader;

interface ElementHandlerInterface
{
    /**
     * Handle an XML element
     *
     * @param DOMNode $node The DOM node
     * @param XMLReader $reader The XML reader
     * @return void
     */
    public function handle(DOMNode $node, XMLReader $reader): void;
    
    /**
     * Handle the end of an XML element
     *
     * @return void
     */
    public function handleEnd(): void;
}
