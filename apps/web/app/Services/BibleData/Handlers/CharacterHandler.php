<?php

declare(strict_types=1);

namespace App\Services\BibleData\Handlers;

use App\Services\BibleData\UsxParserState;
use App\Services\BibleData\UsxParserLogger;
use DOMNode;
use XMLReader;

class CharacterHandler extends AbstractElementHandler
{
    /**
     * Handle a character style element
     *
     * @param DOMNode $node The DOM node
     * @param XMLReader $reader The XML reader
     * @return void
     */
    public function handle(DOMNode $node, XMLReader $reader): void
    {
        $attributes = $this->getAttributes($node);
        $styleCode = $attributes['style'] ?? null;

        if (!$styleCode) {
            return;
        }

        $styleAttributes = [
            'style_type' => $styleCode,
            'is_ot_quote' => false,
            'is_addition' => false,
            'is_emphasis' => false,
            'is_devine_name' => false,
            'has_variant' => false,
            'word_type' => null,
            'word_group_id' => null,
            'variant_group_id' => null
        ];

        // Map style codes to attributes
        switch ($styleCode) {
            case 'it':
                $styleAttributes['is_emphasis'] = true;
                $this->state->currentWordType = NULL;
                break;
            case 'add':
                $styleAttributes['is_addition'] = true;
                $this->state->currentWordType = 'add';
                // Generate a unique word group ID if not already set
                if (!$this->state->currentWordGroupId) {
                    // Generate a shorter, more efficient ID for additions
                    $randomPart = substr(md5(microtime()), 0, 4);
                    $this->state->currentWordGroupId = sprintf('add_%s_%s_%s',
                        $this->state->book->order ?? 'x',
                        $this->state->chapterNumber ?? '0',
                        $randomPart
                    );
                }
                $styleAttributes['word_group_id'] = $this->state->currentWordGroupId;
                $this->logger->debug("Set addition word group ID: {$this->state->currentWordGroupId}");
                break;
            case 'fr':
                // Footnote reference, handled separately
                break;
            case 'em':
                $styleAttributes['is_emphasized'] = true;
                $this->state->currentWordType = NULL;
                break;
            case 'xot':
                $styleAttributes['is_ot_quote'] = true;
                $this->state->currentWordType = 'xot';
                // Generate a unique word group ID if not already set
                if (!$this->state->currentWordGroupId) {
                    $randomPart = substr(md5(microtime()), 0, 4);
                    $this->state->currentWordGroupId = sprintf('otq_%s',
                        $randomPart
                    );
                }
                $styleAttributes['word_group_id'] = $this->state->currentWordGroupId;
                $this->logger->debug("Set OT quote word group ID: {$this->state->currentWordGroupId}");
                break;
        }

        // Update the state with the new style attributes
        $this->state->currentStyle = array_merge($this->state->currentStyle ?? [], $styleAttributes);

        if ($styleAttributes['word_group_id']) {
            // Don't overwrite existing word group ID for nested styles
            if (!$this->state->currentWordGroupId) {
                $this->state->currentWordGroupId = $styleAttributes['word_group_id'];
                $this->logger->debug("Set current word group ID to: {$styleAttributes['word_group_id']}");
            }
        }

        // Generate a unique character group ID for this character style
        $randomPart = substr(md5(microtime()), 0, 4);
        $this->state->currentCharacterGroupId = sprintf('chr_%s_%s_%s',
            $this->state->book->order ?? 'x',
            $this->state->chapterNumber ?? '0',
            $randomPart
        );
        $this->logger->debug("Generated character group ID: {$this->state->currentCharacterGroupId} for style: {$styleCode}");

        // Reset text processing flag to ensure text within character styles is processed
        $this->state->textProcessed = false;
        $this->logger->debug("Reset textProcessed flag for character style start");
    }

    /**
     * Handle the end of a character style element
     *
     * @return void
     */
    public function handleEnd(): void
    {
        // Get the current type before clearing it
        $currentWordType = $this->state->currentWordType ?? null;

        $this->logger->debug("Ending character type: " . ($currentWordType ?? 'unknown'));

        // Check if we're ending an OT quote
        if ($currentWordType === 'xot') {
            $this->logger->debug("Ending OT quote with word group ID: {$this->state->currentWordGroupId}");
            $this->state->currentWordGroupId = null;
            $this->state->currentWordType = null;
        }

        // Check if we're ending an addition
        if ($currentWordType === 'add') {
            $this->logger->debug("Ending addition with word group ID: {$this->state->currentWordGroupId}");
            $this->state->currentWordGroupId = null;
            $this->state->currentWordType = null;
        }

        // Clear the character group ID
        $this->logger->debug("Clearing character group ID: {$this->state->currentCharacterGroupId}");
        $this->state->currentCharacterGroupId = null;

        // Clear the current style
        $this->state->currentStyle = null;

        // Clear the variant group ID if it was set by this character style
        if ($this->state->currentVariantGroupId !== NULL) {
            $this->state->currentVariantGroupId = null;
            $this->logger->debug("Cleared variant group ID at end of character style");
        }

        // Reset text processing flag to ensure text after character styles is processed
        $this->state->textProcessed = false;
        $this->logger->debug("Reset textProcessed flag for character style end");
    }
}
