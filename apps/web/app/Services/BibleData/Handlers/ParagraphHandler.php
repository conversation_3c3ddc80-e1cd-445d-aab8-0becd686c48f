<?php

declare(strict_types=1);

namespace App\Services\BibleData\Handlers;

use App\Services\BibleData\UsxParserState;
use App\Services\BibleData\UsxParserLogger;
use App\Services\BibleData\ModelFactory;
use DOMNode;
use XMLReader;

class ParagraphHandler extends AbstractElementHandler
{
    protected ModelFactory $modelFactory;

    public function __construct(
        UsxParserState $state,
        UsxParserLogger $logger,
        ModelFactory $modelFactory
    ) {
        parent::__construct($state, $logger);
        $this->modelFactory = $modelFactory;
    }

    /**
     * Handle a paragraph element
     *
     * @param DOMNode $node The DOM node
     * @param XMLReader $reader The XML reader
     * @return void
     */
    public function handle(DOMNode $node, XMLReader $reader): void
    {
        // Create or reuse paragraph group ID based on style (prose vs poetry)
        $paragraphStyle = $this->modelFactory->createParagraphStyle($node);
        if ($paragraphStyle) {
            $this->logger->debug("Processing paragraph style: {$paragraphStyle->style_code}");

            // List of poetic paragraph styles
            $poeticStyles = ['q', 'q1', 'q2', 'q3', 'q4', 'q5', 'q6'];
            if (in_array($paragraphStyle->style_code, $poeticStyles, true)) {
                // Poetry: generate a new group ID for each poetic line
                $this->state->currentParagraphGroupId = $this->modelFactory->generateParagraphGroupId();
                $verseNum = $this->state->verse ? $this->state->verse->number : 'null';
                $this->logger->debug("Generated new poetic paragraph group ID: {$this->state->currentParagraphGroupId} for verse {$verseNum}");
            } else {
                // Prose: use the paragraph_group_id from the verse, so all words in the verse share the same group id
                if ($this->state->verse && $this->state->verse->paragraph_group_id) {
                    $this->state->currentParagraphGroupId = $this->state->verse->paragraph_group_id;
                    $this->logger->debug("Reusing prose paragraph group ID from verse: {$this->state->currentParagraphGroupId}");
                } else {
                    // Fallback: generate a new group id if verse is not set yet
                    $this->state->currentParagraphGroupId = $this->modelFactory->generateParagraphGroupId();
                    $this->logger->debug("Generated new prose paragraph group ID at paragraph start (no verse): {$this->state->currentParagraphGroupId}");
                }
            } // end prose/poetry differentiation
            $this->state->isPericopeStart = true;
            $this->state->currentVerseParagraphStyle = $paragraphStyle;

            // Update any existing verse with the new style and group ID
            // Also mark it as pericope start since it's the first verse in the paragraph
            if (!$this->state->verse) {
                // No verse set yet: store pending paragraph style and group id in state, to be applied when verse is created
                $this->state->pendingParagraphStyle = $paragraphStyle;
                $this->state->pendingParagraphGroupId = $this->state->currentParagraphGroupId;
                $this->logger->debug(
                    "ParagraphHandler: No verse set, storing pending paragraph style/group id for later assignment. " .
                    "Paragraph style: {$paragraphStyle->style_code}, " .
                    "Current chapter: " . ($this->state->chapterNumber ?? 'null')
                );
            }
            if ($this->state->verse) {
                $this->logger->debug("Updating verse with paragraph style and setting is_pericope_start=true");
                $this->logger->debug("Setting is_pericope_start in para to: " . ($this->state->isPericopeStart ? 'true' : 'false'));

                $this->state->verse->update([
                    'paragraph_style_id' => $paragraphStyle->id,
                    'paragraph_group_id' => $this->state->currentParagraphGroupId,
                    'is_pericope_start' => $this->state->isPericopeStart
                ]);

                // Verify the value was set correctly
                $this->state->verse->refresh();
            }
        }
    }

    /**
     * Handle the end of a paragraph element
     *
     * @return void
     */
    public function handleEnd(): void
    {
        // Check if we're ending a word-level paragraph
        $this->state->currentParagraphGroupId = null;
        if ($this->state->currentWordParagraphStyle !== null) {
            $this->state->currentWordParagraphStyle = null;
        } else {
            $this->state->currentVerseParagraphStyle = null;
        }
    }
}
