<?php

declare(strict_types=1);

namespace App\Services\BibleData\Handlers;

use App\Services\BibleData\UsxParserState;
use App\Services\BibleData\UsxParserLogger;
use App\Services\BibleData\ModelFactory;
use DOMNode;
use XMLReader;

class <PERSON>Handler extends AbstractElementHandler
{
    protected ModelFactory $modelFactory;

    public function __construct(
        UsxParserState $state,
        UsxParserLogger $logger,
        ModelFactory $modelFactory
    ) {
        parent::__construct($state, $logger);
        $this->modelFactory = $modelFactory;
    }

    /**
     * Handle a note element
     *
     * @param DOMNode $node The DOM node
     * @param XMLReader $reader The XML reader
     * @return void
     */
    public function handle(DOMNode $node, XMLReader $reader): void
    {
        if (!$this->state->verse) {
            $this->logger->debug("Skipping note - no active verse");
            return;
        }

        $this->state->inNote = true;

        // Get note attributes
        $attributes = $this->getAttributes($node);
        $noteType = $attributes['style'] ?? 'f';  // Default to footnote

        $this->logger->debug("Processing note of type: {$noteType}");

        // Store the current position before the footnote
        $positionBeforeNote = $this->state->position;
        $this->logger->debug("Position before note: {$positionBeforeNote}");

        // Store the last word before the footnote for later reference
        // This will help with attaching punctuation after the footnote
        if (!empty($this->state->words)) {
            $lastWord = end($this->state->words);
            if ($lastWord) {
                $this->logger->debug("Last word before note: {$lastWord->text} at position {$lastWord->position}");
                // Store the position in the state for later use
                $this->state->lastWordBeforeFootnotePosition = $lastWord->position;
            }
        }

        // Create the footnote at the current position
        $footnote = $this->modelFactory->createFootnote($node, $this->state->position);

        if ($footnote) {
            $this->state->footnote = $footnote;
            $this->logger->debug("Created footnote with ID: {$footnote->id}");

            // Process any references within the note
            $contentStructure = [
                'elements' => [],
                'metadata' => [
                    'has_references' => false,
                    'total_elements' => 0
                ]
            ];

            $searchableText = '';
            $content = '';

            // Process all child nodes to build content structure
            $currentTextContent = '';
            foreach ($node->childNodes as $childNode) {
                if ($childNode->nodeType === XML_TEXT_NODE) {
                    $currentTextContent .= $childNode->nodeValue;
                } elseif ($childNode->nodeName === 'char') {
                    // If we have accumulated text, add it as an element
                    if ($currentTextContent !== '') {
                        $contentStructure['elements'][] = ['type' => 'text', 'content' => $currentTextContent];
                        $searchableText .= $currentTextContent;
                        $content .= $currentTextContent;
                        $currentTextContent = '';
                    }

                    $style = $childNode->getAttribute('style');
                    $charContent = $childNode->nodeValue;

                    if ($style === 'fr') {
                        // Add the caller element
                        $contentStructure['elements'][] = ['type' => 'caller', 'style' => 'fr', 'content' => $charContent];
                    } else {
                        // Handle other char styles like 'it'
                        $contentStructure['elements'][] = ['type' => 'char', 'style' => $style, 'content' => $charContent];
                        $searchableText .= $charContent;
                        $content .= $charContent;

                        if ($style === 'it') {
                            $footnote->has_italics = true;
                        }
                    }
                } elseif ($childNode->nodeName === 'ref') {
                    // If we have accumulated text, add it as an element
                    if ($currentTextContent !== '') {
                        $contentStructure['elements'][] = ['type' => 'text', 'content' => $currentTextContent];
                        $searchableText .= $currentTextContent;
                        $content .= $currentTextContent;
                        $currentTextContent = '';
                    }

                    // Create reference and add to content structure
                    $reference = $this->modelFactory->createReference($childNode);
                    if ($reference) {
                        // Get the actual reference value
                        $refContent = $childNode->nodeValue;

                        // Check if this is a same-book reference (no book name/abbreviation)
                        if (!preg_match('/^[A-Za-z]+\s+\d/', $refContent)) {
                            // Check if the reference starts with a preposition
                            if (preg_match('/^(in|zu|bei|von|an)\s+(.+)$/i', $refContent, $matches)) {
                                // Add the preposition to the text content
                                $contentStructure['elements'][] = ['type' => 'text', 'content' => $matches[1] . ' '];
                                $searchableText .= ' ' . $matches[1] . ' ';
                                $content .= ' ' . $matches[1] . ' ';

                                // Use only the actual reference part
                                $refContent = $matches[2];
                            }

                            // Add the reference without the preposition
                            $contentStructure['elements'][] = ['type' => 'ref', 'content' => $refContent];
                        } else {
                            // This is a cross-reference with book name, use as is
                            $contentStructure['elements'][] = ['type' => 'ref', 'content' => $refContent];
                        }

                        $contentStructure['metadata']['has_references'] = true;
                        $searchableText .= ' ' . $refContent;
                        $content .= ' ' . $refContent;
                    }
                }
            }

            // Add any remaining text content
            if ($currentTextContent !== '') {
                $contentStructure['elements'][] = ['type' => 'text', 'content' => $currentTextContent];
                $searchableText .= $currentTextContent;
                $content .= $currentTextContent;
            }

            $contentStructure['metadata']['total_elements'] = count($contentStructure['elements']);

            // Update the footnote with the complete content structure
            if ($footnote) {
                $footnote->searchable_text = trim($searchableText);
                $footnote->content = trim($content);
                $footnote->content_structure = $contentStructure;
                $footnote->save();
            }
        }

        // Mark that we need to process text after this note
        $this->state->textProcessed = false;
        $this->logger->debug("Text processed flag reset at start of note");
    }

    /**
     * Handle the end of a note element
     *
     * @return void
     */
    public function handleEnd(): void
    {
        $this->logger->debug("Handling end of note");

        // Set a flag to indicate we just finished processing a footnote
        // This will be used by the TextProcessor to handle punctuation correctly
        $this->state->justFinishedFootnote = true;
        $this->logger->debug("Set justFinishedFootnote flag to true");
        $this->logger->debug("State in handleEnd: " . json_encode($this->state));

        // Store the last word position for attaching punctuation
        $lastWord = null;
        if (!empty($this->state->words)) {
            $lastWord = end($this->state->words);
        }
        if (!$lastWord && $this->state->verse) {
            // Fallback: get last word from DB
            $lastWord = $this->state->verse->words()->orderByDesc('position')->first();
        }
        if ($lastWord) {
            $this->logger->debug("Last word before footnote: {$lastWord->text} at position {$lastWord->position}");
            $this->state->lastWordBeforeFootnotePosition = $lastWord->position;
        } else {
            $this->logger->debug("No last word found before footnote for verse {$this->state->verse->id}; punctuation after footnote may not be attached.");
        }

        // Reset note state
        $this->state->inNote = false;

        // Don't reset the footnote property yet - we need it to track that we're processing text after a footnote
        // It will be reset when we start processing the next verse or when we encounter another footnote

        // Reset text processing flag to ensure text after footnotes is processed
        $this->state->textProcessed = false;
        $this->logger->debug("Reset inNote flag to false and textProcessed flag to false");
    }
}
