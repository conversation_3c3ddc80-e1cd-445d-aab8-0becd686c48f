<?php

declare(strict_types=1);

namespace App\Services\BibleData\Handlers;

use App\Services\BibleData\UsxParserState;
use App\Services\BibleData\UsxParserLogger;
use DOMNode;
use XMLReader;

class VariantHandler extends AbstractElementHandler
{
    /**
     * Handle a milestone element (used for variants)
     *
     * @param DOMNode $node The milestone element
     * @param XMLReader $reader The XML reader
     * @return void
     */
    public function handle(DOMNode $node, XMLReader $reader = null): void
    {
        $attributes = $this->getAttributes($node);
        //$style = $attributes['style'] ?? null;
        $sID = $attributes['sid'] ?? null;
        $eID = $attributes['eid'] ?? null;
        $type = $attributes['type'] ?? null;

        $this->logger->debug("Handling milestone with type: {$type}, sID: {$sID}, eID: {$eID}");

        // Handle variant start - check both style and type for compatibility with different USX formats
        if (($type === 'va' || $type === 'xot') && $sID) {
            $this->handleVariantStart($sID, $type);
        }

        // Handle variant end
        if ($eID) {
            $this->handleVariantEnd($eID);
        }
    }

    /**
     * Handle the start of a variant
     *
     * @param string $sID The variant start ID
     * @param string|null $type The variant type (e.g., 'va', 'addition', etc.)
     * @return void
     */
    protected function handleVariantStart(string $sID, ?string $type = null): void
    {
        $this->logger->debug("Handling variant start with sID: {$sID}, type: " . ($type ?? 'null'));

        // Save the current position before variant starts
        $this->state->lastPositionBeforeVariant = $this->state->position;
        $this->logger->debug("Saved position before variant: {$this->state->position}");

        // Generate appropriate group IDs based on type
        if ($type === 'va') {
            // For text variants (va), use variant_group_id
            $variantGroupId = 'v_' . substr(md5($sID . ($this->state->verse ? $this->state->verse->id : 'no_verse')), 0, 4);
            $this->logger->debug("Generated variant group ID: {$variantGroupId}");

            // If we already have a variant group ID, push it onto the stack
            if ($this->state->currentVariantGroupId) {
                if (!isset($this->state->variantGroupIdStack)) {
                    $this->state->variantGroupIdStack = [];
                }

                array_push($this->state->variantGroupIdStack, $this->state->currentVariantGroupId);
                $this->logger->debug("Pushed current variant group ID onto stack: {$this->state->currentVariantGroupId}");
            }

            // Set the current variant group ID to the new one
            $this->state->currentVariantGroupId = $variantGroupId;
            //$this->state->currentWordGroupId = 'variant';
            $this->logger->debug("Set current variant group ID to: {$variantGroupId}");
        }
        else if ($type === 'xot') {
            // For OT quotes (xot), use word_group_id
            $randomPart = substr(md5($sID), 0, 4);
            $wordGroupId = sprintf('otq_%s', $randomPart);
            $this->state->currentWordGroupId = $wordGroupId;
            $this->logger->debug("Generated OT quote word group ID: {$wordGroupId}");

            // Mark the verse as having an OT quote
            if ($this->state->verse) {
                $this->state->verse->has_ot_quote = true;
                $this->state->verse->save(['has_ot_quote']);
                $this->logger->debug("Marked verse {$this->state->verse->number} as having an OT quote");
            }

            // Store the variant type in the state
            $this->state->currentWordType = $type;
            $this->logger->debug("Set current word type to: {$this->state->currentWordType}");
        }

        // If we have a verse and this is a text variant (va), mark it as having a variant
        if ($this->state->verse && $type === 'va') {
            $this->logger->debug("Marking verse {$this->state->verse->number} as having a variant");

            // Update the verse to indicate it has a variant
            $this->state->verse->has_text_variant = true;
            $this->state->verse->save(['has_text_variant']);
            //$this->state->currentWordGroupId = 'variant';

            $this->logger->debug("Updated verse with has_text_variant = true");
        }

        // Reset text processing flag to ensure text in variants is processed
        $this->state->textProcessed = false;
        $this->logger->debug("Reset textProcessed flag to false for variant start");
    }

    /**
     * Handle the end of a variant
     *
     * @param string $eID The variant end ID
     * @return void
     */
    protected function handleVariantEnd(string $eID): void
    {
        $this->logger->debug("Handling variant end with eID: {$eID}");

        // Set a flag to indicate we just finished processing a variant
        // This will be used by the TextProcessor to handle punctuation correctly
        $this->state->justFinishedVariant = true;
        $this->logger->debug("Set justFinishedVariant flag to true");

        $wordType = $this->state->currentWordType;

        // Handle specific variant type cleanup
        if ($wordType === 'xot') {
            $this->logger->debug("Ending OT quote with word group ID: {$this->state->currentWordGroupId}");
            $this->state->currentWordGroupId = null;
        }

        $this->state->currentWordType = null;

        // Only handle variant group stack for text variants (va)
        if ($wordType === 'va') {
            // If we have a stack of variant group IDs, pop the last one
            if (isset($this->state->variantGroupIdStack) && !empty($this->state->variantGroupIdStack)) {
                $this->state->currentVariantGroupId = array_pop($this->state->variantGroupIdStack);
                $this->logger->debug("Popped variant group ID from stack: {$this->state->currentVariantGroupId}");
            } else {
                // If there's no stack, we're at the end of all variants
                $this->state->currentVariantGroupId = null;
                $this->logger->debug("No more variant group IDs in stack, setting to null");
            }
        }

        // Check if there's pending punctuation that needs to be attached to the last word
        if ($this->state->pendingPunctuation) {
            $this->logger->debug("Found pending punctuation after variant: {$this->state->pendingPunctuation}");

            // Try to find the last word to attach the punctuation to
            $wordToUpdate = null;

            if (!empty($this->state->words)) {
                $lastWord = end($this->state->words);
                if ($lastWord) {
                    $wordToUpdate = $lastWord;
                    $this->logger->debug("Found last word to attach punctuation to: {$lastWord->text}");
                }
            }

            if ($wordToUpdate) {
                try {
                    $this->logger->debug("Attaching post-variant punctuation '{$this->state->pendingPunctuation}' to word: {$wordToUpdate->text}");

                    // Update the word text directly in the database
                    $wordToUpdate->update([
                        'text' => $wordToUpdate->text . $this->state->pendingPunctuation
                    ]);

                    // Reset the pending punctuation since we've used it
                    $this->state->pendingPunctuation = null;
                } catch (\Exception $e) {
                    $this->logger->error("Error updating word with punctuation: " . $e->getMessage());
                }
            }
        }

        // Reset text processing flag to ensure text after variants is processed
        $this->state->textProcessed = false;
        $this->logger->debug("Reset textProcessed flag to false for variant end");
    }

    /**
     * Handle the end of a milestone element
     *
     * @return void
     */
    public function handleEnd(): void
    {
        // No specific end handling needed for milestone elements
    }
}
