<?php

namespace App\Services;

use App\Models\Book;
use App\Enums\Testament;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class BookOrganizationService
{
    private const CACHE_KEY = 'organized_books';
    private const CACHE_TTL = 60 * 60 * 24; // 24 hours

    public function getOrganizedBooks(): array
    {
        return Cache::remember(self::CACHE_KEY, self::CACHE_TTL, function () {
            return $this->organizeBooks();
        });
    }

    private function organizeBooks(): array
    {
        $books = Book::with(['chapters' => function ($query) {
            $query->select('id', 'book_id', 'number')
                ->withCount('verses')
                ->orderBy('number');
        }])
        ->select('id', 'name', 'testament', 'category', 'order', 'abbreviation', 'chapters_count', 'slug')
        ->orderBy('order')
        ->get();

        Log::info('Retrieved books:', ['count' => $books->count()]);

        $sections = [
            'old_testament' => [],
            'new_testament' => []
        ];

        $availableBooks = [];

        foreach ($books as $book) {
            // Check if book has any chapters with verses
            $hasVerses = $book->chapters->contains(function ($chapter) {
                return $chapter->verses_count > 0;
            });

            // Convert book data to array format
            $bookData = [
                'id' => $book->id,
                'name' => $book->name,
                'abbreviation' => $book->abbreviation,
                'chapterCount' => $book->chapters_count,
                'chapters' => $book->chapters->map(function ($chapter) {
                    return [
                        'number' => $chapter->number,
                        'verseCount' => $chapter->verses_count
                    ];
                })->toArray(),
                'testament' => $book->testament->value,
                'category' => $book->category->value,
                'order' => $book->order,
                'slug' => $book->slug,
                'hasContent' => $hasVerses
            ];

            // Check special sections first
            if ($book->testament === Testament::OT) {
                $sections['old_testament'][] = $bookData;
            } elseif ($book->testament === Testament::NT) {
                $sections['new_testament'][] = $bookData;
            }

            // Add to available books if it has verses
            if ($hasVerses) {
                $availableBooks[] = [
                    'slug' => $book->slug,
                    'order' => $book->order,
                ];
            }
        }

        // Sort books within each section by their order
        foreach ($sections as &$sectionBooks) {
            usort($sectionBooks, function ($a, $b) {
                return $a['order'] - $b['order'];
            });
        }

        // Log::info('Organized sections:', ['sections' => $sections]);

        return [
            'sections' => [
                [
                    'name' => 'Altes Testament',
                    'books' => $sections['old_testament']
                ],
                [
                    'name' => 'Neues Testament',
                    'books' => $sections['new_testament']
                ]
            ],
            'availableBooks' => collect($availableBooks)->sortBy('order')->values()->all()
        ];
    }

    public function getGroupedBooks()
    {
        return Book::orderBy('order')
            ->get()
            ->groupBy('category');
    }

    public function clearCache(): void
    {
        Cache::forget(self::CACHE_KEY);
    }
}
