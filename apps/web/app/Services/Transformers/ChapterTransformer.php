<?php

declare(strict_types=1);

namespace App\Services\Transformers;

use App\Models\Verse;
use App\Services\FootnoteReferenceService;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Collection;

class ChapterTransformer
{
    public function __construct(
        private readonly FootnoteReferenceService $footnoteReferenceService
    ) {}
    /**
     * Transform verses into their API representation with optional word groups and footnotes
     *
     * @param \Illuminate\Database\Eloquent\Collection $verses The verses to transform
     * @param array $options Transformation options
     *   - bool $includeWords Whether to include the words array (default: true)
     *   - bool $includeFootnotes Whether to include footnote data (default: true)
     *   - bool $includeWordGroups Whether to include pre-grouped words (default: true)
     *   - bool $useCache Whether to use caching (default: true)
     * @return array
     */
    public function transformVerses($verses, array $options = []): array
    {
        $defaultOptions = [
            'includeWords' => false,
            'includeFootnotes' => true,
            'includeWordGroups' => true,
            'useCache' => true,
        ];

        $options = array_merge($defaultOptions, $options);

        // If word groups are requested but words are not, we still need words for grouping
        $options['_needsWordsForGroups'] = $options['includeWordGroups'] && !$options['includeWords'];

        return $verses->map(function($verse) use ($options) {
            return $this->transformVerse($verse, $options);
        })->toArray();
    }

    /**
     * Transform a single verse into its API representation
     */
    protected function transformVerse($verse, array $options = []): array
    {
        $cacheKey = $this->getCacheKey($verse, $options);

        if ($options['useCache'] && Cache::has($cacheKey)) {
            /*\Log::debug('Retrieving verse from cache', [
                'verse_id' => $verse->id,
                'cache_key' => $cacheKey,
                'options' => $options,
            ]);*/
            return Cache::get($cacheKey);
        }

        /*\Log::debug('Transforming verse', [
            'verse_id' => $verse->id,
            'verse_number' => $verse->number,
            'options' => $options,
        ]);*/

        $transformed = [
            'number' => $verse->number,
            'paragraphGroupId' => $verse->paragraph_group_id,
            'paragraphStyle' => $verse->paragraph_style_id
                ? ($verse->paragraph_style->style_code ?? \App\Models\ParagraphStyle::find($verse->paragraph_style_id)?->style_code)
                : null,
            'isPericopeStart' => $verse->is_pericope_start,
            'hasOtQuote' => $verse->has_ot_quote,
            'hasTextVariant' => $verse->has_text_variant,
        ];

        // Log verse footnotes before transformation
        if ($verse->relationLoaded('footnotes')) {
            /*\Log::debug('Verse footnotes loaded', [
                'verse_id' => $verse->id,
                'footnote_count' => $verse->footnotes->count(),
                'footnote_positions' => $verse->footnotes->pluck('position')->toArray(),
            ]);*/
        } else {
            \Log::warning('Footnotes relation not loaded for verse', [
                'verse_id' => $verse->id,
                'relations' => $verse->getRelations(),
            ]);
        }

        // Transform words once to avoid duplicate processing
        $words = $this->transformWords($verse);

        // Add footnotes to words if requested
        if ($options['includeFootnotes']) {
            $words = $this->addFootnotesToWords($words, $verse);

            // Log words with footnotes after processing
            $wordsWithFootnotes = array_filter($words, fn($w) => !empty($w['footnote']));
            if (!empty($wordsWithFootnotes)) {
                /*\Log::debug('Words with footnotes after processing', [
                    'verse_id' => $verse->id,
                    'footnote_words' => array_map(fn($w) => [
                        'position' => $w['position'],
                        'text' => $w['text'],
                        'footnote_id' => $w['footnote']['id'] ?? null,
                    ], $wordsWithFootnotes),
                ]);*/
            }
        }

        // Transform words for word groups if requested
        if ($options['includeWordGroups']) {
            $transformed['wordGroups'] = $this->groupWords($words);
        }

        // Only include words array if explicitly requested (for backward compatibility)
        if ($options['includeWords']) {
            $transformed['words'] = $words;
        }

        if ($options['useCache']) {
            Cache::put($cacheKey, $transformed, now()->addDay());
            \Log::debug('Verse data cached', [
                'verse_id' => $verse->id,
                'cache_key' => $cacheKey,
                'has_word_groups' => $options['includeWordGroups'],
                'has_words' => $options['includeWords'],
            ]);
        }

        return $transformed;
    }
    /**
     * Transform words collection to array format
     */
    protected function transformWords($verse): array
    {
        $words = $verse->words->map(function($word) use ($verse) {
            $transformed = [
                'text' => $word->text,
                'textAfter' => $word->text_after,
                'position' => $word->position,
                'isOtQuote' => (bool) $word->is_ot_quote,
                'isEmphasized' => (bool) $word->is_emphasized,
                'isAddition' => (bool) $word->is_addition,
                'isFootnote' => (bool) $word->has_footnote,
                'isVariant' => (bool) $word->has_variant,
                'wordType' => $word->word_type,
                'variantGroupId' => $word->variant_group_id,
                'wordGroupId' => $word->word_group_id,
                'footnoteGroupId' => $word->footnote_group_id,
                'footnote' => null, // Will be filled by addFootnotesToWords if needed
                'paragraphGroupId' => $word->paragraph_group_id,
                'paragraphStyle' => $word->paragraph_style_id
                    ? ($word->paragraph_style->style_code ?? \App\Models\ParagraphStyle::find($word->paragraph_style_id)?->style_code)
                    : null,
                'verseNumber' => $verse->number
            ];

            if ($transformed['isFootnote']) {
                /*\Log::debug('Word marked as footnote in transformWords', [
                    'verse_id' => $verse->id,
                    'word_position' => $transformed['position'],
                    'word_text' => $transformed['text'],
                    'footnoteGroupId' => $transformed['footnoteGroupId'],
                ]);*/
            }

            return $transformed;
        })->all();

        /*\Log::debug('transformWords completed', [
            'verse_id' => $verse->id,
            'total_words' => count($words),
            'footnote_words' => count(array_filter($words, fn($w) => $w['isFootnote'])),
        ]);*/

        return $words;
    }

    /**
     * Group words based on variant readings, OT quotes, and additions
     */
    protected function groupWords(array $words): array
    {
        $groups = [];
        $currentGroup = null;

        foreach ($words as $word) {
            if ($this->shouldStartNewGroup($currentGroup, $word)) {
                if ($currentGroup !== null) {
                    $groups[] = $currentGroup;
                }

                $currentGroup = [
                    'words' => [],
                    'isVariant' => $word['isVariant'],
                    'isOtQuote' => $word['isOtQuote'],
                    'isAddition' => $word['isAddition'],
                    'wordGroupId' => $word['wordGroupId'],
                    'wordType' => $word['wordType'],
                    'variantGroupId' => $word['variantGroupId'],
                    'paragraphStyle' => $word['paragraphStyle'] ?? null,
                ];
            }

            $currentGroup['words'][] = $word;
        }

        // Add the last group if it exists
        if ($currentGroup !== null) {
            $groups[] = $currentGroup;
        }

        return $groups;
    }

    /**
     * Add footnote data to words
     */
    protected function addFootnotesToWords(array $words, $verse): array
    {
        /*\Log::debug('addFootnotesToWords called', [
            'verse_id' => $verse->id,
            'word_count' => count($words),
            'footnote_count' => $verse->footnotes ? $verse->footnotes->count() : 0,
        ]);*/

        return collect($words)->map(function($word, $index) use ($verse) {
            $footnote = $verse->footnotes->where('position', $index+1)->first();

            if ($footnote) {
                $contentStructure = $footnote->content_structure;
                
                // Process references in the footnote content
                if (is_array($contentStructure) && isset($contentStructure['elements'])) {
                    try {
                        $contentStructure = $this->footnoteReferenceService->processContentStructure(
                            $contentStructure,
                            $verse->chapter->book->slug,
                            (string)$verse->chapter->number // Pass current chapter number for verse-only references
                        );
                    } catch (\Exception $e) {
                    \Log::error('Failed to process footnote content structure', [
                        'verse_id' => $verse->id,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                }
                }
                
                $word['isFootnote'] = true;
                $word['footnoteGroupId'] = (string) $footnote->id;
                $word['footnote'] = [
                    'id' => $footnote->id,
                    'searchableText' => $footnote->searchable_text,
                    'contentStructure' => $contentStructure,
                    'isReference' => $footnote->is_reference,
                    'referencedWord' => $footnote->referenced_word,
                    'verseId' => $footnote->verse_id,
                    'hasItalics' => $footnote->has_italics,
                    'position' => $footnote->position,
                ];
                \Log::debug('Footnote attached to word', [
                    'verse_id' => $verse->id,
                    'word_position' => $index,
                    'word_text' => $word['text'],
                    'footnote_id' => $footnote->id,
                ]);
            } else if (!empty($word['isFootnote'])) {
                \Log::warning('Word marked as footnote but no footnote found', [
                    'verse_id' => $verse->id,
                    'word_position' => $index,
                    'word_text' => $word['text'],
                    'word' => $word,
                ]);
            }

            return $word;
        })->all();
    }

    /**
     * Determine if a new word group should be started
     */
    protected function shouldStartNewGroup(?array $currentGroup, array $word): bool
    {
        if ($currentGroup === null) {
            return true;
        }

        // Variant group boundary
        if ($currentGroup['isVariant'] !== $word['isVariant']) {
            return true;
        }

        if ($word['isVariant'] && $word['variantGroupId'] !== $currentGroup['variantGroupId']) {
            return true;
        }

        // Addition group boundary
        if ($currentGroup['isAddition'] !== $word['isAddition']) {
            return true;
        }

        if ($word['isAddition'] && $word['wordGroupId'] !== $currentGroup['wordGroupId']) {
            return true;
        }

        // OT Quote group boundary
        if ($currentGroup['isOtQuote'] !== $word['isOtQuote']) {
            return true;
        }

        if ($word['isOtQuote'] && $word['wordGroupId'] !== $currentGroup['wordGroupId']) {
            return true;
        }

        return false;
    }

    /**
     * Generate a cache key for the verse
     */
    protected function getCacheKey($verse, array $options): string
    {
        return sprintf(
            'verse:%d:%d:%d:%d:%d',
            $verse->id,
            (int) $options['includeFootnotes'],
            (int) $options['includeWordGroups'],
            $verse->updated_at?->timestamp ?? 0,
            $verse->words->max('updated_at')?->timestamp ?? 0
        );
    }

    /**
     * Clear cache for a specific verse
     */
    public function clearCache($verse): void
    {
        // Clear all variations of the verse cache
        $options = [
            ['includeFootnotes' => true, 'includeWordGroups' => true],
            ['includeFootnotes' => true, 'includeWordGroups' => false],
            ['includeFootnotes' => false, 'includeWordGroups' => true],
            ['includeFootnotes' => false, 'includeWordGroups' => false],
        ];

        foreach ($options as $option) {
            $cacheKey = $this->getCacheKey($verse, array_merge($option, ['useCache' => true]));
            Cache::forget($cacheKey);
        }
    }
}
