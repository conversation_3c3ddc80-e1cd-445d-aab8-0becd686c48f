<?php

declare(strict_types=1);

namespace App\Services\Transformers;

use App\Models\Verse;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Collection;

// this is used in the API call, not in the BibleDisplay component (via BibleTextController)
// TODO: redundant?
class VerseTransformer
{
    /**
     * Transform a verse into its API representation
     */
    /**
     * Transform a verse into its API representation with optional word groups and footnotes
     *
     * @param Verse $verse The verse to transform
     * @param array $options Transformation options
     *   - bool $includeWords Whether to include the words array (default: false)
     *   - bool $includeFootnotes Whether to include footnote data (default: false)
     *   - bool $includeWordGroups Whether to include pre-grouped words (default: true)
     *   - bool $useCache Whether to use caching (default: true)
     * @return array
     */
    public function transform(Verse $verse, array $options = []): array
    {
        $defaultOptions = [
            'includeWords' => false,
            'includeFootnotes' => true,
            'includeWordGroups' => true,
            'useCache' => true,
        ];

        $options = array_merge($defaultOptions, $options);
        $cacheKey = $this->getCacheKey($verse, $options);

        if ($options['useCache'] && Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        $data = [
            'number' => $verse->number,
            'chapter' => $verse->chapter->number,
            'text' => $verse->words->pluck('text')->join(' '),
        ];

        // Transform words if needed for either words array or word groups
        $words = [];
        if ($options['includeWords'] || $options['includeWordGroups']) {
            $words = $this->transformWords($verse);

            if ($options['includeWords']) {
                $data['words'] = $options['includeFootnotes']
                    ? $this->addFootnotesToWords($words, $verse)
                    : $words;
            }
        }

        // Add word groups if requested
        if ($options['includeWordGroups']) {
            $data['wordGroups'] = $this->groupWords($words);
        }

        if ($options['useCache']) {
            Cache::put($cacheKey, $data, now()->addDay());
        }

        return $data;
    }

    /**
     * Transform words collection to array format
     */
    protected function transformWords(Verse $verse): array
    {
        return $verse->words->map(function ($word) use ($verse) {
            return [
                'text' => $word->text,
                'textAfter' => $word->text_after,
                'position' => $word->position,
                'isOtQuote' => (bool) $word->is_ot_quote,
                'isEmphasized' => (bool) $word->is_emphasis,
                'isAddition' => (bool) $word->is_addition,
                'isFootnote' => (bool) $word->is_footnote,
                'isVariant' => (bool) $word->has_variant,
                'wordType' => $word->word_type,
                'variantGroupId' => $word->variant_group_id,
                'wordGroupId' => $word->word_group_id,
                'footnoteGroupId' => $word->footnote_group_id,
                'footnote' => null,
                'paragraphGroupId' => $word->paragraph_group_id,
                'paragraphStyle' => $word->paragraph_style_id
                    ? ($word->paragraph_style->style_code ?? \App\Models\ParagraphStyle::find($word->paragraph_style_id)?->style_code)
                    : null,
                'verseNumber' => $verse->number
            ];
        })->all();
    }

    /**
     * Group words based on variant readings, OT quotes, and additions
     */
    protected function groupWords(array $words): array
    {
        $groups = [];
        $currentGroup = null;

        foreach ($words as $word) {
            if ($this->shouldStartNewGroup($currentGroup, $word)) {
                if ($currentGroup !== null) {
                    $groups[] = $currentGroup;
                }

                $currentGroup = [
                    'words' => [],
                    'isVariant' => $word['isVariant'],
                    'isOtQuote' => $word['isOtQuote'],
                    'isAddition' => $word['isAddition'],
                    'wordGroupId' => $word['wordGroupId'],
                    'wordType' => $word['wordType'],
                    'variantGroupId' => $word['variantGroupId'],
                    'paragraphStyle' => $word['paragraphStyle'] ?? null,
                ];
            }

            $currentGroup['words'][] = $word;
        }

        // Add the last group if it exists
        if ($currentGroup !== null) {
            $groups[] = $currentGroup;
        }

        return $groups;
    }

    /**
     * Add footnote data to words
     */
    protected function addFootnotesToWords(array $words, Verse $verse): array
    {
        return collect($words)->map(function ($word, $index) use ($verse) {
            $footnote = $verse->footnotes->where('position', $index+1)->first();

            if ($footnote) {
                $word['isFootnote'] = true;
                $word['footnoteGroupId'] = (string) $footnote->id;
                $word['footnote'] = [
                    'id' => $footnote->id,
                    'searchableText' => $footnote->searchable_text,
                    'contentStructure' => $footnote->content_structure,
                    'isReference' => $footnote->is_reference,
                    'referencedWord' => $footnote->referenced_word,
                    'verseId' => $footnote->verse_id,
                    'hasItalics' => $footnote->has_italics,
                    'position' => $footnote->position,
                ];
            }

            return $word;
        })->all();
    }

    /**
     * Determine if a new word group should be started
     */
    protected function shouldStartNewGroup(?array $currentGroup, array $word): bool
    {
        if ($currentGroup === null) {
            return true;
        }

        // Variant group boundary
        if ($currentGroup['isVariant'] !== $word['isVariant']) {
            return true;
        }

        if ($word['isVariant'] && $word['variantGroupId'] !== $currentGroup['variantGroupId']) {
            return true;
        }

        // Addition group boundary
        if ($currentGroup['isAddition'] !== $word['isAddition']) {
            return true;
        }

        if ($word['isAddition'] && $word['wordGroupId'] !== $currentGroup['wordGroupId']) {
            return true;
        }

        // OT Quote group boundary
        if ($currentGroup['isOtQuote'] !== $word['isOtQuote']) {
            return true;
        }

        if ($word['isOtQuote'] && $word['wordGroupId'] !== $currentGroup['wordGroupId']) {
            return true;
        }

        return false;
    }

    /**
     * Generate a cache key for the verse
     */
    protected function getCacheKey(Verse $verse, array $options): string
    {
        return sprintf(
            'verse:%d:%d:%d:%d:%d',
            $verse->id,
            (int) $options['includeFootnotes'],
            (int) $options['includeWordGroups'],
            $verse->updated_at?->timestamp ?? 0,
            $verse->words->max('updated_at')?->timestamp ?? 0
        );
    }

    /**
     * Clear cache for a specific verse
     */
    public function clearCache(Verse $verse): void
    {
        // Clear all variations of the verse cache
        $options = [
            ['includeFootnotes' => true, 'includeWordGroups' => true],
            ['includeFootnotes' => true, 'includeWordGroups' => false],
            ['includeFootnotes' => false, 'includeWordGroups' => true],
            ['includeFootnotes' => false, 'includeWordGroups' => false],
        ];

        foreach ($options as $option) {
            $cacheKey = $this->getCacheKey($verse, array_merge($option, ['useCache' => true]));
            Cache::forget($cacheKey);
        }
    }
}
