<?php

namespace App\Services\Transformers;

use App\Models\Book;
use App\Enums\Testament;

class BookTransformer
{
    public function transform(Book $book)
    {
        return [
            'id' => $book->id,
            'name' => $book->name,
            'slug' => $book->slug,
            'order' => $book->order,
            'chapterCount' => $book->chapters_count,
            'abbreviation' => $book->abbreviation,
            'testament' => $book->testament?->value ?? 'ot',
            'testamentLabel' => $book->testament === Testament::OT ? 'AT' : 'NT',
            'category' => $book->category?->value ?? 'law',
            'searchNames' => $book->search_names,
            'metadata' => [
                'location' => $book->location,
                'authors' => $book->authors, //array
                'writtenYear' => $book->written_year,
                'theme' => $book->theme,
                'keyPeople' => $book->key_people, //comma strings
                'keyWords' => $book->key_words,
                'keyTeachings' => $book->key_teachings,
                'keyVerses' => $book->key_verses,
                'covenants' => $book->covenants,
                'attributesOfGod' => $book->attributes_of_god,
                'historicalPeriod' => $book->historical_period,
                'originalLanguage' => $book->original_language?->value ?? 'hebrew',
            ]
        ];
    }

    public function transformCore(Book $book)
    {
        return [
            'id' => $book->id,
            'name' => $book->name,
            'slug' => $book->slug,
            'order' => $book->order,
            'chapterCount' => $book->chapters_count,
            'abbreviation' => $book->abbreviation,
            'testament' => $book->testament?->value ?? 'ot',
            'testamentLabel' => $book->testament === Testament::OT ? 'AT' : 'NT',
            'category' => $book->category?->value ?? 'law',
            'searchNames' => $book->search_names,
        ];
    }

    public function transformForNavigation(Book $book)
    {
        return [
            'id' => $book->id,
            'name' => $book->name,
            'slug' => $book->slug,
            'order' => $book->order,
            'chapterCount' => $book->chapters_count,
            'abbreviation' => $book->abbreviation,
            'testament' => $book->testament?->value ?? 'ot',
            'testamentLabel' => $book->testament === Testament::OT ? 'AT' : 'NT',
            'hasContent' => $book->has_content,
        ];
    }
}
