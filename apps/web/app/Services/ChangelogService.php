<?php

declare(strict_types=1);

namespace App\Services;

use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class ChangelogService
{
    private string $changelogPath;

    public function __construct()
    {
        $this->changelogPath = base_path('CHANGELOG.md');
    }

    /**
     * Get the parsed changelog data grouped by release
     *
     * @return array<string, array{version: string, date: string|null, sections: array<string, array<int, array{id: string, content: string}>>}>
     */
    public function getChangelog(): array
    {
        if (!File::exists($this->changelogPath)) {
            Log::debug('Changelog file not found at: ' . $this->changelogPath);
            return [];
        }

        $content = File::get($this->changelogPath);
        $releases = [];
        $currentVersion = null;
        $currentRelease = null;
        $currentSection = null;
        $lines = explode("\n", $content);

        foreach ($lines as $lineNumber => $line) {
            $line = trim($line);

            // Skip empty lines and main header
            if (empty($line) || $line === '# Changelog') {
                //Log::debug('Skipping line ' . $lineNumber . ': ' . $line);
                continue;
            }

            // Version headers (## [1.0.0-beta.8] - 2025-06-03)
            if (preg_match('/^## \[(.+)\](?: - (\d{4}-\d{2}-\d{2}))?/', $line, $matches)) {
                $currentVersion = $matches[1];
                $currentRelease = [
                    'version' => $currentVersion,
                    'date' => $matches[2] ?? null,
                    'sections' => []
                ];
                $releases[$currentVersion] = $currentRelease;
                $currentSection = null;
                //Log::debug('Found version: ' . $currentVersion . ', date: ' . ($matches[2] ?? 'null'));
            }
            // Section headers (### Added, ### Changed, etc.)
            elseif ($currentRelease && preg_match('/^### (.+)$/', $line, $matches)) {
                $currentSection = $matches[1];
                $currentRelease['sections'][$currentSection] = [];
                $releases[$currentVersion] = $currentRelease;
                //Log::debug('Found section: ' . $currentSection);
            }
            // List items
            elseif ($currentRelease && $currentSection && preg_match('/^- (.+)$/', $line, $matches)) {
                $item = [
                    'id' => Str::uuid()->toString(),
                    'content' => trim($matches[1])
                ];
                $currentRelease['sections'][$currentSection][] = $item;
                $releases[$currentVersion] = $currentRelease;
                //Log::debug('Added item to section ' . $currentSection . ': ' . $matches[1]);
            }
        }

        //Log::debug('Finished parsing changelog', ['releases' => $releases]);
        return $releases;
    }
}
