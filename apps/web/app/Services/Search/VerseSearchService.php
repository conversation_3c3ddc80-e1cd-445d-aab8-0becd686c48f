<?php

namespace App\Services\Search;

use App\Models\Verse;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;

class VerseSearchService
{
    public function search(string $query, ?string $bookId = null, int $perPage = 20): LengthAwarePaginator
    {
        return Verse::query()
            ->with(['book', 'chapter'])
            ->when($bookId, function ($q) use ($bookId) {
                $q->whereHas('book', function ($query) use ($bookId) {
                    $query->where('id', $bookId);
                });
            })
            ->where(function ($q) use ($query) {
                $q->where('text', 'like', "%{$query}%")
                  ->orWhere('strongs_numbers', 'like', "%{$query}%");
            })
            ->orderBy('book_id')
            ->orderBy('chapter_number')
            ->orderBy('verse_number')
            ->paginate($perPage);
    }
}
