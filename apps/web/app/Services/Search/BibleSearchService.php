<?php

namespace App\Services\Search;

use App\DataTransferObjects\SearchResult;
use App\Models\Book;
use App\Models\SearchableText;
use App\Services\BibleReference;
use App\Services\BibleReferenceParser;
use Illuminate\Support\Facades\Log;

class BibleSearchService
{
    public function __construct(
        private readonly BibleReferenceParser $referenceParser
    ) {}

    public function search(
        ?string $query,
        array $types = [],
        ?string $bookId = null,
        bool $includeMetadata = false,
        int $perPage = 20,
        int $page = 1
    ): SearchResult {
        Log::info('Starting search in BibleSearchService', [
            'query' => $query,
            'types' => $types,
            'bookId' => $bookId,
            'includeMetadata' => $includeMetadata,
            'page' => $page,
            'perPage' => $perPage
        ]);

        if (!$query && !$bookId) {
            return SearchResult::fromSearch([], ['message' => 'No search criteria provided']);
        }

        try {
            // Only try parsing as a reference if it looks like one (contains numbers or dots)
            if ($query && (preg_match('/[\d\.]/', $query) || str_contains($query, ','))) {
                Log::info('Attempting reference parsing', ['query' => $query]);

                // Try exact reference match first
                $verseResult = SearchableText::findByReference($query);
                if ($verseResult) {
                    Log::info('Found exact reference match', ['result' => $verseResult]);
                    $verseResult->load(['book', 'chapter', 'verse']);
                    return SearchResult::fromSearch([$this->transformResult($verseResult)]);
                }

                // Try reference parsing as fallback
                $parsedRef = $this->referenceParser->parse($query);
                if ($parsedRef && $parsedRef['book'] !== null && $parsedRef['chapter'] !== null) {
                    $reference = new BibleReference(
                        book: $parsedRef['book'],
                        chapter: $parsedRef['chapter'],
                        verseStart: $parsedRef['verse_start'] ?? null,
                        verseEnd: $parsedRef['verse_end'] ?? null
                    );
                    Log::info('Found parsed reference match', ['reference' => $reference]);
                    return SearchResult::fromReference($reference);
                }
            }

            // Map frontend types to database types
            $typeMap = [
                'books' => 'verse',
                'verses' => 'verse',
                'footnotes' => 'footnote',
                'words' => 'word',
                'metadata' => 'metadata'
            ];

            // Convert frontend types to database types
            $searchTypes = empty($types)
                ? array_values($typeMap)
                : array_map(fn($type) => $typeMap[$type] ?? $type, $types);

            Log::info('Searching with types', ['types' => $searchTypes]);

            // Build the base query
            $searchQuery = SearchableText::search($query)
                ->when($bookId, function ($query) use ($bookId) {
                    $query->where('book_id', $bookId);
                })
                ->when(!empty($searchTypes), function ($query) use ($searchTypes) {
                    $query->whereIn('type', $searchTypes);
                });

            // Get paginated results with explicit page number
            $results = $searchQuery->paginate(
                perPage: $perPage,
                page: $page
            );

            // Load relationships after getting results
            $results->load(['book', 'chapter', 'verse']);

            // Transform the results
            $transformedResults = $results->through(fn($result) => $this->transformResult($result));

            return SearchResult::fromSearch($transformedResults);

        } catch (\Exception $e) {
            Log::error('Search error', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return SearchResult::error($e->getMessage());
        }
    }

    private function transformResult($result, bool $includeMetadata = true): array
    {
        $reference = $result->book
            ? sprintf('%s %d:%d', $result->book->name, $result->chapter->number, $result->verse->number)
            : null;

        $data = [
            'id' => $result->id,
            'type' => $result->type,
            'title' => $reference,
            'subtitle' => $result->book ? $result->book->name : null,
            'content' => $result->content,
            'url' => $result->book
                ? $result->book->slug . '' . $result->chapter->number . ',' . $result->verse->number
                : '/',
            'relevance' => 1,
            'reference' => $reference,
        ];

        // For footnote results, add additional information
        if ($result->type === 'footnote' && $result->verse_id) {
            // Load the verse if not already loaded
            if (!isset($result->verse) || !$result->verse) {
                $verse = \App\Models\Verse::find($result->verse_id);
            } else {
                $verse = $result->verse;
            }

            $data['footnoteInfo'] = [
                'verseId' => $result->verse_id,
                'position' => $result->position,
                'id' => $result->id
            ];

            // Add verse content if we have the verse
            if ($verse) {
                $data['footnoteInfo']['searchableText'] = $verse->text;

                // Find the word that references this footnote
                $word = \App\Models\Word::where('footnote_id', $result->id)
                    ->first();

                if ($word) {
                    $data['footnoteInfo']['referencedWord'] = $word->text;
                }
            }
        }

        if ($includeMetadata) {
            $data['metadata'] = [
                'tags' => $result->metadata['tags'] ?? [],
                'endVerse' => $result->metadata['end_verse'] ?? null,
                'startVerse' => $result->metadata['start_verse'] ?? null,
                'hasOtQuote' => $result->metadata['has_ot_quote'] ?? false,
                'hasTextVariant' => $result->metadata['has_text_variant'] ?? false,
                'bookName' => $result->book ? $result->book->name : null,
                'chapterNumber' => $result->chapter ? $result->chapter->number : null,
                'verseNumber' => $result->verse ? $result->verse->number : null,
            ];
        }

        return $data;
    }

    private function getMatchingMetadataContent(Book $book, string $query): string
    {
        // Implement logic to get matching metadata content
        // For now, just return the book's name
        return $book->name;
    }
}
