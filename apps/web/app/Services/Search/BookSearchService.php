<?php

namespace App\Services\Search;

use App\Models\Book;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;

class BookSearchService
{
    public function search(string $query, bool $includeMetadata = false, int $perPage = 10): LengthAwarePaginator
    {
        return Book::query()
            ->when($query, function ($q) use ($query, $includeMetadata) {
                $q->where(function ($subQ) use ($query, $includeMetadata) {
                    $subQ->where('name', 'like', "%{$query}%")
                         ->orWhere('abbreviation', 'like', "%{$query}%")
                         ->orWhere('search_names', 'like', "%{$query}%");

                    if ($includeMetadata) {
                        $this->addMetadataSearch($subQ, $query);
                    }
                });
            })
            ->orderBy('order')
            ->paginate($perPage);
    }

    private function addMetadataSearch(Builder $query, string $searchTerm): void
    {
        $metadataFields = [
            'authors',
            'theme',
            'key_people',
            'attributes_of_god',
            'key_words',
            'covenants',
            'key_teachings',
            'key_verses'
        ];

        foreach ($metadataFields as $field) {
            $query->orWhere($field, 'like', "%{$searchTerm}%");
        }
    }
}
