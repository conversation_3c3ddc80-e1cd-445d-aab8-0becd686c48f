<?php

namespace App\Services;

use App\Models\Book;
use App\Enums\Testament;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

class ChapterService
{
    public function __construct(
        private VerseCacheService $verseCache
    ) {}

    public function getChapterWithContext(Book $book, int $chapterNumber)
    {
        $current = $this->getChapter($book, $chapterNumber);
        $previous = $this->getPreviousChapter($book, $chapterNumber);
        $next = $this->getNextChapter($book, $chapterNumber);

        return [
            'current' => $current,
            'previous' => $previous,
            'next' => $next,
            'hasPrevious' => $previous !== null,
            'hasNext' => $next !== null,
        ];
    }

    /**
     * Get a chapter with optional word data
     *
     * @param Book $book The book to get the chapter from
     * @param int $number The chapter number
     * @param array $options Options for including word data
     *   - bool $includeWords Whether to include the words array (default: false)
     *   - bool $includeWordGroups Whether to include word groups (default: true)
     *   - bool $includeFootnotes Whether to include footnotes (default: true)
     * @return array|null The formatted chapter data or null if not found
     */
    public function getChapter(Book $book, int $number, array $options = [])
    {
        // Set default options
        $options = array_merge([
            'includeWords' => false,
            'includeWordGroups' => true,
            'includeFootnotes' => true,
        ], $options);

        // Get chapter metadata
        $chapter = $book->chapters()
            ->where('number', $number)
            ->first();

        if (!$chapter) {
            Log::warning('Chapter not found', [
                'book' => $book->slug,
                'chapter' => $number
            ]);
            return null;
        }


        // Get chapter content from cache or database
        $chapterData = $this->verseCache->getOrSetChapter(
            $book->slug,
            $number,
            function () use ($chapter, $options) {
                // Load relationships based on options
                $query = $chapter->with([
                    'verses' => function($q) {
                        $q->orderBy('number');
                    }
                ]);

                // Only load footnotes if needed
                if ($options['includeFootnotes']) {
                    $query->with(['verses.footnotes']);
                }

                return $query->first()->toArray();
            },
            [
                'includeWords' => $options['includeWords'],
                'includeWordGroups' => $options['includeWordGroups'],
                'includeFootnotes' => $options['includeFootnotes'],
            ]
        );

        if (!$chapterData) {
            $this->logError('Failed to get chapter data from cache', $book->slug, $number);
            return null;
        }

        return $this->formatChapterData($chapterData, $book, $number, $options);
    }

    /**
     * Format chapter data into a consistent structure
     *
     * @param array $chapterData The raw chapter data
     * @param Book $book The book model
     * @param int $number The chapter number
     * @param array $options Formatting options
     * @return array|null The formatted chapter data or null on error
     */
    private function formatChapterData(array $chapterData, Book $book, int $number, array $options = [])
    {
        // Handle both direct and wrapped content
        $content = isset($chapterData['content']) ? $chapterData['content'] : $chapterData;

        // Ensure we have verses
        if (!isset($content['verses']) || !is_array($content['verses'])) {
            $this->logError('Invalid chapter data structure - missing verses array', $book->slug, $number, [
                'content_keys' => array_keys($content)
            ]);
            return null;
        }

        // Map the data to the expected format
        try {
            return [
                'id' => $content['id'],
                'number' => $number,
                'verses' => $this->formatVerses($content['verses'], $options),
                'book' => $this->formatBookData($book)
            ];
        } catch (\Exception $e) {
            $this->logError('Error mapping chapter data', $book->slug, $number, [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * Format verses with optional word data
     *
     * @param array $verses The verses to format
     * @param array $options Formatting options
     * @return array The formatted verses
     */
    private function formatVerses(array $verses, array $options = []): array
    {
        return array_map(function ($verse) use ($options) {
            $formatted = [
                'id' => $verse['id'],
                'number' => $verse['number'],
                'text' => $verse['text'],
                'paragraph_group_id' => $verse['paragraph_group_id'],
                'paragraph_style_id' => $verse['paragraph_style_id'],
                'is_pericope_start' => (bool)($verse['is_pericope_start'] ?? false),
            ];

            // Format words first
            $formatted['words'] = [];
            if ($options['includeWords'] || $options['includeWordGroups']) {
                $formatted['words'] = $this->formatWords($verse['words'] ?? []);
            }

            // Process footnotes if requested
            if ($options['includeFootnotes'] && !empty($verse['footnotes'])) {
                $footnotes = $this->formatFootnotes(['footnotes' => $verse['footnotes']]);
                $formatted['footnotes'] = $footnotes;

                // Associate footnotes with their words
                foreach ($footnotes as $footnote) {
                    if (isset($footnote['position']) && isset($formatted['words'][$footnote['position']])) {
                        $formatted['words'][$footnote['position']]['footnote'] = $footnote;
                    }
                }
            }

            return $formatted;
        }, $verses);
    }

    /**
     * Format footnotes data
     *
     * @param array $verse Array containing 'footnotes' key with footnotes data
     * @return array Formatted footnotes array
     */
    private function formatFootnotes(array $verse): array
    {
        if (!isset($verse['footnotes']) || !is_array($verse['footnotes'])) {
            return [];
        }

        return collect($verse['footnotes'])->map(function ($footnote) {
            return [
                'id' => $footnote['id'] ?? null,
                'content' => $footnote['content'] ?? null,
                'content_structure' => $footnote['content_structure'] ?? null,
                'caller' => $footnote['caller'] ?? null,
                'position' => (int)($footnote['position'] ?? 0),
                'is_reference' => (bool)($footnote['is_reference'] ?? false),
                'referenced_word' => $footnote['referenced_word'] ?? null,
                'has_italics' => (bool)($footnote['has_italics'] ?? false),
                'verse_id' => $footnote['verse_id'] ?? null,
            ];
        })->values()->all();
    }

    /**
     * Format words data
     *
     * @param array $words Array of word data to format
     * @return array Formatted words array
     */
    private function formatWords(array $words): array
    {
        return array_map(function ($word) {
            return [
                'text' => $word['text'] ?? '',
                'textAfter' => $word['text_after'] ?? null,
                'position' => (int) ($word['position'] ?? 0),
                'isOtQuote' => (bool) ($word['is_ot_quote'] ?? false),
                'isEmphasized' => (bool) ($word['is_emphasis'] ?? false),
                'isAddition' => (bool) ($word['is_addition'] ?? false),
                'isFootnote' => (bool) ($word['is_footnote'] ?? false),
                'isVariant' => (bool) ($word['has_variant'] ?? false),
                'wordType' => $word['word_type'] ?? null,
                'variantGroupId' => $word['variant_group_id'] ?? null,
                'wordGroupId' => $word['word_group_id'] ?? null,
                'footnoteGroupId' => $word['footnote_group_id'] ?? null,
                'footnote' => null, // Will be populated by formatFootnotes if needed
                'paragraphGroupId' => $word['paragraph_group_id'] ?? null,
                'paragraphStyle' => $word['paragraph_style_id']
                    ? ($word['paragraph_style']['style_code'] ?? null)
                    : null,
                'verseNumber' => (int) ($word['verse_number'] ?? $word['verse_id'] ?? 0)
            ];
        }, $words);
    }

    /**
     * Format book data
     */
    private function formatBookData(Book $book): array
    {
        return [
            'id' => $book->id,
            'name' => $book->name,
            'slug' => $book->slug,
            'testament' => $book->testament,
            'order' => $book->order,
            'chapterCount' => $book->chapters_count
        ];
    }

    public function getChapterWindow(Book $book, int $chapterNumber)
    {
        $currentChapter = $this->fetchChapterWithVerses($book, $chapterNumber);

        if (!$currentChapter) {
            $this->logError('Current chapter not found', $book->slug, $chapterNumber);
            return null;
        }

        // Initialize window data
        $windowData = [
            'current' => $currentChapter,
            'previousPrevious' => null,
            'previous' => null,
            'next' => null,
            'nextNext' => null,
            'hasMorePrevious' => false,
            'hasMoreNext' => false,
            'previousBook' => null,
            'nextBook' => null
        ];

        // Handle previous chapters
        $this->populatePreviousChapters($windowData, $book, $chapterNumber);

        // Handle next chapters
        $this->populateNextChapters($windowData, $book, $chapterNumber);

        return $windowData;
    }

    /**
     * Populate previous chapters in the window data
     */
    private function populatePreviousChapters(array &$windowData, Book $book, int $chapterNumber): void
    {
        if ($chapterNumber > 1) {
            // Previous chapters in the same book
            $windowData['previous'] = $this->fetchChapterWithVerses($book, $chapterNumber - 1);

            if ($chapterNumber > 2) {
                $windowData['previousPrevious'] = $this->fetchChapterWithVerses($book, $chapterNumber - 2);
                $windowData['hasMorePrevious'] = $chapterNumber > 3;
            }
        } else {
            // Check previous book
            $previousBook = $this->getPreviousBook($book);
            $windowData['previousBook'] = $previousBook;

            if ($previousBook) {
                $windowData['previous'] = $this->fetchChapterWithVerses(
                    $previousBook,
                    $previousBook->chapters_count
                );

                if ($previousBook->chapters_count > 1) {
                    $windowData['previousPrevious'] = $this->fetchChapterWithVerses(
                        $previousBook,
                        $previousBook->chapters_count - 1
                    );
                    $windowData['hasMorePrevious'] = $previousBook->chapters_count > 2;
                }
            }
        }
    }

    /**
     * Populate next chapters in the window data
     */
    private function populateNextChapters(array &$windowData, Book $book, int $chapterNumber): void
    {
        if ($chapterNumber < $book->chapters_count) {
            // Next chapters in the same book
            $windowData['next'] = $this->fetchChapterWithVerses($book, $chapterNumber + 1);

            if ($chapterNumber < $book->chapters_count - 1) {
                $windowData['nextNext'] = $this->fetchChapterWithVerses($book, $chapterNumber + 2);
                $windowData['hasMoreNext'] = $chapterNumber < $book->chapters_count - 2;
            }
        } else {
            // Check next book
            $nextBook = $this->getNextBook($book);
            $windowData['nextBook'] = $nextBook;

            if ($nextBook) {
                $windowData['next'] = $this->fetchChapterWithVerses($nextBook, 1);

                if ($nextBook->chapters_count > 1) {
                    $windowData['nextNext'] = $this->fetchChapterWithVerses($nextBook, 2);
                    $windowData['hasMoreNext'] = $nextBook->chapters_count > 2;
                }
            }
        }
    }

    /**
     * Fetch a chapter with its verses
     */
    private function fetchChapterWithVerses(Book $book, int $number)
    {
        return $book->chapters()
            ->where('number', $number)
            ->with([
            'verses.words.paragraphStyle'
        ])
            ->first();
    }

    public function getExtendedChapterWindow(Book $book, int $chapterNumber, string $direction = 'both'): array
    {
        $window = $this->getChapterWindow($book, $chapterNumber);

        if ($direction === 'previous') {
            return $this->extendPreviousWindow($window);
        } elseif ($direction === 'next') {
            return $this->extendNextWindow($window);
        }

        return $window;
    }

    /**
     * Extend the window with more previous chapters
     */
    private function extendPreviousWindow(array $window): array
    {
        $previousPrevious = null;
        $hasMorePrevious = $window['hasMorePrevious'];

        if ($window['previous']) {
            $prevBook = $window['previous']->book;
            $prevChapterNum = $window['previous']->number;

            if ($prevChapterNum > 1) {
                // Previous-previous chapter is in the same book
                $previousPrevious = $this->getChapter($prevBook, $prevChapterNum - 1);
                $hasMorePrevious = $prevChapterNum > 2;
            } else {
                // Need to look in the previous book
                $prevPrevBook = $this->getPreviousBook($prevBook);
                if ($prevPrevBook) {
                    $previousPrevious = $this->getChapter($prevPrevBook, $prevPrevBook->chapters_count);
                    $hasMorePrevious = $prevPrevBook->chapters_count > 1;
                    $window['navigation']['previousPreviousBook'] = [
                        'slug' => $prevPrevBook->slug,
                        'order' => $prevPrevBook->order,
                        'chapterCount' => $prevPrevBook->chapters_count,
                    ];
                }
            }
        }

        return array_merge($window, [
            'previousPrevious' => $previousPrevious,
            'hasMorePrevious' => $hasMorePrevious
        ]);
    }

    /**
     * Extend the window with more next chapters
     */
    private function extendNextWindow(array $window): array
    {
        // Similar implementation to extendPreviousWindow but for next chapters
        // Implementation would go here
        return $window;
    }

    public function getChaptersForBook(Book $book, array $numbers): Collection
    {
        return collect($numbers)->map(function ($number) use ($book) {
            return $this->getChapter($book, $number);
        })->filter();
    }

    /**
     * Helper method to get previous chapter, handling book boundaries
     */
    private function getPreviousChapter(Book $book, int $chapterNumber)
    {
        if ($chapterNumber > 1) {
            return $this->getChapter($book, $chapterNumber - 1);
        }

        $prevBook = $this->getPreviousBook($book);
        return $prevBook ? $this->getChapter($prevBook, $prevBook->chapters_count) : null;
    }

    /**
     * Helper method to get next chapter, handling book boundaries
     */
    private function getNextChapter(Book $book, int $chapterNumber)
    {
        if ($chapterNumber < $book->chapters_count) {
            return $this->getChapter($book, $chapterNumber + 1);
        }

        $nextBook = $this->getNextBook($book);
        return $nextBook ? $this->getChapter($nextBook, 1) : null;
    }

    /**
     * Get the previous book in order
     */
    private function getPreviousBook(Book $book)
    {
        return Book::where('order', '<', $book->order)
            ->orderBy('order', 'desc')
            ->first();
    }

    /**
     * Get the next book in order
     */
    private function getNextBook(Book $book)
    {
        return Book::where('order', '>', $book->order)
            ->orderBy('order')
            ->first();
    }

    /**
     * Log an error with consistent context
     */
    private function logError(string $message, string $bookSlug, int $chapterNumber, array $additionalContext = []): void
    {
        Log::error($message, array_merge([
            'book' => $bookSlug,
            'chapter' => $chapterNumber
        ], $additionalContext));
    }
}
