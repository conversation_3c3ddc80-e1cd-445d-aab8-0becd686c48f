<?php

namespace App\Services;

use App\Models\Book;
use Illuminate\Support\Facades\Log;
use App\DataTransferObjects\ReferenceResult;

class BibleReferenceService
{
    public function parseReference(?string $query): ?ReferenceResult
    {
        if (!$query) {
            return null;
        }

        // Normalize the query first
        $query = $this->normalizeBookName($query);

        // Pattern to match Bible references
        $pattern = '/^
            (\d{0,1}\.?\s*[A-Za-zäöüÄÖÜß]+) # Book part with optional number
            [\s\.]*                          # Optional spaces or dots
            (\d+)                            # Chapter
            (?:                              # Optional verse part
                [,\.](\d+)                   # Verse number (with comma or dot)
                (?:-(\d+))?                  # Optional verse range
            )?
            $/x';

        if (!preg_match($pattern, $query, $matches)) {
            Log::info('No reference match found for: ' . $query);
            return null;
        }

        $bookPart = trim($matches[1]);
        $chapter = (int) $matches[2];
        $verse = isset($matches[3]) ? (int) $matches[3] : null;
        $verseEnd = isset($matches[4]) ? (int) $matches[4] : null;

        // Try to find the book
        $book = Book::findByReference($bookPart);
        if (!$book || $chapter > $book->chapters_count) {
            return null;
        }

        $url = $this->buildCanonicalUrl($book->name, $chapter, $verse, $verseEnd);

        return new ReferenceResult(
            book: $book,
            chapter: $chapter,
            verse: $verse,
            verseEnd: $verseEnd,
            url: $url
        );
    }

    protected function normalizeBookName(string $name): string
    {
        return preg_replace('/\.?\s+/', '.', trim(urldecode($name)));
    }

    protected function buildCanonicalUrl(string $bookName, int $chapter, ?int $verse = null, ?int $verseEnd = null): string
    {
        // Handle numbered books (e.g., "1. Mose")
        if (preg_match('/^(\d+)[ \.]?(.+)$/', $bookName, $parts)) {
            $canonicalName = $parts[1] . '.' . preg_replace('/[^A-Za-zäöüÄÖÜß]/', '', $parts[2]);
        } else {
            $canonicalName = preg_replace('/[^A-Za-zäöüÄÖÜß]/', '', $bookName);
        }

        $url = $canonicalName . $chapter;

        if ($verse) {
            $url .= ',' . $verse;
            if ($verseEnd) {
                $url .= '-' . $verseEnd;
            }
        }

        return $url;
    }
}
