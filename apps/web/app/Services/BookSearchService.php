<?php

namespace App\Services;

use App\Models\Book;
use Illuminate\Database\Eloquent\Builder;

class BookSearchService
{
    public function search(string $query = null, bool $includeMetadata = false): Builder
    {
        return Book::query()
            ->when($query, function ($query, $searchQuery) use ($includeMetadata) {
                $query->where(function ($q) use ($searchQuery, $includeMetadata) {
                    $this->applyBasicSearchCriteria($q, $searchQuery);
                    
                    if ($includeMetadata) {
                        $this->applyMetadataSearchCriteria($q, $searchQuery);
                    }
                });
            });
    }

    private function applyBasicSearchCriteria($query, $searchQuery): void
    {
        $query->where('name', 'like', "%{$searchQuery}%")
              ->orWhere('abbreviation', 'like', "%{$searchQuery}%")
              ->orWhere('search_names', 'like', "%{$searchQuery}%");
    }

    private function applyMetadataSearchCriteria($query, $searchQuery): void
    {
        $query->orWhere('authors', 'like', "%{$searchQuery}%")
              ->orWhere('theme', 'like', "%{$searchQuery}%")
              ->orWhere('key_people', 'like', "%{$searchQuery}%")
              ->orWhere('attributes_of_god', 'like', "%{$searchQuery}%")
              ->orWhere('key_words', 'like', "%{$searchQuery}%")
              ->orWhere('covenants', 'like', "%{$searchQuery}%")
              ->orWhere('key_teachings', 'like', "%{$searchQuery}%")
              ->orWhere('key_verses', 'like', "%{$searchQuery}%");
    }
}
