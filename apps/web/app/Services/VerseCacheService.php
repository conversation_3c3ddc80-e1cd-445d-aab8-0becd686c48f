<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class VerseCacheService
{
    // Cache for 1 year since content is static
    private const CACHE_TTL = 31536000; // 60 * 60 * 24 * 365
    private const CACHE_PREFIX = 'chapter:';

    /**
     * Get or set an entire chapter's content in cache
     *
     * @param string $bookSlug The book slug
     * @param int $chapter The chapter number
     * @param callable $callback The callback to generate the content if not in cache
     * @param array $options Options for including word data
     *   - bool $includeWords Whether to include the words array (default: false)
     *   - bool $includeWordGroups Whether to include word groups (default: true)
     *   - bool $includeFootnotes Whether to include footnotes (default: true)
     * @return array|null The cached or generated chapter data
     */
    public function getOrSetChapter(string $bookSlug, int $chapter, callable $callback, array $options = []): ?array
    {
        // Set default options
        $options = array_merge([
            'includeWords' => false,
            'includeWordGroups' => true,
            'includeFootnotes' => true,
        ], $options);

        // Generate a unique cache key based on the book, chapter, and options
        $key = $this->getCacheKey($bookSlug, $chapter, $options);

        return Cache::store('bible')->remember($key, self::CACHE_TTL, function () use ($callback) {
            try {
                return $callback();
            } catch (\Exception $e) {
                Log::error('Failed to cache chapter content', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                return null;
            }
        });
    }

    /**
     * Get multiple chapters at once
     *
     * @param string $bookSlug The book slug
     * @param array $chapters Array of chapter numbers
     * @param callable $callback The callback to generate missing chapters
     * @param array $options Options for including word data
     *   - bool $includeWords Whether to include the words array (default: false)
     *   - bool $includeWordGroups Whether to include word groups (default: true)
     *   - bool $includeFootnotes Whether to include footnotes (default: false)
     * @return array The cached and generated chapter data
     */
    public function getOrSetMultipleChapters(string $bookSlug, array $chapters, callable $callback, array $options = [])
    {
        $keys = array_map(
            fn($chapter) => $this->getCacheKey($bookSlug, $chapter, $options),
            $chapters
        );

        // Get all cached chapters
        $cached = Cache::store('bible')->many($keys);

        // Find missing chapters
        $missingChapters = [];
        foreach ($chapters as $chapter) {
            $key = $this->getCacheKey($bookSlug, $chapter);
            if (!isset($cached[$key])) {
                $missingChapters[] = $chapter;
            }
        }

        // Fetch missing chapters if any
        if (!empty($missingChapters)) {
            $newChapters = $callback($missingChapters);

            // Cache new chapters
            $toCache = [];
            foreach ($newChapters as $chapterNum => $content) {
                $key = $this->getCacheKey($bookSlug, $chapterNum);
                $toCache[$key] = $content;
            }

            if (!empty($toCache)) {
                Cache::store('bible')->putMany($toCache, self::CACHE_TTL);
                $cached = array_merge($cached, $toCache);
            }
        }

        return $cached;
    }

    /**
     * Invalidate chapter cache
     */
    public function invalidateChapter(string $bookSlug, int $chapter): void
    {
        $key = $this->getCacheKey($bookSlug, $chapter);
        Cache::store('bible')->forget($key);
    }

    /**
     * Invalidate book cache
     */
    public function invalidateBook(string $bookSlug): void
    {
        Cache::store('bible')->tags(["book:$bookSlug"])->flush();
    }

    /**
     * Get the cache key for a chapter
     *
     * @param string $bookSlug The book slug
     * @param int $chapter The chapter number
     * @param array $options Options that affect the cached content
     * @return string The cache key
     */
    private function getCacheKey(string $bookSlug, int $chapter, array $options = []): string
    {
        // Create a hash of the options to include in the cache key
        $optionsHash = '';
        if (!empty($options)) {
            // Only include options that affect the output
            $relevantOptions = [
                'includeWords' => $options['includeWords'] ?? false,
                'includeWordGroups' => $options['includeWordGroups'] ?? true,
                'includeFootnotes' => $options['includeFootnotes'] ?? true,
            ];
            $optionsHash = ':' . md5(json_encode($relevantOptions));
        }

        return self::CACHE_PREFIX . "{$bookSlug}:{$chapter}{$optionsHash}";
    }

    /**
     * Clear cache for a specific chapter
     *
     * @param string $bookSlug The book slug
     * @param int $chapter The chapter number
     * @return void
     */
    public function clearChapter(string $bookSlug, int $chapter): void
    {
        // Get all keys matching the pattern for this chapter
        $pattern = $this->getCacheKey($bookSlug, $chapter) . '*';
        $this->clearMatchingKeys($pattern);
    }

    /**
     * Clear cache for all chapters of a book
     *
     * @param string $bookSlug The book slug
     * @return void
     */
    public function clearBook(string $bookSlug): void
    {
        // Get all keys matching the pattern for this book
        $pattern = self::CACHE_PREFIX . "{$bookSlug}:*";
        $this->clearMatchingKeys($pattern);
    }

    /**
     * Clear all cache keys matching the given pattern
     *
     * @param string $pattern The pattern to match keys against
     * @return void
     */
    private function clearMatchingKeys(string $pattern): void
    {
        $store = Cache::store('bible');
        $prefix = $store->getPrefix();

        // Remove the prefix from the pattern as Redis stores keys without the Laravel prefix
        $pattern = str_replace($prefix, '', $pattern);

        // Use Redis SCAN to find and delete matching keys
        $redis = $store->connection();
        $iterator = null;

        do {
            // SCAN returns a new iterator and results
            $keys = $redis->scan($iterator, $pattern, 1000);

            // Delete the keys if any were found
            if (!empty($keys)) {
                $redis->del($keys);
            }
        } while ($iterator > 0);
    }
}
