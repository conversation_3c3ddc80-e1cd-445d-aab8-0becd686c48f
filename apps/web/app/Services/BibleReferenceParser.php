<?php

namespace App\Services;

use App\Models\Book;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class BibleReferenceParser
{
    /**
     * Parse a Bible reference string into its components
     *
     * @param string $reference The reference string (e.g., "2.Korinther3", "2Kor3,16", "Joh3:16-18")
     * @return array{book: ?Book, chapter: ?int, is_frontmatter: ?bool, verse_start: ?int, verse_end: ?int, verse_ranges: array}
     * @throws \Exception
     */
    public function parse(string $reference): array
    {
        Log::info('Parsing reference input', ['reference' => $reference]);

        $result = [
            'book' => null,
            'chapter' => null,
            'is_frontmatter' => null,
            'verse_start' => null,
            'verse_end' => null,
            'verse_ranges' => [], // New array to hold multiple verse ranges
        ];

        // Extract verse ranges if present (e.g., "3:16-18+20+22-24" or "3,16-18+20+22-24")
        if (preg_match('/[,:](.+)$/', $reference, $matches)) {
            $verseRanges = explode('+', $matches[1]);

            foreach ($verseRanges as $range) {
                if (preg_match('/^(\d+)(?:-(\d+))?$/', $range, $rangeMatches)) {
                    $result['verse_ranges'][] = [
                        'start' => (int)$rangeMatches[1],
                        'end' => isset($rangeMatches[2]) ? (int)$rangeMatches[2] : (int)$rangeMatches[1]
                    ];
                } else {
                    throw new \Exception('Invalid verse range format');
                }
            }

            // For backward compatibility and simpler access, set verse_start and verse_end
            // to the first and last verse in the ranges
            if (!empty($result['verse_ranges'])) {
                $result['verse_start'] = $result['verse_ranges'][0]['start'];
                $lastRange = end($result['verse_ranges']);
                $result['verse_end'] = $lastRange['end'];
            }

            // Remove verse part from reference
            $reference = preg_replace('/[,:].+$/', '', $reference);
        }

        // Extract chapter if present at the end of the string
        if (preg_match('/(\d+)$/', $reference, $matches)) {
            $result['chapter'] = (int)$matches[1];
            // Remove chapter number from reference
            $reference = preg_replace('/\d+$/', '', $reference);
        }

        // Clean up the book name
        $bookName = trim($reference);
        $result['book'] = $this->findBook($bookName);

        if (!$result['book']) {
            throw new \Exception('Book not found');
        }

        // Validate chapter number
        if ($result['chapter'] !== null) {
            if ($result['chapter'] < 1 || $result['chapter'] > $result['book']->chapters_count) {
                throw new \Exception('Invalid chapter number');
            }
        }

        // Validate verse ranges
        if (!empty($result['verse_ranges'])) {
            foreach ($result['verse_ranges'] as $range) {
                if ($range['start'] < 1) {
                    throw new \Exception('Invalid verse number');
                }
                if ($range['end'] < $range['start']) {
                    throw new \Exception('Invalid verse range: end verse must be greater than or equal to start verse');
                }
            }
        }

        $result['is_frontmatter'] = !$result['chapter'];

        Log::info('Parsed reference', [
            'book' => $result['book']?->name,
            'chapter' => $result['chapter'],
            'is_frontmatter' => $result['is_frontmatter'],
            'verse_start' => $result['verse_start'],
            'verse_end' => $result['verse_end'],
            'verse_ranges' => $result['verse_ranges']
        ]);

        return $result;
    }

    /**
     * Find a book by its name or variation
     *
     * @param string $bookName
     * @return Book|null
     */
    protected function findBook(string $bookName): ?Book
    {
        // Normalize the book name
        $bookName = $this->normalizeBookName($bookName);

        Log::info('Finding book', [
            'original_name' => $bookName,
            'normalized_name' => $bookName
        ]);

        // Try direct match first
        $book = Book::where('name', $bookName)
            ->orWhere('abbreviation', $bookName)
            ->orWhere('slug', $bookName)
            ->first();

        if ($book) {
            Log::info('Found book by direct match', ['book' => $book->name]);
            return $book;
        }

        // Try to find the book using variations
        $bookId = DB::table('book_name_variations')
            ->where(function($query) use ($bookName) {
                $query->where('variation', $bookName)
                    ->orWhere('variation', str_replace('.', '. ', $bookName)) // Try with space after dot
                    ->orWhere('variation', str_replace('. ', '.', $bookName)) // Try without space after dot
                    ->orWhere('variation', str_replace(' ', '', $bookName)); // Try without any spaces
            })
            ->value('book_id');

        if ($bookId) {
            $book = Book::find($bookId);
            Log::info('Found book by variation', ['book' => $book->name]);
            return $book;
        }

        // If no exact match, try fuzzy matching
        $bookId = DB::table('book_name_variations')
            ->where(function($query) use ($bookName) {
                $query->where('variation', 'like', '%' . $bookName . '%')
                    ->orWhere('variation', 'like', '%' . str_replace('.', '. ', $bookName) . '%')
                    ->orWhere('variation', 'like', '%' . str_replace('. ', '.', $bookName) . '%')
                    ->orWhere('variation', 'like', '%' . str_replace(' ', '', $bookName) . '%');
            })
            ->value('book_id');

        if ($bookId) {
            $book = Book::find($bookId);
            Log::info('Found book by fuzzy match', ['book' => $book->name]);
            return $book;
        }

        Log::warning('Book not found', [
            'name' => $bookName,
            'normalized_name' => $this->normalizeBookName($bookName)
        ]);
        return null;
    }

    /**
     * Normalize a book name for searching
     *
     * @param string $bookName
     * @return string
     */
    protected function normalizeBookName(string $bookName): string
    {
        // Remove any trailing dots and spaces
        $bookName = trim($bookName, '. ');

        // Normalize spaces around dots
        $bookName = preg_replace('/\s*\.\s*/', '.', $bookName);

        // Remove any double spaces
        $bookName = preg_replace('/\s+/', ' ', $bookName);

        return $bookName;
    }

    /**
     * Format a reference for display
     *
     * @param Book $book
     * @param int $chapter
     * @param int|null $verseStart
     * @param int|null $verseEnd
     * @return string
     */
    public function format(Book $book, int $chapter, ?int $verseStart = null, ?int $verseEnd = null): string
    {
        $reference = $book->name . ' ' . $chapter;

        if ($verseStart !== null) {
            $reference .= ',' . $verseStart;
            if ($verseEnd !== null && $verseEnd !== $verseStart) {
                $reference .= '-' . $verseEnd;
            }
        }

        return $reference;
    }

    /**
     * Validate if a chapter exists in a book
     *
     * @param Book $book
     * @param int $chapter
     * @return bool
     */
    public function isValidChapter(Book $book, int $chapter): bool
    {
        return $chapter >= 1 && $chapter <= $book->chapters_count;
    }

    /**
     * Validate if verses exist in a chapter
     *
     * @param Book $book
     * @param int $chapter
     * @param int $verseStart
     * @param int|null $verseEnd
     * @return bool
     */
    public function areValidVerses(Book $book, int $chapter, int $verseStart, ?int $verseEnd = null): bool
    {
        $chapterModel = $book->chapters()->where('number', $chapter)->first();
        if (!$chapterModel) {
            return false;
        }

        $maxVerse = $chapterModel->verses()->max('number');
        return $verseStart >= 1 && $verseStart <= $maxVerse &&
               ($verseEnd === null || ($verseEnd >= $verseStart && $verseEnd <= $maxVerse));
    }
}
