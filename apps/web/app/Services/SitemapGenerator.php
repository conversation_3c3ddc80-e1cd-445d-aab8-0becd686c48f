<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\Book;
use App\Models\Chapter;
use Carbon\Carbon;
use Illuminate\Support\Facades\File;
use DOMDocument;

class SitemapGenerator
{
    /**
     * Base URL for the website
     */
    protected string $baseUrl;

    /**
     * Path to the sitemap file
     */
    protected string $sitemapPath;

    /**
     * Constructor
     */
    public function __construct(string $baseUrl = 'https://esrabibel.de')
    {
        $this->baseUrl = rtrim($baseUrl, '/');
        $this->sitemapPath = public_path('sitemap.xml');
    }

    /**
     * Generate a complete sitemap with all Bible books and chapters
     *
     * @return bool True if sitemap was generated successfully
     */
    public function generateCompleteSitemap(): bool
    {
        $dom = new DOMDocument('1.0', 'UTF-8');
        $dom->formatOutput = true;

        // Create root element
        $urlset = $dom->createElement('urlset');
        $urlset->setAttribute('xmlns', 'http://www.sitemaps.org/schemas/sitemap/0.9');
        $dom->appendChild($urlset);

        // Add homepage
        $this->addUrl($dom, $urlset, [
            'loc' => $this->baseUrl . '/',
            'lastmod' => Carbon::now()->format('Y-m-d'),
            'changefreq' => 'weekly',
            'priority' => '1.0'
        ]);
        
        // Add available-books page
        $this->addUrl($dom, $urlset, [
            'loc' => $this->baseUrl . '/available-books',
            'lastmod' => Carbon::now()->format('Y-m-d'),
            'changefreq' => 'weekly',
            'priority' => '0.8'
        ]);

        // Add all books with content
        $books = Book::whereHas('chapters', function ($query) {
            $query->whereHas('verses');
        })->get();

        foreach ($books as $book) {
            // Add book chapters
            $chapters = Chapter::where('book_id', $book->id)
                ->whereHas('verses')
                ->orderBy('number')
                ->get();

            foreach ($chapters as $chapter) {
                $this->addUrl($dom, $urlset, [
                    'loc' => $this->baseUrl . '/' . $book->slug . $chapter->number,
                    'lastmod' => Carbon::now()->format('Y-m-d'),
                    'changefreq' => 'weekly',
                    'priority' => '0.8'
                ]);
            }
        }

        // Save the sitemap
        return $dom->save($this->sitemapPath) !== false;
    }

    /**
     * Update the sitemap with a specific book's chapters
     *
     * @param Book $book The book to add/update in the sitemap
     * @return bool True if sitemap was updated successfully
     */
    public function updateSitemapWithBook(Book $book): bool
    {
        // If sitemap doesn't exist, generate a complete one
        if (!File::exists($this->sitemapPath)) {
            return $this->generateCompleteSitemap();
        }

        try {
            // Load existing sitemap
            $dom = new DOMDocument('1.0', 'UTF-8');
            $dom->preserveWhiteSpace = false;
            $dom->formatOutput = true;
            $dom->load($this->sitemapPath);

            $urlset = $dom->documentElement;

            // Get all chapters for this book
            $chapters = Chapter::where('book_id', $book->id)
                ->whereHas('verses')
                ->orderBy('number')
                ->get();

            // Check if book chapters already exist in sitemap
            $existingUrls = [];
            $urls = $dom->getElementsByTagName('url');

            foreach ($urls as $url) {
                $locElements = $url->getElementsByTagName('loc');
                if ($locElements->length > 0) {
                    $existingUrls[] = $locElements->item(0)->nodeValue;
                }
            }

            // Add missing chapters
            $updated = false;
            foreach ($chapters as $chapter) {
                $chapterUrl = $this->baseUrl . '/' . $book->slug . $chapter->number;

                if (!in_array($chapterUrl, $existingUrls)) {
                    // Add new chapter URL
                    $this->addUrl($dom, $urlset, [
                        'loc' => $chapterUrl,
                        'lastmod' => Carbon::now()->format('Y-m-d'),
                        'changefreq' => 'weekly',
                        'priority' => '0.8'
                    ]);
                    $updated = true;
                } else {
                    // Update lastmod date for existing chapter URL
                    $xpath = new \DOMXPath($dom);
                    // Register the namespace to make XPath work correctly
                    $xpath->registerNamespace('s', 'http://www.sitemaps.org/schemas/sitemap/0.9');
                    
                    // Use the namespace in the query and escape the URL properly
                    $escapedUrl = htmlspecialchars($chapterUrl, ENT_QUOTES);
                    $urlNodes = $xpath->query("//s:url[s:loc='$escapedUrl']");
                    
                    if ($urlNodes->length > 0) {
                        $urlNode = $urlNodes->item(0);
                        $lastmodNodes = $xpath->query('s:lastmod', $urlNode);
                        
                        if ($lastmodNodes->length > 0) {
                            $lastmodNode = $lastmodNodes->item(0);
                            $lastmodNode->nodeValue = Carbon::now()->format('Y-m-d');
                            $updated = true;
                        }
                    }
                }
            }

            // Only save if changes were made
            if ($updated) {
                return $dom->save($this->sitemapPath) !== false;
            }

            return true;
        } catch (\Exception $e) {
            // If there's an error, regenerate the complete sitemap
            return $this->generateCompleteSitemap();
        }
    }

    /**
     * Add a URL to the sitemap
     *
     * @param DOMDocument $dom The DOM document
     * @param \DOMElement $urlset The urlset element
     * @param array $urlData URL data (loc, lastmod, changefreq, priority)
     * @return void
     */
    protected function addUrl(DOMDocument $dom, \DOMElement $urlset, array $urlData): void
    {
        $url = $dom->createElement('url');

        foreach ($urlData as $key => $value) {
            $element = $dom->createElement($key, $value);
            $url->appendChild($element);
        }

        $urlset->appendChild($url);
    }
}
