<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\Book;
use Illuminate\Support\Facades\Log;

class FootnoteReferenceService
{
    public function __construct(
        private readonly BibleReferenceService $bibleReferenceService,
        private readonly BibleReferenceParser $bibleReferenceParser
    ) {}
    /**
     * Process content structure and resolve references
     *
     * @param array $contentStructure
     * @param string $currentBookSlug
     * @return array
     */
    /**
     * Process content structure and resolve references
     *
     * @param array $contentStructure The content structure to process
     * @param string $currentBookSlug The current book slug for context
     * @param string|null $currentChapter The current chapter for verse-only references
     * @return array Processed content structure with resolved references
     */
    public function processContentStructure(
        array $contentStructure,
        string $currentBookSlug,
        ?string $currentChapter = null
    ): array {
        if (!isset($contentStructure['elements']) || !is_array($contentStructure['elements'])) {
            return $contentStructure;
        }

        $currentBookContext = $currentBookSlug;

        foreach ($contentStructure['elements'] as &$element) {
            if ($element['type'] === 'ref' && !empty($element['content'])) {
                $element = $this->processReferenceElement($element, $currentBookContext, $currentChapter);

                // Update book context if this reference contains a book abbreviation
                if (preg_match('/^([1-3]?[A-Za-zäöüÄÖÜß]+\.?)\s*\d+[,\s]+\d+/', $element['content'], $matches)) {
                    $bookAbbr = $matches[1];
                    $book = $this->findBookByAbbreviation($bookAbbr);
                    if ($book) {
                        $currentBookContext = $book->slug;
                    }
                }
            }
        }

        return $contentStructure;
    }

    /**
     * Process a single reference element
     */
    /**
     * Process a single reference element, handling various formats
     * Examples:
     * - Simple verse: "V. 30"
     * - Chapter and verse: "4,32" or "4.32"
     * - Book with chapter and verse: "Lk 7,25"
     * - Multiple references: "Lk 7,25; 13,17; 1Kor 4,10"
     */
    protected function processReferenceElement(array $element, string $currentBookSlug, ?string $currentChapter = null): array
    {
        $refStr = trim($element['content']);

        // If the reference contains semicolons, process each part separately
        if (str_contains($refStr, ';')) {
            return $this->processMultipleReferences($element, $refStr, $currentBookSlug, $currentChapter);
        }

        // 1. Try to match full reference with book (e.g., "Joh3,16", "1Kor 5,20", "2 Tim 3,13", "1Thes 5,9")
        if (preg_match('/^([1-3]?\s*[A-Za-zäöüÄÖÜß]+)(?:\.|\s+)?(\d+)[,\s]+(\d+)$/i', $refStr, $matches)) {
            // Clean up the book abbreviation (remove all whitespace and ensure proper formatting)
            $bookAbbr = preg_replace('/\s+/', '', $matches[1]); // Remove all whitespace from the book part
            
            // Log the match for debugging
            \Log::debug('Matched reference', [
                'original' => $refStr,
                'book_abbr' => $bookAbbr,
                'chapter' => $matches[2],
                'verse' => $matches[3]
            ]);
            $chapter = $matches[2];
            $verse = $matches[3];

            if ($book = $this->findBookByAbbreviation($bookAbbr)) {
                $element['href'] = "/{$book->slug}{$chapter},{$verse}";
                $element['title'] = "{$book->name} {$chapter},{$verse}";
                return $element;
            }
        }

        // 2. Try to match chapter and multiple verses in current book (e.g., "12,4.6.23" or "3,16")
        if (preg_match('/^(\d+)[,\s]+(\d+(?:\.\d+)*)$/', $refStr, $matches)) {
            $chapter = $matches[1];
            $verses = explode('.', $matches[2]);

            // If there's only one verse, create a simple link
            if (count($verses) === 1) {
                $verse = $verses[0];
                $element['href'] = "/{$currentBookSlug}{$chapter},{$verse}";
                $book = Book::where('slug', $currentBookSlug)->first();
                $element['title'] = $book ? "{$book->name} {$chapter},{$verse}" : $refStr;
                return $element;
            }

            // For multiple verses, create a group of links
            $book = Book::where('slug', $currentBookSlug)->first();
            $bookName = $book ? $book->name : '';

            $elements = [];
            foreach ($verses as $index => $verse) {
                if (empty($verse)) continue;

                $verseElement = $element;
                $verseElement['content'] = $verse;
                $verseElement['href'] = "/{$currentBookSlug}{$chapter},{$verse}";
                $verseElement['title'] = "{$bookName} {$chapter},{$verse}";
                $elements[] = $verseElement;

                // Add period between verses (except after the last one)
                if ($index < count($verses) - 1) {
                    $elements[] = [
                        'type' => 'text',
                        'content' => '.',
                    ];
                }
            }

            // If we have multiple verses, wrap them in a group
            if (count($elements) > 1) {
                return [
                    'type' => 'group',
                    'elements' => $elements,
                    'title' => "{$bookName} {$chapter}," . implode(',', $verses),
                ];
            }

            return $elements[0] ?? $element;
        }

        // 3. Try to match single verse in current book and chapter (e.g., "16")
        if (preg_match('/^(\d+)$/', $refStr, $matches) && $currentChapter !== null) {
            $verse = $matches[1];

            $element['href'] = "/{$currentBookSlug}{$currentChapter},{$verse}";
            $book = Book::where('slug', $currentBookSlug)->first();
            $element['title'] = $book ? "{$book->name} {$currentChapter},{$verse}" : $refStr;
            return $element;
        }

        // 4. Handle verse-only references (e.g., "V. 30") as fallback
        if (preg_match('/^V\.?\s*(\d+)$/i', $refStr, $matches) && $currentChapter !== null) {
            $verse = $matches[1];
            $element['href'] = "/{$currentBookSlug}{$currentChapter},{$verse}";
            $book = Book::where('slug', $currentBookSlug)->first();
            $element['title'] = $book ? "{$book->name} {$currentChapter},{$verse}" : $refStr;
            return $element;
        }

        // Handle verse-only references (e.g., "V. 30", "siehe 4,32", "s. 4,32", "S. 4,32")
        if (preg_match('/^(?:V\.?|siehe|s\.?|S\.?)\s*(\d+)(?:[,\s]+(\d+))?/i', $refStr, $matches)) {
            $verse = $matches[1];
            $chapter = isset($matches[2]) ? $matches[2] : null;

            if ($chapter !== null) {
                // Handle format like "4,32" (chapter,verse) - swap them
                $temp = $verse;
                $verse = $chapter;
                $chapter = $temp;

                $element['href'] = "/{$currentBookSlug}{$chapter},{$verse}";
                $book = Book::where('slug', $currentBookSlug)->first();
                $element['title'] = $book ? "{$book->name} {$chapter},{$verse}" : $refStr;
            } else {
                // Handle format like "V. 30" (verse only)
                $element['href'] = "/{$currentBookSlug},{$verse}";
                $book = Book::where('slug', $currentBookSlug)->first();
                $element['title'] = $book ? "{$book->name} {$verse}" : $refStr;
            }

            return $element;
        }

        // Try to parse the reference using BibleReferenceService first
        $result = $this->bibleReferenceService->parseReference($refStr);

        if ($result) {
            $element['href'] = $result->url;
            $element['title'] = $result->book->name . ' ' . $result->chapter .
                               ($result->verse ? ',' . $result->verse : '');
            return $element;
        }

        // Fall back to BibleReferenceParser for more complex references
        try {
            $parsed = $this->bibleReferenceParser->parse($refStr);

            if ($parsed['book']) {
                $url = "/{$parsed['book']->slug}{$parsed['chapter']}";

                if ($parsed['verse_start']) {
                    $url .= ",{$parsed['verse_start']}";

                    if ($parsed['verse_end'] && $parsed['verse_end'] !== $parsed['verse_start']) {
                        $url .= "-{$parsed['verse_end']}";
                    }
                }

                $element['href'] = $url;
                $element['title'] = $parsed['book']->name . ' ' . $parsed['chapter'];

                if ($parsed['verse_start']) {
                    $element['title'] .= ",{$parsed['verse_start']}";

                    if ($parsed['verse_end'] && $parsed['verse_end'] !== $parsed['verse_start']) {
                        $element['title'] .= "-{$parsed['verse_end']}";
                    }
                }
            }
        } catch (\Exception $e) {
            Log::warning('Failed to parse reference', [
                'reference' => $refStr,
                'error' => $e->getMessage(),
            ]);
        }

        return $element;
    }

    /**
     * Process a string containing multiple references separated by semicolons
     */
    protected function processMultipleReferences(array $element, string $refStr, string $currentBookSlug, ?string $currentChapter = null): array
    {
        $parts = array_map('trim', explode(';', $refStr));
        $result = [];
        $currentBookSlug = $currentBookSlug;

        foreach ($parts as $i => $part) {
            if (empty($part)) continue;

            $refElement = $element;
            $refElement['content'] = $part;

            // Process this part as a regular reference
            $processed = $this->processReferenceElement($refElement, $currentBookSlug, $currentChapter);

            // If this part contains a book abbreviation, update the current book context
            $bookMatch = [];
            if (preg_match('/^([1-3]?[A-Za-zäöüÄÖÜß]+\.?)\s*(\d+)[,\s]+(\d+)/', $part, $bookMatch)) {
                $bookAbbr = $bookMatch[1];
                $book = $this->findBookByAbbreviation($bookAbbr);
                if ($book) {
                    $currentBookSlug = $book->slug;
                    // Update chapter context if a full reference is found
                    $currentChapter = $bookMatch[2];
                }
            }

            $result[] = $processed;

            // Add semicolon between references (except after the last one)
            if ($i < count($parts) - 1) {
                $result[] = [
                    'type' => 'text',
                    'content' => '; ',
                ];
            }
        }

        // If we have multiple results, return them as a group
        if (count($result) > 1) {
            return [
                'type' => 'group',
                'elements' => $result,
            ];
        }

        return $result[0] ?? $element;
    }

    /**
     * Find a book by its abbreviation, reference, or name
     * Handles various formats like '2Tim', '1Kor', '1. Tim', '1 Tim', '1Thes', etc.
     */
    protected function findBookByAbbreviation(string $abbreviation): ?Book
    {
        $abbreviation = trim($abbreviation);
        $originalAbbreviation = $abbreviation;

        // Log the attempt to find a book
        \Log::debug('Looking up book abbreviation', [
            'abbreviation' => $abbreviation,
            'original' => $originalAbbreviation
        ]);

        // Try to find using the Book model's findByReference method first
        // This handles numbered books and various abbreviation formats
        if ($book = Book::findByReference($abbreviation)) {
            \Log::debug('Found book via findByReference', [
                'abbreviation' => $abbreviation,
                'book' => $book->name,
                'slug' => $book->slug
            ]);
            return $book;
        }

        // Handle common abbreviations that might not be in the database
        $commonAbbreviations = [
            '1thes' => '1thess',
            '2thes' => '2thess',
            '1tim' => '1tim',
            '2tim' => '2tim',
            '1kor' => '1kor',
            '2kor' => '2kor',
            // Add more as needed
        ];

        $lowerAbbr = strtolower($abbreviation);
        if (isset($commonAbbreviations[$lowerAbbr])) {
            $normalizedAbbr = $commonAbbreviations[$lowerAbbr];
            $book = Book::where('slug', $normalizedAbbr)->first();
            if ($book) {
                \Log::debug('Found book via common abbreviations', [
                    'abbreviation' => $abbreviation,
                    'normalized' => $normalizedAbbr,
                    'book' => $book->name,
                    'slug' => $book->slug
                ]);
                return $book;
            }
        }


        // Try exact match for abbreviation or name
        $book = Book::query()
            ->where('abbreviation', $abbreviation)
            ->orWhere('name', $abbreviation)
            ->first();

        if ($book) {
            \Log::debug('Found book via exact match', [
                'abbreviation' => $abbreviation,
                'book' => $book->name,
                'slug' => $book->slug
            ]);
            return $book;
        }

        // Try case-insensitive search as a fallback
        $book = Book::query()
            ->whereRaw('LOWER(abbreviation) = ?', [strtolower($abbreviation)])
            ->orWhereRaw('LOWER(name) = ?', [strtolower($abbreviation)])
            ->first();

        if ($book) {
            \Log::debug('Found book via case-insensitive match', [
                'abbreviation' => $abbreviation,
                'book' => $book->name,
                'slug' => $book->slug
            ]);
        } else {
            \Log::warning('Could not find book for abbreviation', [
                'abbreviation' => $abbreviation,
                'original' => $originalAbbreviation
            ]);
        }

        return $book;
    }
}
