<?php

namespace App\Services;

use PhpOffice\PhpWord\Element\TextRun;

class TextFormatter
{
    /**
     * Get formatting information for a text element
     *
     * @param PhpOffice\PhpWord\Element\Text|string $element The element to analyze
     * @return array Formatting information
     */
    public function getFormatting($element): array
    {
        $formatting = [
            'is_bold' => false,
            'is_italic' => false,
            'is_underlined' => false,
            'is_superscript' => false,
            'is_subscript' => false,
            'is_strikethrough' => false,
            'is_smallcaps' => false,
            'is_emphasized' => false,
            'has_footnote' => false,
            'has_variant' => false,
            'is_ot_quote' => false,
            'has_ellipsis' => false,
            'font_size' => null,
            'font_family' => null,
            'text_color' => null,
            'background_color' => null,
            'metadata' => [
                'non_original_words' => []
            ]
        ];

        // If element is a PhpWord element
        if (is_object($element) && method_exists($element, 'getFontStyle')) {
            $fontStyle = $element->getFontStyle();
            $formatting['is_bold'] = $fontStyle->isBold();
            $formatting['is_italic'] = $fontStyle->isItalic();
            $formatting['is_underlined'] = $fontStyle->getUnderline() !== 'none';
            $formatting['is_superscript'] = $fontStyle->isSuperScript();
            $formatting['is_subscript'] = $fontStyle->isSubScript();
            $formatting['is_strikethrough'] = $fontStyle->isStrikethrough();
            $formatting['is_smallcaps'] = $fontStyle->isSmallCaps();
            $formatting['font_size'] = $fontStyle->getSize();
            $formatting['font_family'] = $fontStyle->getName();
            $formatting['text_color'] = $fontStyle->getColor();
            $formatting['background_color'] = $fontStyle->getFgColor();
            
            // Get text content if available
            $text = method_exists($element, 'getText') ? $element->getText() : '';
        } else {
            $text = (string)$element;
        }

        // Process text-based formatting
        $formatting['has_variant'] = $this->hasFootnoteMarker($text);
        $formatting['is_ot_quote'] = $this->isDirectQuote($text);
        $formatting['has_ellipsis'] = $this->hasEllipsis($text);
        $formatting['metadata']['non_original_words'] = $this->extractNonOriginalWords($text);

        // Determine if text is emphasized based on formatting
        $formatting['is_emphasized'] = $formatting['is_bold'] || 
                                     $formatting['is_italic'] || 
                                     $formatting['is_underlined'] ||
                                     $formatting['is_smallcaps'];

        return $formatting;
    }

    public function isItalic(TextRun $element): bool
    {
        return $element->getFontStyle()->isItalic();
    }

    public function isBold(TextRun $element): bool
    {
        return $element->getFontStyle()->isBold();
    }

    public function isAllCaps(TextRun $element): bool
    {
        return $element->getFontStyle()->isSmallCaps();
    }

    public function extractNonOriginalWords(string $text): array
    {
        preg_match_all('/$$(.*?)$$/', $text, $matches);
        return $matches[1] ?? [];
    }

    public function hasFootnoteMarker(string $text): bool
    {
        return preg_match('/$$Textvariante:(.*?)$$/', $text);
    }

    public function extractFootnoteContent(string $text): string
    {
        preg_match('/$$Textvariante:(.*?)$$/', $text, $matches);
        return $matches[1] ?? '';
    }

    public function isDirectQuote(string $text): bool
    {
        return preg_match('/»(.*?)«/', $text);
    }

    public function hasEllipsis(string $text): bool
    {
        return strpos($text, '–') !== false;
    }
}
