<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\BookController;
use App\Http\Controllers\Api\BibleTextController;
use App\Http\Controllers\Api\BookSearchController;
use App\Http\Controllers\Api\SearchController;
use App\Http\Controllers\FileUploadController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Book and chapter related API routes
Route::get('/chapters/fetch', [BookController::class, 'fetchAdditionalChapters'])
    ->name('api.chapters.fetch');

Route::get('/books', [BookController::class, 'fetchAllBooks'])
    ->name('api.books');

Route::get('/books/recent', [BookController::class, 'getRecentBooks'])
    ->name('api.books.recent');

Route::get('/books/content-status', [BookController::class, 'getContentStatus'])
    ->name('api.books.content-status');

Route::get('/books/{slug}', [BookController::class, 'getBook'])
    ->name('api.books.show');

Route::get('/chapters/{reference}/adjacent', [BookController::class, 'getAdjacentChapters'])
    ->name('api.chapters.adjacent')
    ->where('reference', '[\w\d,\-]+');

Route::get('/search/books', BookSearchController::class)
    ->name('api.search.books');

// Search API routes
Route::get('/search', [SearchController::class, 'search'])
    ->name('api.search');

// File upload API route
Route::post('/upload', [FileUploadController::class, 'upload'])
    ->name('api.file.upload');

// Bible text API routes
Route::get('/bible/{reference}', [BibleTextController::class, 'getText'])
    ->name('api.bible.text')
    ->where('reference', '[\w\d,\-]+');
