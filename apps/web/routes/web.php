<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\BibleImportController;
use App\Http\Controllers\FileUploadController;
use App\Http\Controllers\SearchController;
use App\Http\Controllers\BookController;
use App\Http\Controllers\ChangelogController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use App\Models\Book;
use App\Models\Chapter;
use App\Models\Verse;
use App\Models\Word;
use App\Models\Footnote;

// Public routes
Route::get('/', function () {
    return Inertia::render('Start');
});

Route::get('/available-books', function () {
    return Inertia::render('AvailableBooks');
})->name('available-books');

// Add this route for the bookmarks page
Route::get('/bookmarks', function () {
    return Inertia::render('Bookmarks');
})->name('bookmarks');

// Search routes
Route::get('/search', SearchController::class)->name('search.index');
Route::match(['get', 'post'], '/search/{query?}/{page?}', SearchController::class)
    ->name('search.query');
Route::post('/search/settings', [SearchController::class, 'saveSettings'])->name('search.settings');

// Admin routes
Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('/dashboard', function () {
        return Inertia::render('Dashboard');
    })->name('dashboard');

    Route::get('/import-bible', [BibleImportController::class, 'index'])
        ->name('bible.import');
    Route::post('/import-bible', [BibleImportController::class, 'import'])
        ->name('bible.import.store');

    Route::post('/api/upload', [FileUploadController::class, 'upload'])
        ->name('file.upload');

    Route::get('/profile', [ProfileController::class, 'edit'])
        ->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])
        ->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])
        ->name('profile.destroy');
});

// Changelog routes
Route::get('/changelog', [ChangelogController::class, 'index'])->name('changelog.index');
Route::get('/api/changelog', [ChangelogController::class, 'data'])->name('changelog.data');

// Bible reference routes - place after other specific routes
Route::controller(BookController::class)->group(function () {
    // Catch-all Bible reference route last
    Route::get('/{reference}', 'show')
        ->name('books.show')
        ->where('reference', '[a-zA-Z0-9äöüÄÖÜß\.,\-\+]+'); // Allow Latin chars, German umlauts, digits, periods, commas, hyphens, and plus signs
});

// Fallback route for 404s
Route::fallback(function () {
    return Inertia::render('NotFound', [
        'requestedPath' => request()->path()
    ])->toResponse(request())->setStatusCode(404);
});

require __DIR__.'/auth.php';
