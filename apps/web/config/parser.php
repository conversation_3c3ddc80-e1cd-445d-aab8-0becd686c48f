<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Bible Data Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration options for Bible data parsing and
    | processing. These settings control how the USX parser behaves,
    | memory management, batch processing, and more.
    |
    */

    'parsing' => [
        /*
        |--------------------------------------------------------------------------
        | Batch Processing
        |--------------------------------------------------------------------------
        |
        | These settings control how data is processed in batches to improve
        | performance and memory usage.
        |
        */
        
        // Number of items to process in a single batch
        'batch_size' => env('BIBLE_BATCH_SIZE', 500),
        
        // Number of operations per database transaction
        'transaction_size' => env('BIBLE_TRANSACTION_SIZE', 100),
        
        /*
        |--------------------------------------------------------------------------
        | Memory Management
        |--------------------------------------------------------------------------
        |
        | These settings control memory usage and garbage collection during
        | parsing of large Bible files.
        |
        */
        
        // Memory limit for parsing (used for ini_set)
        'memory_limit' => env('BIBLE_MEMORY_LIMIT', '1G'),
        
        // Memory threshold for triggering garbage collection (in bytes)
        'memory_threshold' => env('BIBLE_MEMORY_THRESHOLD', 100 * 1024 * 1024), // 100 MB
        
        // How often to check memory usage (operations)
        'memory_check_interval' => env('BIBLE_MEMORY_CHECK_INTERVAL', 1000),
        
        /*
        |--------------------------------------------------------------------------
        | Error Handling and Recovery
        |--------------------------------------------------------------------------
        |
        | These settings control how the parser handles errors and attempts
        | to recover from them.
        |
        */
        
        // Maximum number of retry attempts for parsing errors
        'retry_attempts' => env('BIBLE_RETRY_ATTEMPTS', 3),
        
        // Delay between retry attempts (seconds)
        'retry_delay' => env('BIBLE_RETRY_DELAY', 1),
        
        /*
        |--------------------------------------------------------------------------
        | Parsing Options
        |--------------------------------------------------------------------------
        |
        | These settings control various aspects of the parsing process.
        |
        */
        
        // Whether to enable debug mode for more verbose logging
        'debug' => env('BIBLE_PARSING_DEBUG', false),
        
        // Allowed paragraph styles
        'allowed_styles' => [
            'p',   // Normal paragraph
            'q',   // Poetry, level 1
            'q1',  // Poetry, level 1 (equivalent to q)
            'q2',  // Poetry, level 2
            'q3',  // Poetry, level 3
            'qr',  // Right-aligned poetic line
            'qc',  // Centered poetic line
            'pi',  // Indented paragraph
            'pi1', // Indented paragraph, level 1
            'pi2', // Indented paragraph, level 2
            'li',  // List item
            'li1', // List item, level 1
            'li2', // List item, level 2
            'm',   // Margin paragraph
            'mi',  // Indented margin paragraph
            'pc',  // Centered paragraph
            'pr',  // Right-aligned paragraph
            'ph',  // Hanging paragraph
            'cls', // Closure
        ],
        
        /*
        |--------------------------------------------------------------------------
        | Character Styles
        |--------------------------------------------------------------------------
        |
        | These settings define how different character styles in USX are
        | interpreted and processed.
        |
        */
        
        'char_styles' => [
            // Special text
            'add' => ['type' => 'editorial', 'subtype' => 'addition'], // Word not in original text
            'bk' => ['type' => 'reference', 'subtype' => 'book'],
            'dc' => ['type' => 'special', 'subtype' => 'deuterocanonical'],
            'ior' => ['type' => 'reference', 'subtype' => 'published'],
            'iqt' => ['type' => 'quote', 'subtype' => 'introduction'],
            'k' => ['type' => 'keyword', 'subtype' => 'keyword'],
            'nd' => ['type' => 'name', 'subtype' => 'divine'],
            'ord' => ['type' => 'number', 'subtype' => 'ordinal'],
            'pn' => ['type' => 'name', 'subtype' => 'proper'],
            'png' => ['type' => 'name', 'subtype' => 'geographic'],
            'qac' => ['type' => 'special', 'subtype' => 'acrostic'],
            'qs' => ['type' => 'quote', 'subtype' => 'selah'],
            'qt' => ['type' => 'quote', 'subtype' => 'quoted'],
            'rq' => ['type' => 'reference', 'subtype' => 'inline'],
            'sig' => ['type' => 'special', 'subtype' => 'signature'],
            'sls' => ['type' => 'special', 'subtype' => 'selah'],
            'tl' => ['type' => 'foreign', 'subtype' => 'transliterated'],
            'wj' => ['type' => 'quote', 'subtype' => 'words-of-jesus'],

            // Character formatting
            'em' => ['type' => 'format', 'subtype' => 'emphasis'],
            'bd' => ['type' => 'format', 'subtype' => 'bold'],
            'it' => ['type' => 'format', 'subtype' => 'italic'],
            'bdit' => ['type' => 'format', 'subtype' => 'bold-italic'],
            'no' => ['type' => 'format', 'subtype' => 'normal'],
            'sc' => ['type' => 'format', 'subtype' => 'small-caps'],
            'sup' => ['type' => 'format', 'subtype' => 'superscript'],

            // Special features
            'rb' => ['type' => 'ruby', 'subtype' => 'base'],
            'rt' => ['type' => 'ruby', 'subtype' => 'gloss'],
            'cat' => ['type' => 'category', 'subtype' => 'category'],

            // Variants
            'va' => ['type' => 'variant', 'subtype' => 'alternate'], // Text variants
            'vp' => ['type' => 'variant', 'subtype' => 'primary'], // Primary variant

            // Notes
            'fm' => ['type' => 'note', 'subtype' => 'footnote-marker'],
            'xm' => ['type' => 'note', 'subtype' => 'cross-reference-marker'],

            // Special text
            'fig' => ['type' => 'figure', 'subtype' => 'figure'],
            'ndx' => ['type' => 'index', 'subtype' => 'subject-index'],
            'pro' => ['type' => 'pronunciation', 'subtype' => 'pronunciation'],
            'w' => ['type' => 'word', 'subtype' => 'wordlist'],
            'wg' => ['type' => 'word', 'subtype' => 'greek'],
            'wh' => ['type' => 'word', 'subtype' => 'hebrew'],
            'wa' => ['type' => 'word', 'subtype' => 'aramaic'],

            // Cross References
            'xt' => ['type' => 'reference', 'subtype' => 'target'],
            'xo' => ['type' => 'reference', 'subtype' => 'origin'],
            'xk' => ['type' => 'reference', 'subtype' => 'keyword'],
            'xq' => ['type' => 'reference', 'subtype' => 'quotation'],
            'xot' => ['type' => 'reference', 'subtype' => 'ot-quote'],
            'xnt' => ['type' => 'reference', 'subtype' => 'nt-quote'],
            'xdc' => ['type' => 'reference', 'subtype' => 'deuterocanonical'],
        ],
    ],
];
