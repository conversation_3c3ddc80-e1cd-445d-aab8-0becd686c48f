# General
daemonize yes
pidfile /var/run/redis.pid
port 6379
bind 127.0.0.1
timeout 0
loglevel notice
logfile /var/log/redis.log
databases 16

# Snapshotting
save 900 1
save 300 10
save 60 10000
rdbcompression yes
dbfilename dump.rdb
dir /var/lib/redis

# Security
# No password by default, add if needed
# requirepass your_password_here

# Limits
maxclients 10000

# Append Only Mode
appendonly no

# Other
tcp-keepalive 0
supervised auto
