<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Browser Testing Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration for cross-browser testing setup.
    | You can configure which browsers to test against and their specific
    | settings for different environments.
    |
    */

    'default_browser' => env('DUSK_DEFAULT_BROWSER', 'chrome'),

    'browsers' => [
        'chrome' => [
            'enabled' => env('DUSK_CHROME_ENABLED', true),
            'driver_url' => env('DUSK_CHROME_DRIVER_URL', 'http://localhost:9515'),
            'binary_path' => env('CHROME_BINARY_PATH', null),
            'options' => [
                '--disable-search-engine-choice-screen',
                '--disable-smooth-scrolling',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--window-size=1920,1080',
            ],
            'headless_options' => [
                '--disable-gpu',
                '--headless=new',
                '--no-sandbox',
                '--disable-dev-shm-usage',
            ],
        ],

        'firefox' => [
            'enabled' => env('DUSK_FIREFOX_ENABLED', true),
            'driver_url' => env('DUSK_FIREFOX_DRIVER_URL', 'http://localhost:4444'),
            'binary_path' => env('FIREFOX_BINARY_PATH', null),
            'profile_preferences' => [
                'dom.webnotifications.enabled' => false,
                'media.navigator.permission.disabled' => true,
                'geo.enabled' => false,
                'dom.push.enabled' => false,
            ],
            'options' => [
                '--width=1920',
                '--height=1080',
            ],
            'headless_options' => [
                '--headless',
            ],
        ],

        'safari' => [
            'enabled' => env('DUSK_SAFARI_ENABLED', PHP_OS_FAMILY === 'Darwin'),
            'driver_url' => env('DUSK_SAFARI_DRIVER_URL', 'http://localhost:10444'),
            'options' => [
                // Safari options are limited
            ],
        ],

        'edge' => [
            'enabled' => env('DUSK_EDGE_ENABLED', false),
            'driver_url' => env('DUSK_EDGE_DRIVER_URL', 'http://localhost:9516'),
            'binary_path' => env('EDGE_BINARY_PATH', null),
            'options' => [
                '--disable-search-engine-choice-screen',
                '--disable-smooth-scrolling',
                '--window-size=1920,1080',
            ],
            'headless_options' => [
                '--disable-gpu',
                '--headless=new',
                '--no-sandbox',
                '--disable-dev-shm-usage',
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Environment-specific Settings
    |--------------------------------------------------------------------------
    */

    'environments' => [
        'local' => [
            'headless' => env('DUSK_HEADLESS', false),
            'browsers' => ['chrome', 'firefox', 'safari'],
        ],

        'ci' => [
            'headless' => true,
            'browsers' => ['chrome', 'firefox'], // Safari not available in most CI environments
        ],

        'testing' => [
            'headless' => env('DUSK_HEADLESS', true),
            'browsers' => ['chrome'],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Test Execution Settings
    |--------------------------------------------------------------------------
    */

    'execution' => [
        'parallel' => env('DUSK_PARALLEL', false),
        'max_parallel_browsers' => env('DUSK_MAX_PARALLEL', 3),
        'timeout' => env('DUSK_TIMEOUT', 30),
        'implicit_wait' => env('DUSK_IMPLICIT_WAIT', 10),
        'page_load_timeout' => env('DUSK_PAGE_LOAD_TIMEOUT', 30),
    ],

    /*
    |--------------------------------------------------------------------------
    | Screenshot and Video Settings
    |--------------------------------------------------------------------------
    */

    'capture' => [
        'screenshots_on_failure' => env('DUSK_SCREENSHOTS_ON_FAILURE', true),
        'screenshots_path' => env('DUSK_SCREENSHOTS_PATH', 'tests/Browser/screenshots'),
        'console_logs_on_failure' => env('DUSK_CONSOLE_LOGS_ON_FAILURE', true),
        'source_on_failure' => env('DUSK_SOURCE_ON_FAILURE', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Cloud Testing Services
    |--------------------------------------------------------------------------
    |
    | Configuration for cloud testing services like BrowserStack, Sauce Labs,
    | or LambdaTest for comprehensive cross-browser testing.
    |
    */

    'cloud_services' => [
        'browserstack' => [
            'enabled' => env('BROWSERSTACK_ENABLED', false),
            'username' => env('BROWSERSTACK_USERNAME'),
            'access_key' => env('BROWSERSTACK_ACCESS_KEY'),
            'hub_url' => 'https://hub-cloud.browserstack.com/wd/hub',
            'capabilities' => [
                'project' => env('BROWSERSTACK_PROJECT', 'Esra Bibel'),
                'build' => env('BROWSERSTACK_BUILD', 'Local Build'),
                'name' => env('BROWSERSTACK_SESSION_NAME', 'Browser Test'),
                'browserstack.debug' => true,
                'browserstack.console' => 'errors',
                'browserstack.networkLogs' => true,
            ],
        ],

        'saucelabs' => [
            'enabled' => env('SAUCELABS_ENABLED', false),
            'username' => env('SAUCELABS_USERNAME'),
            'access_key' => env('SAUCELABS_ACCESS_KEY'),
            'hub_url' => 'https://ondemand.saucelabs.com:443/wd/hub',
        ],

        'lambdatest' => [
            'enabled' => env('LAMBDATEST_ENABLED', false),
            'username' => env('LAMBDATEST_USERNAME'),
            'access_key' => env('LAMBDATEST_ACCESS_KEY'),
            'hub_url' => 'https://hub.lambdatest.com/wd/hub',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Browser Capabilities Matrix
    |--------------------------------------------------------------------------
    |
    | Define specific browser versions and platforms for comprehensive testing
    |
    */

    'capabilities_matrix' => [
        'chrome' => [
            ['browserName' => 'chrome', 'version' => 'latest'],
            ['browserName' => 'chrome', 'version' => 'latest-1'],
        ],
        'firefox' => [
            ['browserName' => 'firefox', 'version' => 'latest'],
            ['browserName' => 'firefox', 'version' => 'latest-1'],
        ],
        'safari' => [
            ['browserName' => 'safari', 'version' => 'latest'],
            ['browserName' => 'safari', 'version' => 'latest-1'],
        ],
    ],
];
