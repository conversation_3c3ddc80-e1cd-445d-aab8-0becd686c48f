<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Default Search Engine
    |--------------------------------------------------------------------------
    |
    | This option controls the default search connection that gets used while
    | using Laravel Scout. This connection is used when syncing all models
    | to the search service. You should adjust this based on your needs.
    |
    | Supported: "algolia", "meilisearch", "typesense",
    |            "database", "collection", "null"
    |
    */

    'driver' => env('SCOUT_DRIVER', 'meilisearch'),

    /*
    |--------------------------------------------------------------------------
    | Index Prefix
    |--------------------------------------------------------------------------
    |
    | Here you may specify a prefix that will be applied to all search index
    | names used by Scout. This prefix may be useful if you have multiple
    | "tenants" or applications sharing the same search infrastructure.
    |
    */

    'prefix' => env('SCOUT_PREFIX', ''),

    /*
    |--------------------------------------------------------------------------
    | Queue Data Syncing
    |--------------------------------------------------------------------------
    |
    | This option allows you to control if the operations that sync your data
    | with your search engines are queued. When this is set to "true" then
    | all automatic data syncing will get queued for better performance.
    |
    */

    'queue' => env('SCOUT_QUEUE', false),

    /*
    |--------------------------------------------------------------------------
    | Database Transactions
    |--------------------------------------------------------------------------
    |
    | This configuration option determines if your data will only be synced
    | with your search indexes after every open database transaction has
    | been committed, thus preventing any discarded data from syncing.
    |
    */

    'after_commit' => false,

    /*
    |--------------------------------------------------------------------------
    | Chunk Sizes
    |--------------------------------------------------------------------------
    |
    | These options allow you to control the maximum chunk size when you are
    | mass importing data into the search engine. This allows you to fine
    | tune each of these chunk sizes based on the power of the servers.
    |
    */

    'chunk' => [
        'searchable' => 500,
        'unsearchable' => 500,
    ],

    /*
    |--------------------------------------------------------------------------
    | Soft Deletes
    |--------------------------------------------------------------------------
    |
    | This option allows to control whether to keep soft deleted records in
    | the search indexes. Maintaining soft deleted records can be useful
    | if your application still needs to search for the records later.
    |
    */

    'soft_delete' => false,

    /*
    |--------------------------------------------------------------------------
    | Identify User
    |--------------------------------------------------------------------------
    |
    | This option allows you to control whether to notify the search engine
    | of the user performing the search. This is sometimes useful if the
    | engine supports any analytics based on this application's users.
    |
    | Supported engines: "algolia"
    |
    */

    'identify' => env('SCOUT_IDENTIFY', false),

    /*
    |--------------------------------------------------------------------------
    | Meilisearch Configuration
    |--------------------------------------------------------------------------
    |
    | Here you may configure your Meilisearch settings. Meilisearch is an open
    | source search engine with minimal configuration. Below, you can state
    | the host and key information for your own Meilisearch installation.
    |
    | See: https://www.meilisearch.com/docs/learn/configuration/instance_options#all-instance-options
    |
    */

   'meilisearch' => [
    'host' => env('MEILISEARCH_HOST', 'http://localhost:7700'),
    'key' => env('MEILISEARCH_KEY', 'Tb3-U5epRvWZrs2KNQoxI2dP9r3vnYvvMOXb1-cq18o'),
    'index-settings' => [
        'books' => [
            'displayedAttributes' => [
                'id', 'name', 'slug', 'abbreviation', 'testament', 
                'category', 'order', 'has_content', 'chapters_count'
            ],
            'searchableAttributes' => [
                'name',
                'abbreviation',
                'slug',
                'search_terms',
                'variations',
                'chapters',
                'verses'
            ],
            'filterableAttributes' => [
                'testament',
                'category',
                'has_content',
                'order',
                'chapters_count'
            ],
            'sortableAttributes' => [
                'order',
                'name',
                'chapters_count'
            ],
            'rankingRules' => [
                'words',
                'typo',
                'proximity',
                'attribute',
                'sort',
                'exactness',
                'order:desc'  // Higher order (NT books) first
            ],
            'stopWords' => [
                'der', 'die', 'das', 'den', 'dem', 'des',
                'ein', 'eine', 'eines', 'einem', 'einen',
                'und', 'oder', 'aber', 'sondern', 'denn',
                'an', 'auf', 'bei', 'mit', 'nach', 'seit',
                'von', 'zu', 'zum', 'zur', 'im', 'in',
                'und', 'oder', 'aber', 'denn', 'weil', 'dass',
                'als', 'wenn', 'da', 'obwohl', 'während', 'nachdem'
            ],
            'typoTolerance' => [
                'enabled' => true,
                'minWordSizeForTypos' => [
                    'oneTypo' => 2,    // Allow 1 typo for words >= 2 chars
                    'twoTypos' => 4     // Allow 2 typos for words >= 4 chars
                ]
            ],
            'pagination' => [
                'maxTotalHits' => 100
            ]
        ],
        'verses' => [
            'displayedAttributes' => ['*'],
            'searchableAttributes' => [
                'text',
                'tags'
            ],
            'filterableAttributes' => [
                'chapter_id',
                'number',
                'start_verse',
                'end_verse',
                'is_pericope_start',
                'has_ot_quote',
                'has_text_variant'
            ],
            'sortableAttributes' => [
                'chapter_id',
                'number',
                'start_verse',
                'end_verse'
            ],
            'rankingRules' => [
                'words',
                'typo',
                'proximity',
                'attribute',
                'exactness'
            ],
            'stopWords' => [
                'der', 'die', 'das', 'den', 'dem', 'des',
                'ein', 'eine', 'eines', 'einem', 'einen',
                'und', 'oder', 'aber', 'sondern', 'denn',
                'an', 'auf', 'bei', 'mit', 'nach', 'seit',
                'von', 'zu', 'zum', 'zur', 'im', 'in'
            ],
            'typoTolerance' => [
                'enabled' => true,
                'minWordSizeForTypos' => [
                    'oneTypo' => 4,
                    'twoTypos' => 8
                ]
            ],
            'pagination' => [
                'maxTotalHits' => 100
            ]
        ],
        'words' => [
            'displayedAttributes' => [
                'id',
                'text',
                'strongs_number',
                'transliteration',
                'definition',
                'variant_type',
                'verse_id',
                'has_variant',
                'is_emphasized',
                'is_ot_quote'
            ],
            'searchableAttributes' => [
                'text',
                'strongs_number',
                'transliteration',
                'definition'
            ],
            'filterableAttributes' => [
                'verse_id',
                'has_variant',
                'is_emphasized',
                'is_ot_quote',
                'variant_type',
            ],
            'sortableAttributes' => [
                'verse_id',
                'position',
                'book_name',
                'chapter_number',
                'verse_number'
            ],
            'rankingRules' => [
                'words',
                'typo',
                'proximity',
                'attribute',
                'exactness'
            ],
            'stopWords' => [
                'der', 'die', 'das', 'den', 'dem', 'des',
                'ein', 'eine', 'eines', 'einem', 'einen',
                'und', 'oder', 'aber', 'sondern', 'denn',
                'an', 'auf', 'bei', 'mit', 'nach', 'seit',
                'von', 'zu', 'zum', 'zur', 'im', 'in'
            ],
            'typoTolerance' => [
                'enabled' => true,
                'minWordSizeForTypos' => [
                    'oneTypo' => 4,
                    'twoTypos' => 8
                ]
            ],
            'pagination' => [
                'maxTotalHits' => 100
            ]
        ],
        'searchable_texts' => [
            'displayedAttributes' => ['*'],
            'searchableAttributes' => [
                'content',
                'searchable_content',
                'reference_full',
                'book_name'
            ],
            'filterableAttributes' => [
                'type',
                'book_id',
                'chapter_number',
                'verse_number'
            ],
            'sortableAttributes' => [
                'order',
                'chapter_number',
                'verse_number'
            ]
        ],
        'footnotes' => [
            'displayedAttributes' => ['*'],
            'searchableAttributes' => [
                'content'
            ],
            'filterableAttributes' => [
                'is_reference',
                'has_italics',
                'position',
                'verse_id'
            ],
            'sortableAttributes' => [
                'id',
                'book_name',
                'chapter_number',
                'verse_number'
            ],
            'rankingRules' => [
                'words',
                'typo',
                'proximity',
                'attribute',
                'exactness'
            ],
            'stopWords' => [
                'der', 'die', 'das', 'den', 'dem', 'des',
                'ein', 'eine', 'eines', 'einem', 'einen',
                'und', 'oder', 'aber', 'sondern', 'denn',
                'an', 'auf', 'bei', 'mit', 'nach', 'seit',
                'von', 'zu', 'zum', 'zur', 'im', 'in'
            ],
            'typoTolerance' => [
                'enabled' => true,
                'minWordSizeForTypos' => [
                    'oneTypo' => 4,
                    'twoTypos' => 8
                ]
            ],
            'pagination' => [
                'maxTotalHits' => 100
            ]
        ]
    ],
],

    /*
    |--------------------------------------------------------------------------
    | Typesense Configuration
    |--------------------------------------------------------------------------
    |
    | Here you may configure your Typesense settings. Typesense is an open
    | source search engine using minimal configuration. Below, you will
    | state the host, key, and schema configuration for the instance.
    |
    */

    'typesense' => [
        'client-settings' => [
            'api_key' => env('TYPESENSE_API_KEY', 'xyz'),
            'nodes' => [
                [
                    'host' => env('TYPESENSE_HOST', 'localhost'),
                    'port' => env('TYPESENSE_PORT', '8108'),
                    'path' => env('TYPESENSE_PATH', ''),
                    'protocol' => env('TYPESENSE_PROTOCOL', 'http'),
                ],
            ],
            'nearest_node' => [
                'host' => env('TYPESENSE_HOST', 'localhost'),
                'port' => env('TYPESENSE_PORT', '8108'),
                'path' => env('TYPESENSE_PATH', ''),
                'protocol' => env('TYPESENSE_PROTOCOL', 'http'),
            ],
            'connection_timeout_seconds' => env('TYPESENSE_CONNECTION_TIMEOUT_SECONDS', 2),
            'healthcheck_interval_seconds' => env('TYPESENSE_HEALTHCHECK_INTERVAL_SECONDS', 30),
            'num_retries' => env('TYPESENSE_NUM_RETRIES', 3),
            'retry_interval_seconds' => env('TYPESENSE_RETRY_INTERVAL_SECONDS', 1),
        ],
        'model-settings' => [
            // Model class => Typesense index settings
            // e.g. App\Models\Post::class => [ 'collection-schema' => [ /* ... */ ] ]
        ],
    ],

];
