<xs:schema attributeFormDefault="unqualified" elementFormDefault="qualified" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:element name="usx">
    <xs:complexType>
      <xs:sequence>
        <xs:element name="book">
          <xs:complexType>
            <xs:choice maxOccurs="unbounded" minOccurs="0">
              <xs:element name="chapter">
                <xs:complexType>
                  <xs:simpleContent>
                    <xs:extension base="xs:string">
                      <xs:attribute type="xs:byte" name="number"/>
                      <xs:attribute type="xs:string" name="style"/>
                      <xs:attribute type="xs:string" name="sid"/>
                    </xs:extension>
                  </xs:simpleContent>
                </xs:complexType>
              </xs:element>
              <xs:element name="para" maxOccurs="unbounded" minOccurs="0">
                <xs:complexType mixed="true">
                  <xs:choice maxOccurs="unbounded" minOccurs="0">
                    <xs:element name="verse">
                      <xs:complexType>
                        <xs:simpleContent>
                          <xs:extension base="xs:string">
                            <xs:attribute type="xs:byte" name="number" use="optional"/>
                            <xs:attribute type="xs:string" name="style" use="optional"/>
                            <xs:attribute type="xs:string" name="sid" use="optional"/>
                            <xs:attribute type="xs:string" name="eid" use="optional"/>
                          </xs:extension>
                        </xs:simpleContent>
                      </xs:complexType>
                    </xs:element>
                    <xs:element name="char">
                      <xs:complexType mixed="true">
                        <xs:sequence>
                          <xs:element name="char" minOccurs="0">
                            <xs:complexType>
                              <xs:simpleContent>
                                <xs:extension base="xs:string">
                                  <xs:attribute type="xs:string" name="style"/>
                                </xs:extension>
                              </xs:simpleContent>
                            </xs:complexType>
                          </xs:element>
                          <xs:element name="ref" minOccurs="0">
                            <xs:complexType>
                              <xs:simpleContent>
                                <xs:extension base="xs:string">
                                  <xs:attribute type="xs:string" name="loc"/>
                                </xs:extension>
                              </xs:simpleContent>
                            </xs:complexType>
                          </xs:element>
                        </xs:sequence>
                        <xs:attribute type="xs:string" name="style" use="optional"/>
                        <xs:attribute type="xs:string" name="eid" use="optional"/>
                        <xs:attribute type="xs:string" name="caller" use="optional"/>
                      </xs:complexType>
                    </xs:element>
                    <xs:element name="note">
                      <xs:complexType mixed="true">
                        <xs:sequence>
                          <xs:element name="char" maxOccurs="unbounded" minOccurs="0">
                            <xs:complexType>
                              <xs:simpleContent>
                                <xs:extension base="xs:string">
                                  <xs:attribute type="xs:string" name="style" use="optional"/>
                                </xs:extension>
                              </xs:simpleContent>
                            </xs:complexType>
                          </xs:element>
                        </xs:sequence>
                        <xs:attribute type="xs:string" name="style" use="optional"/>
                        <xs:attribute type="xs:string" name="caller" use="optional"/>
                        <xs:attribute type="xs:string" name="eid" use="optional"/>
                      </xs:complexType>
                    </xs:element>
                    <xs:element name="ms">
                      <xs:complexType>
                        <xs:simpleContent>
                          <xs:extension base="xs:string">
                            <xs:attribute type="xs:string" name="sid" use="optional"/>
                            <xs:attribute type="xs:string" name="type" use="optional"/>
                            <xs:attribute type="xs:string" name="eid" use="optional"/>
                          </xs:extension>
                        </xs:simpleContent>
                      </xs:complexType>
                    </xs:element>
                  </xs:choice>
                  <xs:attribute type="xs:string" name="style" use="optional"/>
                  <xs:attribute type="xs:byte" name="number" use="optional"/>
                  <xs:attribute type="xs:string" name="sid" use="optional"/>
                </xs:complexType>
              </xs:element>
            </xs:choice>
            <xs:attribute type="xs:string" name="code"/>
            <xs:attribute type="xs:string" name="style"/>
          </xs:complexType>
        </xs:element>
        <xs:element name="para" maxOccurs="unbounded" minOccurs="0">
          <xs:complexType>
            <xs:simpleContent>
              <xs:extension base="xs:string">
                <xs:attribute type="xs:string" name="style" use="optional"/>
              </xs:extension>
            </xs:simpleContent>
          </xs:complexType>
        </xs:element>
      </xs:sequence>
      <xs:attribute type="xs:float" name="version"/>
    </xs:complexType>
  </xs:element>
</xs:schema>
