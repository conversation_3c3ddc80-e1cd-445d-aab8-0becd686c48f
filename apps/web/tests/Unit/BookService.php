<?php

use App\Models\Book;
use App\Services\BookService;

beforeEach(function () {
    $this->bookService = new BookService();
    $this->book = Book::factory()->create(['chapters_count' => 5]);
});

it('returns correct statistics for a book', function () {
    $statistics = $this->bookService->getStatistics($this->book);

    expect($statistics)
        ->toBeArray()
        ->toHaveKeys(['verses_count', 'words_count', 'chapters_count', 'footnotes_count']);
});

it('calculates reading progress correctly', function () {
    $user = User::factory()->create();
    $chapterNumber = 3;

    $this->bookService->updateReadingProgress($this->book, $user->id, $chapterNumber);

    $progress = $this->book->readingProgress()
        ->where('user_id', $user->id)
        ->first();

    expect($progress)
        ->last_chapter_read->toBe($chapterNumber)
        ->completion_percentage->toBe(60.0) // (3/5) * 100
        ->started_at->not->toBeNull()
        ->completed_at->toBeNull();
});

it('marks book as completed when reading last chapter', function () {
    $user = User::factory()->create();

    $this->bookService->updateReadingProgress($this->book, $user->id, $this->book->chapters_count);

    $progress = $this->book->readingProgress()
        ->where('user_id', $user->id)
        ->first();

    expect($progress)
        ->completion_percentage->toBe(100.0)
        ->completed_at->not->toBeNull();
});

it('caches book outline', function () {
    Cache::shouldReceive('remember')
        ->once()
        ->with("book.{$this->book->id}.outline", 3600, Closure::class)
        ->andReturn([]);

    $this->bookService->getOutline($this->book);
});

it('generates correct outline structure', function () {
    // Create some chapters and verses for the book
    $chapter = Chapter::factory()
        ->for($this->book)
        ->has(Verse::factory()->state(['number' => 1, 'text' => 'First verse']))
        ->create(['number' => 1]);

    $outline = $this->bookService->getOutline($this->book);

    expect($outline)
        ->toBeArray()
        ->sequence(
            fn ($item) => $item
                ->chapter_number->toBe(1)
                ->first_verse->toBe('First verse')
        );
});
