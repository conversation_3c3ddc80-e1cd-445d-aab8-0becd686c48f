<?php

namespace Tests\Unit\Services;

use App\Services\VerseCacheService;
use Illuminate\Support\Facades\Cache;
use Mo<PERSON>y\MockInterface;
use Tests\TestCase;

class VerseCacheServiceSimpleTest extends TestCase
{
    protected $mockRedis;
    protected $testData;
    protected $service;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a mock for the cache repository
        $this->mockCache = $this->createMock(\Illuminate\Cache\Repository::class);

        // Bind our mock cache to the container
        $this->app->instance('cache', $this->mockCache);
        $this->app->instance('cache.store', $this->mockCache);

        // Create the service with our mocked cache
        $this->service = new VerseCacheService();

        // Simple test data that matches the expected structure
        $this->testData = [
            'id' => 1,
            'book_id' => 1,
            'number' => 1,
            'verse_count' => 31,
            'verses' => [
                [
                    'id' => 1,
                    'number' => 1,
                    'chapter_id' => 1,
                    'text' => 'In the beginning, God created the heavens and the earth.',
                    'words' => [
                        [
                            'id' => 1,
                            'position' => 1,
                            'text' => 'In',
                            'text_after' => ' ',
                            'strongs_number' => 'H0001',
                            'verse_id' => 1,
                        ]
                    ]
                ]
            ]
        ];
    }

    /** @test */
    public function it_caches_chapter_data()
    {
        $callback = fn() => $this->testData;

        // Configure the mock to return our test data
        $this->mockCache->expects($this->once())
            ->method('remember')
            ->willReturn($this->testData);

        // Call the method
        $result = $this->service->getOrSetChapter('genesis', 1, $callback);

        // Assert the result matches our test data
        $this->assertEquals($this->testData, $result);
    }

    /** @test */
    public function it_handles_different_cache_keys_based_on_options()
    {
        $callback = fn() => $this->testData;

        // Configure the mock to expect three different cache keys
        $this->mockCache->expects($this->exactly(3))
            ->method('remember')
            ->willReturn($this->testData);

        // Call with default options (no words, no footnotes)
        $default = $this->service->getOrSetChapter('genesis', 1, $callback);

        // Call with words included - should be a different cache key
        $withWords = $this->service->getOrSetChapter('genesis', 1, $callback, [
            'includeWords' => true,
            'includeWordGroups' => true,
            'includeFootnotes' => false,
        ]);

        // Call with footnotes included - should be a different cache key
        $withFootnotes = $this->service->getOrSetChapter('genesis', 1, $callback, [
            'includeWords' => false,
            'includeWordGroups' => false,
            'includeFootnotes' => true,
        ]);

        // Assert the results
        $this->assertEquals($this->testData, $default);
        $this->assertEquals($this->testData, $withWords);
        $this->assertEquals($this->testData, $withFootnotes);
    }

    /** @test */
    public function it_returns_null_on_callback_exception()
    {
        $callback = function () {
            throw new \Exception('Test exception');
        };

        // Configure the mock to call the callback which will throw
        $this->mockCache->expects($this->once())
            ->method('remember')
            ->willReturnCallback(function ($key, $ttl, $callback) {
                return $callback();
            });

        $result = $this->service->getOrSetChapter('genesis', 1, $callback);

        $this->assertNull($result);
    }

    /** @test */
    public function it_clears_cache_for_chapter()
    {
        // Set up the mock to expect a delete call with a pattern
        $this->mockCache->expects($this->once())
            ->method('forget')
            ->with($this->stringContains('genesis:1'));

        // Clear cache for this chapter
        $this->service->clearChapter('genesis', 1);
    }

    /** @test */
    public function it_clears_cache_for_book()
    {
        // Set up the mock to expect a delete call with a pattern
        $this->mockCache->expects($this->once())
            ->method('forget')
            ->with($this->stringContains('genesis:'));

        // Clear cache for the book
        $this->service->clearBook('genesis');
    }
}
