<?php

use App\Models\Book;
use App\Models\Chapter;
use App\Models\Verse;
use App\Models\Word;
use App\Models\Footnote;
use App\Models\Reference;
use App\Services\BibleData\UsxParser;
use App\Services\BibleData\UsxParserState;
use App\Services\BibleData\UsxParserConfig;
use App\Services\BibleData\UsxParserLogger;
use App\Services\BibleData\TextProcessor;
use App\Services\BibleData\Handlers\ElementHandler;
use App\Services\BibleData\ModelFactory;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\Console\Output\BufferedOutput;

uses(RefreshDatabase::class);

beforeEach(function () {
    // Create a mock USX file for testing
    Storage::fake('bibeltext');

    // Create test directory structure
    Storage::disk('bibeltext')->makeDirectory('TestBook');

    // Create a simple USX file with minimal content for testing
    $usxContent = <<<XML
<?xml version="1.0" encoding="utf-8"?>
<usx version="3.0">
  <book code="TST" style="id">TestBook</book>
  <para style="h">TestBook</para>
  <chapter number="1" style="c" />
  <para style="p">
    <verse number="1" style="v" />This is verse 1 text.
    <verse number="2" style="v" />This is verse 2 text.
    <note caller="a" style="f">This is a footnote<char style="fr">1</char></note>
  </para>
  <chapter number="2" style="c" />
  <para style="p">
    <verse number="1" style="v" />Chapter 2 verse 1.
    <verse number="2" style="v" />Chapter 2 verse 2 with <char style="xot">OT quote</char>.
  </para>
</usx>
XML;

    Storage::disk('bibeltext')->put('TestBook/TestBook.usx', $usxContent);

    // Set up the test book
    $this->book = Book::create([
        'name' => 'TestBook',
        'slug' => 'testbook',
        'order' => 1
    ]);

    // Set up the parser components
    $this->output = new BufferedOutput();
    $this->state = new UsxParserState();
    $this->config = new UsxParserConfig();
    $this->logger = new UsxParserLogger(null, true, false);
    $this->modelFactory = new ModelFactory($this->state, $this->logger);
    $this->textProcessor = new TextProcessor($this->state, $this->logger, $this->modelFactory);
    $this->elementHandler = new ElementHandler($this->state, $this->logger, $this->textProcessor, $this->modelFactory);

    // Initialize the parser
    $this->parser = new UsxParser(
        $this->state,
        $this->config,
        $this->logger,
        $this->textProcessor,
        $this->elementHandler,
        $this->modelFactory
    );

    // Set the test file path
    $this->usxFilePath = Storage::disk('bibeltext')->path('TestBook/TestBook.usx');
});

test('parser creates correct book structure', function () {
    // Parse the test file
    $stats = $this->parser->parseFile($this->usxFilePath, $this->book);

    // Verify book structure
    expect(Chapter::count())->toBe(2);
    expect(Verse::count())->toBe(4);

    // Verify chapter 1
    $chapter1 = Chapter::where('number', 1)->first();
    expect($chapter1)->not->toBeNull();
    expect($chapter1->verses()->count())->toBe(2);

    // Verify chapter 2
    $chapter2 = Chapter::where('number', 2)->first();
    expect($chapter2)->not->toBeNull();
    expect($chapter2->verses()->count())->toBe(2);

    // Verify verse content
    $verse1 = Verse::where('chapter_id', $chapter1->id)->where('number', 1)->first();
    expect($verse1->text)->toContain('This is verse 1 text');
});

test('parser creates footnotes correctly', function () {
    // Parse the test file
    $stats = $this->parser->parseFile($this->usxFilePath, $this->book);

    // Verify footnotes
    expect(Footnote::count())->toBe(1);

    $footnote = Footnote::first();
    expect($footnote)->not->toBeNull();
    expect($footnote->caller)->toBe('1');
    expect($footnote->content)->toContain('This is a footnote');
});

test('parser handles OT quotes correctly', function () {
    // Parse the test file
    $stats = $this->parser->parseFile($this->usxFilePath, $this->book);

    // Find the verse with OT quote
    $chapter2 = Chapter::where('number', 2)->first();
    $verse2 = Verse::where('chapter_id', $chapter2->id)->where('number', 2)->first();

    // Verify OT quote
    expect($verse2->has_ot_quote)->toBeTrue();

    // Check for words with OT quote flag
    $otQuoteWords = Word::where('verse_id', $verse2->id)
                        ->where('is_ot_quote', true)
                        ->get();

    expect($otQuoteWords)->not->toBeEmpty();
    expect($otQuoteWords->first()->text)->toBe('OT');
});

test('parser supports chapter filtering', function () {
    // Parse with chapter filter
    $options = ['chapters' => [1]];
    $stats = $this->parser->parseFile($this->usxFilePath, $this->book, $options);

    // Verify only chapter 1 was parsed
    expect(Chapter::count())->toBe(1);
    expect(Chapter::first()->number)->toBe(1);
    expect(Verse::count())->toBe(2);
});

test('parser handles memory efficiently', function () {
    // Parse the test file
    $stats = $this->parser->parseFile($this->usxFilePath, $this->book);

    // Verify memory usage stats
    expect($stats)->toHaveKey('memory_peak');
    expect($stats['memory_peak'])->toBeGreaterThan(0);
});

test('parser recovers from errors', function () {
    // Create a malformed USX file to test error recovery
    $malformedUsx = <<<XML
<?xml version="1.0" encoding="utf-8"?>
<usx version="3.0">
  <book code="TST" style="id">TestBook</book>
  <para style="h">TestBook</para>
  <chapter number="1" style="c" />
  <para style="p">
    <verse number="1" style="v" />This is verse 1 text.
    <verse number="2" style="v" />This is verse 2 text.
    <note caller="a" style="f">This is a footnote<char style="fr">1</char></note>
  </para>
  <chapter number="2" style="c" />
  <para style="p">
    <verse number="1" style="v" />Chapter 2 verse 1.
    <verse number="2" style="v" />Chapter 2 verse 2 with <char style="xot">OT quote</char>.
    <malformed_tag>This should be skipped</malformed_tag>
  </para>
</usx>
XML;

    Storage::disk('bibeltext')->put('TestBook/Malformed.usx', $malformedUsx);
    $malformedPath = Storage::disk('bibeltext')->path('TestBook/Malformed.usx');

    // This should not throw an exception due to error recovery
    try {
        $options = ['max_retries' => 3, 'retry_delay' => 0];
        $stats = $this->parser->parseFile($malformedPath, $this->book, $options);

        // If we get here, the test passes
        expect(true)->toBeTrue();
    } catch (\Exception $e) {
        // If we catch an exception, the test fails
        $this->fail("Parser did not recover from error: " . $e->getMessage());
    }
});
