<?php

use App\Models\Book;
use App\Models\Chapter;
use App\Models\Verse;
use App\Models\Footnote;
use App\Models\Reference;
use App\Models\Word;
use App\Services\BibleData\Handlers\ReferenceHandler;
use App\Services\BibleData\UsxParserState;
use App\Services\BibleData\UsxParserConfig;
use App\Services\BibleData\UsxParserLogger;
use App\Services\BibleData\ModelFactory;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Symfony\Component\Console\Output\BufferedOutput;

uses(RefreshDatabase::class);

beforeEach(function () {
    // Set up the test components
    $this->output = new BufferedOutput();
    $this->config = new UsxParserConfig();
    $this->state = new UsxParserState($this->config);
    $this->logger = new UsxParserLogger($this->output, true, false);
    $this->modelFactory = new ModelFactory($this->state, $this->logger);
    
    // Create test book, chapter, and verse
    $this->book = Book::create([
        'name' => 'TestBook',
        'slug' => 'testbook',
        'order' => 1
    ]);
    
    $this->chapter = Chapter::create([
        'book_id' => $this->book->id,
        'number' => 1
    ]);
    
    $this->verse = Verse::create([
        'chapter_id' => $this->chapter->id,
        'number' => 1,
        'text' => 'Test verse text'
    ]);
    
    // Set up parser state
    $this->state->book = $this->book;
    $this->state->chapter = $this->chapter;
    $this->state->verse = $this->verse;
    
    // Create the reference handler
    $this->referenceHandler = new ReferenceHandler($this->state, $this->logger, $this->modelFactory);
});

test('reference handler processes OT quotes correctly', function () {
    // Create a simulated XML element for OT quote
    $xmlElement = new SimpleXMLElement('<char style="xot">This is an OT quote</char>');
    
    // Process the OT quote
    $this->referenceHandler->handleElement($xmlElement, 'char');
    
    // Create some words with the OT quote
    $word1 = Word::create([
        'verse_id' => $this->verse->id,
        'text' => 'This',
        'position' => 1,
        'is_ot_quote' => true,
        'ot_quote_group_id' => $this->state->currentOTQuoteGroupId
    ]);
    
    $word2 = Word::create([
        'verse_id' => $this->verse->id,
        'text' => 'is',
        'position' => 2,
        'is_ot_quote' => true,
        'ot_quote_group_id' => $this->state->currentOTQuoteGroupId
    ]);
    
    // End the OT quote
    $this->referenceHandler->endOTQuote();
    
    // Verify verse was marked as having an OT quote
    $this->verse->refresh();
    expect($this->verse->has_ot_quote)->toBeTrue();
    
    // Verify words were marked correctly
    expect($word1->is_ot_quote)->toBeTrue();
    expect($word1->ot_quote_group_id)->toBe($this->state->currentOTQuoteGroupId);
    expect($word2->is_ot_quote)->toBeTrue();
    expect($word2->ot_quote_group_id)->toBe($this->state->currentOTQuoteGroupId);
});

test('reference handler processes cross reference targets correctly', function () {
    // Create a footnote first
    $footnote = Footnote::create([
        'verse_id' => $this->verse->id,
        'caller' => 'x',
        'content' => 'See reference',
        'type' => 'cross_reference'
    ]);
    
    // Set the footnote in the state
    $this->state->footnote = $footnote;
    $this->state->inNote = true;
    
    // Create a simulated XML element for cross reference target
    $xmlElement = new SimpleXMLElement('<char style="xt">Matt 1:1</char>');
    
    // Process the cross reference target
    $this->referenceHandler->handleElement($xmlElement, 'char');
    
    // Verify reference was created
    $reference = Reference::first();
    expect($reference)->not->toBeNull();
    expect($reference->footnote_id)->toBe($footnote->id);
    expect($reference->target)->toBe('Matt 1:1');
});

test('reference handler processes multiple reference types', function () {
    // Test different reference types
    $referenceTypes = [
        'xt' => 'target',
        'xo' => 'origin',
        'xk' => 'keyword',
        'xq' => 'quotation',
        'xot' => 'ot-quote',
        'xnt' => 'nt-quote',
        'xdc' => 'deuterocanonical'
    ];
    
    // Create a footnote
    $footnote = Footnote::create([
        'verse_id' => $this->verse->id,
        'caller' => 'x',
        'content' => 'Multiple references',
        'type' => 'cross_reference'
    ]);
    
    // Set the footnote in the state
    $this->state->footnote = $footnote;
    $this->state->inNote = true;
    
    // Process each reference type
    foreach ($referenceTypes as $style => $subtype) {
        // Skip OT quote as it's handled differently
        if ($style === 'xot') continue;
        
        $xmlElement = new SimpleXMLElement("<char style=\"$style\">Reference $subtype</char>");
        $this->referenceHandler->handleElement($xmlElement, 'char');
    }
    
    // Verify references were created
    $references = Reference::all();
    expect($references)->toHaveCount(count($referenceTypes) - 1); // Minus 1 for xot
    
    // Check each reference has the correct target
    foreach ($referenceTypes as $style => $subtype) {
        if ($style === 'xot') continue;
        
        $reference = $references->where('target', "Reference $subtype")->first();
        expect($reference)->not->toBeNull();
        expect($reference->footnote_id)->toBe($footnote->id);
    }
});

test('reference handler handles nested OT quotes correctly', function () {
    // Start first OT quote
    $firstElement = new SimpleXMLElement('<char style="xot">First OT quote</char>');
    $this->referenceHandler->handleElement($firstElement, 'char');
    
    // Remember the first group ID
    $firstGroupId = $this->state->currentOTQuoteGroupId;
    
    // Create a word in the first OT quote
    $firstWord = Word::create([
        'verse_id' => $this->verse->id,
        'text' => 'First',
        'position' => 1,
        'is_ot_quote' => true,
        'ot_quote_group_id' => $firstGroupId
    ]);
    
    // Start second OT quote (nested)
    $secondElement = new SimpleXMLElement('<char style="xot">Nested OT quote</char>');
    $this->referenceHandler->handleElement($secondElement, 'char');
    
    // Remember the second group ID
    $secondGroupId = $this->state->currentOTQuoteGroupId;
    
    // Create a word in the second OT quote
    $secondWord = Word::create([
        'verse_id' => $this->verse->id,
        'text' => 'Nested',
        'position' => 2,
        'is_ot_quote' => true,
        'ot_quote_group_id' => $secondGroupId
    ]);
    
    // End second OT quote
    $this->referenceHandler->endOTQuote();
    
    // Verify we're back to the first OT quote
    expect($this->state->currentOTQuoteGroupId)->toBe($firstGroupId);
    
    // Create another word in the first OT quote
    $thirdWord = Word::create([
        'verse_id' => $this->verse->id,
        'text' => 'After',
        'position' => 3,
        'is_ot_quote' => true,
        'ot_quote_group_id' => $firstGroupId
    ]);
    
    // End first OT quote
    $this->referenceHandler->endOTQuote();
    
    // Verify state was reset
    expect($this->state->currentOTQuoteGroupId)->toBeNull();
    
    // Verify verse was marked as having an OT quote
    $this->verse->refresh();
    expect($this->verse->has_ot_quote)->toBeTrue();
    
    // Verify words were marked correctly
    expect($firstWord->is_ot_quote)->toBeTrue();
    expect($firstWord->ot_quote_group_id)->toBe($firstGroupId);
    
    expect($secondWord->is_ot_quote)->toBeTrue();
    expect($secondWord->ot_quote_group_id)->toBe($secondGroupId);
    
    expect($thirdWord->is_ot_quote)->toBeTrue();
    expect($thirdWord->ot_quote_group_id)->toBe($firstGroupId);
});

test('reference handler handles inline references correctly', function () {
    // Create a simulated XML element for inline reference
    $xmlElement = new SimpleXMLElement('<char style="rq">See John 3:16</char>');
    
    // Process the inline reference
    $this->referenceHandler->handleElement($xmlElement, 'char');
    
    // Create a word with the inline reference
    $word = Word::create([
        'verse_id' => $this->verse->id,
        'text' => 'See',
        'position' => 1,
        'has_reference' => true
    ]);
    
    // Verify word was marked correctly
    expect($word->has_reference)->toBeTrue();
});
