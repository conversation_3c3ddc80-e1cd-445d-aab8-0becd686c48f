<?php

use App\Models\Book;
use App\Models\Chapter;
use App\Models\Verse;
use App\Models\Footnote;
use App\Services\BibleData\Handlers\NoteHandler;
use App\Services\BibleData\UsxParserState;
use App\Services\BibleData\UsxParserConfig;
use App\Services\BibleData\UsxParserLogger;
use App\Services\BibleData\ModelFactory;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Symfony\Component\Console\Output\BufferedOutput;

uses(RefreshDatabase::class);

beforeEach(function () {
    // Set up the test components
    $this->output = new BufferedOutput();
    $this->config = new UsxParserConfig();
    $this->state = new UsxParserState($this->config);
    $this->logger = new UsxParserLogger($this->output, true, false);
    $this->modelFactory = new ModelFactory($this->state, $this->logger);
    
    // Create test book, chapter, and verse
    $this->book = Book::create([
        'name' => 'TestBook',
        'slug' => 'testbook',
        'order' => 1
    ]);
    
    $this->chapter = Chapter::create([
        'book_id' => $this->book->id,
        'number' => 1
    ]);
    
    $this->verse = Verse::create([
        'chapter_id' => $this->chapter->id,
        'number' => 1,
        'text' => 'Test verse text'
    ]);
    
    // Set up parser state
    $this->state->book = $this->book;
    $this->state->chapter = $this->chapter;
    $this->state->verse = $this->verse;
    
    // Create the note handler
    $this->noteHandler = new NoteHandler($this->state, $this->logger, $this->modelFactory);
});

test('note handler processes footnotes correctly', function () {
    // Create a simulated XML element for footnote
    $xmlElement = new SimpleXMLElement('<note caller="+" style="f">This is a footnote</note>');
    
    // Process the note
    $this->noteHandler->handleElement($xmlElement, 'note');
    
    // Verify state was updated
    expect($this->state->inNote)->toBeTrue();
    expect($this->state->footnote)->not->toBeNull();
    
    // Verify footnote was created
    $footnote = Footnote::first();
    expect($footnote)->not->toBeNull();
    expect($footnote->caller)->toBe('+');
    expect($footnote->content)->toBe('This is a footnote');
    expect($footnote->verse_id)->toBe($this->verse->id);
    
    // End the note
    $this->noteHandler->endNote();
    
    // Verify state was reset
    expect($this->state->inNote)->toBeFalse();
    expect($this->state->footnote)->toBeNull();
});

test('note handler processes footnote reference correctly', function () {
    // Create a simulated XML element for footnote
    $xmlElement = new SimpleXMLElement('<note caller="+" style="f">Footnote <char style="fr">1,2</char> with reference</note>');
    
    // Process the note
    $this->noteHandler->handleElement($xmlElement, 'note');
    
    // Simulate processing of the reference char
    $refElement = new SimpleXMLElement('<char style="fr">1,2</char>');
    $this->noteHandler->handleElement($refElement, 'char');
    
    // End the note
    $this->noteHandler->endNote();
    
    // Verify footnote was created with reference
    $footnote = Footnote::first();
    expect($footnote)->not->toBeNull();
    expect($footnote->caller)->toBe('+');
    expect($footnote->content)->toBe('Footnote 1,2 with reference');
    expect($footnote->reference)->toBe('1,2');
});

test('note handler processes cross references correctly', function () {
    // Create a simulated XML element for cross reference
    $xmlElement = new SimpleXMLElement('<note caller="x" style="x">See <char style="xt">Matt 1:1</char></note>');
    
    // Process the note
    $this->noteHandler->handleElement($xmlElement, 'note');
    
    // Verify state was updated
    expect($this->state->inNote)->toBeTrue();
    expect($this->state->footnote)->not->toBeNull();
    
    // Simulate processing of the target reference
    $xtElement = new SimpleXMLElement('<char style="xt">Matt 1:1</char>');
    $this->noteHandler->handleElement($xtElement, 'char');
    
    // End the note
    $this->noteHandler->endNote();
    
    // Verify footnote was created with cross reference
    $footnote = Footnote::first();
    expect($footnote)->not->toBeNull();
    expect($footnote->caller)->toBe('x');
    expect($footnote->content)->toBe('See Matt 1:1');
    expect($footnote->type)->toBe('cross_reference');
});

test('note handler handles nested char elements correctly', function () {
    // Create a simulated XML element for footnote with nested char elements
    $xmlElement = new SimpleXMLElement('<note caller="+" style="f">This is a <char style="it">formatted</char> footnote</note>');
    
    // Process the note
    $this->noteHandler->handleElement($xmlElement, 'note');
    
    // Simulate processing of the italic char
    $itElement = new SimpleXMLElement('<char style="it">formatted</char>');
    $this->noteHandler->handleElement($itElement, 'char');
    
    // End the note
    $this->noteHandler->endNote();
    
    // Verify footnote was created with formatted content
    $footnote = Footnote::first();
    expect($footnote)->not->toBeNull();
    expect($footnote->content)->toBe('This is a formatted footnote');
});

test('note handler handles multiple footnotes in same verse', function () {
    // Create first footnote
    $firstElement = new SimpleXMLElement('<note caller="a" style="f">First footnote</note>');
    $this->noteHandler->handleElement($firstElement, 'note');
    $this->noteHandler->endNote();
    
    // Create second footnote
    $secondElement = new SimpleXMLElement('<note caller="b" style="f">Second footnote</note>');
    $this->noteHandler->handleElement($secondElement, 'note');
    $this->noteHandler->endNote();
    
    // Verify both footnotes were created
    $footnotes = Footnote::all();
    expect($footnotes)->toHaveCount(2);
    
    $first = $footnotes->where('caller', 'a')->first();
    expect($first->content)->toBe('First footnote');
    
    $second = $footnotes->where('caller', 'b')->first();
    expect($second->content)->toBe('Second footnote');
});

test('note handler handles malformed notes gracefully', function () {
    // Create a note without a style attribute
    $malformedElement = new SimpleXMLElement('<note caller="+">Malformed note</note>');
    
    // This should not throw an exception
    $this->noteHandler->handleElement($malformedElement, 'note');
    
    // No footnote should be created
    expect(Footnote::count())->toBe(0);
    
    // State should be reset
    expect($this->state->inNote)->toBeFalse();
});
