<?php

namespace Tests\Unit\Services;

use App\Models\Book;
use App\Models\Chapter;
use App\Models\Verse;
use App\Models\Word;
use App\Models\Footnote;
use App\Services\VerseCacheService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

class VerseCacheServiceTest extends TestCase
{
    use RefreshDatabase;

    private VerseCacheService $service;
    private Book $book;
    private Chapter $chapter;
    private array $testData;

    protected function setUp(): void
    {
        parent::setUp();

        // Set up test data
        $this->book = Book::factory()->create([
            'slug' => 'genesis',
            'name' => 'Genesis',
        ]);

        $this->chapter = Chapter::factory()->create([
            'book_id' => $this->book->id,
            'number' => 1,
        ]);

        $verse = Verse::factory()->create([
            'chapter_id' => $this->chapter->id,
            'number' => 1,
            'text' => 'In the beginning, God created the heavens and the earth.',
        ]);

        $word = Word::factory()->create([
            'verse_id' => $verse->id,
            'position' => 1,
            'text' => 'In',
            'strongs_number' => 'H0001',
        ]);

        $footnote = Footnote::factory()->create([
            'verse_id' => $verse->id,
            'number' => 1,
            'text' => 'Footnote text',
        ]);

        $this->testData = [
            'id' => $this->chapter->id,
            'book_id' => $this->book->id,
            'number' => 1,
            'verses' => [
                [
                    'id' => $verse->id,
                    'number' => 1,
                    'text' => 'In the beginning, God created the heavens and the earth.',
                    'words' => [
                        [
                            'id' => $word->id,
                            'position' => 1,
                            'text' => 'In',
                            'strongs_number' => 'H0001',
                        ]
                    ],
                    'footnotes' => [
                        [
                            'id' => $footnote->id,
                            'number' => 1,
                            'text' => 'Footnote text',
                        ]
                    ]
                ]
            ]
        ];

        $this->service = new VerseCacheService();
        Cache::store('bible')->clear();
    }

    /** @test */
    public function it_caches_chapter_data()
    {
        $callback = fn() => $this->testData;

        // First call - should cache the data
        $result1 = $this->service->getOrSetChapter('genesis', 1, $callback);

        // Second call - should use cached data
        $result2 = $this->service->getOrSetChapter('genesis', 1, function () {
            $this->fail('Callback should not be called when data is cached');
        });

        $this->assertEquals($this->testData, $result1);
        $this->assertEquals($result1, $result2);
    }

    /** @test */
    public function it_handles_different_cache_keys_based_on_options()
    {
        $callback = fn() => $this->testData;

        // Call with default options (no words, no footnotes)
        $this->service->getOrSetChapter('genesis', 1, $callback);

        // Call with words included
        $this->service->getOrSetChapter('genesis', 1, $callback, [
            'includeWords' => true,
            'includeWordGroups' => true,
            'includeFootnotes' => false,
        ]);

        // Call with footnotes included
        $this->service->getOrSetChapter('genesis', 1, $callback, [
            'includeWords' => false,
            'includeWordGroups' => false,
            'includeFootnotes' => true,
        ]);

        // Should have 3 different cache entries
        $this->assertCount(3, Cache::store('bible')->getStore()->getAllKeys());
    }

    /** @test */
    public function it_returns_null_on_callback_exception()
    {
        $callback = function () {
            throw new \Exception('Test exception');
        };

        $result = $this->service->getOrSetChapter('genesis', 1, $callback);

        $this->assertNull($result);
    }

    /** @test */
    public function it_handles_multiple_chapters()
    {
        $chapters = [1, 2, 3];
        $callback = function ($chapter) {
            $data = $this->testData;
            $data['number'] = $chapter;
            return $data;
        };

        $results = $this->service->getOrSetMultipleChapters('genesis', $chapters, $callback);

        $this->assertCount(3, $results);
        $this->assertEquals(1, $results[0]['number']);
        $this->assertEquals(2, $results[1]['number']);
        $this->assertEquals(3, $results[2]['number']);

        // Verify the results were cached
        $cachedResults = $this->service->getOrSetMultipleChapters('genesis', $chapters, function () {
            $this->fail('Callback should not be called when data is cached');
        });

        $this->assertEquals($results, $cachedResults);
    }

    /** @test */
    public function it_clears_cache_for_chapter()
    {
        $callback = fn() => $this->testData;

        // Cache the data
        $this->service->getOrSetChapter('genesis', 1, $callback);
        $this->service->getOrSetChapter('genesis', 1, $callback, ['includeWords' => true]);

        // Clear cache for this chapter
        $this->service->clearChapter('genesis', 1);

        // Should be no cache entries for this chapter
        $keys = Cache::store('bible')->getStore()->getAllKeys();
        $this->assertEmpty(array_filter($keys, fn($key) => str_contains($key, 'genesis:1')));
    }

    /** @test */
    public function it_clears_cache_for_book()
    {
        $callback = fn() => $this->testData;

        // Cache data for multiple chapters
        $this->service->getOrSetChapter('genesis', 1, $callback);
        $this->service->getOrSetChapter('genesis', 2, $callback);
        $this->service->getOrSetChapter('exodus', 1, $callback);

        // Clear cache for genesis
        $this->service->clearBook('genesis');

        // Should be no cache entries for genesis, but exodus should remain
        $keys = Cache::store('bible')->getStore()->getAllKeys();
        $this->assertEmpty(array_filter($keys, fn($key) => str_contains($key, 'genesis:')));
        $this->assertNotEmpty(array_filter($keys, fn($key) => str_contains($key, 'exodus:')));
    }
}
