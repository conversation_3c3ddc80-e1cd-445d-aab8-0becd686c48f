<?php

namespace Tests\Unit;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Schema;
use App\Models\Book;
use App\Enums\Testament;
use App\Enums\BookCategory;
use App\Enums\OriginalLanguage;

class DatabaseSchemaTest extends TestCase
{
    use RefreshDatabase;

    public function test_books_table_has_required_columns()
    {
        $this->assertTrue(Schema::hasTable('books'));
        
        $expected_columns = [
            'id',
            'name',
            'slug',
            'abbreviation',
            'order',
            'testament',
            'category',
            'chapters_count',
            'original_language',
            'location',
            'historical_period',
            'authors',
            'written_year',
            'theme',
            'key_people',
            'attributes_of_god',
            'key_words',
            'covenants',
            'key_teachings',
            'key_verses',
            'search_names',
            'created_at',
            'updated_at'
        ];

        $actual_columns = Schema::getColumnListing('books');
        
        foreach ($expected_columns as $column) {
            $this->assertTrue(in_array($column, $actual_columns), "Column {$column} is missing");
        }
    }

    public function test_books_table_column_types()
    {
        $book = new Book();
        
        // Test required fields
        $this->assertTrue($book->getKeyType() === 'int');
        $this->assertTrue($book->incrementing);
        
        // Test enum fields
        $this->assertTrue(enum_exists(Testament::class));
        $this->assertTrue(enum_exists(BookCategory::class));
        $this->assertTrue(enum_exists(OriginalLanguage::class));
        
        // Test enum values
        $this->assertEquals(['ot', 'nt'], array_column(Testament::cases(), 'value'));
        $this->assertEquals(
            ['law', 'history', 'wisdom', 'prophecy', 'gospel', 'epistle', 'apocalypse'], 
            array_column(BookCategory::cases(), 'value')
        );
        $this->assertEquals(
            ['hebrew', 'greek', 'aramaic'], 
            array_column(OriginalLanguage::cases(), 'value')
        );
    }

    public function test_book_model_can_be_created()
    {
        $book = Book::create([
            'name' => 'Test Book',
            'slug' => 'test-book',
            'abbreviation' => 'TB',
            'order' => 67,
            'testament' => Testament::NT,
            'category' => BookCategory::EPISTLE,
            'chapters_count' => 5,
            'original_language' => OriginalLanguage::GREEK,
            'theme' => 'Test Theme',
            'search_names' => 'Test Book,TB'
        ]);

        $this->assertDatabaseHas('books', [
            'name' => 'Test Book',
            'slug' => 'test-book',
            'abbreviation' => 'TB'
        ]);

        $this->assertEquals(Testament::NT, $book->testament);
        $this->assertEquals(BookCategory::EPISTLE, $book->category);
        $this->assertEquals(OriginalLanguage::GREEK, $book->original_language);
    }
}