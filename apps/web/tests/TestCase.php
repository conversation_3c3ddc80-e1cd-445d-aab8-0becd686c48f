<?php

namespace Tests;

use Illuminate\Foundation\Testing\TestCase as BaseTestCase;
use Illuminate\Support\Facades\View;
use Inertia\Testing\AssertableInertia;
use Illuminate\Testing\TestResponse;
use Illuminate\Support\Facades\Session;

abstract class TestCase extends BaseTestCase
{
    use CreatesApplication;

    protected function setUp(): void
    {
        parent::setUp();

        // Start the session
        Session::start();

        // Disable CSRF token verification for tests
        $this->withoutMiddleware(\App\Http\Middleware\VerifyCsrfToken::class);

        // Configure Inertia test response macros
        TestResponse::macro('props', function ($key = null) {
            $props = json_decode(json_encode($this->original->getData()['page']['props']), JSON_OBJECT_AS_ARRAY);

            if ($key) {
                return data_get($props, $key);
            }

            return $props;
        });
    }

    protected function withoutVite()
    {
        // Mock Vite manifest
        $manifestPath = public_path('build/manifest.json');
        if (!file_exists(dirname($manifestPath))) {
            mkdir(dirname($manifestPath), 0755, true);
        }

        $manifest = [
            'resources/js/app.js' => [
                'file' => 'assets/app.js',
                'src' => 'resources/js/app.js',
                'isEntry' => true,
                'css' => ['assets/app.css']
            ]
        ];

        file_put_contents($manifestPath, json_encode($manifest));

        return $this;
    }
}
