<?php

uses(
    Tests\DuskTestCase::class,
    // Illuminate\Foundation\Testing\DatabaseMigrations::class,
)->in('Browser');

use Illuminate\Foundation\Testing\RefreshDatabase;

/*
|--------------------------------------------------------------------------
| Test Case
|--------------------------------------------------------------------------
|
| The closure you provide to your test functions is always bound to a specific PHPUnit test
| case class. By default, that class is "PHPUnit\Framework\TestCase". Of course, you may
| need to change it using the "uses()" function to bind a different classes or traits.
|
*/

uses(\Tests\TestCase::class, RefreshDatabase::class)->in(__DIR__ . '/Feature');

/*
|--------------------------------------------------------------------------
| Expectations
|--------------------------------------------------------------------------
|
| When you're writing tests, you often need to check that values meet certain conditions. The
| "expect()" function gives you access to a set of "expectations" methods that you can use
| to assert different things. Of course, you may extend the Expectation API at any time.
|
*/

expect()->extend('toBeOne', function () {
    return $this->toBe(1);
});

expect()->extend('toBeBook', function () {
    return $this->value
        ->toBeInstanceOf(App\Models\Book::class)
        ->toHaveProperties(['name', 'abbreviation', 'testament']);
});

expect()->extend('toHaveValidProgress', function () {
    return $this->value
        ->toHaveProperties(['last_chapter_read', 'completion_percentage'])
        ->completion_percentage->toBeBetween(0, 100);
});

/*
|--------------------------------------------------------------------------
| Functions
|--------------------------------------------------------------------------
|
| While Pest is very powerful out-of-the-box, you may have some testing code specific to your
| project that you don't want to repeat in every file. Here you can also expose helpers as
| global functions to help you to reduce the number of lines of code in your test files.
|
*/

function createBookWithChapters(int $chapterCount = 5) {
    $book = \App\Models\Book::factory()->create();
    
    for ($i = 1; $i <= $chapterCount; $i++) {
        $chapter = \App\Models\Chapter::factory()->create([
            'book_id' => $book->id,
            'number' => $i
        ]);
        
        \App\Models\Verse::factory()->count(5)->create([
            'chapter_id' => $chapter->id
        ]);
    }
    
    return $book;
}
