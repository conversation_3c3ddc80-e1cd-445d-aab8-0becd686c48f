<?php

use App\Models\Book;
use App\Models\Chapter;
use App\Models\Verse;
use App\Enums\Testament;
use App\Enums\BookCategory;
use App\Enums\OriginalLanguage;
use Inertia\Testing\AssertableInertia as Assert;
use Tests\TestCase;

uses(TestCase::class, Illuminate\Foundation\Testing\RefreshDatabase::class)->in('Feature');

beforeEach(function () {
    // Mock Vite manifest
    test()->mock(\Illuminate\Foundation\Vite::class, function ($mock) {
        $mock->shouldReceive('__invoke')->andReturn('');
        $mock->shouldReceive('useIntegrityKey')->andReturnSelf();
        $mock->shouldReceive('useBuildDirectory')->andReturnSelf();
        $mock->shouldReceive('withEntryPoints')->andReturnSelf();
    });

    // Create a test book
    $this->book = Book::factory()->create([
        'slug' => 'johannes',
        'name' => '<PERSON>',
        'testament' => Testament::NT,
        'order' => 43, // <PERSON> is the 43rd book in the Bible
        'category' => BookCategory::GOSPEL,
        'chapters_count' => 21,
        'original_language' => OriginalLanguage::GREEK,
        'abbreviation' => 'joh',
        'theme' => 'Das Evangelium von Jesus Christus',
        'search_names' => 'Johannes,John,Joh',
    ]);

    // Create chapter 1
    $this->chapter = Chapter::create([
        'book_id' => $this->book->id,
        'number' => 1,
        'title' => 'Chapter 1',
    ]);

    // Create verses for chapter 1
    for ($i = 1; $i <= 8; $i++) {
        Verse::create([
            'chapter_id' => $this->chapter->id,
            'number' => $i,
            'text' => "Verse $i text",
        ]);
    }

    // Mock the BibleReferenceParser service
    test()->mock(\App\Services\BibleReferenceParser::class, function ($mock) {
        $mock->shouldReceive('parse')->andReturn([
            'book' => $this->book,
            'chapter' => 1,
            'is_frontmatter' => false,
            'verse_start' => null,
            'verse_end' => null,
        ])->byDefault();

        // Special case for frontmatter
        $mock->shouldReceive('parse')->with('johannes')->andReturn([
            'book' => $this->book,
            'chapter' => null,
            'is_frontmatter' => true,
            'verse_start' => null,
            'verse_end' => null,
        ]);

        // Special case for verse reference
        $mock->shouldReceive('parse')->with('johannes1,4')->andReturn([
            'book' => $this->book,
            'chapter' => 1,
            'is_frontmatter' => false,
            'verse_start' => 4,
            'verse_end' => null,
        ]);

        // Special case for verse range
        $mock->shouldReceive('parse')->with('johannes1,4-8')->andReturn([
            'book' => $this->book,
            'chapter' => 1,
            'is_frontmatter' => false,
            'verse_start' => 4,
            'verse_end' => 8,
        ]);

        // Special case for invalid references and nonexistent books
        foreach (['johannes1,4,8', 'johannes1-4', 'johannes1:4', 'invalid1,4-8', 'nonexistentbook1'] as $invalidRef) {
            $mock->shouldReceive('parse')->with($invalidRef)->andThrow(new \Exception('Invalid reference format'));
        }

        // Special case for nonexistent chapter
        $mock->shouldReceive('parse')->with('johannes22')->andThrow(new \Exception('Invalid chapter number'));

        // Special case for nonexistent verse
        $mock->shouldReceive('parse')->with('johannes1,9')->andThrow(new \Exception('Invalid verse number'));
    });

    // Mock the ChapterService
    test()->mock(\App\Services\ChapterService::class, function ($mock) {
        $mock->shouldReceive('getChapterWindow')->andReturn([
            'current' => $this->chapter,
            'previous' => null,
            'previousPrevious' => null,
            'next' => null,
            'nextNext' => null,
            'hasMoreNext' => false,
            'hasMorePrevious' => false,
        ]);
    });
});

test('it starts at chapter 1 for book chapter reference', function () {
    $response = $this->get('/johannes1');

    $response->assertStatus(200);
    $response->assertInertia(fn (Assert $page) => $page
        ->component('Display')
        ->where('reference.book.slug', 'johannes')
        ->where('reference.chapter', 1)
        ->where('reference.frontmatter', false)
        ->where('reference.verseStart', null)
        ->where('reference.verseEnd', null)
    );
});

test('it highlights specific verse for book chapter verse reference', function () {
    $response = $this->get('/johannes1,4');

    $response->assertStatus(200);
    $response->assertInertia(fn (Assert $page) => $page
        ->component('Display')
        ->where('reference.book.slug', 'johannes')
        ->where('reference.chapter', 1)
        ->where('reference.verseStart', 4)
        ->where('reference.verseEnd', null)
    );
});

test('it highlights verse range for book chapter verse range reference', function () {
    $response = $this->get('/johannes1,4-8');

    $response->assertStatus(200);
    $response->assertInertia(fn (Assert $page) => $page
        ->component('Display')
        ->where('reference.book.slug', 'johannes')
        ->where('reference.chapter', 1)
        ->where('reference.verseStart', 4)
        ->where('reference.verseEnd', 8)
    );
});

test('it returns 404 for invalid reference format', function () {
    $invalidReferences = [
        '/johannes1,4,8',     // Invalid format
        '/johannes1-4',       // Invalid format
        '/johannes1:4',       // Invalid format
        '/invalid1,4-8',      // Invalid book
    ];

    foreach ($invalidReferences as $reference) {
        $response = $this->get($reference);
        $response->assertStatus(404);
        $response->assertInertia(fn (Assert $page) => $page
            ->component('NotFound')
        );
    }
});

test('it starts at frontmatter for book only reference', function () {
    $response = $this->get('/johannes');

    $response->assertStatus(200);
    $response->assertInertia(fn (Assert $page) => $page
        ->component('Display')
        ->where('reference.book.slug', 'johannes')
        ->where('reference.frontmatter', true)
    );
});

test('it shows custom page for valid but nonexistent book', function () {
    // First ensure the book doesn't exist
    $this->assertDatabaseMissing('books', ['slug' => 'nonexistentbook']);

    $response = $this->get('/nonexistentbook1');

    $response->assertStatus(404);
    $response->assertInertia(fn (Assert $page) => $page
        ->component('NotFound')
    );
});

test('it returns book with all metadata', function () {
    $response = $this->get('/johannes');

    $response->assertStatus(200);
    $response->assertInertia(fn (Assert $page) => $page
        ->component('Display')
        ->where('reference.book.name', 'Johannes')
        ->where('reference.book.testament', Testament::NT->value)
        ->where('reference.book.category', BookCategory::GOSPEL->value)
        ->where('reference.book.original_language', OriginalLanguage::GREEK->value)
        ->where('reference.book.chapters_count', 21)
        ->where('reference.book.theme', 'Das Evangelium von Jesus Christus')
        ->where('reference.book.search_names', 'Johannes,John,Joh')
    );
});

test('it returns chapter with all verses', function () {
    $response = $this->get('/johannes1');

    $response->assertStatus(200);
    $response->assertInertia(fn (Assert $page) => $page
        ->component('Display')
        ->has('chapter.verses', 8)
        ->where('chapter.number', 1)
        ->where('chapter.title', 'Chapter 1')
    );
});

test('it returns 404 for nonexistent chapter', function () {
    $response = $this->get('/johannes22'); // Book only has 21 chapters

    $response->assertStatus(404);
    $response->assertInertia(fn (Assert $page) => $page
        ->component('NotFound')
    );
});

test('it returns 404 for nonexistent verse', function () {
    $response = $this->get('/johannes1,9'); // Chapter only has 8 verses

    $response->assertStatus(404);
    $response->assertInertia(fn (Assert $page) => $page
        ->component('NotFound')
    );
});
