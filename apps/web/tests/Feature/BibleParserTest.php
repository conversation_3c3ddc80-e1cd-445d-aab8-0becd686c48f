<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Book;
use App\Models\Chapter;
use App\Models\Verse;
use App\Models\Word;
use App\Enums\Testament;
use App\Enums\BookCategory;
use App\Enums\OriginalLanguage;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Console\Commands\ParseBibleUsxCommand;
use App\Services\BibleData\UsxParserState;
use App\Services\BibleData\UsxParserConfig;
use App\Services\BibleData\UsxParserLogger;
use Symfony\Component\Console\Output\BufferedOutput;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

uses(RefreshDatabase::class);

class BibleParserTest extends TestCase
{
    use RefreshDatabase;
    
    protected ParseBibleUsxCommand $command;
    protected string $bibletextPath;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Set path to bibeltext directory
        $this->bibletextPath = '/Applications/MAMP/htdocs/ebtc/esra-bibel/apps/web/bibletext';
        Log::info("Bibeltext path: " . $this->bibletextPath);
        
        // Initialize parser command
        $this->command = new ParseBibleUsxCommand();
    }

    /**
     * Test parsing all USX files in the bibeltext directory
     */
    public function test_parses_all_usx_files()
    {
        $this->markTestSkipped('Parser tests are temporarily disabled');
        
        // Get all USX files
        $usxFiles = glob($this->bibletextPath . '/*/*.usx');
        $this->assertNotEmpty($usxFiles, 'No USX files found in bibeltext directory');
        
        foreach ($usxFiles as $usxFile) {
            $bookDir = dirname($usxFile);
            $bookName = basename($bookDir);
            
            Log::info("Testing USX parsing for {$bookName}");
            Log::info("USX file path: {$usxFile}");
            
            // Create book record
            $book = Book::create([
                'name' => $bookName,
                'slug' => strtolower($bookName),
                'number' => 1,
                'testament' => Testament::NT,
                'abbreviation' => strtoupper(substr($bookName, 0, 3)),
                'order' => 1,
                'chapters_count' => 1,
                'category' => BookCategory::GOSPEL,
                'original_language' => OriginalLanguage::GREEK
            ]);
            
            // Parse the USX file
            $result = $this->artisan('bible:parse-usx', [
                'usx_file' => $usxFile,
                'slug' => strtolower($bookName),
                '--verbose' => true
            ])->assertSuccessful();
            
            // Verify database state
            $this->assertDatabaseHas('books', ['slug' => strtolower($bookName)]);
            $this->assertGreaterThan(0, Chapter::count(), "No chapters found for {$bookName}");
            $this->assertGreaterThan(0, Verse::count(), "No verses found for {$bookName}");
            $this->assertGreaterThan(0, Word::count(), "No words found for {$bookName}");
            
            // Test specific features
            $this->assertTextVariantsForBook($book);
            $this->assertOTQuotesForBook($book);
            
            // Clean up for next book
            DB::table('words')->delete();
            DB::table('verses')->delete();
            DB::table('chapters')->delete();
            DB::table('books')->delete();
        }
    }
    
    /**
     * Assert text variants are properly parsed for a book
     */
    protected function assertTextVariantsForBook(Book $book)
    {
        $variantWords = Word::where('has_variant', true)
            ->whereHas('verse', function ($query) use ($book) {
                $query->whereHas('chapter', function ($query) use ($book) {
                    $query->where('book_id', $book->id);
                });
            })
            ->get();
            
        if ($variantWords->isNotEmpty()) {
            Log::info("Found {$variantWords->count()} variant words in {$book->name}");
            foreach ($variantWords as $word) {
                $this->assertNotNull($word->variant_type);
                $this->assertTrue(in_array($word->variant_type, ['addition', 'not_in_original']));
            }
        } else {
            Log::info("No variant words found in {$book->name}");
        }
    }
    
    /**
     * Assert OT quotes are properly parsed for a book
     */
    protected function assertOTQuotesForBook(Book $book)
    {
        $otQuoteWords = Word::where('is_ot_quote', true)
            ->whereHas('verse', function ($query) use ($book) {
                $query->whereHas('chapter', function ($query) use ($book) {
                    $query->where('book_id', $book->id);
                });
            })
            ->get();
            
        if ($otQuoteWords->isNotEmpty()) {
            Log::info("Found {$otQuoteWords->count()} OT quote words in {$book->name}");
            foreach ($otQuoteWords as $word) {
                $this->assertTrue($word->verse->has_ot_quote);
            }
        } else {
            Log::info("No OT quote words found in {$book->name}");
        }
    }
}
