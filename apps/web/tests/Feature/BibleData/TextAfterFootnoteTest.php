<?php

namespace Tests\Feature\BibleData;

use App\Models\Book;
use App\Models\Chapter;
use App\Models\Verse;
use App\Models\Word;
use App\Models\Footnote;
use App\Services\BibleData\UsxParser;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class TextAfterFootnoteTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test that text after footnotes is correctly processed
     */
    public function test_text_after_footnote_is_processed(): void
    {
        // Create a simple USX XML with a footnote followed by text
        $usxXml = <<<XML
<?xml version="1.0" encoding="utf-8"?>
<usx version="3.0">
  <book code="GEN" style="id">Genesis</book>
  <chapter number="1" style="c" />
  <para style="p">
    <verse number="1" style="v" />This is text before<note caller="+" style="f">This is a footnote</note> and this is text after the footnote.
  </para>
</usx>
XML;

        // Create the book
        $book = Book::create([
            'name' => 'Genesis',
            'abbreviation' => 'GEN',
            'testament' => 'ot',
            'order' => 1,
            'chapters_count' => 50
        ]);

        // Parse the USX XML
        $parser = app(UsxParser::class);
        $parser->parseString($usxXml, $book);

        // Get the verse
        $verse = Verse::where('number', 1)->first();
        
        // Assert that the verse exists
        $this->assertNotNull($verse);
        
        // Get all words for the verse
        $words = Word::where('verse_id', $verse->id)->orderBy('position')->get();
        
        // Assert that we have words
        $this->assertGreaterThan(0, $words->count());
        
        // Check if the words include text after the footnote
        $wordTexts = $words->pluck('text')->toArray();
        $this->assertContains('after', $wordTexts);
        
        // Assert that the verse text includes text after the footnote
        $this->assertStringContainsString('after', $verse->text);
        
        // Assert that the footnote exists
        $footnote = Footnote::where('verse_id', $verse->id)->first();
        $this->assertNotNull($footnote);
    }

    /**
     * Test that text after variants is correctly processed
     */
    public function test_text_after_variant_is_processed(): void
    {
        // Create a simple USX XML with a variant followed by text
        $usxXml = <<<XML
<?xml version="1.0" encoding="utf-8"?>
<usx version="3.0">
  <book code="GEN" style="id">Genesis</book>
  <chapter number="1" style="c" />
  <para style="p">
    <verse number="1" style="v" />This is text before<milestone style="va" sID="variant1" />variant text<milestone style="va*" eID="variant1" /> and this is text after the variant.
  </para>
</usx>
XML;

        // Create the book
        $book = Book::create([
            'name' => 'Genesis',
            'abbreviation' => 'GEN',
            'testament' => 'ot',
            'order' => 1,
            'chapters_count' => 50
        ]);

        // Parse the USX XML
        $parser = app(UsxParser::class);
        $parser->parseString($usxXml, $book);

        // Get the verse
        $verse = Verse::where('number', 1)->first();
        
        // Assert that the verse exists
        $this->assertNotNull($verse);
        
        // Get all words for the verse
        $words = Word::where('verse_id', $verse->id)->orderBy('position')->get();
        
        // Assert that we have words
        $this->assertGreaterThan(0, $words->count());
        
        // Check if the words include text after the variant
        $wordTexts = $words->pluck('text')->toArray();
        $this->assertContains('after', $wordTexts);
        
        // Assert that the verse text includes text after the variant
        $this->assertStringContainsString('after', $verse->text);
        
        // Assert that the verse has a text variant
        $this->assertTrue($verse->has_text_variant);
    }

    /**
     * Test that text after character styles is correctly processed
     */
    public function test_text_after_character_style_is_processed(): void
    {
        // Create a simple USX XML with a character style followed by text
        $usxXml = <<<XML
<?xml version="1.0" encoding="utf-8"?>
<usx version="3.0">
  <book code="GEN" style="id">Genesis</book>
  <chapter number="1" style="c" />
  <para style="p">
    <verse number="1" style="v" />This is text before<char style="it">italic text</char> and this is text after the character style.
  </para>
</usx>
XML;

        // Create the book
        $book = Book::create([
            'name' => 'Genesis',
            'abbreviation' => 'GEN',
            'testament' => 'ot',
            'order' => 1,
            'chapters_count' => 50
        ]);

        // Parse the USX XML
        $parser = app(UsxParser::class);
        $parser->parseString($usxXml, $book);

        // Get the verse
        $verse = Verse::where('number', 1)->first();
        
        // Assert that the verse exists
        $this->assertNotNull($verse);
        
        // Get all words for the verse
        $words = Word::where('verse_id', $verse->id)->orderBy('position')->get();
        
        // Assert that we have words
        $this->assertGreaterThan(0, $words->count());
        
        // Check if the words include text after the character style
        $wordTexts = $words->pluck('text')->toArray();
        $this->assertContains('after', $wordTexts);
        
        // Assert that the verse text includes text after the character style
        $this->assertStringContainsString('after', $verse->text);
    }
}
