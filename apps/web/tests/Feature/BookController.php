<?php

use App\Models\Book;
use App\Models\User;
use function Pest\Laravel\actingAs;
use function Pest\Laravel\get;
use Tests\TestCase;

beforeEach(function () {
    $this->user = User::factory()->create();
    $this->oldTestamentBook = Book::factory()->create(['testament' => 'old']);
    $this->newTestamentBook = Book::factory()->create(['testament' => 'new']);
});

it('displays books grouped by testament', function () {
    actingAs($this->user)
        ->get(route('books.index'))
        ->assertInertia(fn ($page) => $page
            ->component('Books/Index')
            ->has('books.old')
            ->has('books.new')
        );
});

it('shows book details', function () {
    actingAs($this->user)
        ->get(route('books.show', $this->oldTestamentBook))
        ->assertInertia(fn ($page) => $page
            ->component('Books/Show')
            ->has('book')
            ->has('readingProgress')
            ->has('statistics')
        );
});

it('requires authentication to access book details', function () {
    get(route('books.show', $this->oldTestamentBook))
        ->assertRedirect(route('login'));
});
