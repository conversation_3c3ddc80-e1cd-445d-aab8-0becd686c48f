<?php

declare(strict_types=1);

namespace Tests\Feature;

use App\Console\Commands\ChangelogNewCommand;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\File;
use Tests\TestCase;

class ChangelogNewCommandTest extends TestCase
{
    private string $changelogPath;
    private string $packageJsonPath;
    private string $envVersionPath;
    private string $testVersion = '1.2.3';
    private string $testDate;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Set up test paths
        $this->changelogPath = base_path('CHANGELOG.md');
        $this->packageJsonPath = base_path('package.json');
        $this->envVersionPath = base_path('.env.VERSION');
        
        // Backup existing files if they exist
        $this->backupFiles();
        
        // Set fixed date for testing
        $this->testDate = Carbon::now()->format('Y-m-d');
        Carbon::setTestNow(Carbon::createFromFormat('Y-m-d', $this->testDate));
    }

    protected function tearDown(): void
    {
        // Restore backed up files
        $this->restoreFiles();
        
        // Reset Carbon mock
        Carbon::setTestNow();
        
        parent::tearDown();
    }
    
    /** @test */
    public function it_creates_new_changelog_file_if_none_exists()
    {
        // Ensure changelog doesn't exist
        if (File::exists($this->changelogPath)) {
            File::delete($this->changelogPath);
        }
        
        // Create package.json with test version
        File::put($this->packageJsonPath, json_encode(['version' => $this->testVersion]));
        
        // Run the command
        $this->artisan('changelog:new', ['--message' => 'Test message'])
            ->expectsOutput("Creating changelog entry for version {$this->testVersion}")
            ->expectsOutput('Created new CHANGELOG.md file')
            ->assertExitCode(0);
        
        // Check that changelog was created with correct content
        $this->assertFileExists($this->changelogPath);
        $content = File::get($this->changelogPath);
        
        $this->assertStringContainsString("## [{$this->testVersion}] - {$this->testDate}", $content);
        $this->assertStringContainsString('### Added', $content);
        $this->assertStringContainsString('- Test message', $content);
    }
    
    /** @test */
    public function it_adds_new_version_to_existing_changelog()
    {
        // Create existing changelog
        $existingContent = <<<EOT
# Changelog

All notable changes to this project will be documented in this file.

## [1.0.0] - 2025-01-01

### Added

- Initial release
EOT;
        File::put($this->changelogPath, $existingContent);
        
        // Create package.json with test version
        File::put($this->packageJsonPath, json_encode(['version' => $this->testVersion]));
        
        // Run the command
        $this->artisan('changelog:new', ['--message' => 'New feature'])
            ->expectsOutput("Creating changelog entry for version {$this->testVersion}")
            ->expectsOutput("Added new entry for version {$this->testVersion}")
            ->assertExitCode(0);
        
        // Check that changelog was updated with new version
        $content = File::get($this->changelogPath);
        
        $this->assertStringContainsString("## [{$this->testVersion}] - {$this->testDate}", $content);
        $this->assertStringContainsString('- New feature', $content);
        $this->assertStringContainsString('## [1.0.0] - 2025-01-01', $content);
    }
    
    /** @test */
    public function it_updates_existing_version_entry()
    {
        // Create existing changelog with the test version
        $existingContent = <<<EOT
# Changelog

All notable changes to this project will be documented in this file.

## [{$this->testVersion}] - {$this->testDate}

### Added

- Existing feature
EOT;
        File::put($this->changelogPath, $existingContent);
        
        // Create package.json with test version
        File::put($this->packageJsonPath, json_encode(['version' => $this->testVersion]));
        
        // Run the command with confirmation
        $this->artisan('changelog:new', ['--message' => 'Another feature'])
            ->expectsConfirmation(
                "Version {$this->testVersion} already exists in the changelog. Do you want to update it?",
                'yes'
            )
            ->expectsOutput("Creating changelog entry for version {$this->testVersion}")
            ->expectsOutput("Updated existing entry for version {$this->testVersion}")
            ->assertExitCode(0);
        
        // Check that changelog was updated
        $content = File::get($this->changelogPath);
        
        $this->assertStringContainsString("## [{$this->testVersion}] - {$this->testDate}", $content);
        $this->assertStringContainsString('- Existing feature', $content);
        $this->assertStringContainsString('- Another feature', $content);
    }
    
    /** @test */
    public function it_reads_version_from_env_version_file()
    {
        // Ensure changelog doesn't exist
        if (File::exists($this->changelogPath)) {
            File::delete($this->changelogPath);
        }
        
        // Create .env.VERSION with test version
        File::put($this->envVersionPath, "VERSION={$this->testVersion}");
        
        // Run the command
        $this->artisan('changelog:new', ['--message' => 'Test message'])
            ->expectsOutput("Creating changelog entry for version {$this->testVersion}")
            ->assertExitCode(0);
        
        // Check that changelog was created with correct version
        $content = File::get($this->changelogPath);
        $this->assertStringContainsString("## [{$this->testVersion}] - {$this->testDate}", $content);
    }
    
    private function backupFiles(): void
    {
        foreach ([$this->changelogPath, $this->packageJsonPath, $this->envVersionPath] as $path) {
            if (File::exists($path)) {
                File::copy($path, $path . '.bak');
            }
        }
    }
    
    private function restoreFiles(): void
    {
        foreach ([$this->changelogPath, $this->packageJsonPath, $this->envVersionPath] as $path) {
            // Delete test file
            if (File::exists($path)) {
                File::delete($path);
            }
            
            // Restore backup if it exists
            if (File::exists($path . '.bak')) {
                File::copy($path . '.bak', $path);
                File::delete($path . '.bak');
            }
        }
    }
}
