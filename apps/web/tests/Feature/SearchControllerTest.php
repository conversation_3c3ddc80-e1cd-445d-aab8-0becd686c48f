<?php

use App\Models\Book;
use App\Enums\Testament;
use App\Enums\BookCategory;
use App\Enums\OriginalLanguage;
use App\Services\Search\BibleSearchService;
use App\Services\BibleReferenceService;
use App\DataTransferObjects\SearchResult;
use App\Services\BookOrganizationService;
use App\DataTransferObjects\ReferenceResult;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Inertia\Testing\AssertableInertia as Assert;
use Mockery;
use Illuminate\Support\Facades\File;

uses(RefreshDatabase::class);

beforeEach(function () {
    // Mock Vite Manifest
    File::makeDirectory(public_path('build'), 0755, true, true);
    File::put(
        public_path('build/manifest.json'),
        json_encode([
            'resources/js/app.ts' => [
                'file' => 'assets/app-4ed993c7.js',
                'src' => 'resources/js/app.ts',
                'isEntry' => true,
                'css' => ['assets/app-3ea8b221.css']
            ],
            'resources/js/Pages/Start.vue' => [
                'file' => 'assets/Start-4ed993c7.js',
                'src' => 'resources/js/Pages/Start.vue',
                'isDynamicEntry' => true,
            ],
            'resources/js/Pages/SearchResults.vue' => [
                'file' => 'assets/SearchResults-4ed993c7.js',
                'src' => 'resources/js/Pages/SearchResults.vue',
                'isDynamicEntry' => true,
            ],
        ])
    );

    // Create a test book
    $this->book = Book::factory()->create([
        'name' => '1.Mose',
        'abbreviation' => '1Mo',
        'chapters_count' => 50,
        'testament' => Testament::OT->value,
        'category' => BookCategory::LAW->value,
        'original_language' => OriginalLanguage::HEBREW->value,
        'order' => 1
    ]);

    // Mock the services
    $this->searchService = Mockery::mock(BibleSearchService::class);
    $this->referenceService = Mockery::mock(BibleReferenceService::class);
    $this->bookOrganizationService = Mockery::mock(BookOrganizationService::class);

    app()->instance(BibleSearchService::class, $this->searchService);
    app()->instance(BibleReferenceService::class, $this->referenceService);
    app()->instance(BookOrganizationService::class, $this->bookOrganizationService);

    // Set up default mocks
    $this->bookOrganizationService
        ->shouldReceive('getOrganizedBooks')
        ->andReturn([]);
});

afterEach(function () {
    File::deleteDirectory(public_path('build'));
});

test('it redirects to correct url for bible references', function () {
    // Test cases with different URL patterns
    $testCases = [
        // Simple chapter reference
        ['query' => '1.Mose1', 'expected' => '/1.Mose1'],
        ['query' => '1.Mose 1', 'expected' => '/1.Mose1'],
        ['query' => '1. Mose 1', 'expected' => '/1.Mose1'],

        // With verse
        ['query' => '1.Mose1,1', 'expected' => '/1.Mose1,1'],
        ['query' => '1.Mose 1,1', 'expected' => '/1.Mose1,1'],
        ['query' => '1. Mose 1,1', 'expected' => '/1.Mose1,1'],

        // With verse range
        ['query' => '1.Mose1,1-3', 'expected' => '/1.Mose1,1-3'],
        ['query' => '1.Mose 1,1-3', 'expected' => '/1.Mose1,1-3'],
        ['query' => '1. Mose 1,1-3', 'expected' => '/1.Mose1,1-3'],
    ];

    foreach ($testCases as $case) {
        // Mock the reference service to return a valid reference
        $this->referenceService
            ->shouldReceive('parseReference')
            ->with($case['query'])
            ->andReturn(new ReferenceResult(
                book: $this->book,
                chapter: 1,
                verse: null,
                verseEnd: null,
                url: ltrim($case['expected'], '/')
            ));

        $response = $this->get('/search?q=' . urlencode($case['query']));
        $response->assertRedirect($case['expected']);
    }
});

test('it returns search page for invalid reference', function () {
    $this->referenceService
        ->shouldReceive('parseReference')
        ->with('invalid reference')
        ->andReturnNull();

    $this->searchService
        ->shouldReceive('search')
        ->andReturn(SearchResult::fromSearch([]));

    $response = $this->get('/search?q=invalid reference');
    $response->assertOk()
        ->assertInertia(fn (Assert $page) =>
            $page->component('SearchResults')
                ->has('query')
                ->has('types')
                ->has('filters')
                ->has('results')
        );
});

test('it returns search page when no query', function () {
    $response = $this->get('/search');
    $response->assertOk()
        ->assertInertia(fn (Assert $page) =>
            $page->component('Start')
                ->has('query')
                ->has('types')
                ->has('filters')
        );
});
