<?php

namespace Tests\Feature;

use App\Models\Book;
use App\Models\Chapter;
use App\Models\Verse;
use App\Models\Word;
use App\Services\BibleData\UsxParser;
use App\Services\BibleData\UsxParserConfig;
use App\Services\BibleData\UsxParserLogger;
use App\Services\BibleData\UsxParserState;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;

class FootnotePunctuationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test book
        $this->book = Book::factory()->create([
            'name' => 'Test Book',
            'abbreviation' => 'TST',
            'testament' => 'NT',
        ]);
    }

    /** @test */
    public function it_correctly_attaches_punctuation_after_footnotes()
    {
        // Create a simple USX string with a footnote followed by punctuation
        $usxContent = <<<XML
<?xml version='1.0' encoding='utf-8' standalone='yes'?>
<usx version="3.0">
  <book code="TST" style="id">
    <chapter number="1" style="c" sid="TST 1"/>
    <para style="p">
      <verse number="1" style="v" sid="TST 1:1"/>This is a test verse with a footnote<note style="f" caller="a">This is a footnote.</note>.<verse eid="TST 1:1"/>
    </para>
  </book>
</usx>
XML;

        // Create a temporary file with the USX content
        $tempFile = tempnam(sys_get_temp_dir(), 'usx_test_');
        file_put_contents($tempFile, $usxContent);

        // Set up the parser
        $config = new UsxParserConfig();
        $state = new UsxParserState($config);
        $logger = new UsxParserLogger();
        
        // Create the parser
        $parser = new UsxParser($state, $logger);
        
        // Parse the test file
        $parser->parseFile($tempFile, $this->book);
        
        // Clean up
        unlink($tempFile);
        
        // Get the verse and its words
        $verse = Verse::where('number', 1)->first();
        $this->assertNotNull($verse, 'Verse should be created');
        
        $words = $verse->words()->orderBy('position')->get();
        
        // The last word should be "footnote" with the period attached
        $lastWord = $words->last();
        $this->assertNotNull($lastWord, 'There should be words in the verse');
        $this->assertEquals('footnote.', $lastWord->text, 'The last word should have the punctuation attached');
        
        // The complete verse text should include the punctuation
        $this->assertStringEndsWith('.', $verse->text, 'The verse text should end with the punctuation');
    }

    /** @test */
    public function it_correctly_handles_multiple_punctuation_marks_after_footnotes()
    {
        // Create a USX string with a footnote followed by multiple punctuation marks
        $usxContent = <<<XML
<?xml version='1.0' encoding='utf-8' standalone='yes'?>
<usx version="3.0">
  <book code="TST" style="id">
    <chapter number="1" style="c" sid="TST 1"/>
    <para style="p">
      <verse number="2" style="v" sid="TST 1:2"/>Another test verse with a footnote<note style="f" caller="b">Another footnote.</note>!?<verse eid="TST 1:2"/>
    </para>
  </book>
</usx>
XML;

        // Create a temporary file with the USX content
        $tempFile = tempnam(sys_get_temp_dir(), 'usx_test_');
        file_put_contents($tempFile, $usxContent);

        // Set up the parser
        $config = new UsxParserConfig();
        $state = new UsxParserState($config);
        $logger = new UsxParserLogger();
        
        // Create the parser
        $parser = new UsxParser($state, $logger);
        
        // Parse the test file
        $parser->parseFile($tempFile, $this->book);
        
        // Clean up
        unlink($tempFile);
        
        // Get the verse and its words
        $verse = Verse::where('number', 2)->first();
        $this->assertNotNull($verse, 'Verse should be created');
        
        $words = $verse->words()->orderBy('position')->get();
        
        // The last word should be "footnote" with the punctuation attached
        $lastWord = $words->last();
        $this->assertNotNull($lastWord, 'There should be words in the verse');
        $this->assertEquals('footnote!?', $lastWord->text, 'The last word should have the multiple punctuation marks attached');
        
        // The complete verse text should include the punctuation
        $this->assertStringEndsWith('!?', $verse->text, 'The verse text should end with the punctuation marks');
    }
}
