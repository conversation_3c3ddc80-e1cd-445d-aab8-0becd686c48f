<?php

namespace Tests\Feature\Api;

use App\Models\Book;
use App\Models\Chapter;
use App\Models\Verse;
use App\Models\Word;
use App\Models\Footnote;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class BibleTextEndpointTest extends TestCase
{
    use RefreshDatabase;

    private Book $book;
    private Chapter $chapter;
    private array $verses;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data
        $this->book = Book::factory()->create([
            'slug' => 'Johannes',
            'name' => '<PERSON>',
            'order' => 43,
            'testament' => 'nt',
            'category' => 'gospel',
            'chapters_count' => 21
        ]);

        $this->chapter = Chapter::factory()->create([
            'book_id' => $this->book->id,
            'number' => 1,
            'title' => 'Kapitel 1'
        ]);

        // Create 10 verses with words and footnotes
        $this->verses = [];
        for ($i = 1; $i <= 10; $i++) {
            $verse = Verse::factory()->create([
                'chapter_id' => $this->chapter->id,
                'number' => $i
            ]);

            // Add words to verse
            Word::factory()->create([
                'verse_id' => $verse->id,
                'text' => 'Word',
                'position' => 1,
                'strongs_number' => 'G1234'
            ]);

            Word::factory()->create([
                'verse_id' => $verse->id,
                'text' => 'Test',
                'position' => 2,
                'strongs_number' => 'G5678'
            ]);

            // Add a footnote
            Footnote::factory()->create([
                'verse_id' => $verse->id,
                'searchable_text' => 'Test footnote',
                'content_structure' => json_encode(['text' => 'Test footnote']),
                'content' => 'Test footnote',
                'position' => 1
            ]);

            $this->verses[] = $verse;
        }
    }

    /** @test */
    public function it_can_get_entire_book()
    {
        $response = $this->getJson('/api/bible/Johannes');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'book' => [
                    'slug',
                    'name',
                    'canonicalOrder',
                    'testament',
                    'category'
                ],
                'reference' => [
                    'chapter',
                    'verseStart',
                    'verseEnd'
                ],
                'verses' => [
                    '*' => [
                        'number',
                        'chapter',
                        'text',
                        'words' => [
                            '*' => [
                                'text',
                                'position',
                                'strongs_number'
                            ]
                        ]
                    ]
                ],
                'metadata' => [
                    'includesFootnotes',
                    'totalVerses',
                    'totalChapters'
                ]
            ])
            ->assertJson([
                'book' => [
                    'slug' => 'Johannes',
                    'name' => 'Johannes',
                    'canonicalOrder' => 43,
                    'testament' => 'nt',
                    'category' => 'gospel'
                ],
                'metadata' => [
                    'includesFootnotes' => false,
                    'totalVerses' => 10,
                    'totalChapters' => 21
                ]
            ]);
    }

    /** @test */
    public function it_can_get_specific_chapter()
    {
        $response = $this->getJson('/api/bible/Johannes1');

        $response->assertStatus(200)
            ->assertJson([
                'reference' => [
                    'chapter' => 1,
                    'verseStart' => null,
                    'verseEnd' => null
                ],
                'metadata' => [
                    'totalVerses' => 10
                ]
            ]);
    }

    /** @test */
    public function it_can_get_single_verse()
    {
        $response = $this->getJson('/api/bible/Johannes1,1');

        $response->assertStatus(200)
            ->assertJson([
                'reference' => [
                    'chapter' => 1,
                    'verseStart' => 1,
                    'verseEnd' => 1
                ],
                'metadata' => [
                    'totalVerses' => 1,
                    'totalChapters' => 21
                ]
            ]);
    }

    /** @test */
    public function it_can_get_verse_range()
    {
        $response = $this->getJson('/api/bible/Johannes1,1-5');

        $response->assertStatus(200)
            ->assertJson([
                'reference' => [
                    'chapter' => 1,
                    'verseStart' => 1,
                    'verseEnd' => 5
                ],
                'metadata' => [
                    'totalVerses' => 5
                ]
            ]);

        $this->assertCount(5, $response->json('verses'));
    }

    /** @test */
    public function it_includes_footnotes_when_requested()
    {
        $response = $this->getJson('/api/bible/Johannes1,1?footnotes=true');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'verses' => [
                    '*' => [
                        'footnotes' => [
                            '*' => [
                                'content',
                                'position',
                                'searchable_text',
                                'content_structure'
                            ]
                        ]
                    ]
                ]
            ])
            ->assertJson([
                'metadata' => [
                    'includesFootnotes' => true
                ]
            ]);

        $this->assertNotEmpty($response->json('verses.0.footnotes'));
    }

    /** @test */
    public function it_returns_404_for_invalid_book()
    {
        $response = $this->getJson('/api/bible/InvalidBook');

        $response->assertStatus(404)
            ->assertJsonStructure([
                'error',
                'message'
            ]);
    }

    /** @test */
    public function it_returns_404_for_invalid_chapter()
    {
        $response = $this->getJson('/api/bible/Johannes99');

        $response->assertStatus(404)
            ->assertJsonStructure([
                'error',
                'message'
            ]);
    }

    /** @test */
    public function it_returns_404_for_invalid_verse()
    {
        $response = $this->getJson('/api/bible/Johannes1,999');

        $response->assertStatus(404)
            ->assertJsonStructure([
                'error',
                'message'
            ]);
    }
}
