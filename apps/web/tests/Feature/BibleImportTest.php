<?php

use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use App\Models\Book;
use Tests\TestCase;

beforeEach(function () {
    // Set test database name
    config(['database.connections.mysql.database' => env('DB_DATABASE_TEST', 'esra-bibel-test')]);

    // Clear the database connection
    DB::purge('mysql');
    DB::reconnect('mysql');

    // Run migrations and seed books
    Artisan::call('migrate:fresh');
    Artisan::call('db:seed', ['--class' => 'Database\\Seeders\\BooksTableSeeder']);

    // Debug: Check what books are in the database
    $books = Book::all();
    foreach ($books as $book) {
        info("Book in database: {$book->name} (slug: {$book->slug})");
    }

    // Debug: Check the current database connection
    info("Current database: " . DB::connection()->getDatabaseName());
});

function assertUsingTestDatabase(): void
{
    expect(DB::connection()->getDatabaseName())
        ->toBe(env('DB_DATABASE_TEST', 'esra-bibel-test'))
        ->and(fn() => throw_if(
            DB::connection()->getDatabaseName() !== env('DB_DATABASE_TEST', 'esra-bibel-test'),
            new Exception('Not using test database')
        ));
}

function getBibletextFolders(): array
{
    $path = base_path('bibletext');
    return array_filter(
        scandir($path),
        fn($item) => $item !== '.' && $item !== '..' && is_dir($path . '/' . $item)
    );
}

function parseUsxFile(string $folder): void
{
    $usxPath = base_path("bibletext/{$folder}/{$folder}.usx");
    expect(file_exists($usxPath))->toBeTrue("{$folder} USX file not found");

    try {
        // Run the parser command
        test()->artisan('bible:parse-usx', [
            'usx_file' => $usxPath,
            'slug' => $folder,
            '-v' => true
        ]);

        // Give the database a moment to process
        sleep(1);

        // Verify database entries - just check for the slug
        expect(DB::table('books')->where('slug', $folder)->exists())
            ->toBeTrue("Book with slug {$folder} not found in database");

        // Debug: Log the current state of the books table
        $books = Book::all();
        foreach ($books as $book) {
            info("Book in database: {$book->name} (slug: {$book->slug})");
        }

        // Check if verses were created
        $book = Book::where('slug', $folder)->first();

        // Log the current database connection state
        info("Current database connection: " . DB::connection()->getDatabaseName());

        expect($book)->not->toBeNull("{$folder} book not found in database");

        $verseCount = $book->verses()->count();
        expect($verseCount)->toBeGreaterThan(0, "No verses were created for {$folder}");

    } catch (Exception $e) {
        test()->fail("Failed to parse USX file for {$folder}. Error: " . $e->getMessage());
    }
}

test('xml to usx conversion', function () {
    assertUsingTestDatabase();

    $folders = getBibletextFolders();
    expect($folders)->not->toBeEmpty('No book folders found in bibletext directory');

    foreach ($folders as $folder) {
        // Use XML file from bibletext folder
        $xmlPath = base_path("bibletext/{$folder}/source.xml");
        $usxPath = base_path("bibletext/{$folder}/{$folder}.usx");

        // Run the conversion script
        $pythonScript = base_path('scripts/import/xml2usx.py');
        shell_exec("python3 {$pythonScript} {$xmlPath} {$folder}");

        // Verify USX file was created
        expect(file_exists($usxPath))
            ->toBeTrue("USX file was not created for {$folder}");

        // Verify USX content
        $usxContent = file_get_contents($usxPath);
        $lines = explode("\n", $usxContent);
        
        expect($lines[0])->toBe('<?xml version=\'1.0\' encoding=\'utf-8\' standalone=\'yes\'?>', "Missing XML declaration in {$folder}");
        expect($lines[1])->toBe('<usx version="3.0">', "Missing USX version in {$folder}");
        expect($lines[2])->toBe('  <book code="JHN" style="id">', "Missing book code in {$folder}");
    }
});

test('usx parsing and database import', function () {
    assertUsingTestDatabase();

    $folders = getBibletextFolders();
    expect($folders)->not->toBeEmpty('No book folders found in bibletext directory');

    foreach ($folders as $folder) {
        parseUsxFile($folder);
    }
});

test('complete import chain', function () {
    assertUsingTestDatabase();

    $folders = getBibletextFolders();
    expect($folders)->not->toBeEmpty('No book folders found in bibletext directory');

    foreach ($folders as $folder) {
        // Use XML file from bibletext folder
        $xmlPath = base_path("bibletext/{$folder}/source.xml");

        // First convert XML to USX
        $pythonScript = base_path('scripts/import/xml2usx.py');
        shell_exec("python3 {$pythonScript} {$xmlPath} {$folder}");

        // Then parse the USX file
        parseUsxFile($folder);
    }
});

function assertDatabaseStructure(Book $book): void
{
    // Book structure
    expect($book->name)->not->toBeNull();
    expect($book->slug)->not->toBeNull();
    expect($book->testament)->not->toBeNull();

    // Get first chapter
    $chapter = $book->chapters()->first();
    expect($chapter)->not->toBeNull();

    // Verify chapter structure
    expect($chapter->number)->not->toBeNull();
    expect($chapter->book_id)->toBe($book->id);

    // Get first verse
    $verse = $chapter->verses()->first();
    expect($verse)->not->toBeNull();

    // Verify verse structure
    expect($verse->number)->not->toBeNull();
    expect($verse->text)->not->toBeNull();
    expect($verse->chapter_id)->toBe($chapter->id);

    // Verify words if present
    if ($verse->words()->exists()) {
        $word = $verse->words()->first();
        expect($word)->not->toBeNull();
        expect($word->text)->not->toBeNull();
        expect($word->position)->not->toBeNull();
    }

    // Verify footnotes if present
    if ($verse->footnotes()->exists()) {
        $footnote = $verse->footnotes()->first();
        expect($footnote)->not->toBeNull();
        expect($footnote->content)->not->toBeNull();
    }
}
