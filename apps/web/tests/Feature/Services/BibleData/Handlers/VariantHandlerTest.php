<?php

namespace Tests\Feature\Services\BibleData\Handlers;

use App\Models\Book;
use App\Models\Chapter;
use App\Models\Verse;
use App\Models\Word;
use App\Services\BibleData\Handlers\VariantHandler;
use App\Services\BibleData\UsxParserState;
use App\Services\BibleData\UsxParserConfig;
use App\Services\BibleData\UsxParserLogger;
use App\Services\BibleData\ModelFactory;
use DOMNode;
use Illuminate\Foundation\Testing\RefreshDatabase;
use SimpleXMLElement;
use Symfony\Component\Console\Output\BufferedOutput;
use Tests\TestCase;
use XMLReader;

class VariantHandlerTest extends TestCase
{
    use RefreshDatabase;
    
    protected $output;
    protected $config;
    protected $state;
    protected $logger;
    protected $modelFactory;
    protected $variantHandler;
    protected $xmlPath;
    protected $reader;
    protected $xmlReader;
    
    protected function setUp(): void
    {
        parent::setUp();
        
        // Set up the test components
        $this->output = new BufferedOutput();
        $this->config = new UsxParserConfig(['is_test' => true]);
        $this->state = new UsxParserState($this->config);
        $this->logger = new UsxParserLogger($this->output, true, false);
        $this->modelFactory = new ModelFactory($this->state, $this->logger);
        
        // Create the variant handler
        $this->variantHandler = new VariantHandler($this->state, $this->logger, $this->modelFactory);
        
        // Set up the XML reader
        $this->xmlPath = '/Applications/MAMP/htdocs/ebtc/esra-bibel/apps/web/bibeltext/Johannes/Johannes.xml';
        $this->reader = new XMLReader();
        $this->reader->open($this->xmlPath);
        $this->xmlReader = $this->reader;
    }
    
    protected function tearDown(): void
    {
        if (isset($this->reader) && $this->reader instanceof XMLReader) {
            $this->reader->close();
        }
        
        parent::tearDown();
    }
    
    /**
     * Helper method to convert SimpleXMLElement to DOMNode
     */
    protected function convertToDOMNode(SimpleXMLElement $element): DOMNode
    {
        $dom = dom_import_simplexml($element);
        return $dom;
    }
    
    public function test_variant_handler_can_process_xml_file()
    {
        // Create test book for Johannes
        $book = Book::create([
            'name' => 'Johannes',
            'slug' => 'johannes',
            'order' => 4,
            'abbreviation' => 'Joh',
            'chapters_count' => 21
        ]);
        
        $this->state->book = $book;
        
        // Create test chapter
        $chapter = Chapter::create([
            'book_id' => $book->id,
            'number' => 1,
            'title' => 'Chapter 1'
        ]);
        
        $this->state->chapter = $chapter;
        
        // Create test verse
        $verse = Verse::create([
            'chapter_id' => $chapter->id,
            'number' => 1,
            'text' => 'Test verse'
        ]);
        
        $this->state->verse = $verse;
        
        // Process the XML file
        $foundElement = false;
        
        // Open the XML file
        if ($this->reader->open($this->xmlPath)) {
            // Read through the XML
            while ($this->reader->read()) {
                // We only need to find at least one element to verify the handler can process the file
                if ($this->reader->nodeType == XMLReader::ELEMENT) {
                    $foundElement = true;
                    break;
                }
            }
        }
        
        // Assert that we found at least one element
        $this->assertTrue($foundElement, 'No elements found in the XML file');
        
        // Test passed if we got here without errors
        $this->assertTrue(true);
    }
    
    public function test_variant_handler_can_process_primary_variants()
    {
        // Create test book for Johannes
        $book = Book::create([
            'name' => 'Johannes',
            'slug' => 'johannes',
            'order' => 4,
            'abbreviation' => 'Joh',
            'chapters_count' => 21
        ]);
        
        $this->state->book = $book;
        
        // Create chapter
        $chapter = Chapter::create([
            'book_id' => $book->id,
            'number' => 1
        ]);
        
        $this->state->chapter = $chapter;
        
        // Create verse
        $verse = Verse::create([
            'chapter_id' => $chapter->id,
            'number' => 1,
            'text' => 'Test verse'
        ]);
        
        $this->state->verse = $verse;
        
        // Manually create a variant milestone and primary variant char
        $variantMilestone = simplexml_load_string('<ms sid="test_variant" type="va" />');
        $domNode = $this->convertToDOMNode($variantMilestone);
        $this->variantHandler->handle($domNode, $this->xmlReader);
        
        // Verify state was updated
        $this->assertEquals('var_' . substr(md5('test_variant' . $this->state->verse->id), 0, 8), $this->state->currentVariantGroupId);
        
        // Create a primary variant char element
        $primaryVariant = simplexml_load_string('<char style="vp">Primary variant text</char>');
        $domNode = $this->convertToDOMNode($primaryVariant);
        $this->variantHandler->handle($domNode, $this->xmlReader);
        
        // End the variant
        $endMilestone = simplexml_load_string('<ms eid="test_variant" />');
        $domNode = $this->convertToDOMNode($endMilestone);
        $this->variantHandler->handle($domNode, $this->xmlReader);
        
        // Verify state was reset
        $this->assertNull($this->state->currentVariantGroupId);
        
        // Verify verse was marked as having a variant
        $verse->refresh();
        $this->assertTrue($verse->has_text_variant);
    }
    
    public function test_variant_handler_handles_nested_variants_correctly()
    {
        // Create test book, chapter, and verse
        $book = Book::create([
            'name' => 'TestBook',
            'slug' => 'testbook',
            'order' => 1,
            'abbreviation' => 'TB',
            'chapters_count' => 1
        ]);
        
        $this->state->book = $book;
        
        $chapter = Chapter::create([
            'book_id' => $book->id,
            'number' => 1
        ]);
        
        $this->state->chapter = $chapter;
        
        $verse = Verse::create([
            'chapter_id' => $chapter->id,
            'number' => 1,
            'text' => 'Test verse'
        ]);
        
        $this->state->verse = $verse;
        
        // Start outer variant
        $outerElement = simplexml_load_string('<ms sid="outer" type="va" />');
        $domNode = $this->convertToDOMNode($outerElement);
        $this->variantHandler->handle($domNode, $this->xmlReader);
        
        // Verify state was updated
        $this->assertEquals('var_' . substr(md5('outer' . $this->state->verse->id), 0, 8), $this->state->currentVariantGroupId);
        
        // Start inner variant
        $innerElement = simplexml_load_string('<ms sid="inner" type="va" />');
        $domNode = $this->convertToDOMNode($innerElement);
        $this->variantHandler->handle($domNode, $this->xmlReader);
        
        // Verify state updated to inner variant
        $this->assertEquals('var_' . substr(md5('inner' . $this->state->verse->id), 0, 8), $this->state->currentVariantGroupId);
        
        // End inner variant
        $innerEndElement = simplexml_load_string('<ms eid="inner" />');
        $domNode = $this->convertToDOMNode($innerEndElement);
        $this->variantHandler->handle($domNode, $this->xmlReader);
        
        // Verify we're back to the outer variant
        $this->assertEquals('var_' . substr(md5('outer' . $this->state->verse->id), 0, 8), $this->state->currentVariantGroupId);
        
        // End outer variant
        $outerEndElement = simplexml_load_string('<ms eid="outer" />');
        $domNode = $this->convertToDOMNode($outerEndElement);
        $this->variantHandler->handle($domNode, $this->xmlReader);
        
        // Verify state was reset
        $this->assertNull($this->state->currentVariantGroupId);
        
        // Verify verse was marked as having a variant
        $verse->refresh();
        $this->assertTrue($verse->has_text_variant);
    }
    
    public function test_variant_handler_handles_malformed_variant_elements_gracefully()
    {
        // Create test book, chapter, and verse
        $book = Book::create([
            'name' => 'TestBook',
            'slug' => 'testbook',
            'order' => 1,
            'abbreviation' => 'TB',
            'chapters_count' => 1
        ]);
        
        $this->state->book = $book;
        
        $chapter = Chapter::create([
            'book_id' => $book->id,
            'number' => 1
        ]);
        
        $this->state->chapter = $chapter;
        
        $verse = Verse::create([
            'chapter_id' => $chapter->id,
            'number' => 1,
            'text' => 'Test verse'
        ]);
        
        $this->state->verse = $verse;
        
        // Create a simulated XML element with missing vid attribute
        $xmlElement = simplexml_load_string('<ms type="va" />');
        $domNode = $this->convertToDOMNode($xmlElement);
        
        // Process the milestone start (should not throw exception)
        $this->variantHandler->handle($domNode, $this->xmlReader);
        
        // Verify state remains unchanged
        $this->assertNull($this->state->currentVariantGroupId);
        
        // Create a simulated XML element with mismatched eid attribute
        $startElement = simplexml_load_string('<ms sid="variant1" type="va" />');
        $domNode = $this->convertToDOMNode($startElement);
        $this->variantHandler->handle($domNode, $this->xmlReader);
        
        // End with wrong eid
        $endElement = simplexml_load_string('<ms eid="wrong_id" />');
        $domNode = $this->convertToDOMNode($endElement);
        $this->variantHandler->handle($domNode, $this->xmlReader);
        
        // Verify state was still reset (graceful handling)
        $this->assertNull($this->state->currentVariantGroupId);
    }
}
