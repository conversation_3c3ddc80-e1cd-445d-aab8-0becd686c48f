<?php

namespace Tests\Feature\Services\BibleData\Handlers;

use App\Models\Book;
use App\Models\Chapter;
use App\Models\Verse;
use App\Models\Word;
use App\Services\BibleData\Handlers\VariantHandler;
use App\Services\BibleData\TextProcessor;
use App\Services\BibleData\UsxParserState;
use App\Services\BibleData\UsxParserConfig;
use App\Services\BibleData\UsxParserLogger;
use App\Services\BibleData\ModelFactory;
use DOMNode;
use Illuminate\Foundation\Testing\RefreshDatabase;
use SimpleXMLElement;
use Symfony\Component\Console\Output\BufferedOutput;
use Tests\TestCase;
use XMLReader;

class VariantPunctuationTest extends TestCase
{
    use RefreshDatabase;
    
    protected $output;
    protected $config;
    protected $state;
    protected $logger;
    protected $modelFactory;
    protected $variantHandler;
    protected $textProcessor;
    protected $xmlPath;
    protected $reader;
    protected $xmlReader;
    
    protected function setUp(): void
    {
        parent::setUp();
        
        // Set up the test components
        $this->output = new BufferedOutput();
        $this->config = new UsxParserConfig(['is_test' => true]);
        $this->state = new UsxParserState($this->config);
        $this->logger = new UsxParserLogger($this->output, true, false);
        $this->modelFactory = new ModelFactory($this->state, $this->logger);
        
        // Create the variant handler
        $this->variantHandler = new VariantHandler($this->state, $this->logger);
        
        // Create the text processor
        $this->textProcessor = new TextProcessor($this->state, $this->logger, $this->modelFactory);
        
        // Set up the XML reader
        $this->xmlPath = '/Applications/MAMP/htdocs/ebtc/esra-bibel/apps/web/bibeltext/Johannes/Johannes.xml';
        $this->reader = new XMLReader();
        $this->reader->open($this->xmlPath);
        $this->xmlReader = $this->reader;
    }
    
    protected function tearDown(): void
    {
        if (isset($this->reader) && $this->reader instanceof XMLReader) {
            $this->reader->close();
        }
        
        parent::tearDown();
    }
    
    /**
     * Helper method to convert SimpleXMLElement to DOMNode
     */
    protected function convertToDOMNode(SimpleXMLElement $element): DOMNode
    {
        $dom = dom_import_simplexml($element);
        return $dom;
    }
    
    /**
     * Test that punctuation is correctly attached after a variant section
     */
    public function test_punctuation_is_attached_after_variant()
    {
        // Create test book, chapter, and verse
        $book = Book::create([
            'name' => 'TestBook',
            'slug' => 'testbook',
            'order' => 1,
            'abbreviation' => 'TB',
            'chapters_count' => 1
        ]);
        
        $this->state->book = $book;
        
        $chapter = Chapter::create([
            'book_id' => $book->id,
            'number' => 1
        ]);
        
        $this->state->chapter = $chapter;
        
        $verse = Verse::create([
            'chapter_id' => $chapter->id,
            'number' => 1,
            'text' => 'Test verse'
        ]);
        
        $this->state->verse = $verse;
        
        // Create a word before the variant
        $word = Word::create([
            'verse_id' => $verse->id,
            'text' => 'Before',
            'position' => 1
        ]);
        
        $this->state->words[] = $word;
        $this->state->position = 2;
        
        // Start variant
        $variantStart = simplexml_load_string('<ms sid="test_variant" type="va" />');
        $domNode = $this->convertToDOMNode($variantStart);
        $this->variantHandler->handle($domNode, $this->xmlReader);
        
        // Add a word inside the variant
        $this->textProcessor->processAccumulatedText('Variant', $this->state->position);
        
        // End variant
        $variantEnd = simplexml_load_string('<ms eid="test_variant" />');
        $domNode = $this->convertToDOMNode($variantEnd);
        $this->variantHandler->handle($domNode, $this->xmlReader);
        
        // Process punctuation after the variant
        $this->textProcessor->processAccumulatedText('.', $this->state->position);
        
        // Verify the punctuation was attached to the last word
        $lastWord = Word::where('verse_id', $verse->id)->orderBy('position', 'desc')->first();
        
        $this->assertEquals('Variant.', $lastWord->text, 'Punctuation should be attached to the last word in the variant');
    }
    
    /**
     * Test that punctuation is correctly attached to the word before a variant
     * when the variant is empty
     */
    public function test_punctuation_is_attached_to_word_before_empty_variant()
    {
        // Create test book, chapter, and verse
        $book = Book::create([
            'name' => 'TestBook',
            'slug' => 'testbook',
            'order' => 1,
            'abbreviation' => 'TB',
            'chapters_count' => 1
        ]);
        
        $this->state->book = $book;
        
        $chapter = Chapter::create([
            'book_id' => $book->id,
            'number' => 1
        ]);
        
        $this->state->chapter = $chapter;
        
        $verse = Verse::create([
            'chapter_id' => $chapter->id,
            'number' => 1,
            'text' => 'Test verse'
        ]);
        
        $this->state->verse = $verse;
        
        // Create a word before the variant
        $word = Word::create([
            'verse_id' => $verse->id,
            'text' => 'Before',
            'position' => 1
        ]);
        
        $this->state->words[] = $word;
        $this->state->position = 2;
        $this->state->lastWordBeforeVariantPosition = 1;
        
        // Start variant
        $variantStart = simplexml_load_string('<ms sid="test_variant" type="va" />');
        $domNode = $this->convertToDOMNode($variantStart);
        $this->variantHandler->handle($domNode, $this->xmlReader);
        
        // End variant immediately (empty variant)
        $variantEnd = simplexml_load_string('<ms eid="test_variant" />');
        $domNode = $this->convertToDOMNode($variantEnd);
        $this->variantHandler->handle($domNode, $this->xmlReader);
        
        // Process punctuation after the variant
        $this->textProcessor->processAccumulatedText('.', $this->state->position);
        
        // Verify the punctuation was attached to the word before the variant
        $word->refresh();
        
        $this->assertEquals('Before.', $word->text, 'Punctuation should be attached to the word before the variant when the variant is empty');
    }
    
    /**
     * Test that punctuation is correctly handled when a variant is inside a footnote
     */
    public function test_punctuation_with_variant_inside_footnote()
    {
        // Create test book, chapter, and verse
        $book = Book::create([
            'name' => 'TestBook',
            'slug' => 'testbook',
            'order' => 1,
            'abbreviation' => 'TB',
            'chapters_count' => 1
        ]);
        
        $this->state->book = $book;
        
        $chapter = Chapter::create([
            'book_id' => $book->id,
            'number' => 1
        ]);
        
        $this->state->chapter = $chapter;
        
        $verse = Verse::create([
            'chapter_id' => $chapter->id,
            'number' => 1,
            'text' => 'Test verse'
        ]);
        
        $this->state->verse = $verse;
        
        // Create a word before the footnote
        $word = Word::create([
            'verse_id' => $verse->id,
            'text' => 'Before',
            'position' => 1
        ]);
        
        $this->state->words[] = $word;
        $this->state->position = 2;
        $this->state->lastWordBeforeFootnotePosition = 1;
        
        // Start footnote
        $this->state->inNote = true;
        
        // Start variant inside footnote
        $variantStart = simplexml_load_string('<ms sid="test_variant" type="va" />');
        $domNode = $this->convertToDOMNode($variantStart);
        $this->variantHandler->handle($domNode, $this->xmlReader);
        
        // Add a word inside the variant
        $this->textProcessor->processAccumulatedText('Variant', $this->state->position);
        
        // End variant
        $variantEnd = simplexml_load_string('<ms eid="test_variant" />');
        $domNode = $this->convertToDOMNode($variantEnd);
        $this->variantHandler->handle($domNode, $this->xmlReader);
        
        // End footnote
        $this->state->inNote = false;
        $this->state->justFinishedFootnote = true;
        
        // Process punctuation after the footnote
        $this->textProcessor->processAccumulatedText('.', $this->state->position);
        
        // Verify the punctuation was attached to the word before the footnote
        $word->refresh();
        
        $this->assertEquals('Before.', $word->text, 'Punctuation should be attached to the word before the footnote when there is a variant inside the footnote');
    }
    
    /**
     * Test that punctuation is correctly handled with nested variants
     */
    public function test_punctuation_with_nested_variants()
    {
        // Create test book, chapter, and verse
        $book = Book::create([
            'name' => 'TestBook',
            'slug' => 'testbook',
            'order' => 1,
            'abbreviation' => 'TB',
            'chapters_count' => 1
        ]);
        
        $this->state->book = $book;
        
        $chapter = Chapter::create([
            'book_id' => $book->id,
            'number' => 1
        ]);
        
        $this->state->chapter = $chapter;
        
        $verse = Verse::create([
            'chapter_id' => $chapter->id,
            'number' => 1,
            'text' => 'Test verse'
        ]);
        
        $this->state->verse = $verse;
        
        // Create a word before the variant
        $word = Word::create([
            'verse_id' => $verse->id,
            'text' => 'Before',
            'position' => 1
        ]);
        
        $this->state->words[] = $word;
        $this->state->position = 2;
        
        // Start outer variant
        $outerVariantStart = simplexml_load_string('<ms sid="outer_variant" type="va" />');
        $domNode = $this->convertToDOMNode($outerVariantStart);
        $this->variantHandler->handle($domNode, $this->xmlReader);
        
        // Add a word inside the outer variant
        $this->textProcessor->processAccumulatedText('Outer', $this->state->position);
        
        // Start inner variant
        $innerVariantStart = simplexml_load_string('<ms sid="inner_variant" type="va" />');
        $domNode = $this->convertToDOMNode($innerVariantStart);
        $this->variantHandler->handle($domNode, $this->xmlReader);
        
        // Add a word inside the inner variant
        $this->textProcessor->processAccumulatedText('Inner', $this->state->position);
        
        // End inner variant
        $innerVariantEnd = simplexml_load_string('<ms eid="inner_variant" />');
        $domNode = $this->convertToDOMNode($innerVariantEnd);
        $this->variantHandler->handle($domNode, $this->xmlReader);
        
        // End outer variant
        $outerVariantEnd = simplexml_load_string('<ms eid="outer_variant" />');
        $domNode = $this->convertToDOMNode($outerVariantEnd);
        $this->variantHandler->handle($domNode, $this->xmlReader);
        
        // Process punctuation after the variants
        $this->textProcessor->processAccumulatedText('.', $this->state->position);
        
        // Verify the punctuation was attached to the last word
        $lastWord = Word::where('verse_id', $verse->id)->orderBy('position', 'desc')->first();
        
        $this->assertEquals('Inner.', $lastWord->text, 'Punctuation should be attached to the last word in the nested variants');
    }
}
