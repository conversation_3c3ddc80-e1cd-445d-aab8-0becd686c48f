<?php

declare(strict_types=1);

namespace Tests\Feature\Services\BibleData;

use App\Models\Book;
use App\Models\Chapter;
use App\Models\Verse;
use App\Models\Word;
use App\Services\BibleData\Handlers\VariantHandler;
use App\Services\BibleData\ModelFactory;
use App\Services\BibleData\TextProcessor;
use App\Services\BibleData\UsxParserLogger;
use App\Services\BibleData\UsxParserState;
use DOMDocument;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class VariantTextHandlingTest extends TestCase
{
    use RefreshDatabase;

    private UsxParserState $state;
    private UsxParserLogger $logger;
    private ModelFactory $modelFactory;
    private VariantHandler $variantHandler;
    private TextProcessor $textProcessor;
    private Book $book;
    private Chapter $chapter;
    private Verse $verse;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data
        $this->book = Book::factory()->create(['code' => 'JHN']);
        $this->chapter = Chapter::factory()->create([
            'book_id' => $this->book->id,
            'number' => 1
        ]);
        $this->verse = Verse::factory()->create([
            'chapter_id' => $this->chapter->id,
            'number' => 1,
            'start_verse' => 1,
            'end_verse' => 1,
            'has_text_variant' => false
        ]);

        // Set up parser state
        $this->state = new UsxParserState();
        $this->state->book = $this->book;
        $this->state->chapter = $this->chapter;
        $this->state->verse = $this->verse;
        $this->state->position = 0;

        // Set up logger and model factory
        $this->logger = new UsxParserLogger();
        $this->modelFactory = new ModelFactory($this->state, $this->logger);

        // Create handlers
        $this->variantHandler = new VariantHandler($this->state, $this->logger, $this->modelFactory);
        $this->textProcessor = new TextProcessor($this->state, $this->logger, $this->modelFactory);
    }

    /**
     * Test that only words within a variant section are marked with has_variant = true
     */
    public function testOnlyWordsWithinVariantSectionAreMarkedWithHasVariant(): void
    {
        // Create a DOM document with a milestone element
        $doc = new DOMDocument();
        
        // Create words before the variant
        $this->textProcessor->processAccumulatedText("In the beginning was the", 0);
        
        // Start variant
        $variantStart = $doc->createElement('milestone');
        $variantStart->setAttribute('style', 'va');
        $variantStart->setAttribute('sID', 'test_variant');
        $this->variantHandler->handle($variantStart);
        
        // Create words within the variant
        $this->textProcessor->processAccumulatedText("Word", 5);
        
        // End variant
        $variantEnd = $doc->createElement('milestone');
        $variantEnd->setAttribute('style', 'va*');
        $variantEnd->setAttribute('eID', 'test_variant');
        $this->variantHandler->handle($variantEnd);
        
        // Create words after the variant
        $this->textProcessor->processAccumulatedText("and the Word was with God", 6);
        
        // Refresh the verse from the database
        $this->verse->refresh();
        
        // Assert that has_text_variant is set to true on the verse
        $this->assertTrue($this->verse->has_text_variant);
        
        // Get all words for the verse
        $words = Word::where('verse_id', $this->verse->id)->orderBy('position')->get();
        
        // Assert that only the word within the variant section has has_variant = true
        foreach ($words as $word) {
            if ($word->position == 5) {
                $this->assertTrue($word->has_variant, "Word at position 5 should have has_variant = true");
                $this->assertNotNull($word->variant_group_id, "Word at position 5 should have a variant_group_id");
            } else {
                $this->assertFalse($word->has_variant, "Word at position {$word->position} should have has_variant = false");
                $this->assertNull($word->variant_group_id, "Word at position {$word->position} should not have a variant_group_id");
            }
        }
    }

    /**
     * Test that nested variants are handled correctly
     */
    public function testNestedVariantsAreHandledCorrectly(): void
    {
        // Create a DOM document
        $doc = new DOMDocument();
        
        // Create words before the outer variant
        $this->textProcessor->processAccumulatedText("In the beginning", 0);
        
        // Start outer variant
        $outerStart = $doc->createElement('milestone');
        $outerStart->setAttribute('style', 'va');
        $outerStart->setAttribute('sID', 'outer_variant');
        $this->variantHandler->handle($outerStart);
        $outerVariantId = $this->state->currentVariantGroupId;
        
        // Create words within the outer variant but before the inner variant
        $this->textProcessor->processAccumulatedText("was the", 3);
        
        // Start inner variant
        $innerStart = $doc->createElement('milestone');
        $innerStart->setAttribute('style', 'va');
        $innerStart->setAttribute('sID', 'inner_variant');
        $this->variantHandler->handle($innerStart);
        $innerVariantId = $this->state->currentVariantGroupId;
        
        // Create words within the inner variant
        $this->textProcessor->processAccumulatedText("Word", 5);
        
        // End inner variant
        $innerEnd = $doc->createElement('milestone');
        $innerEnd->setAttribute('style', 'va*');
        $innerEnd->setAttribute('eID', 'inner_variant');
        $this->variantHandler->handle($innerEnd);
        
        // Create more words within the outer variant but after the inner variant
        $this->textProcessor->processAccumulatedText("of God", 6);
        
        // End outer variant
        $outerEnd = $doc->createElement('milestone');
        $outerEnd->setAttribute('style', 'va*');
        $outerEnd->setAttribute('eID', 'outer_variant');
        $this->variantHandler->handle($outerEnd);
        
        // Create words after all variants
        $this->textProcessor->processAccumulatedText("and the Word was with God", 8);
        
        // Refresh the verse from the database
        $this->verse->refresh();
        
        // Assert that has_text_variant is set to true on the verse
        $this->assertTrue($this->verse->has_text_variant);
        
        // Get all words for the verse
        $words = Word::where('verse_id', $this->verse->id)->orderBy('position')->get();
        
        // Assert that words have the correct variant flags and group IDs
        foreach ($words as $word) {
            if ($word->position >= 3 && $word->position <= 7) {
                // Words in the outer variant
                $this->assertTrue($word->has_variant, "Word at position {$word->position} should have has_variant = true");
                
                if ($word->position == 5) {
                    // Word in the inner variant
                    $this->assertEquals($innerVariantId, $word->variant_group_id, "Word at position 5 should have inner variant group ID");
                } else {
                    // Words in the outer variant but not in the inner variant
                    $this->assertEquals($outerVariantId, $word->variant_group_id, "Word at position {$word->position} should have outer variant group ID");
                }
            } else {
                // Words outside any variant
                $this->assertFalse($word->has_variant, "Word at position {$word->position} should have has_variant = false");
                $this->assertNull($word->variant_group_id, "Word at position {$word->position} should not have a variant_group_id");
            }
        }
    }
}
