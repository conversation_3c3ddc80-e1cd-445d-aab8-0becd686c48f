<?php

declare(strict_types=1);

namespace Tests\Feature\Services\BibleData;

use App\Models\Book;
use App\Models\Chapter;
use App\Models\Verse;
use App\Models\Word;
use App\Services\BibleData\Handlers\VariantHandler;
use App\Services\BibleData\ModelFactory;
use App\Services\BibleData\UsxParserLogger;
use App\Services\BibleData\UsxParserState;
use DOMDocument;
use DOMNode;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class VariantHandlerTest extends TestCase
{
    use RefreshDatabase;

    private UsxParserState $state;
    private UsxParserLogger $logger;
    private ModelFactory $modelFactory;
    private VariantHandler $handler;
    private Book $book;
    private Chapter $chapter;
    private Verse $verse;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data
        $this->book = Book::factory()->create(['code' => 'GEN']);
        $this->chapter = Chapter::factory()->create([
            'book_id' => $this->book->id,
            'number' => 1
        ]);
        $this->verse = Verse::factory()->create([
            'chapter_id' => $this->chapter->id,
            'number' => 1,
            'start_verse' => 1,
            'end_verse' => 1,
            'has_text_variant' => false
        ]);

        // Set up parser state
        $this->state = new UsxParserState();
        $this->state->book = $this->book;
        $this->state->chapter = $this->chapter;
        $this->state->verse = $this->verse;
        $this->state->position = 0;

        // Set up logger and model factory
        $this->logger = new UsxParserLogger();
        $this->modelFactory = new ModelFactory($this->state, $this->logger);

        // Create handler
        $this->handler = new VariantHandler($this->state, $this->logger, $this->modelFactory);
    }

    /**
     * Test that variant start sets has_text_variant flag correctly
     */
    public function testVariantStartSetsHasTextVariantFlag(): void
    {
        // Create a DOM document with a milestone element
        $doc = new DOMDocument();
        $milestone = $doc->createElement('milestone');
        $milestone->setAttribute('style', 'va');
        $milestone->setAttribute('sID', 'test_variant');

        // Handle the milestone element
        $this->handler->handle($milestone);

        // Refresh the verse from the database
        $this->verse->refresh();

        // Assert that has_text_variant is set to true
        $this->assertTrue($this->verse->has_text_variant);
    }

    /**
     * Test that words created with variant_group_id have has_variant set
     */
    public function testWordCreatedWithVariantGroupIdHasVariantSet(): void
    {
        // Create a DOM document with a milestone element
        $doc = new DOMDocument();
        $milestone = $doc->createElement('milestone');
        $milestone->setAttribute('style', 'va');
        $milestone->setAttribute('sID', 'test_variant');

        // Handle the milestone element
        $this->handler->handle($milestone);

        // Create a word with the current variant group ID
        $word = $this->modelFactory->createWord([
            'verse_id' => $this->verse->id,
            'text' => 'TestWord',
            'position' => 1,
            'variant_group_id' => $this->state->currentVariantGroupId
        ]);

        // Assert that the word has has_variant set to true
        $this->assertTrue($word->has_variant);
        
        // Refresh the verse from the database
        $this->verse->refresh();
        
        // Assert that has_text_variant is set to true on the verse
        $this->assertTrue($this->verse->has_text_variant);
    }

    /**
     * Test that variant end correctly restores previous variant group ID
     */
    public function testVariantEndRestoresPreviousVariantGroupId(): void
    {
        // Create a DOM document with milestone elements
        $doc = new DOMDocument();
        
        // Start outer variant
        $outerStart = $doc->createElement('milestone');
        $outerStart->setAttribute('style', 'va');
        $outerStart->setAttribute('sID', 'outer_variant');
        $this->handler->handle($outerStart);
        $outerVariantId = $this->state->currentVariantGroupId;
        
        // Start inner variant
        $innerStart = $doc->createElement('milestone');
        $innerStart->setAttribute('style', 'va');
        $innerStart->setAttribute('sID', 'inner_variant');
        $this->handler->handle($innerStart);
        $innerVariantId = $this->state->currentVariantGroupId;
        
        // End inner variant
        $innerEnd = $doc->createElement('milestone');
        $innerEnd->setAttribute('style', 'va*');
        $innerEnd->setAttribute('eID', 'inner_variant');
        $this->handler->handle($innerEnd);
        
        // Assert that the current variant group ID is restored to the outer variant ID
        $this->assertEquals($outerVariantId, $this->state->currentVariantGroupId);
        
        // End outer variant
        $outerEnd = $doc->createElement('milestone');
        $outerEnd->setAttribute('style', 'va*');
        $outerEnd->setAttribute('eID', 'outer_variant');
        $this->handler->handle($outerEnd);
        
        // Assert that the current variant group ID is null
        $this->assertNull($this->state->currentVariantGroupId);
    }
}
