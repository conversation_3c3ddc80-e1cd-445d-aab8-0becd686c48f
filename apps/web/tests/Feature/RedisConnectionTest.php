<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Support\Facades\Redis;
use Illuminate\Foundation\Testing\RefreshDatabase;

class RedisConnectionTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();
        
        // Force Redis for testing
        config(['cache.default' => 'redis']);
        config(['session.driver' => 'redis']);
        config(['queue.default' => 'redis']);
    }

    public function test_redis_connection()
    {
        try {
            // Test default connection
            Redis::connection('default')->set('test_key', 'working');
            $this->assertEquals('working', Redis::connection('default')->get('test_key'));
            
            // Test cache connection
            Redis::connection('cache')->set('cache_test', 'cache_working');
            $this->assertEquals('cache_working', Redis::connection('cache')->get('cache_test'));
            
            // Test bible connection
            Redis::connection('bible')->set('bible_test', 'bible_working');
            $this->assertEquals('bible_working', Redis::connection('bible')->get('bible_test'));
            
            // Test search connection
            Redis::connection('search')->set('search_test', 'search_working');
            $this->assertEquals('search_working', Redis::connection('search')->get('search_test'));
            
        } finally {
            // Clean up
            Redis::connection('default')->del('test_key');
            Redis::connection('cache')->del('cache_test');
            Redis::connection('bible')->del('bible_test');
            Redis::connection('search')->del('search_test');
        }
    }
}
