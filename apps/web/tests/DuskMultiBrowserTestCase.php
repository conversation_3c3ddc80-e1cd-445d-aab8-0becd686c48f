<?php

declare(strict_types=1);

namespace Tests;

use Facebook\WebDriver\Chrome\ChromeOptions;
use Facebook\WebDriver\Firefox\FirefoxOptions;
use Facebook\WebDriver\Firefox\FirefoxProfile;
use Facebook\WebDriver\Remote\DesiredCapabilities;
use Facebook\WebDriver\Remote\RemoteWebDriver;
use Facebook\WebDriver\Safari\SafariOptions;
use Illuminate\Support\Collection;
use Laravel\Dusk\TestCase as BaseTestCase;
use PHPUnit\Framework\Attributes\BeforeClass;
use Symfony\Component\Process\Process;

abstract class DuskMultiBrowserTestCase extends BaseTestCase
{
    /**
     * The base URL used by Dusk when testing the application.
     */
    protected static string $baseUrl = 'http://127.0.0.1:8000';

    use CreatesApplication;

    /**
     * @var Process
     */
    protected static $serveProcess;

    /**
     * Available browsers for testing
     */
    protected static array $browsers = ['chrome', 'firefox', 'safari'];

    /**
     * Current browser being tested
     */
    protected static string $currentBrowser = 'chrome';

    /**
     * Prepare for Dusk test execution.
     */
    #[BeforeClass]
    public static function prepare(): void
    {
        // Build front-end assets for Dusk
        $build = new Process(['npm', 'run', 'build'], dirname(__DIR__));
        $build->run();

        // Refresh and seed the test database with only E2E data
        $refresh = new Process(['php', 'artisan', 'db:refresh-all', '--env=testing'], dirname(__DIR__));
        $refresh->run();

        // Parse test data
        $parseMarkus = new Process(['php', 'artisan', 'bible:parse', 'Markus', '--output', '--env=testing'], dirname(__DIR__));
        $parseMarkus->setTimeout(null);
        $parseMarkus->run();

        $parseLukas = new Process(['php', 'artisan', 'bible:parse', 'Lukas', '--output', '--env=testing'], dirname(__DIR__));
        $parseLukas->setTimeout(null);
        $parseLukas->run();

        if (!static::runningInSail()) {
            static::startDrivers();
        }

        // Start Laravel server for browser tests
        static::$serveProcess = new Process(['php', 'artisan', 'serve', '--host=127.0.0.1', '--port=8000', '--env=testing'], dirname(__DIR__));
        static::$serveProcess->start();
        sleep(2);
    }

    /**
     * Start all browser drivers
     */
    protected static function startDrivers(): void
    {
        // Start ChromeDriver
        static::startChromeDriver(['--port=9515']);

        // Start GeckoDriver for Firefox (if available)
        if (static::isGeckoDriverAvailable()) {
            static::startGeckoDriver(['--port=4444']);
        }

        // Safari driver is built into macOS, no need to start separately
    }

    /**
     * Check if GeckoDriver is available
     */
    protected static function isGeckoDriverAvailable(): bool
    {
        $process = new Process(['which', 'geckodriver']);
        $process->run();
        return $process->isSuccessful();
    }

    /**
     * Start GeckoDriver
     */
    protected static function startGeckoDriver(array $arguments = []): void
    {
        $process = new Process(array_merge(['geckodriver'], $arguments));
        $process->start();
        sleep(1);
    }

    /**
     * Create the RemoteWebDriver instance based on current browser
     */
    protected function driver(): RemoteWebDriver
    {
        $browser = static::$currentBrowser;

        return match ($browser) {
            'chrome' => $this->createChromeDriver(),
            'firefox' => $this->createFirefoxDriver(),
            'safari' => $this->createSafariDriver(),
            default => $this->createChromeDriver(),
        };
    }

    /**
     * Create Chrome WebDriver
     */
    protected function createChromeDriver(): RemoteWebDriver
    {
        $options = (new ChromeOptions)->addArguments(collect([
            $this->shouldStartMaximized() ? '--start-maximized' : '--window-size=1920,1080',
            '--disable-search-engine-choice-screen',
            '--disable-smooth-scrolling',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
        ])->unless($this->hasHeadlessDisabled(), function (Collection $items) {
            return $items->merge([
                '--disable-gpu',
                '--headless=new',
                '--no-sandbox',
                '--disable-dev-shm-usage',
            ]);
        })->all());

        return RemoteWebDriver::create(
            $_ENV['DUSK_DRIVER_URL'] ?? env('DUSK_DRIVER_URL') ?? 'http://localhost:9515',
            DesiredCapabilities::chrome()->setCapability(
                ChromeOptions::CAPABILITY,
                $options
            )
        );
    }

    /**
     * Create Firefox WebDriver
     */
    protected function createFirefoxDriver(): RemoteWebDriver
    {
        $profile = new FirefoxProfile();
        $profile->setPreference('dom.webnotifications.enabled', false);
        $profile->setPreference('media.navigator.permission.disabled', true);

        $options = new FirefoxOptions();
        $options->setProfile($profile);

        if (!$this->hasHeadlessDisabled()) {
            $options->addArguments(['--headless']);
        }

        $capabilities = DesiredCapabilities::firefox();
        $capabilities->setCapability(FirefoxOptions::CAPABILITY, $options);

        return RemoteWebDriver::create(
            $_ENV['DUSK_FIREFOX_DRIVER_URL'] ?? env('DUSK_FIREFOX_DRIVER_URL') ?? 'http://localhost:4444',
            $capabilities
        );
    }

    /**
     * Create Safari WebDriver (macOS only)
     */
    protected function createSafariDriver(): RemoteWebDriver
    {
        $options = new SafariOptions();
        $capabilities = DesiredCapabilities::safari();
        $capabilities->setCapability(SafariOptions::CAPABILITY, $options);

        return RemoteWebDriver::create(
            $_ENV['DUSK_SAFARI_DRIVER_URL'] ?? env('DUSK_SAFARI_DRIVER_URL') ?? 'http://localhost:10444',
            $capabilities
        );
    }

    /**
     * Set the current browser for testing
     */
    public static function setBrowser(string $browser): void
    {
        if (in_array($browser, static::$browsers)) {
            static::$currentBrowser = $browser;
        }
    }

    /**
     * Get available browsers for the current environment
     */
    public static function getAvailableBrowsers(): array
    {
        $available = ['chrome']; // Chrome is always available

        // Check Firefox availability
        if (static::isGeckoDriverAvailable()) {
            $available[] = 'firefox';
        }

        // Check Safari availability (macOS only)
        if (PHP_OS_FAMILY === 'Darwin') {
            $available[] = 'safari';
        }

        return $available;
    }

    /**
     * Run test across all available browsers
     */
    protected function runAcrossAllBrowsers(callable $testCallback): void
    {
        $availableBrowsers = static::getAvailableBrowsers();

        foreach ($availableBrowsers as $browser) {
            static::setBrowser($browser);
            
            $this->browse(function ($browser) use ($testCallback) {
                $testCallback($browser);
            });
        }
    }

    /**
     * Stop Laravel server after tests.
     */
    public static function tearDownAfterClass(): void
    {
        parent::tearDownAfterClass();
        if (static::$serveProcess) {
            static::$serveProcess->stop();
        }
    }
}
