<?php

namespace Tests\Browser;

use <PERSON><PERSON>\Dusk\Browser;
use Tests\DuskMultiBrowserTestCase;
use Tests\Traits\CrossBrowserTesting;

class CrossBrowserExampleTest extends DuskMultiBrowserTestCase
{
    use CrossBrowserTesting;

    /**
     * Test homepage loads correctly across all browsers
     */
    public function test_homepage_loads_across_all_browsers()
    {
        $this->runAcrossAllBrowsers(function (Browser $browser) {
            $browser->visit('/')
                    ->waitForText('Entdecke die Esra Bibel', 10)
                    ->assertSee('Entdecke die Esra Bibel')
                    ->assertPresent('body');
        });
    }

    /**
     * Test Bible navigation works in all browsers
     */
    public function test_bible_navigation_across_browsers()
    {
        $this->runAcrossAllBrowsers(function (Browser $browser) {
            $browser->visit('/Markus2')
                    ->waitFor('[data-chapter-id="Markus2"]', 15)
                    ->assertVisible('[data-chapter-id="Markus2"]')
                    ->assertPathIs('/Markus2');

            // Test scrolling behavior
            $browser->scrollIntoView('[data-chapter-id="Markus2"]')
                    ->pause(1000) // Allow for scroll animations
                    ->assertVisible('[data-chapter-id="Markus2"]');
        });
    }

    /**
     * Test responsive design across browsers and viewports
     */
    public function test_responsive_design_across_browsers()
    {
        $viewports = $this->getCommonViewports();

        $this->testResponsiveAcrossBrowsers($viewports, function (Browser $browser, array $viewport) {
            $browser->visit('/')
                    ->waitForText('Entdecke die Esra Bibel', 10);

            // Test mobile-specific behavior
            if ($viewport['width'] <= 768) {
                // Add mobile-specific assertions
                $browser->assertPresent('body'); // Replace with actual mobile elements
            } else {
                // Add desktop-specific assertions
                $browser->assertPresent('body'); // Replace with actual desktop elements
            }
        });
    }

    /**
     * Test only in Chrome and Firefox (skip Safari)
     */
    public function test_specific_feature_chrome_firefox_only()
    {
        $this->onlyInBrowsers(['chrome', 'firefox'], function (Browser $browser) {
            $browser->visit('/Markus1')
                    ->waitFor('[data-chapter-id="Markus1"]', 10)
                    ->assertVisible('[data-chapter-id="Markus1"]');

            // Test feature that might not work well in Safari
            $browser->keys('body', ['{CTRL}', 'f']); // Ctrl+F might behave differently
        });
    }

    /**
     * Test with browser-specific behavior
     */
    public function test_browser_specific_behavior()
    {
        $this->runAcrossAllBrowsers(function (Browser $browser) {
            $browserName = $browser->browserName ?? 'unknown';

            $browser->visit('/');

            // Handle browser-specific differences
            switch ($browserName) {
                case 'safari':
                    // Safari might need longer waits
                    $browser->waitForText('Entdecke die Esra Bibel', 15);
                    break;
                
                case 'firefox':
                    // Firefox-specific behavior
                    $browser->waitForText('Entdecke die Esra Bibel', 12);
                    break;
                
                case 'chrome':
                default:
                    $browser->waitForText('Entdecke die Esra Bibel', 10);
                    break;
            }

            $browser->assertSee('Entdecke die Esra Bibel');
        });
    }

    /**
     * Test form interactions across browsers
     */
    public function test_form_interactions_across_browsers()
    {
        $this->runAcrossAllBrowsers(function (Browser $browser) {
            $browser->visit('/');

            // Test search functionality if available
            if ($browser->element('input[type="search"]')) {
                $browser->type('input[type="search"]', 'Johannes')
                        ->pause(500) // Allow for search suggestions
                        ->keys('input[type="search"]', '{ENTER}');
                
                // Verify search results
                $this->waitForElementWithBrowserTimeout($browser, '.search-results');
            }
        });
    }

    /**
     * Test JavaScript functionality across browsers
     */
    public function test_javascript_functionality_across_browsers()
    {
        $this->runAcrossAllBrowsers(function (Browser $browser) {
            $browser->visit('/Markus1');

            // Test JavaScript-heavy features
            $browser->waitFor('[data-chapter-id="Markus1"]', 15)
                    ->assertVisible('[data-chapter-id="Markus1"]');

            // Test scroll-based navigation (JavaScript dependent)
            $browser->script('window.scrollTo(0, 500);');
            $browser->pause(1000); // Allow for scroll events to process

            // Verify JavaScript state changes
            $browser->assertPresent('[data-chapter-id="Markus1"]');
        });
    }

    /**
     * Test accessibility features across browsers
     */
    public function test_accessibility_across_browsers()
    {
        $this->runAcrossAllBrowsers(function (Browser $browser) {
            $browser->visit('/');

            // Test keyboard navigation
            $browser->keys('body', '{TAB}')
                    ->pause(200)
                    ->keys('body', '{TAB}')
                    ->pause(200);

            // Test focus indicators
            $focusedElement = $browser->driver->switchTo()->activeElement();
            $this->assertNotNull($focusedElement);

            // Test ARIA attributes
            $browser->assertAttribute('body', 'role', 'document');
        });
    }

    /**
     * Test performance across browsers
     */
    public function test_page_load_performance_across_browsers()
    {
        $this->runAcrossAllBrowsers(function (Browser $browser) {
            $startTime = microtime(true);
            
            $browser->visit('/');
            $browser->waitForText('Entdecke die Esra Bibel', 15);
            
            $loadTime = microtime(true) - $startTime;
            
            // Assert reasonable load time (adjust threshold as needed)
            $this->assertLessThan(10, $loadTime, "Page load took too long in {$browser->browserName}");
        });
    }

    /**
     * Test error handling across browsers
     */
    public function test_error_handling_across_browsers()
    {
        $this->runAcrossAllBrowsers(function (Browser $browser) {
            // Test 404 page
            $browser->visit('/non-existent-page')
                    ->assertSee('404'); // Adjust based on your 404 page

            // Test JavaScript errors don't break the page
            $browser->visit('/')
                    ->script('throw new Error("Test error");')
                    ->pause(1000)
                    ->assertPresent('body'); // Page should still be functional
        });
    }
}
