<?php

namespace Tests\Browser;

use <PERSON><PERSON>\Dusk\Browser;
use Tests\DuskMultiBrowserTestCase;
use Tests\Traits\CrossBrowserTesting;

class CloudBrowserExampleTest extends DuskMultiBrowserTestCase
{
    use CrossBrowserTesting;

    /**
     * Test homepage across multiple browsers and platforms using cloud service
     */
    public function test_homepage_across_cloud_browsers()
    {
        // This will automatically use cloud service if configured
        $this->runAcrossAllBrowsers(function (Browser $browser) {
            $browser->visit('/')
                    ->waitForText('Entdecke die Esra Bibel', 15)
                    ->assertSee('Entdecke die Esra Bibel')
                    ->assertPresent('body');
        });
    }

    /**
     * Test Bible navigation on different platforms
     */
    public function test_bible_navigation_cross_platform()
    {
        $this->runAcrossAllBrowsers(function (Browser $browser) {
            $browser->visit('/Markus2')
                    ->waitFor('[data-chapter-id="Markus2"]', 20) // Longer timeout for cloud
                    ->assertVisible('[data-chapter-id="Markus2"]')
                    ->assertPathIs('/Markus2');

            // Test scrolling behavior across different browsers
            $browser->scrollIntoView('[data-chapter-id="Markus2"]')
                    ->pause(2000) // Allow for network latency
                    ->assertVisible('[data-chapter-id="Markus2"]');
        });
    }

    /**
     * Test mobile browsers (only available with cloud services)
     */
    public function test_mobile_browser_compatibility()
    {
        // This test only runs if cloud service is enabled
        if (!$this->shouldUseCloudService()) {
            $this->markTestSkipped('Mobile testing requires cloud service');
        }

        // Test on mobile Chrome
        static::setBrowser('mobile_chrome');
        $this->browse(function (Browser $browser) {
            $browser->visit('/')
                    ->waitForText('Entdecke die Esra Bibel', 20)
                    ->assertSee('Entdecke die Esra Bibel');

            // Test mobile-specific interactions
            $browser->tap('body') // Mobile tap instead of click
                    ->pause(1000);
        });

        // Test on mobile Safari
        static::setBrowser('mobile_safari');
        $this->browse(function (Browser $browser) {
            $browser->visit('/Markus1')
                    ->waitFor('[data-chapter-id="Markus1"]', 20)
                    ->assertVisible('[data-chapter-id="Markus1"]');
        });
    }

    /**
     * Test across different screen resolutions (cloud service advantage)
     */
    public function test_different_screen_resolutions()
    {
        $resolutions = [
            ['width' => 1920, 'height' => 1080], // Desktop
            ['width' => 1366, 'height' => 768],  // Laptop
            ['width' => 768, 'height' => 1024],  // Tablet
            ['width' => 375, 'height' => 667],   // Mobile
        ];

        $this->runAcrossAllBrowsers(function (Browser $browser) use ($resolutions) {
            foreach ($resolutions as $resolution) {
                $browser->resize($resolution['width'], $resolution['height'])
                        ->visit('/')
                        ->waitForText('Entdecke die Esra Bibel', 15)
                        ->assertSee('Entdecke die Esra Bibel');

                // Take screenshot for visual verification
                $browser->screenshot("resolution_{$resolution['width']}x{$resolution['height']}");
            }
        });
    }

    /**
     * Test real Safari behavior (only possible with cloud/macOS)
     */
    public function test_safari_specific_behavior()
    {
        $this->onlyInBrowsers(['safari'], function (Browser $browser) {
            $browser->visit('/Markus1')
                    ->waitFor('[data-chapter-id="Markus1"]', 20);

            // Test Safari-specific JavaScript behavior
            $browser->script('
                // Safari has different behavior for some JS features
                window.testSafariFeature = true;
            ');

            $result = $browser->script('return window.testSafariFeature;');
            $this->assertTrue($result[0]);
        });
    }

    /**
     * Test performance across different browsers and platforms
     */
    public function test_performance_across_platforms()
    {
        $this->runAcrossAllBrowsers(function (Browser $browser) {
            $startTime = microtime(true);
            
            $browser->visit('/')
                    ->waitForText('Entdecke die Esra Bibel', 30); // Longer timeout for cloud
            
            $loadTime = microtime(true) - $startTime;
            
            // More lenient timing for cloud testing due to network latency
            $this->assertLessThan(30, $loadTime, "Page load took too long");
            
            // Log performance data (cloud services often provide this)
            echo "Load time: {$loadTime}s\n";
        });
    }

    /**
     * Test file upload across browsers (cloud service handles file system)
     */
    public function test_file_upload_cross_browser()
    {
        $this->runAcrossAllBrowsers(function (Browser $browser) {
            $browser->visit('/');

            // Test file upload if your app has this feature
            if ($browser->element('input[type="file"]')) {
                // Cloud services handle file uploads differently
                $browser->attach('input[type="file"]', __DIR__ . '/fixtures/test-file.txt')
                        ->pause(2000); // Allow for upload processing
            }
        });
    }

    /**
     * Test geolocation features (cloud services provide different locations)
     */
    public function test_geolocation_features()
    {
        $this->runAcrossAllBrowsers(function (Browser $browser) {
            $browser->visit('/');

            // Test geolocation if your app uses it
            $browser->script('
                if (navigator.geolocation) {
                    navigator.geolocation.getCurrentPosition(function(position) {
                        window.testLocation = {
                            lat: position.coords.latitude,
                            lng: position.coords.longitude
                        };
                    });
                }
            ');

            $browser->pause(3000); // Allow for geolocation request

            // Verify geolocation worked (cloud services can simulate different locations)
            $location = $browser->script('return window.testLocation;');
            if ($location[0]) {
                $this->assertIsArray($location[0]);
            }
        });
    }

    /**
     * Test timezone handling across different regions
     */
    public function test_timezone_handling()
    {
        $this->runAcrossAllBrowsers(function (Browser $browser) {
            $browser->visit('/');

            // Test timezone-sensitive features
            $timezone = $browser->script('return Intl.DateTimeFormat().resolvedOptions().timeZone;');
            $this->assertIsString($timezone[0]);

            // Your app might display different content based on timezone
            echo "Browser timezone: {$timezone[0]}\n";
        });
    }

    /**
     * Helper method to check if cloud service is being used
     */
    protected function shouldUseCloudService(): bool
    {
        return config('browser-testing.cloud_services.browserstack.enabled', false) ||
               config('browser-testing.cloud_services.saucelabs.enabled', false) ||
               config('browser-testing.cloud_services.lambdatest.enabled', false);
    }
}
