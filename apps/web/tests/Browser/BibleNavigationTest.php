<?php

namespace Tests\Browser;

use <PERSON><PERSON>\Dusk\Browser;
use Tests\DuskTestCase;

class BibleNavigationTest extends DuskTestCase
{
    /**
     * @description Verify that a given reference URL loads the correct chapter section
     */
    public function test_initial_reference_loads_correct_section()
    {
        $this->browse(function (Browser $browser) {
            $browser->visit('/Markus2')
                    ->waitFor('[data-chapter-id="Markus2"]', 10)
                    ->assertVisible('[data-chapter-id="Markus2"]')
                    ->assertPathIs('/Markus2');
        });
    }

    /**
     * @description Scroll through chapters within a book and across book boundaries
     */
    public function test_scrolling_loads_and_unloads_adjacent_chapters()
    {
        $this->browse(function (Browser $browser) {
            // Start at first chapter of Markus
            $browser->visit('/Markus1')
                    ->waitFor('[data-chapter-id="Markus1"]', 10)
                    ->assertVisible('[data-chapter-id="Markus1"]');

            // Scroll to next chapter in same book
            $browser->scrollIntoView('[data-chapter-id="Markus2"]')
                    ->waitFor('[data-chapter-id="Markus2"]', 10)
                    ->assertVisible('[data-chapter-id="Markus2"]')
                    ->assertPathIs('/Markus2');

            // Scroll into next book frontmatter
            $browser->scrollIntoView('[data-chapter-id="Lukas"]')
                    ->waitFor('[data-chapter-id="Lukas"]', 10)
                    ->assertVisible('[data-chapter-id="Lukas"]')
                    ->assertPathIs('/Lukas');

            // Scroll back to previous book start
            $browser->scrollIntoView('[data-chapter-id="Markus1"]')
                    ->waitFor('[data-chapter-id="Markus1"]', 10)
                    ->assertVisible('[data-chapter-id="Markus1"]')
                    ->assertPathIs('/Markus1');
        });
    }
}
