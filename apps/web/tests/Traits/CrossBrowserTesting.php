<?php

declare(strict_types=1);

namespace Tests\Traits;

use Illuminate\Support\Facades\Config;

trait CrossBrowserTesting
{
    /**
     * Run a test across all enabled browsers
     */
    protected function runAcrossAllBrowsers(callable $testCallback, array $browsers = null): void
    {
        $browsers = $browsers ?? $this->getEnabledBrowsers();

        foreach ($browsers as $browser) {
            $this->runInBrowser($browser, $testCallback);
        }
    }

    /**
     * Run a test in a specific browser
     */
    protected function runInBrowser(string $browser, callable $testCallback): void
    {
        // Set the current browser context
        static::setBrowser($browser);

        // Add browser context to test name for better reporting
        $originalName = $this->getName();
        $this->setName("{$originalName} [{$browser}]");

        try {
            $this->browse(function ($browserInstance) use ($testCallback, $browser) {
                // Add browser information to the browser instance
                $browserInstance->browserName = $browser;
                $testCallback($browserInstance);
            });
        } finally {
            // Restore original test name
            $this->setName($originalName);
        }
    }

    /**
     * Get list of enabled browsers for current environment
     */
    protected function getEnabledBrowsers(): array
    {
        $environment = app()->environment();
        $envConfig = Config::get("browser-testing.environments.{$environment}", []);
        $defaultBrowsers = Config::get('browser-testing.environments.local.browsers', ['chrome']);
        
        $configuredBrowsers = $envConfig['browsers'] ?? $defaultBrowsers;
        $enabledBrowsers = [];

        foreach ($configuredBrowsers as $browser) {
            if ($this->isBrowserEnabled($browser) && $this->isBrowserAvailable($browser)) {
                $enabledBrowsers[] = $browser;
            }
        }

        return $enabledBrowsers;
    }

    /**
     * Check if a browser is enabled in configuration
     */
    protected function isBrowserEnabled(string $browser): bool
    {
        return Config::get("browser-testing.browsers.{$browser}.enabled", false);
    }

    /**
     * Check if a browser is available in the current environment
     */
    protected function isBrowserAvailable(string $browser): bool
    {
        return match ($browser) {
            'chrome' => $this->isChromeAvailable(),
            'firefox' => $this->isFirefoxAvailable(),
            'safari' => $this->isSafariAvailable(),
            'edge' => $this->isEdgeAvailable(),
            default => false,
        };
    }

    /**
     * Check if Chrome is available
     */
    protected function isChromeAvailable(): bool
    {
        // Chrome is generally available if ChromeDriver is running
        return true; // Dusk handles Chrome availability
    }

    /**
     * Check if Firefox is available
     */
    protected function isFirefoxAvailable(): bool
    {
        // Check if GeckoDriver is available
        $process = new \Symfony\Component\Process\Process(['which', 'geckodriver']);
        $process->run();
        return $process->isSuccessful();
    }

    /**
     * Check if Safari is available
     */
    protected function isSafariAvailable(): bool
    {
        // Safari is only available on macOS
        return PHP_OS_FAMILY === 'Darwin';
    }

    /**
     * Check if Edge is available
     */
    protected function isEdgeAvailable(): bool
    {
        // Edge availability check (mainly for Windows/Linux with Edge installed)
        return false; // Implement based on your needs
    }

    /**
     * Skip test if browser is not available
     */
    protected function skipIfBrowserNotAvailable(string $browser): void
    {
        if (!$this->isBrowserAvailable($browser)) {
            $this->markTestSkipped("Browser '{$browser}' is not available in this environment.");
        }
    }

    /**
     * Run test only in specific browsers
     */
    protected function onlyInBrowsers(array $browsers, callable $testCallback): void
    {
        $availableBrowsers = array_intersect($browsers, $this->getEnabledBrowsers());
        
        if (empty($availableBrowsers)) {
            $this->markTestSkipped('None of the required browsers are available: ' . implode(', ', $browsers));
        }

        $this->runAcrossAllBrowsers($testCallback, $availableBrowsers);
    }

    /**
     * Skip test in specific browsers
     */
    protected function skipInBrowsers(array $browsers, callable $testCallback): void
    {
        $enabledBrowsers = array_diff($this->getEnabledBrowsers(), $browsers);
        
        if (empty($enabledBrowsers)) {
            $this->markTestSkipped('All available browsers are in the skip list.');
        }

        $this->runAcrossAllBrowsers($testCallback, $enabledBrowsers);
    }

    /**
     * Get browser-specific configuration
     */
    protected function getBrowserConfig(string $browser): array
    {
        return Config::get("browser-testing.browsers.{$browser}", []);
    }

    /**
     * Take screenshot with browser context
     */
    protected function takeScreenshotWithContext(string $name, $browser = null): void
    {
        $browserName = $browser->browserName ?? static::$currentBrowser ?? 'unknown';
        $screenshotName = "{$name}_{$browserName}_" . date('Y-m-d_H-i-s');
        
        if (method_exists($browser, 'screenshot')) {
            $browser->screenshot($screenshotName);
        }
    }

    /**
     * Assert element behavior across browsers
     */
    protected function assertElementBehaviorAcrossBrowsers(string $selector, callable $assertion): void
    {
        $this->runAcrossAllBrowsers(function ($browser) use ($selector, $assertion) {
            $element = $browser->element($selector);
            $assertion($element, $browser);
        });
    }

    /**
     * Test responsive behavior across browsers
     */
    protected function testResponsiveAcrossBrowsers(array $viewports, callable $testCallback): void
    {
        $this->runAcrossAllBrowsers(function ($browser) use ($viewports, $testCallback) {
            foreach ($viewports as $viewport) {
                $browser->resize($viewport['width'], $viewport['height']);
                $testCallback($browser, $viewport);
            }
        });
    }

    /**
     * Common viewport sizes for responsive testing
     */
    protected function getCommonViewports(): array
    {
        return [
            'mobile' => ['width' => 375, 'height' => 667],
            'tablet' => ['width' => 768, 'height' => 1024],
            'desktop' => ['width' => 1920, 'height' => 1080],
            'large_desktop' => ['width' => 2560, 'height' => 1440],
        ];
    }

    /**
     * Wait for element with browser-specific timeout
     */
    protected function waitForElementWithBrowserTimeout($browser, string $selector): void
    {
        $browserName = $browser->browserName ?? 'chrome';
        $timeout = match ($browserName) {
            'safari' => 15, // Safari might be slower
            'firefox' => 12,
            'chrome' => 10,
            default => 10,
        };

        $browser->waitFor($selector, $timeout);
    }
}
