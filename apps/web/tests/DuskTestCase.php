<?php

declare(strict_types=1);

namespace Tests;

use Facebook\WebDriver\Chrome\ChromeOptions;
use Facebook\WebDriver\Remote\DesiredCapabilities;
use Facebook\WebDriver\Remote\RemoteWebDriver;
use Illuminate\Support\Collection;
use <PERSON><PERSON>\Dusk\TestCase as BaseTestCase;
use PHPUnit\Framework\Attributes\BeforeClass;
use Symfony\Component\Process\Process;

abstract class DuskTestCase extends BaseTestCase
{
    /**
     * The base URL used by Dusk when testing the application.
     *
     * @var string
     */
    protected static $baseUrl = 'http://127.0.0.1:8000';

    use CreatesApplication;

    /**
     * @var Process
     */
    protected static $serveProcess;

    /**
     * Prepare for Dusk test execution.
     */
    #[BeforeClass]
    public static function prepare(): void
    {
        // Build front-end assets for Dusk
        $build = new Process(['npm', 'run', 'build'], dirname(__DIR__));
        $build->run();
        // Mock Vite manifest if needed
        //(new static)->withoutVite(); // in individual tests
        // Refresh and seed the test database with only E2E data
        $refresh = new Process(['php', 'artisan', 'db:refresh-all',  '--env=testing'], dirname(__DIR__));
        $refresh->run();

        // Parse Markus and Lukas using USX parser, ensuring only test DB is affected
        $parseMarkus = new Process(['php', 'artisan', 'bible:parse', 'Markus', '--output', '--env=testing'], dirname(__DIR__));
        $parseMarkus->setTimeout(null);
        $parseMarkus->run();

        $parseLukas = new Process(['php', 'artisan', 'bible:parse', 'Lukas', '--output', '--env=testing'], dirname(__DIR__));
        $parseLukas->setTimeout(null);
        $parseLukas->run();

        if (! static::runningInSail()) {
            static::startChromeDriver(['--port=9515']);
        }
        // Start Laravel server for browser tests
        static::$serveProcess = new Process(['php', 'artisan', 'serve', '--host=127.0.0.1', '--port=8000', '--env=testing'], dirname(__DIR__));
        static::$serveProcess->start();
        sleep(1);
    }

    /**
     * Create the RemoteWebDriver instance.
     */
    protected function driver(): RemoteWebDriver
    {
        $options = (new ChromeOptions)->addArguments(collect([
            $this->shouldStartMaximized() ? '--start-maximized' : '--window-size=1920,1080',
            '--disable-search-engine-choice-screen',
            '--disable-smooth-scrolling',
        ])->unless($this->hasHeadlessDisabled(), function (Collection $items) {
            return $items->merge([
                '--disable-gpu',
                '--headless=new',
            ]);
        })->all());

        return RemoteWebDriver::create(
            $_ENV['DUSK_DRIVER_URL'] ?? env('DUSK_DRIVER_URL') ?? 'http://localhost:9515',
            DesiredCapabilities::chrome()->setCapability(
                ChromeOptions::CAPABILITY, $options
            )
        );
    }

    /**
     * Stop Laravel server after tests.
     */
    public static function tearDownAfterClass(): void
    {
        parent::tearDownAfterClass();
        if (static::$serveProcess) {
            static::$serveProcess->stop();
        }
    }
}
