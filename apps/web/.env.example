APP_NAME=esra-bibel
APP_ENV=production
APP_KEY=${APP_KEY:-null}
APP_DEBUG=false
APP_TIMEZONE=Europe/Berlin
APP_URL=http://app.esrabibel.de/

APP_LOCALE=de
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=de_DE

APP_MAINTENANCE_DRIVER=file

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=${DB_DATABASE:-esra-bibel}
DB_USERNAME=${DB_USERNAME:-root}
DB_PASSWORD=${DB_PASSWORD:-root}

SESSION_DRIVER=redis
SESSION_LIFETIME=${SESSION_LIFETIME:-120}
SESSION_ENCRYPT=true
SESSION_PATH=/
SESSION_DOMAIN=${SESSION_DOMAIN:-.esrabibel.de}

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=redis

CACHE_STORE=redis
CACHE_PREFIX=${CACHE_PREFIX:-esb_}

REDIS_CLIENT=phpredis
REDIS_HOST=${REDIS_HOST:-127.0.0.1}
REDIS_PASSWORD=${REDIS_PASSWORD:-secret}
REDIS_PORT=${REDIS_PORT:-6379}
REDIS_PREFIX=${REDIS_PREFIX:-esb_}
REDIS_CACHE_DB=1
REDIS_BIBLE_DB=2
REDIS_SEARCH_DB=3
REDIS_SESSION_DB=4
REDIS_QUEUE_DB=5

MAIL_MAILER=log
MAIL_HOST=${MAIL_HOST:-127.0.0.1}
MAIL_PORT=${MAIL_PORT:-2525}
MAIL_USERNAME=${MAIL_USERNAME:-null}
MAIL_PASSWORD=${MAIL_PASSWORD:-null}
MAIL_ENCRYPTION=${MAIL_ENCRYPTION:-null}
MAIL_FROM_ADDRESS=${MAIL_FROM_ADDRESS:-"<EMAIL>"}
MAIL_FROM_NAME="${APP_NAME}"

VITE_APP_NAME="${APP_NAME}"
VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

MEILISEARCH_HOST=${MEILISEARCH_HOST:-http://127.0.0.1:7700}
MEILISEARCH_KEY=${MEILISEARCH_KEY:-}

VITE_APP_ENV=${APP_ENV}
VITE_SENTRY_DSN=${SENTRY_DSN:-}
VITE_SENTRY_AUTH_TOKEN=${SENTRY_AUTH_TOKEN:-}
