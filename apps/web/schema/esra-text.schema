{"type": "object", "required": ["book", "chapters"], "properties": {"book": {"type": "object", "required": ["name", "number"], "properties": {"name": {"type": "string"}, "number": {"type": "integer"}}}, "chapters": {"type": "array", "items": {"type": "object", "required": ["chapter", "verses"], "properties": {"chapter": {"type": "integer"}, "pericopes": {"type": "array", "items": {"type": "object", "required": ["start_verse", "end_verse", "title"], "properties": {"start_verse": {"type": "integer"}, "end_verse": {"type": ["integer", "null"]}, "title": {"type": "string"}}}}, "verses": {"type": "array", "items": {"type": "object", "required": ["vers", "content"], "properties": {"vers": {"type": "integer"}, "is_pericope_start": {"type": "boolean"}, "content": {"type": "array", "items": {"type": "object", "required": ["text"], "properties": {"text": {"type": "string"}, "styles": {"type": "array", "items": {"type": "string", "enum": ["normal", "ot_quote", "emphasized", "variant", "greek_footnote", "pericope_title", "chapter_number", "verse_number", "pericope_verse_number", "book_title"]}}, "metadata": {"type": "object", "properties": {"is_bracketed": {"type": "boolean"}, "is_italics": {"type": "boolean"}}}, "footnotes": {"type": "array", "items": {"type": "object", "required": ["id", "referenced_word", "content"], "properties": {"id": {"type": "string"}, "referenced_word": {"type": "string"}, "content": {"type": "array", "items": {"type": "object", "required": ["text"], "properties": {"text": {"type": "string"}, "styles": {"type": "array", "items": {"type": "string", "enum": ["normal", "greek_footnote", "emphasized"]}}}}}}}}}}}}}}}}}}}