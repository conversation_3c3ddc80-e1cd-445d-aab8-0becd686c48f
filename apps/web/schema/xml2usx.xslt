<?xml version="1.0" encoding="UTF-8"?>
<xsl:stylesheet version="1.0" 
    xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
    xmlns:idPkg="http://ns.adobe.com/AdobeInDesign/idml/1.0/packaging">
    
    <xsl:output 
        method="xml" 
        indent="yes"
        encoding="UTF-8"
        omit-xml-declaration="no"
        standalone="yes"/>

    <!-- Parameters from PHP -->
    <xsl:param name="book-common-name"/>
    <xsl:param name="book-alternate-names"/>
    
    <xsl:variable name="footnote-letters" select="'abcdefghijklmnopqrstuvwxyz'"/>
    <xsl:strip-space elements="*"/>
    
    <!-- Root template -->
    <xsl:template match="/">
        <usx version="3.0">
            <book code="{$book-common-name}" style="id">
                <!-- Full title from source XML -->
                <para style="h">
                    <xsl:apply-templates select="//ParagraphStyleRange[contains(@AppliedParagraphStyle, 'Title')]"/>
                </para>
                
                <!-- Common name -->
                <para style="toc1">
                    <xsl:value-of select="$book-common-name"/>
                </para>
                
                <!-- Alternate names if available -->
                <xsl:if test="$book-alternate-names != ''">
                    <para style="toc2">
                        <xsl:call-template name="process-alternate-names">
                            <xsl:with-param name="names" select="$book-alternate-names"/>
                        </xsl:call-template>
                    </para>
                </xsl:if>
                
                <!-- Chapter and content -->
                <chapter number="1" style="c"/>
                <para style="p">
                    <verse number="1" style="v"/>
                    <xsl:for-each select="//ParagraphStyleRange[contains(@AppliedParagraphStyle, 'Normal')]/CharacterStyleRange">
                        <xsl:choose>
                            <!-- Skip chapter number -->
                            <xsl:when test="@AppliedCharacterStyle='CharacterStyle/Kapitelnummer'"/>
                            <!-- Skip verse 1 number but process its footnote -->
                            <xsl:when test="@AppliedCharacterStyle='CharacterStyle/Versnummer' and Content='1'">
                                <xsl:apply-templates select="Footnote"/>
                            </xsl:when>
                            <!-- Process everything else -->
                            <xsl:otherwise>
                                <xsl:apply-templates select="."/>
                            </xsl:otherwise>
                        </xsl:choose>
                    </xsl:for-each>
                </para>
            </book>
        </usx>
    </xsl:template>

    <!-- Title processing -->
    <xsl:template match="ParagraphStyleRange[contains(@AppliedParagraphStyle, 'Title')]">
        <xsl:apply-templates select="CharacterStyleRange/Content"/>
    </xsl:template>

    <!-- Character style range processing -->
    <xsl:template match="CharacterStyleRange">
        <xsl:choose>
            <xsl:when test="@AppliedCharacterStyle='CharacterStyle/Versnummer' and Content != '1'">
                <verse number="{Content}" style="v"/>
            </xsl:when>
            <xsl:when test="@AppliedCharacterStyle='CharacterStyle/Fußnotenzahl im Bibeltext'">
                <xsl:apply-templates select="Footnote"/>
            </xsl:when>
            <xsl:when test="@AppliedCharacterStyle='CharacterStyle/Kursiv in den Fußnoten'">
                <char style="it">
                    <xsl:apply-templates select="Content"/>
                </char>
            </xsl:when>
            <xsl:otherwise>
                <xsl:apply-templates select="Content"/>
            </xsl:otherwise>
        </xsl:choose>
    </xsl:template>

    <!-- Footnote processing -->
    <xsl:template match="Footnote">
        <xsl:variable name="footnote-index" select="count(preceding::Footnote) + 1"/>
        <note caller="{substring($footnote-letters, $footnote-index, 1)}" style="f">
            <xsl:apply-templates select=".//ParagraphStyleRange"/>
        </note>
    </xsl:template>

    <!-- Content processing -->
    <xsl:template match="Content">
        <xsl:choose>
            <xsl:when test="ancestor::Footnote">
                <xsl:call-template name="process-footnote-content">
                    <xsl:with-param name="text" select="normalize-space(.)"/>
                </xsl:call-template>
            </xsl:when>
            <xsl:otherwise>
                <xsl:value-of select="."/>
            </xsl:otherwise>
        </xsl:choose>
    </xsl:template>

    <!-- Template to process alternate names -->
    <xsl:template name="process-alternate-names">
        <xsl:param name="names"/>
        <xsl:value-of select="translate($names, '|', '; ')"/>
    </xsl:template>

   <!-- Modified template to process footnote content and identify cross references -->
    <xsl:template name="process-footnote-content">
        <xsl:param name="text"/>
        
        <xsl:choose>
            <!-- Process verse references with V. pattern -->
            <xsl:when test="contains($text, 'V. ')">
                <xsl:value-of select="substring-before($text, 'V. ')"/>
                <xsl:text>V. </xsl:text>
                <xsl:variable name="after-v" select="substring-after($text, 'V. ')"/>
                <xsl:choose>
                    <!-- Modified to handle multiple digits and periods -->
                    <xsl:when test="translate(substring-before(concat($after-v, ' '), ' '),'0123456789.','')=''">
                        <char style="xt">
                            <xsl:value-of select="substring-before($after-v, ' ')"/>
                        </char>
                        <xsl:call-template name="process-footnote-content">
                            <xsl:with-param name="text" select="substring-after($after-v, ' ')"/>
                        </xsl:call-template>
                    </xsl:when>
                    <xsl:otherwise>
                        <xsl:value-of select="$after-v"/>
                    </xsl:otherwise>
                </xsl:choose>
            </xsl:when>

            <!-- Process book references (e.g., "Röm. 5,8") -->
            <xsl:when test="contains($text, '. ')">
                <xsl:variable name="before" select="substring-before($text, '. ')"/>
                <xsl:variable name="after" select="substring-after($text, '. ')"/>
                <xsl:choose>
                    <!-- Modified to better handle book references -->
                    <xsl:when test="string-length($before) &lt;= 4 and contains($after, ',')">
                        <char style="xt">
                            <xsl:value-of select="$before"/>
                            <xsl:text>. </xsl:text>
                            <xsl:value-of select="substring-before(concat($after, ' '), ' ')"/>
                        </char>
                        <xsl:call-template name="process-footnote-content">
                            <xsl:with-param name="text" select="substring-after($after, substring-before(concat($after, ' '), ' '))"/>
                        </xsl:call-template>
                    </xsl:when>
                    <xsl:otherwise>
                        <xsl:value-of select="$before"/>
                        <xsl:text>. </xsl:text>
                        <xsl:call-template name="process-footnote-content">
                            <xsl:with-param name="text" select="$after"/>
                        </xsl:call-template>
                    </xsl:otherwise>
                </xsl:choose>
            </xsl:when>

            <!-- Process direct verse references (e.g., "17,24") -->
            <xsl:when test="translate(substring-before(concat($text, ' '), ' '),'0123456789,','')='' 
                            and string-length(substring-before(concat($text, ' '), ' ')) >= 3">
                <char style="xt">
                    <xsl:value-of select="substring-before($text, ' ')"/>
                </char>
                <xsl:call-template name="process-footnote-content">
                    <xsl:with-param name="text" select="substring-after($text, ' ')"/>
                </xsl:call-template>
            </xsl:when>

            <!-- Process siehe references -->
            <xsl:when test="contains($text, 'siehe ')">
                <xsl:value-of select="substring-before($text, 'siehe ')"/>
                <xsl:text>siehe </xsl:text>
                <xsl:variable name="after-siehe" select="substring-after($text, 'siehe ')"/>
                <xsl:choose>
                    <xsl:when test="contains($after-siehe, ',') or contains($after-siehe, '.')">
                        <char style="xt">
                            <xsl:value-of select="substring-before(concat($after-siehe, ' '), ' ')"/>
                        </char>
                        <xsl:call-template name="process-footnote-content">
                            <xsl:with-param name="text" select="substring-after($after-siehe, substring-before(concat($after-siehe, ' '), ' '))"/>
                        </xsl:call-template>
                    </xsl:when>
                    <xsl:otherwise>
                        <xsl:value-of select="$after-siehe"/>
                    </xsl:otherwise>
                </xsl:choose>
            </xsl:when>

            <xsl:otherwise>
                <xsl:value-of select="$text"/>
            </xsl:otherwise>
        </xsl:choose>
    </xsl:template>


    <!-- Remove processing instructions -->
    <xsl:template match="processing-instruction()"/>
</xsl:stylesheet>
