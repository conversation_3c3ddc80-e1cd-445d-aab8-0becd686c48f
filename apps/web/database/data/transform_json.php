<?php

$jsonFile = __DIR__ . '/bible_books.json';
$jsonContent = file_get_contents($jsonFile);
$data = json_decode($jsonContent, true);

// Extract the books data and make it the root
$booksData = $data['books'];

foreach ($booksData as $bookName => &$bookData) {
    if (isset($bookData['metadata']['key_aspects'])) {
        // Move all key_aspects fields up to metadata level
        foreach ($bookData['metadata']['key_aspects'] as $key => $value) {
            $bookData['metadata'][$key] = $value;
        }
        // Remove the key_aspects node
        unset($bookData['metadata']['key_aspects']);
    }
}

// Save the transformed JSON with pretty print
file_put_contents($jsonFile, json_encode($booksData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));

echo "JSON transformation complete!\n";
