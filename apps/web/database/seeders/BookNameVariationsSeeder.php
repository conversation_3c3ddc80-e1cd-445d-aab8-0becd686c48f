<?php

namespace Database\Seeders;

use App\Models\Book;
use Illuminate\Database\Seeder;

class BookNameVariationsSeeder extends Seeder
{
    private array $bookVariations = [
        // Altes Testament (Old Testament)
        'Genesis' => [
            'abbreviations' => ['Gen', '1Mo', '1 Mo'],
            'alternate_names' => ['1. Mose', '1.Mose', '<PERSON><PERSON><PERSON> Mose', '1 Mose', 'Das erste Buch Mose'],
            'common_names' => ['Genesis'],
            'url_formats' => ['1.Mose', '1Mose']
        ],
        'Exodus' => [
            'abbreviations' => ['Ex', 'Exo', '2Mo', '2 Mo'],
            'alternate_names' => ['2. Mose', '2.Mo<PERSON>', '<PERSON><PERSON><PERSON> Mose', '2 <PERSON><PERSON>', 'Das zweite Buch Mose'],
            'common_names' => ['Exodus'],
            'url_formats' => ['2.Mose', '2Mose']
        ],
        'Levitikus' => [
            'abbreviations' => ['<PERSON>', '3Mo', '3 Mo'],
            'alternate_names' => ['3. Mose', '3.Mose', 'Dr<PERSON> Mose', '3 <PERSON><PERSON>', 'Das dritte Buch Mose'],
            'common_names' => ['Levitikus'],
            'url_formats' => ['3.Mose', '3Mose']
        ],
        'Numeri' => [
            'abbreviations' => ['Num', '4Mo', '4 Mo'],
            'alternate_names' => ['4. Mose', '4.Mose', 'Vierte Mose', '4 Mose', 'Das vierte Buch Mose'],
            'common_names' => ['Numeri'],
            'url_formats' => ['4.Mose', '4Mose']
        ],
        'Deuteronomium' => [
            'abbreviations' => ['Dtn', 'Deut', '5Mo', '5 Mo'],
            'alternate_names' => ['5. Mose', '5.Mose', 'Fünfte Mose', '5 Mose', 'Das fünfte Buch Mose'],
            'common_names' => ['Deuteronomium'],
            'url_formats' => ['5.Mose', '5Mose']
        ],
        'Josua' => [
            'abbreviations' => ['Jos', 'Josh'],
            'alternate_names' => ['Das Buch Josua'],
            'common_names' => ['Josua'],
            'url_formats' => ['Josua']
        ],
        'Richter' => [
            'abbreviations' => ['Ri', 'Richt'],
            'alternate_names' => ['Das Buch der Richter'],
            'common_names' => ['Richter'],
            'url_formats' => ['Richter']
        ],
        'Ruth' => [
            'abbreviations' => ['Rt', 'Rut'],
            'alternate_names' => ['Das Buch Ruth', 'Ruth'],
            'common_names' => ['Rut'],
            'url_formats' => ['Ruth', 'Rut']
        ],
        '1. Samuel' => [
            'abbreviations' => ['1Sam', '1 Sam', '1.Sam'],
            'alternate_names' => ['1. Samuel', '1.Samuel', 'Erste Samuel', 'Das erste Buch Samuel'],
            'common_names' => ['1 Samuel'],
            'url_formats' => ['1.Samuel', '1Samuel']
        ],
        '2. Samuel' => [
            'abbreviations' => ['2Sam', '2 Sam', '2.Sam'],
            'alternate_names' => ['2. Samuel', '2.Samuel', 'Zweite Samuel', 'Das zweite Buch Samuel'],
            'common_names' => ['2 Samuel'],
            'url_formats' => ['2.Samuel', '2Samuel']
        ],
        '1. Könige' => [
            'abbreviations' => ['1Kön', '1 Kön', '1.Kön'],
            'alternate_names' => ['1. Könige', '1.Könige', 'Erste Könige', 'Das erste Buch der Könige'],
            'common_names' => ['1 Könige'],
            'url_formats' => ['1.Koenige', '1Koenige', '1.Könige', '1Könige']
        ],
        '2. Könige' => [
            'abbreviations' => ['2Kön', '2 Kön', '2.Kön'],
            'alternate_names' => ['2. Könige', '2.Könige', 'Zweite Könige', 'Das zweite Buch der Könige'],
            'common_names' => ['2 Könige'],
            'url_formats' => ['2.Koenige', '2Koenige', '2.Könige', '2Könige']
        ],
        '1. Chronik' => [
            'abbreviations' => ['1Chr', '1 Chr', '1.Chr'],
            'alternate_names' => ['1. Chronik', '1.Chronik', 'Erste Chronik', 'Das erste Buch der Chronik'],
            'common_names' => ['1 Chronik'],
            'url_formats' => ['1.Chronik', '1Chronik']
        ],
        '2. Chronik' => [
            'abbreviations' => ['2Chr', '2 Chr', '2.Chr'],
            'alternate_names' => ['2. Chronik', '2.Chronik', 'Zweite Chronik', 'Das zweite Buch der Chronik'],
            'common_names' => ['2 Chronik'],
            'url_formats' => ['2.Chronik', '2Chronik']
        ],
        'Esra' => [
            'abbreviations' => ['Esr'],
            'alternate_names' => ['Das Buch Esra'],
            'common_names' => ['Esra'],
            'url_formats' => ['Esra']
        ],
        'Nehemia' => [
            'abbreviations' => ['Neh'],
            'alternate_names' => ['Das Buch Nehemia'],
            'common_names' => ['Nehemia'],
            'url_formats' => ['Nehemia']
        ],
        'Esther' => [
            'abbreviations' => ['Est'],
            'alternate_names' => ['Das Buch Esther'],
            'common_names' => ['Esther'],
            'url_formats' => ['Esther']
        ],
        'Hiob' => [
            'abbreviations' => ['Hi', 'Hiob'],
            'alternate_names' => ['Das Buch Hiob'],
            'common_names' => ['Hiob'],
            'url_formats' => ['Hiob']
        ],
        'Psalmen' => [
            'abbreviations' => ['Ps', 'Psa'],
            'alternate_names' => ['Der Psalter', 'Psalm'],
            'common_names' => ['Psalmen'],
            'url_formats' => ['Psalmen', 'Psalm']
        ],
        'Sprüche' => [
            'abbreviations' => ['Spr'],
            'alternate_names' => ['Die Sprüche Salomos'],
            'common_names' => ['Sprüche'],
            'url_formats' => ['Sprueche', 'Sprüche']
        ],
        'Prediger' => [
            'abbreviations' => ['Pred'],
            'alternate_names' => ['Der Prediger Salomo', 'Kohelet'],
            'common_names' => ['Prediger'],
            'url_formats' => ['Prediger']
        ],
        'Hohelied' => [
            'abbreviations' => ['Hld', 'Hl'],
            'alternate_names' => ['Das Hohelied Salomos', 'Hoheslied'],
            'common_names' => ['Hohelied'],
            'url_formats' => ['Hohelied', 'Hoheslied']
        ],
        'Jesaja' => [
            'abbreviations' => ['Jes'],
            'alternate_names' => ['Der Prophet Jesaja'],
            'common_names' => ['Jesaja'],
            'url_formats' => ['Jesaja']
        ],
        'Jeremia' => [
            'abbreviations' => ['Jer'],
            'alternate_names' => ['Der Prophet Jeremia'],
            'common_names' => ['Jeremia'],
            'url_formats' => ['Jeremia']
        ],
        'Klagelieder' => [
            'abbreviations' => ['Klgl', 'Kla'],
            'alternate_names' => ['Die Klagelieder Jeremias'],
            'common_names' => ['Klagelieder'],
            'url_formats' => ['Klagelieder']
        ],
        'Hesekiel' => [
            'abbreviations' => ['Hes', 'Ez'],
            'alternate_names' => ['Der Prophet Hesekiel', 'Ezechiel'],
            'common_names' => ['Hesekiel'],
            'url_formats' => ['Hesekiel', 'Ezechiel']
        ],
        'Daniel' => [
            'abbreviations' => ['Dan'],
            'alternate_names' => ['Der Prophet Daniel'],
            'common_names' => ['Daniel'],
            'url_formats' => ['Daniel']
        ],
        'Hosea' => [
            'abbreviations' => ['Hos'],
            'alternate_names' => ['Der Prophet Hosea'],
            'common_names' => ['Hosea'],
            'url_formats' => ['Hosea']
        ],
        'Joel' => [
            'abbreviations' => ['Joel'],
            'alternate_names' => ['Der Prophet Joel'],
            'common_names' => ['Joel'],
            'url_formats' => ['Joel']
        ],
        'Amos' => [
            'abbreviations' => ['Am'],
            'alternate_names' => ['Der Prophet Amos'],
            'common_names' => ['Amos'],
            'url_formats' => ['Amos']
        ],
        'Obadja' => [
            'abbreviations' => ['Ob'],
            'alternate_names' => ['Der Prophet Obadja'],
            'common_names' => ['Obadja'],
            'url_formats' => ['Obadja']
        ],
        'Jona' => [
            'abbreviations' => ['Jon'],
            'alternate_names' => ['Der Prophet Jona'],
            'common_names' => ['Jona'],
            'url_formats' => ['Jona']
        ],
        'Micha' => [
            'abbreviations' => ['Mi'],
            'alternate_names' => ['Der Prophet Micha'],
            'common_names' => ['Micha'],
            'url_formats' => ['Micha']
        ],
        'Nahum' => [
            'abbreviations' => ['Nah'],
            'alternate_names' => ['Der Prophet Nahum'],
            'common_names' => ['Nahum'],
            'url_formats' => ['Nahum']
        ],
        'Habakuk' => [
            'abbreviations' => ['Hab'],
            'alternate_names' => ['Der Prophet Habakuk'],
            'common_names' => ['Habakuk'],
            'url_formats' => ['Habakuk']
        ],
        'Zephanja' => [
            'abbreviations' => ['Zeph'],
            'alternate_names' => ['Der Prophet Zephanja'],
            'common_names' => ['Zephanja'],
            'url_formats' => ['Zephanja']
        ],
        'Haggai' => [
            'abbreviations' => ['Hag'],
            'alternate_names' => ['Der Prophet Haggai'],
            'common_names' => ['Haggai'],
            'url_formats' => ['Haggai']
        ],
        'Sacharja' => [
            'abbreviations' => ['Sach'],
            'alternate_names' => ['Der Prophet Sacharja'],
            'common_names' => ['Sacharja'],
            'url_formats' => ['Sacharja']
        ],
        'Maleachi' => [
            'abbreviations' => ['Mal'],
            'alternate_names' => ['Der Prophet Maleachi'],
            'common_names' => ['Maleachi'],
            'url_formats' => ['Maleachi']
        ],
        // Neues Testament (New Testament)
        'Matthäus' => [
            'abbreviations' => ['Mt', 'Matt'],
            'alternate_names' => ['Das Evangelium nach Matthäus'],
            'common_names' => ['Matthäus'],
            'url_formats' => ['Matthaeus', 'Matthäus']
        ],
        'Markus' => [
            'abbreviations' => ['Mk', 'Mar'],
            'alternate_names' => ['Das Evangelium nach Markus'],
            'common_names' => ['Markus'],
            'url_formats' => ['Markus']
        ],
        'Lukas' => [
            'abbreviations' => ['Lk', 'Luk'],
            'alternate_names' => ['Das Evangelium nach Lukas'],
            'common_names' => ['Lukas'],
            'url_formats' => ['Lukas']
        ],
        'Johannes' => [
            'abbreviations' => ['Joh'],
            'alternate_names' => ['Das Evangelium nach Johannes'],
            'common_names' => ['Johannes'],
            'url_formats' => ['Johannes']
        ],
        'Apostelgeschichte' => [
            'abbreviations' => ['Apg'],
            'alternate_names' => ['Die Apostelgeschichte des Lukas'],
            'common_names' => ['Apostelgeschichte'],
            'url_formats' => ['Apostelgeschichte']
        ],
        'Römer' => [
            'abbreviations' => ['Röm', 'Roem'],
            'alternate_names' => ['Der Brief an die Römer'],
            'common_names' => ['Römer'],
            'url_formats' => ['Roemer', 'Römer']
        ],
        '1. Korinther' => [
            'abbreviations' => ['1Kor', '1 Kor', '1.Kor'],
            'alternate_names' => ['1. Korinther', '1.Korinther', 'Erste Korinther', 'Der erste Brief an die Korinther'],
            'common_names' => ['1 Korinther'],
            'url_formats' => ['1.Korinther', '1Korinther']
        ],
        '2. Korinther' => [
            'abbreviations' => ['2Kor', '2 Kor', '2.Kor'],
            'alternate_names' => ['2. Korinther', '2.Korinther', 'Zweite Korinther', 'Der zweite Brief an die Korinther'],
            'common_names' => ['2 Korinther'],
            'url_formats' => ['2.Korinther', '2Korinther']
        ],
        'Galater' => [
            'abbreviations' => ['Gal'],
            'alternate_names' => ['Der Brief an die Galater'],
            'common_names' => ['Galater'],
            'url_formats' => ['Galater']
        ],
        'Epheser' => [
            'abbreviations' => ['Eph'],
            'alternate_names' => ['Der Brief an die Epheser'],
            'common_names' => ['Epheser'],
            'url_formats' => ['Epheser']
        ],
        'Philipper' => [
            'abbreviations' => ['Phil'],
            'alternate_names' => ['Der Brief an die Philipper'],
            'common_names' => ['Philipper'],
            'url_formats' => ['Philipper']
        ],
        'Kolosser' => [
            'abbreviations' => ['Kol'],
            'alternate_names' => ['Der Brief an die Kolosser'],
            'common_names' => ['Kolosser'],
            'url_formats' => ['Kolosser']
        ],
        '1. Thessalonicher' => [
            'abbreviations' => ['1Thess', '1 Thess', '1.Thess'],
            'alternate_names' => ['1. Thessalonicher', '1.Thessalonicher', 'Erste Thessalonicher', 'Der erste Brief an die Thessalonicher'],
            'common_names' => ['1 Thessalonicher'],
            'url_formats' => ['1.Thessalonicher', '1Thessalonicher']
        ],
        '2. Thessalonicher' => [
            'abbreviations' => ['2Thess', '2 Thess', '2.Thess'],
            'alternate_names' => ['2. Thessalonicher', '2.Thessalonicher', 'Zweite Thessalonicher', 'Der zweite Brief an die Thessalonicher'],
            'common_names' => ['2 Thessalonicher'],
            'url_formats' => ['2.Thessalonicher', '2Thessalonicher']
        ],
        '1. Timotheus' => [
            'abbreviations' => ['1Tim', '1 Tim', '1.Tim'],
            'alternate_names' => ['1. Timotheus', '1.Timotheus', 'Erste Timotheus', 'Der erste Brief an Timotheus'],
            'common_names' => ['1 Timotheus'],
            'url_formats' => ['1.Timotheus', '1Timotheus']
        ],
        '2. Timotheus' => [
            'abbreviations' => ['2Tim', '2 Tim', '2.Tim'],
            'alternate_names' => ['2. Timotheus', '2.Timotheus', 'Zweite Timotheus', 'Der zweite Brief an Timotheus'],
            'common_names' => ['2 Timotheus'],
            'url_formats' => ['2.Timotheus', '2Timotheus']
        ],
        'Titus' => [
            'abbreviations' => ['Tit'],
            'alternate_names' => ['Der Brief an Titus'],
            'common_names' => ['Titus'],
            'url_formats' => ['Titus']
        ],
        'Philemon' => [
            'abbreviations' => ['Phlm', 'Phim'],
            'alternate_names' => ['Der Brief an Philemon'],
            'common_names' => ['Philemon'],
            'url_formats' => ['Philemon']
        ],
        'Hebräer' => [
            'abbreviations' => ['Hebr', 'Heb'],
            'alternate_names' => ['Der Brief an die Hebräer'],
            'common_names' => ['Hebräer'],
            'url_formats' => ['Hebraeer', 'Hebräer']
        ],
        'Jakobus' => [
            'abbreviations' => ['Jak'],
            'alternate_names' => ['Der Brief des Jakobus'],
            'common_names' => ['Jakobus'],
            'url_formats' => ['Jakobus']
        ],
        '1. Petrus' => [
            'abbreviations' => ['1Petr', '1 Petr', '1.Petr'],
            'alternate_names' => ['1. Petrus', '1.Petrus', 'Erste Petrus', 'Der erste Brief des Petrus'],
            'common_names' => ['1 Petrus'],
            'url_formats' => ['1.Petrus', '1Petrus']
        ],
        '2. Petrus' => [
            'abbreviations' => ['2Petr', '2 Petr', '2.Petr'],
            'alternate_names' => ['2. Petrus', '2.Petrus', 'Zweite Petrus', 'Der zweite Brief des Petrus'],
            'common_names' => ['2 Petrus'],
            'url_formats' => ['2.Petrus', '2Petrus']
        ],
        '1. Johannes' => [
            'abbreviations' => ['1Joh', '1 Joh', '1.Joh'],
            'alternate_names' => ['1. Johannes', '1.Johannes', 'Erste Johannes', 'Der erste Brief des Johannes'],
            'common_names' => ['1 Johannes'],
            'url_formats' => ['1.Johannes', '1Johannes']
        ],
        '2. Johannes' => [
            'abbreviations' => ['2Joh', '2 Joh', '2.Joh'],
            'alternate_names' => ['2. Johannes', '2.Johannes', 'Zweite Johannes', 'Der zweite Brief des Johannes'],
            'common_names' => ['2 Johannes'],
            'url_formats' => ['2.Johannes', '2Johannes']
        ],
        '3. Johannes' => [
            'abbreviations' => ['3Joh', '3 Joh', '3.Joh'],
            'alternate_names' => ['3. Johannes', '3.Johannes', 'Dritte Johannes', 'Der dritte Brief des Johannes'],
            'common_names' => ['3 Johannes'],
            'url_formats' => ['3.Johannes', '3Johannes']
        ],
        'Judas' => [
            'abbreviations' => ['Jud'],
            'alternate_names' => ['Der Brief des Judas'],
            'common_names' => ['Judas'],
            'url_formats' => ['Judas']
        ],
        'Offenbarung' => [
            'abbreviations' => ['Offb'],
            'alternate_names' => ['Die Offenbarung des Johannes', 'Apokalypse'],
            'common_names' => ['Offenbarung'],
            'url_formats' => ['Offenbarung']
        ],
    ];

    public function run(): void
    {
        foreach ($this->bookVariations as $bookName => $variations) {
            $book = Book::where('name', $bookName)->first();

            if (!$book) {
                continue;
            }

            foreach ($variations as $type => $names) {
                foreach ($names as $variation) {
                    $book->nameVariations()->create([
                        'variation' => $variation,
                        'type' => str_replace('s', '', $type) // remove 's' from type
                    ]);
                }
            }
        }
    }
}
