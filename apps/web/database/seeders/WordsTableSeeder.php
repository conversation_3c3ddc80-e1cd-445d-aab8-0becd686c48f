<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class WordsTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $words = [
            [
                'text' => 'This',
                'position' => 0,
                'verse_id' => 1,
                'has_footnote' => false,
                'has_variant' => false,
                'is_emphasized' => false,
                'is_ot_quote' => false,
                'variant_type' => null,
            ],
            [
                'text' => '[not]',
                'position' => 1,
                'verse_id' => 1,
                'has_footnote' => false,
                'has_variant' => true,
                'is_emphasized' => false,
                'is_ot_quote' => false,
                'variant_type' => 'not_in_original',
            ],
            [
                'text' => 'in',
                'position' => 2,
                'verse_id' => 1,
                'has_footnote' => false,
                'has_variant' => false,
                'is_emphasized' => false,
                'is_ot_quote' => false,
                'variant_type' => null,
            ],
            [
                'text' => 'the',
                'position' => 3,
                'verse_id' => 1,
                'has_footnote' => false,
                'has_variant' => false,
                'is_emphasized' => false,
                'is_ot_quote' => false,
                'variant_type' => null,
            ],
            [
                'text' => 'original',
                'position' => 4,
                'verse_id' => 1,
                'has_footnote' => false,
                'has_variant' => false,
                'is_emphasized' => false,
                'is_ot_quote' => false,
                'variant_type' => null,
            ],
            [
                'text' => 'text.',
                'position' => 5,
                'verse_id' => 1,
                'has_footnote' => false,
                'has_variant' => false,
                'is_emphasized' => false,
                'is_ot_quote' => false,
                'variant_type' => null,
            ],
        ];

        DB::table('words')->insert($words);
    }
}
