<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // User::factory(10)->create();
        $this->call([
            BooksTableSeeder::class,
            ChaptersTableSeeder::class,
            // Add other seeders here...
        ]);

        User::factory()->create([
            'name' => 'Bibelschmuggler',
            'email' => '<EMAIL>',
            'password' => bcrypt('esra'),
        ]);
    }
}
