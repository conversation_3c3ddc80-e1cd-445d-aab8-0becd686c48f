<?php

namespace Database\Seeders;

use App\Models\Book;
use App\Models\BookNameVariation;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\QueryException;
use RuntimeException;
use Throwable;
use App\Services\BibleData\BibleBooksLoader;
use App\Enums\BookCategory;
use App\Enums\Testament;

class BooksTableSeeder extends Seeder
{
    protected $loader;
    protected $successCount = 0;
    protected $failedBooks = [];

    public function __construct(BibleBooksLoader $loader)
    {
        $this->loader = $loader;
    }

    public function run(): void
    {
        try {
            // Set test database if running in testing environment
            if (app()->environment('testing')) {
                config(['database.connections.mysql.database' => env('DB_DATABASE_TEST', 'esra-bibel-test')]);
                DB::purge('mysql');
                DB::reconnect('mysql');
            }

            $booksData = $this->loader->load();
            $this->command->info("Found " . count($booksData) . " books to import.");
            
            DB::transaction(function () use ($booksData) {
                $createdBooks = collect();
                $variations = collect();

                foreach ($booksData as $bookName => $bookData) {
                    try {
                        $this->command->line("Importing {$bookName}...");

                        $metadata = $bookData['metadata'];

                        $book = Book::create([
                            'name' => $bookName,
                            'slug' => $this->formatSlug($bookName),
                            'abbreviation' => $bookData['abbreviations'][0] ?? null,
                            'order' => $metadata['order'],
                            'testament' => $this->transformTestament($metadata['testament']),
                            'category' => $this->transformCategory($metadata['category']),
                            'chapters_count' => $metadata['chapters_count'],
                            'original_language' => $metadata['original_language'],
                            'location' => $metadata['location'] ?? null,
                            'historical_period' => $metadata['historical_period'] ?? null,
                            'authors' => implode(',', $metadata['authors'] ?? []),
                            'key_people' => implode(',', $metadata['key_people'] ?? []),
                            'attributes_of_god' => implode(',', $metadata['attributes_of_god'] ?? []),
                            'key_words' => implode(',', $metadata['key_words'] ?? []),
                            'covenants' => implode(',', $metadata['covenants'] ?? []),
                            'key_teachings' => implode(',', $metadata['key_teachings'] ?? []),
                            'key_verses' => implode(',', $metadata['key_verses'] ?? []),
                            'search_names' => implode(',', $bookData['alternate_names'] ?? []),
                            'written_year' => $metadata['written_year'] ?? null,
                            'theme' => $metadata['theme'] ?? null
                        ]);

                        $this->command->line("Is of type Book: ".$book instanceof Book);
                        if ($book instanceof Book) {
                            $createdBooks->push($book);
                        } else {
                            $this->command->line('Book creation failed for ' . $bookName);
                        }

                        // Collect variations
                        foreach (['abbreviation' => 'abbreviations', 'alternate_name' => 'alternate_names', 'common_name' => 'common_names'] as $type => $key) {
                            foreach ($bookData[$key] ?? [] as $variation) {
                                $variations->push([
                                    'book_id' => $book->id,
                                    'variation' => $variation,
                                    'type' => $type,
                                    'created_at' => now(),
                                    'updated_at' => now(),
                                ]);
                            }
                        }

                        $this->successCount++;
                        $this->command->info("✓ Successfully imported {$bookName}");

                    } catch (QueryException | RuntimeException $e) {
                        $this->failedBooks[] = [
                            'name' => $bookName,
                            'error' => $e->getMessage()
                        ];
                        $this->command->error("✗ Failed to import {$bookName}: {$e->getMessage()}");
                        Log::error("Failed to import book {$bookName}", [
                            'error' => $e->getMessage(),
                            'trace' => $e->getTraceAsString()
                        ]);
                    }
                }

                // Bulk insert variations in chunks
                if ($variations->isNotEmpty()) {
                    $this->command->info("Importing " . $variations->count() . " name variations...");

                    $variations->chunk(1000)->each(function ($chunk) {
                        try {
                            BookNameVariation::insert($chunk->toArray());
                        } catch (QueryException $e) {
                            $this->command->error("Failed to import some variations: {$e->getMessage()}");
                            Log::error("Failed to import variations chunk", [
                                'error' => $e->getMessage(),
                                'trace' => $e->getTraceAsString()
                            ]);
                        }
                    });
                }

                if ($createdBooks->isNotEmpty()) {
                    $createdBooks->searchable();
                }
            });

        } catch (Throwable $e) {
            $this->command->error("Fatal error during import: {$e->getMessage()}");
            Log::error("Fatal error during Bible books import", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        } finally {
            $this->printSummary();
        }
    }

    protected function printSummary(): void
    {
        $this->command->line("\n=== Import Summary ===");
        $this->command->info("Successfully imported: {$this->successCount} books");

        if (!empty($this->failedBooks)) {
            $this->command->error("Failed to import " . count($this->failedBooks) . " books:");
            foreach ($this->failedBooks as $failure) {
                $this->command->error("- {$failure['name']}: {$failure['error']}");
            }
        } else {
            $this->command->info("No failures reported!");
        }
    }

    private function transformTestament(string $testament): string
    {
        return match (strtolower($testament)) {
            'ot', 'old' => Testament::OT->value,
            'nt', 'new' => Testament::NT->value,
            default => throw new RuntimeException("Invalid testament value: {$testament}")
        };
    }

    private function transformCategory(string $category): string
    {
        return match (strtolower($category)) {
            'law' => BookCategory::LAW->value,
            'history' => BookCategory::HISTORY->value,
            'wisdom' => BookCategory::WISDOM->value,
            'prophecy' => BookCategory::PROPHECY->value,
            'gospel' => BookCategory::GOSPEL->value,
            'epistle' => BookCategory::EPISTLE->value,
            'apocalypse', 'apocalyptic' => BookCategory::APOCALYPTIC->value,
            default => throw new RuntimeException("Invalid category value: {$category}")
        };
    }

    protected function formatSlug(string $name): string
    {
        // Keep periods between numbers and letters (e.g., "2.Mose")
        // but remove other spaces and unnecessary periods
        $slug = preg_replace('/(\d)\s+(\w)/', '$1.$2', $name); // Replace space between number and word with period
        $slug = preg_replace('/\s+/', '', $slug); // Remove remaining spaces
        $slug = preg_replace('/\.+/', '.', $slug); // Replace multiple periods with single period
        
        // Debug: Log the input and output
        Log::info("Formatting slug for book: {$name} -> {$slug}");
        
        return $slug;
    }
}
