<?php

namespace Database\Seeders;

use App\Models\Book;
use App\Models\Chapter;
use App\Models\Verse;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class ChaptersTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Disable foreign key checks
        Schema::disableForeignKeyConstraints();

        try {
            // Clear existing chapters and verses
            Verse::truncate();
            Chapter::truncate();

            DB::transaction(function () {
                $chapterCounts = [
                    '1. Mose' => 50,
                    '2. Mose' => 40,
                    '3. <PERSON><PERSON>' => 27,
                    '4. <PERSON><PERSON>' => 36,
                    '5. <PERSON><PERSON>' => 34,
                    '<PERSON><PERSON><PERSON>' => 24,
                    '<PERSON>' => 21,
                    '<PERSON>' => 4,
                    '1. <PERSON>' => 31,
                    '2. <PERSON>' => 24,
                    '1. <PERSON><PERSON><PERSON><PERSON>' => 22,
                    '2. <PERSON><PERSON><PERSON><PERSON>' => 25,
                    '1. Chronik' => 29,
                    '2. Chronik' => 36,
                    '<PERSON><PERSON>ra' => 10,
                    'Nehemia' => 13,
                    '<PERSON>' => 10,
                    'Hiob' => 42,
                    'Psalmen' => 150,
                    'Sprü<PERSON>' => 31,
                    'Prediger' => 12,
                    '<PERSON>hel<PERSON>' => 8,
                    '<PERSON><PERSON><PERSON>' => 66,
                    '<PERSON>rem<PERSON>' => 52,
                    '<PERSON><PERSON><PERSON><PERSON>' => 5,
                    'He<PERSON><PERSON><PERSON>' => 48,
                    '<PERSON>' => 12,
                    '<PERSON><PERSON>' => 14,
                    '<PERSON>' => 3,
                    '<PERSON>' => 9,
                    'O<PERSON><PERSON>' => 1,
                    '<PERSON>a' => 4,
                    '<PERSON>cha' => 7,
                    '<PERSON>um' => 3,
                    '<PERSON>ba<PERSON>k' => 3,
                    '<PERSON><PERSON><PERSON><PERSON>' => 3,
                    '<PERSON>gg<PERSON>' => 2,
                    'Sacharja' => 14,
                    'Maleachi' => 4,
                    'Matthäus' => 28,
                    'Markus' => 16,
                    'Lukas' => 24,
                    'Johannes' => 21,
                    'Apostelgeschichte' => 28,
                    'Römer' => 16,
                    '1. Korinther' => 16,
                    '2. Korinther' => 13,
                    'Galater' => 6,
                    'Epheser' => 6,
                    'Philipper' => 4,
                    'Kolosser' => 4,
                    '1. Thessalonicher' => 5,
                    '2. Thessalonicher' => 3,
                    '1. Timotheus' => 6,
                    '2. Timotheus' => 4,
                    'Titus' => 3,
                    'Philemon' => 1,
                    'Hebräer' => 13,
                    'Jakobus' => 5,
                    '1. Petrus' => 5,
                    '2. Petrus' => 3,
                    '1. Johannes' => 5,
                    '2. Johannes' => 1,
                    '3. Johannes' => 1,
                    'Judas' => 1,
                    'Offenbarung' => 22,
                ];

                foreach ($chapterCounts as $bookName => $chapterCount) {
                    $book = Book::where('name', $bookName)->first();
                    
                    if (!$book) {
                        $this->command->error("Book not found: {$bookName}");
                        continue;
                    }

                    for ($i = 1; $i <= $chapterCount; $i++) {
                        Chapter::create([
                            'book_id' => $book->id,
                            'number' => $i,
                        ]);
                    }

                    $this->command->info("Created {$chapterCount} chapters for {$bookName}");
                }
            });
        } finally {
            // Re-enable foreign key checks
            Schema::enableForeignKeyConstraints();
        }
    }
}
