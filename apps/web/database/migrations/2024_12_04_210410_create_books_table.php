<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Enums\BookCategory;
use App\Enums\OriginalLanguage;
use App\Enums\Testament;


return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('books', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->id();
            // Core information
            $table->string('name');
            $table->string('slug')->unique();
            $table->string('abbreviation', 10)->unique();
            $table->integer('order')->unique();
            $table->enum('testament', Testament::values());
            $table->enum('category', BookCategory::values());
            $table->integer('chapters_count');
            $table->enum('original_language', OriginalLanguage::values());

            // Metadata
            $table->text('location')->nullable();
            $table->text('historical_period')->nullable();

            // 9 key aspects metadata
            $table->string('authors')->nullable();
            $table->integer('written_year')->nullable();
            $table->string('theme')->nullable();
            $table->text('key_people')->nullable();
            $table->text('attributes_of_god')->nullable();
            $table->text('key_words')->nullable();
            $table->text('covenants')->nullable();
            $table->text('key_teachings')->nullable();
            $table->text('key_verses')->nullable();

            // Add searchable variations of the book name
            $table->string('search_names')->nullable(); // Alternative names/spellings, e.g., "Genesis,Gen,1 Mose, 1.Mo ..etc."

            $table->timestamps();

            // Indexes
            $table->index('testament');
            $table->index('category');
            $table->index(['testament', 'order']);
            $table->index('original_language');
            $table->index('search_names');
            $table->index('abbreviation'); // Already unique, but good for searches

            // Add fulltext indexes only in non-testing environment
            if (app()->environment() !== 'testing') {
                $table->fullText(
                    ['authors', 'theme', 'key_people', 'attributes_of_god', 'key_words', 'covenants', 'key_teachings', 'key_verses'],
                    'books_fulltext_index'
                );
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('books');
    }
};
