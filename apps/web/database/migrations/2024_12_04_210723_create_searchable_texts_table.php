<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('searchable_texts', function (Blueprint $table) {
            $table->id();
            $table->string('type'); // verse, footnote, word, etc.
            $table->text('content')->charset('utf8mb4')->collation('utf8mb4_unicode_ci');
            $table->json('metadata')->nullable();
            
            // Foreign keys for relationships
            $table->foreignId('book_id')->nullable()->constrained()->nullOnDelete();
            $table->foreignId('chapter_id')->nullable()->constrained()->nullOnDelete();
            $table->foreignId('verse_id')->nullable()->constrained()->nullOnDelete();

            // Indexes for efficient searching
            $table->index('type');
            // Add fulltext index only in non-testing environment
            if (app()->environment() !== 'testing') {
                $table->fullText('content');
            }
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('searchable_texts');
    }
};
