<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Traits\Database\Migrations\DisableForeignKeysInDev;

return new class extends Migration
{
    use DisableForeignKeysInDev;

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tags', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Name der Markierung
            $table->timestamps();
        });

        Schema::create('word_tag', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('word_id');
            $table->unsignedBigInteger('tag_id');
            $table->timestamps();

            $table->foreign('word_id')->references('id')->on('words')->onDelete('cascade');
            $table->foreign('tag_id')->references('id')->on('tags')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // In development, disable foreign key checks to make refreshing easier
        $this->disableForeignKeysInDev();

        Schema::dropIfExists('word_tag');
        Schema::dropIfExists('tags');

        // Re-enable foreign key checks
        $this->enableForeignKeysInDev();
    }
};
