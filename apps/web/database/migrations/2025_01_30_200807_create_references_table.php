<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateReferencesTable extends Migration
{
    public function up()
    {
        Schema::create('references', function (Blueprint $table) {
            $table->id();
            $table->foreignId('footnote_id')->nullable()->constrained()->nullOnDelete();
            $table->string('type');  // book_ref, v_ref, direct_ref, etc.
            $table->string('target_book')->nullable();
            $table->integer('target_chapter')->nullable();
            $table->string('target_verse')->nullable();  // String to handle verse ranges like "1-3"
            $table->string('display_text');
            $table->json('attributes')->nullable();
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('references');
    }
}
