<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('footnotes', function (Blueprint $table) {
            $table->id();
            $table->text('searchable_text');
            $table->json('content_structure');
            $table->boolean('is_reference')->default(false);
            $table->string('referenced_word')->nullable();
            $table->foreignId('verse_id')->constrained()->onDelete('cascade');
            $table->boolean('has_italics')->default(false);
            $table->integer('position')->default(0);
            $table->string('caller')->nullable();
            $table->text('content')->nullable();
            $table->timestamps();

            // Update indexes
            $table->index(['position']);
        });

        // Ensure proper charset
        DB::unprepared('ALTER TABLE footnotes CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::disableForeignKeyConstraints();

        Schema::dropIfExists('footnotes');

        Schema::enableForeignKeyConstraints();
    }
};
