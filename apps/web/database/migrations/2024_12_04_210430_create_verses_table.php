<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('verses', function (Blueprint $table) {
            $table->id();
            $table->foreignId('chapter_id')->constrained()->cascadeOnDelete();
            $table->integer('number');
            $table->integer('start_verse')->nullable();
            $table->integer('end_verse')->nullable();
            $table->text('text')->charset('utf8mb4')->collation('utf8mb4_unicode_ci');

            // Tags and metadata
            $table->json('tags')->nullable();

            // Formatting flags
            $table->boolean('is_pericope_start')->default(false);
            $table->boolean('has_ot_quote')->default(false);
            $table->boolean('has_text_variant')->default(false);
            $table->foreignId('paragraph_style_id')->nullable()->constrained('paragraph_styles')->nullOnDelete();
            $table->string('paragraph_group_id')->nullable();

            $table->timestamps();

            // Indexes
            $table->unique(['chapter_id', 'number']);
            $table->index(['chapter_id', 'number']);
            $table->index(['chapter_id', 'start_verse', 'end_verse']);
            $table->index('paragraph_group_id');

            // Add fulltext index only in non-testing environment
            if (app()->environment() !== 'testing') {
                $table->fullText('text'); // Für Volltextsuche in Versen
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // In development, disable foreign key checks to make refreshing easier
        Schema::disableForeignKeyConstraints();

        Schema::dropIfExists('verses');

        Schema::enableForeignKeyConstraints();
    }
};
