<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('verse_references', function (Blueprint $table) {
            $table->id();
            $table->foreignId('footnote_id')->constrained()->onDelete('cascade');
            $table->integer('referenced_chapter');
            $table->integer('referenced_verse');
            $table->timestamps();

            // Add index for better performance
            $table->index(['referenced_chapter', 'referenced_verse']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('verse_references');
    }
};
