<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * Creates the words table with all necessary fields and constraints.
     * This consolidated migration includes:
     * - Original words table creation
     * - UTF8MB4 charset update
     * - Word deduplication
     * - Unique constraint for verse_id and position
     */
    public function up(): void
    {
        Schema::create('words', function (Blueprint $table) {
            $table->id();

            $table->foreignId('verse_id')->constrained()->cascadeOnDelete();

            // Text fields with proper charset
            $table->string('text')->charset('utf8mb4')->collation('utf8mb4_unicode_ci');
            $table->string('text_after')->nullable();

            // Reference fields
            $table->integer('position');

            // Metadata columns
            $table->string('word_type')->nullable(); // e.g., 'va', 'xot', 'add', etc.
            $table->boolean('has_footnote')->default(false);
            $table->foreignId('footnote_id')->nullable()->constrained()->nullOnDelete();
            $table->string('footnote_group_id')->nullable();

            $table->boolean('is_ot_quote')->default(false);
            $table->boolean('is_addition')->default(false);
            $table->boolean('is_emphasized')->default(false);

            // Variant information
            $table->boolean('has_variant')->default(false);
            $table->string('variant_group_id')->nullable();
            $table->string('word_group_id')->nullable();

            // Formatting columns
            $table->string('strongs_number')->nullable();
            $table->foreignId('paragraph_style_id')->nullable()->constrained();
            $table->string('paragraph_group_id')->nullable();

            $table->timestamps();

            // Indexes
            $table->unique(['verse_id', 'position']);
            $table->index(['verse_id', 'position']);
            $table->index(['footnote_id']);
            $table->index('paragraph_style_id');
            $table->index('paragraph_group_id');

        });

        // Ensure proper charset
        DB::unprepared('ALTER TABLE words CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::disableForeignKeyConstraints();
        Schema::dropIfExists('words');
        Schema::enableForeignKeyConstraints();
    }
};
