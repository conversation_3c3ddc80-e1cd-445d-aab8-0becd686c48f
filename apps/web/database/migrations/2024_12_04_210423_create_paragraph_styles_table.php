<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateParagraphStylesTable extends Migration
{
    public function up()
    {
        Schema::create('paragraph_styles', function (Blueprint $table) {
            $table->id();
            $table->string('style_code');  // USX style code (p, m, pi, etc.)
            $table->string('description')->nullable();
            $table->json('attributes')->nullable();
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('paragraph_styles');
    }
}
