<?php

namespace Database\Migrations\Traits;

use Illuminate\Support\Facades\DB;

trait DisableForeignKeysInDev
{
    /**
     * Disable foreign key checks if in development environment.
     */
    protected function disableForeignKeysInDev(): void
    {
        if (app()->environment(['local', 'development', 'testing'])) {
            DB::statement('SET FOREIGN_KEY_CHECKS=0');
        }
    }

    /**
     * Enable foreign key checks if in development environment.
     */
    protected function enableForeignKeysInDev(): void
    {
        if (app()->environment(['local', 'development', 'testing'])) {
            DB::statement('SET FOREIGN_KEY_CHECKS=1');
        }
    }
}
