<?php

namespace Database\Factories;

use App\Models\Book;
use App\Enums\Testament;
use App\Enums\BookCategory;
use App\Enums\OriginalLanguage;
use Illuminate\Database\Eloquent\Factories\Factory;

class BookFactory extends Factory
{
    protected $model = Book::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->unique()->word(),
            'abbreviation' => $this->faker->unique()->lexify('???'),
            'order' => $this->faker->unique()->numberBetween(1, 66),
            'testament' => $this->faker->randomElement(Testament::cases())->value,
            'category' => $this->faker->randomElement(BookCategory::cases())->value,
            'chapters_count' => $this->faker->numberBetween(1, 150),
            'original_language' => $this->faker->randomElement(OriginalLanguage::cases())->value,
            'written_year' => $this->faker->numberBetween(-1500, 100),
            'historical_period' => $this->faker->sentence(),
            'key_people' => $this->faker->words(3, true),
            'location' => $this->faker->city(),
            'search_names' => '',
            'authors' => $this->faker->name(),
            'theme' => $this->faker->sentence(),
            'attributes_of_god' => $this->faker->sentence(),
            'key_words' => $this->faker->words(3, true),
            'covenants' => $this->faker->sentence(),
            'key_teachings' => $this->faker->sentence(),
            'key_verses' => $this->faker->sentence(),
        ];
    }

    public function oldTestament(): static
    {
        return $this->state(fn (array $attributes) => [
            'testament' => Testament::OT->value,
        ]);
    }

    public function newTestament(): static
    {
        return $this->state(fn (array $attributes) => [
            'testament' => Testament::NT->value,
        ]);
    }

    public function law(): static
    {
        return $this->state(fn (array $attributes) => [
            'category' => BookCategory::LAW->value,
        ]);
    }
}
