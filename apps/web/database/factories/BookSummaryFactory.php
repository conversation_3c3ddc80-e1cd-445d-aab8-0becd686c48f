<?php

namespace Database\Factories;

use App\Models\Book;
use App\Models\BookSummary;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\BookSummary>
 */
class BookSummaryFactory extends Factory
{
    protected $model = BookSummary::class;

    public function definition(): array
    {
        return [
            'book_id' => Book::factory(),
            'summary' => $this->faker->paragraphs(3, true),
            'key_verses' => $this->faker->sentences(3, true),
            'main_themes' => $this->faker->words(5, true),
            'historical_context' => $this->faker->paragraph(),
        ];
    }
}
