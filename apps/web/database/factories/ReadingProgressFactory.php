<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ReadingProgress>
 */
namespace Database\Factories;

use App\Models\Book;
use App\Models\User;
use App\Models\ReadingProgress;
use Illuminate\Database\Eloquent\Factories\Factory;

class ReadingProgressFactory extends Factory
{
    protected $model = ReadingProgress::class;

    public function definition(): array
    {
        $book = Book::factory()->create();
        $chaptersRead = $this->faker->numberBetween(1, $book->chapters_count);
        $completionPercentage = ($chaptersRead / $book->chapters_count) * 100;
        $completed = $completionPercentage === 100;

        return [
            'user_id' => User::factory(),
            'book_id' => $book->id,
            'last_chapter_read' => $chaptersRead,
            'completion_percentage' => $completionPercentage,
            'started_at' => $this->faker->dateTimeBetween('-1 year', 'now'),
            'completed_at' => $completed ? $this->faker->dateTimeBetween('-1 month', 'now') : null,
        ];
    }

    public function completed(): static
    {
        return $this->state(function (array $attributes) {
            $book = Book::find($attributes['book_id']);

            return [
                'last_chapter_read' => $book->chapters_count,
                'completion_percentage' => 100,
                'completed_at' => $this->faker->dateTimeBetween('-1 month', 'now'),
            ];
        });
    }
}
