<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Word>
 */
class WordFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'text' => $this->faker->word(),
            'position' => $this->faker->numberBetween(1, 10),
            'strongs_number' => 'G' . $this->faker->numberBetween(1000, 9999),
            'verse_id' => null
        ];
    }
}
