<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Footnote>
 */
class FootnoteFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $content = $this->faker->sentence();
        return [
            'searchable_text' => $content,
            'content_structure' => json_encode(['text' => $content]),
            'is_reference' => false,
            'referenced_word' => null,
            'verse_id' => null,
            'has_italics' => false,
            'position' => $this->faker->numberBetween(1, 5),
            'caller' => null,
            'content' => $content
        ];
    }
}
