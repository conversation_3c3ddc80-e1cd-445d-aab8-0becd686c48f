{"$schema": "https://getcomposer.org/schema.json", "name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "guzzlehttp/guzzle": "^7.9", "http-interop/http-factory-guzzle": "^1.2", "inertiajs/inertia-laravel": "^2.0", "laravel/framework": "^12.0", "laravel/scout": "^10.11", "laravel/tinker": "^2.10", "meilisearch/meilisearch-php": "^1.11", "phpoffice/phpword": "^1.3", "tightenco/ziggy": "^2.4"}, "require-dev": {"fakerphp/faker": "^1.23", "knuckleswtf/scribe": "^5.1", "laravel/breeze": "^2.2", "laravel/dusk": "^8.3", "laravel/pail": "^1.2.2", "laravel/pint": "^1.18", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.6", "pestphp/pest": "^3.7", "pestphp/pest-plugin-laravel": "^3.1"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": ["knuckleswtf/scribe", "laravel/dusk"]}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}