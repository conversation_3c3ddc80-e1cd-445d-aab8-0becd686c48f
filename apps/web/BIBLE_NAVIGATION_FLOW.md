# Bible Navigation Flow Documentation

This document details the execution flow when navigating to a Bible chapter URL (e.g., `/Johannes1`) and scrolling through chapters.

## Table of Contents

1. [Initial URL Navigation](#initial-url-navigation)
2. [Scrolling Between Chapters](#scrolling-between-chapters)
3. [Key Components and Their Responsibilities](#key-components-and-their-responsibilities)
4. [Store Interactions](#store-interactions)
5. [API Interactions](#api-interactions)
6. [Potential Issues and Debugging](#potential-issues-and-debugging)
7. [DOM Update Process for New Chapters](#dom-update-process-for-new-chapters)

## Initial URL Navigation

When a URL like `/Johannes1` is accessed, the following sequence occurs:

### 1. Route Handling

```text
URL: /Johannes1
  ↓
Laravel Router
  ↓
Inertia.js Render
  ↓
Display.vue Component
```

### 2. Display.vue Component Initialization

The Bible reading experience starts with the `Display.vue` component, which serves as the main entry point:

```typescript
// Display.vue
// Props and Store initialization
const props = defineProps<DisplayResponse>(); // Contains initial sections and navigation state
const bibleStore = useBibleStore();
const textSettings = useTextSettingsStore();

// Scroll management setup
const {
    chapterRefs,
    setChapterRef,
    handleScroll,
    scrollToVerse,
    scrollToVerseRange,
    scrollToChapter,
    enableScrollHandling,
} = useScrollManager({
    findMostVisibleChapter: () => {
        // Logic to find the most visible chapter in the viewport
        // Returns the chapter ID of the most visible chapter
    }
});
```

### 3. Component Lifecycle and Initialization

The `onMounted` lifecycle hook triggers the initialization process:

```typescript
// Display.vue
onMounted(async () => {
    // Initialize the store with the initial data
    bibleStore.initialize(props);

    // Set up scroll listener
    window.addEventListener('scroll', handleScroll);

    // Initial scroll to verse if needed
    await initializeDisplay();
});

onUnmounted(() => {
    window.removeEventListener('scroll', handleScroll);
});
```

### 4. Display Initialization Function

The `initializeDisplay` function handles the core initialization:

```typescript
// Display.vue
async function initializeDisplay(): Promise<void> {
    await nextTick();

    try {
        if (props.navigation.scrollToVerseRange) {
            // Handle scrolling to a verse range
            const { verseStart, verseEnd, verseRanges, chapter } =
                props.navigation.scrollToVerseRange;
            await scrollToVerseRange(verseStart, verseEnd, chapter);
        } else if (props.navigation.scrollToVerse) {
            // Handle scrolling to a specific verse
            const { verseStart, chapter } = props.navigation.scrollToVerse;
            await scrollToVerse(verseStart, chapter);
        } else if (props.navigation.scrollToChapter) {
            // Handle scrolling to a specific chapter
            const { book, chapter } = props.navigation.scrollToChapter;
            await scrollToChapter(`${book}${chapter}`);
        } else if (props.navigation.scrollToFrontmatter) {
            // Handle scrolling to book frontmatter
            await scrollToChapter(
                props.navigation.scrollToFrontmatter.book.slug
            );
        } else if (bibleStore.currentBook && props.navigation.currentBook) {
            // Default case: scroll to chapter 1 verse 1 of the current book
            const chapter = 1;
            const verseStart = 1;
            await scrollToVerse(verseStart, chapter);
        }
    } catch (error) {
        console.error('Error during display initialization:', error);
        enableScrollHandling();
    }
}
```

## Scrolling Between Chapters

### 1. Scroll Event Handling

When the user scrolls, the `handleScroll` function from the `useScrollManager` composable is triggered:

```typescript
// useScrollManager.ts
const handleScroll = useDebounce(() => {
    if (!isScrollEnabled.value || !bibleStore.scrollHandlingEnabled) {
        logger.debug('Skipping scroll handler, not enabled');
        return;
    }

    const mostVisibleChapter = options.findMostVisibleChapter();
    if (!mostVisibleChapter) {
        logger.debug('No visible chapter found');
        return;
    }

    logger.debug('Most visible chapter:', mostVisibleChapter);

    const bookSlug = bibleStore.currentBook?.slug || '';

    if (bibleStore.isBookAvailable(bookSlug)) {
        bibleStore.updateCurrentChapter(mostVisibleChapter);
    } else {
        logger.debug('Skipping update for unavailable book:', bookSlug);
    }
}, 200);
```

### 2. Chapter Visibility Calculation

The visibility of chapters is calculated using the `findMostVisibleChapter` function:

```typescript
// Display.vue
findMostVisibleChapter: () => {
    let maxVisibleHeight = 0;
    let mostVisibleChapterId: string | null = null;

    chapterRefs.value.forEach((el, id) => {
        try {
            if (!el) return;

            const rect = el.getBoundingClientRect();
            const visibleHeight =
                Math.min(rect.bottom, window.innerHeight) -
                Math.max(rect.top, 0);

            if (visibleHeight > maxVisibleHeight) {
                maxVisibleHeight = visibleHeight;
                mostVisibleChapterId = id;
            }
        } catch (error) {
            console.error('Error calculating visibility:', error);
        }
    });

    return mostVisibleChapterId;
}
```

### 3. Chapter Update in Bible Store

When a new chapter becomes most visible, the Bible store updates the current chapter:

```typescript
// bibleSectionStore.ts
updateCurrentChapter(chapterId: string) {
    if (!this.scrollHandlingEnabled) {
        logger.debug('Scroll handling disabled, skipping chapter update');
        return;
    }

    logger.debug('Updating current chapter to:', chapterId);

    // Extract book slug and chapter number from the chapter ID
    const match = chapterId.match(/([a-zA-Z]+)(\d+)?/);
    if (!match) {
        logger.error('Invalid chapter ID format:', chapterId);
        return;
    }

    const [, bookSlug, chapterStr] = match;
    const chapter = chapterStr ? parseInt(chapterStr, 10) : 0;

    // Update current book and chapter
    this.currentBook = this.findBookBySlug(bookSlug) || null;
    this.currentChapter = chapter;

    // Check if we need to load more chapters
    this.checkAndLoadAdjacentChapters();
}
```

### 4. Loading Adjacent Chapters

The Bible store checks and loads adjacent chapters as needed:

```typescript
// bibleSectionStore.ts
checkAndLoadAdjacentChapters() {
    if (!this.currentBook || this.currentChapter === null) return;
    
    // Check if we need to load more chapters in either direction
    const visibleChapters = this.visibleChapters;
    const currentIndex = visibleChapters.findIndex(
        section => this.getChapterId(section) === 
            `${this.currentBook!.slug}${this.currentChapter}`
    );
    
    if (currentIndex === -1) return;
    
    // Load more chapters if we're close to the end of the loaded chapters
    if (currentIndex >= visibleChapters.length - 2) {
        this.loadMoreChapters('next');
    } else if (currentIndex <= 1) {
        this.loadMoreChapters('previous');
    }
}
```

## Key Components and Their Responsibilities

### 1. Display.vue

- Main entry point for the Bible reading experience
- Manages the overall layout and chapter rendering
- Initializes the Bible store with data from the server
- Sets up scroll event handling
- Renders chapter components based on visible chapters from the store

### 2. useScrollManager.ts

- Manages scroll-related functionality
- Provides functions for scrolling to specific verses, verse ranges, and chapters
- Handles scroll events and determines the most visible chapter
- Controls scroll handling enabling/disabling during programmatic scrolling
- Manages chapter references for DOM elements

### 3. bibleSectionStore.ts

- Central store for Bible data management
- Maintains the state of loaded chapters, current book, chapter, and verse
- Handles loading of additional chapters as needed
- Updates the current chapter based on scroll position
- Manages book data and availability

## Store Interactions

### 1. Bible Section Store

The `useBibleStore` is the central store for Bible data:

```typescript
// bibleSectionStore.ts
export const useBibleStore = defineStore('bible-section', {
    state: () => ({
        // All loaded chapters (including frontmatter)
        chapters: new Map<string, Section>(),
        // Detailed book data cache
        detailedBooks: new Map<string, Book>(),
        // Current book and chapter information
        currentBook: null as Book | null,
        currentChapter: null as number | null,
        currentVerse: null as number | null,
        // Loading states
        isLoadingNext: false,
        isLoadingPrevious: false,
        // Scroll handling flag
        scrollHandlingEnabled: true,
        // ...other state properties
    }),
    // ...getters and actions
});
```

### 2. Text Settings Store

The `useTextSettingsStore` manages display preferences:

```typescript
// From Display.vue usage
const textSettings = useTextSettingsStore();

// Content style for proper spacing
const contentStyle = computed(() => {
    const widths = {
        'margin-narrow': '44em',
        'margin-normal': '64em',
        'margin-wide': '84em',
    };

    const marginWidth = textSettings.marginSize as keyof typeof widths;
    return {
        'max-width': widths[marginWidth] || widths['margin-normal'],
        margin: '0 auto',
        'padding-bottom': textSettings.showVerseNumbers ? '4rem' : '0',
    };
});
```

### 3. Bible Highlight Store

The `useBibleHighlightStore` manages verse highlighting:

```typescript
// From useScrollManager.ts usage
const highlightStore = useBibleHighlightStore();

// When scrolling to a verse
const success = highlightStore.highlightVerseByReference({
    book: bibleStore.currentBook!.slug,
    chapter: chapterNumber,
    verse: verseNumber,
});
```

## API Interactions

### 1. Loading More Chapters

When the user scrolls near the beginning or end of loaded chapters, the application loads more chapters:

```typescript
// bibleSectionStore.ts
async loadMoreChapters(direction: 'next' | 'previous') {
    if (
        (direction === 'next' && this.isLoadingNext) ||
        (direction === 'previous' && this.isLoadingPrevious)
    ) {
        return;
    }

    // Set loading state
    if (direction === 'next') {
        this.isLoadingNext = true;
    } else {
        this.isLoadingPrevious = true;
    }

    try {
        // Determine reference for API call
        const currentChapterId = this.currentBook && this.currentChapter !== null
            ? `${this.currentBook.slug}${this.currentChapter}`
            : null;
        
        if (!currentChapterId) return;

        // Make API call to load more chapters
        const response = await axios.get(`/api/bible/chapters`, {
            params: {
                reference: currentChapterId,
                direction: direction,
            },
        });

        // Process and add new chapters
        const newSections = response.data.sections;
        newSections.forEach((section: Section) => {
            this.addChapter(section);
        });
    } catch (error) {
        logger.error(`Error loading ${direction} chapters:`, error);
    } finally {
        // Clear loading state
        if (direction === 'next') {
            this.isLoadingNext = false;
        } else {
            this.isLoadingPrevious = false;
        }
    }
}
```

## DOM Update Process for New Chapters

### 1. Chapter Rendering

Chapters are rendered using the `ChapterWrapper` component:

```vue
<!-- Display.vue -->
<template>
    <div class="space-y-12 px-4 py-8 sm:px-6 lg:px-8" :style="contentStyle">
        <template
            v-for="section in visibleChapters"
            :key="getChapterId(section)"
        >
            <template v-if="!hasBookContent(section)">
                <UnavailableBookNotice
                    :book="section.book"
                    :available-books="availableBooks"
                />
            </template>
            <template v-else>
                <ChapterWrapper
                    :ref="(el) => setChapterRef(el, section)"
                    :section="section"
                />
            </template>
        </template>

        <LoadingSpinner v-if="isLoading" />
    </div>
</template>
```

### 2. Chapter Reference Management

The `setChapterRef` function from `useScrollManager` manages chapter DOM references:

```typescript
// useScrollManager.ts
function setChapterRef(
    el: Element | ComponentPublicInstance | null,
    section: Section,
) {
    let htmlElement: HTMLElement | null = null;

    if (el instanceof HTMLElement) {
        htmlElement = el;
    } else if (el && '$el' in el && el.$el instanceof HTMLElement) {
        htmlElement = el.$el;
    }

    if (!htmlElement) return;

    const id = bibleStore.getChapterId(section);
    chapterRefs.value.set(id, htmlElement);
}
```

### 3. Visibility Calculation

The visibility of chapters is calculated to determine which chapter is currently in view:

```typescript
// Display.vue
findMostVisibleChapter: () => {
    let maxVisibleHeight = 0;
    let mostVisibleChapterId: string | null = null;

    chapterRefs.value.forEach((el, id) => {
        try {
            if (!el) return;

            const rect = el.getBoundingClientRect();
            const visibleHeight =
                Math.min(rect.bottom, window.innerHeight) -
                Math.max(rect.top, 0);

            if (visibleHeight > maxVisibleHeight) {
                maxVisibleHeight = visibleHeight;
                mostVisibleChapterId = id;
            }
        } catch (error) {
            console.error('Error calculating visibility:', error);
        }
    });

    return mostVisibleChapterId;
}
```

## Potential Issues and Debugging

### 1. Scroll Handling Issues

If scroll handling is not working correctly:

- Check if `scrollHandlingEnabled` is set to `true` in the Bible store
- Verify that the scroll event listener is properly attached in `Display.vue`
- Check for errors in the console related to visibility calculation

### 2. Chapter Loading Issues

If chapters are not loading correctly:

- Check the network requests to `/api/bible/chapters` for errors
- Verify that the current book and chapter are correctly set in the Bible store
- Check if `checkAndLoadAdjacentChapters` is being called when scrolling

### 3. Verse Highlighting Issues

If verse highlighting is not working:

- Verify that the verse elements have the correct data attributes
- Check if the `highlightVerseByReference` function is being called
- Inspect the DOM to ensure verse elements have the expected structure
