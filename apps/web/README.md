# Bible Reader Application

A Laravel 11 application with Inertia.js (v2), Vue 3, and Pinia for reading and studying the Bible.

## Prerequisites

- PHP 8.2+
- Composer 2+
- Node.js 18+
- MySQL 8.0+
- Meilisearch 1.12+
- Yarn 4.5+ (or any other Node.js package manager)

## Installation of Prerequisites

```bash
# 1. Prerequsites Setup
npm install -g yarn
corepack enable
corepack prepare yarn@4.6.0 --activate
yarn set version 4.6.0
```

## Quick Setup Commands
```bash
# 1. Initial Setup
git clone <repository-url>
cd <project-directory>
composer install
yarn install
cp .env.example .env
php artisan key:generate

# 2. Database
php artisan migrate

# 3. Meilisearch Setup
php artisan meilisearch:setup
php artisan meilisearch:optimize-german

# Clear and rebuild indexes
php artisan scout:flush "App\Models\Book"
php artisan scout:flush "App\Models\Verse"
php artisan scout:flush "App\Models\Word"
php artisan scout:flush "App\Models\Footnote"
php artisan scout:flush "App\Models\SearchableText"

php artisan scout:import "App\Models\Book"
php artisan scout:import "App\Models\Verse"
php artisan scout:import "App\Models\Word"
php artisan scout:import "App\Models\Footnote"
php artisan scout:import "App\Models\SearchableText"

# 4. Start Development Servers
php artisan serve
npm run dev
meilisearch --db-path=/Applications/MAMP/htdocs/meilisearch
```

## Development Commands
```bash
php artisan bible:parse-usx bibletext/Johannes/Johannes.usx Johannes -v (or -vv or -vvv)
```

### Database Management
```bash
php artisan migrate:fresh        # Reset database
php artisan migrate:rollback     # Rollback last migration
php artisan migrate:fresh --seed # Reset and seed database
```

### Cache Management
```bash
php artisan optimize:clear    # Clear all caches
php artisan cache:clear      # Clear application cache
php artisan route:clear      # Clear route cache
php artisan config:clear     # Clear config cache
php artisan view:clear       # Clear view cache
```

### Search Management
```bash
# Rebuild specific index
php artisan scout:flush "App\Models\ModelName"
php artisan scout:import "App\Models\ModelName"

# Check Meilisearch
curl http://127.0.0.1:7700/health
curl http://127.0.0.1:7700/stats
```

### Testing & Code Style
```bash
php artisan test              # Run all tests
./vendor/bin/php-cs-fixer fix # Run PHP CS Fixer
npm run lint                  # Run ESLint
npm run lint:fix             # Run ESLint with auto-fix
```

### Production Build
```bash
npm run build
php artisan optimize
php artisan model:cache  # Optional
```

## Import Scripts

### xml2usx.py

A script to convert InDesign XML exports to USX (Unified Scripture XML) format. Located in `scripts/import/xml2usx.py`.

#### Usage
```bash
python xml2usx.py [-h] [-v] [--verse-limit VERSE_LIMIT] source_xml_path book_name
```

#### Arguments
- `source_xml_path`: Path to the source XML file (output will be in the same folder)
- `book_name`: Name of the book (e.g., "Johannes", "1. Mose")

#### Optional Arguments
- `-h, --help`: Show help message
- `-v, --verbose`: Enable verbose output
- `--verse-limit VERSE_LIMIT`: Stop processing after this verse number (useful for testing)

#### Features
- Converts InDesign XML character styles to USX format
- Handles footnotes, references, and text variants
- Supports all books of the Old and New Testament
- Preserves text formatting (emphasis, quotes, etc.)
- Generates proper USX 3.0 compliant XML
- Includes book metadata and alternate names if provided

#### Character Style Mapping
The script maps InDesign character styles to USX styles:
- Normal text → `no`
- Emphasis → `em`
- Old Testament quotes → `xot`
- Text variants → `va`
- Footnote markers → `fm`
- Chapter numbers → `c`
- Verse numbers → `v`
- Words not in original text → `add`

#### Example
```bash
python xml2usx.py input/johannes.xml "Johannes" -v
```

## Troubleshooting

If search isn't working:
1. Verify Meilisearch is running: `curl http://127.0.0.1:7700/health`
2. Check .env configuration
3. Rebuild indexes
4. Clear Laravel caches: `php artisan optimize:clear`
5. Check Meilisearch logs: `tail -f /var/log/meilisearch.log`

## Version Management

The application uses a centralized version management system that makes the version number available throughout the frontend and backend.

### Version Source

The canonical source of the version is the `version` field in `package.json`. This version follows [Semantic Versioning](https://semver.org/) (MAJOR.MINOR.PATCH).

### How It Works

1. The version from `package.json` is exported to:
   - `resources/js/version.ts` (for TypeScript imports)
   - `.env.VERSION` (for build process)

2. The export happens automatically when running:
   ```bash
   # Export version manually
   yarn version:export
   
   # Also happens automatically during build/dev
   yarn dev
   yarn build
   ```

3. The version is available in your Vue components through:
   - Global property: `$version` (template: `{{ $version }}`)
   - Composable: `useAppVersion()` (script: `const { version, formattedVersion } = useAppVersion()`)
   - Inertia shared data: `$page.props.appVersion`

### Changelog Management

A command is provided to create/update changelog entries based on the current version:

```bash
# Create a new changelog entry
php artisan changelog:new

# Create with a specific message
php artisan changelog:new --message="Added new feature X"
```

This will:
1. Read the current version from `.env.VERSION` or `package.json`
2. Create `CHANGELOG.md` if it doesn't exist
3. Add a new entry for the current version with the provided message

### Updating the Version

To update the application version:

1. Update the version in `package.json`
2. Run `yarn version:export` to propagate the change
3. Run `php artisan changelog:new` to create a changelog entry
4. Commit the changes

## Documentation Links
- [Laravel](https://laravel.com/docs)
- [Inertia.js](https://inertiajs.com/)
- [Vue 3](https://vuejs.org/)
- [Pinia](https://pinia.vuejs.org/)
- [Meilisearch](https://docs.meilisearch.com/)
