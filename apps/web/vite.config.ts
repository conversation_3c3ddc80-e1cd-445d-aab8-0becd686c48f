import { sentryVitePlugin } from '@sentry/vite-plugin';
import vue from '@vitejs/plugin-vue';
import laravel from 'laravel-vite-plugin';
import { resolve } from 'path';
import { defineConfig, loadEnv } from 'vite';
import fs from 'fs';

const plugins = [
    laravel({
        input: ['resources/css/app.css', 'resources/js/app.ts'],
        refresh: true,
    }),
    vue({
        script: {
            defineModel: true,
            propsDestructure: true,
        },
    }),
];

// Skip Sentry in development to avoid authentication errors
if (process.env.VITE_APP_ENV === 'production') {
    plugins.push(
        sentryVitePlugin({
            org: 'ebtc-de',
            project: 'esra-bibel',
            // Sentry auth token should be different from DSN
            // Get it from Sentry > Settings > Auth Tokens
            authToken: process.env.VITE_SENTRY_AUTH_TOKEN,
            telemetry: false,
        }),
    );
}

// Try to load version from .env.VERSION or fallback to package.json
function getAppVersion() {
    try {
        // First try to read from .env.VERSION
        if (fs.existsSync('.env.VERSION')) {
            const envContent = fs.readFileSync('.env.VERSION', 'utf8');
            const versionMatch = envContent.match(/VERSION=(.+)/i);
            if (versionMatch && versionMatch[1]) {
                return versionMatch[1];
            }
        }

        // Fallback to package.json
        const packageJson = JSON.parse(fs.readFileSync('./package.json', 'utf8'));
        return packageJson.version || '0.0.0';
    } catch (error) {
        console.warn('Failed to read version, using fallback:', error);
        return '0.0.0';
    }
}

export default defineConfig(({ mode }) => {
    // Load env variables
    const env = loadEnv(mode, process.cwd(), '');
    const appVersion = getAppVersion();
    
    // Expose version to the client
    process.env.VITE_APP_VERSION = appVersion;

    return {
        build: {
            target: 'esnext',
            sourcemap: true,
            chunkSizeWarningLimit: 600,
            rollupOptions: {
                output: {
                    manualChunks: {
                        vendor: ['@inertiajs/vue3', 'vue', 'pinia', '@sentry/vue'],
                        components: [
                            './resources/js/Components/Search/BibleSearch.vue',
                        ],
                    },
                },
            },
        },
        define: {
            __APP_VERSION__: JSON.stringify(appVersion),
        },
        plugins,
        resolve: {
            alias: {
                '@': resolve(__dirname, './resources/js'),
                '@esbo/types': resolve(__dirname, '../../libs/types/src'),
            },
        },
    };
});
