name: Cross-Browser Tests

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'apps/web/**'
      - '.github/workflows/browser-tests.yml'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'apps/web/**'
      - '.github/workflows/browser-tests.yml'

jobs:
  browser-tests:
    runs-on: ubuntu-latest
    
    strategy:
      fail-fast: false
      matrix:
        browser: [chrome, firefox]
        php-version: [8.3]
        node-version: [20]

    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: secret
          MYSQL_DATABASE: esra_bibel_test
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3

      redis:
        image: redis:alpine
        ports:
          - 6379:6379
        options: --health-cmd="redis-cli ping" --health-interval=10s --health-timeout=5s --health-retries=3

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php-version }}
          extensions: dom, curl, libxml, mbstring, zip, pcntl, pdo, sqlite, pdo_sqlite, bcmath, soap, intl, gd, exif, iconv
          coverage: none

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}

      - name: Enable Corepack
        run: corepack enable

      - name: Get Composer Cache Directory
        id: composer-cache
        run: echo "dir=$(composer config cache-files-dir)" >> $GITHUB_OUTPUT

      - name: Cache Composer dependencies
        uses: actions/cache@v3
        with:
          path: ${{ steps.composer-cache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
          restore-keys: ${{ runner.os }}-composer-

      - name: Get yarn cache directory path
        id: yarn-cache-dir-path
        run: echo "dir=$(yarn config get cacheFolder)" >> $GITHUB_OUTPUT

      - name: Cache Yarn dependencies
        uses: actions/cache@v3
        with:
          path: ${{ steps.yarn-cache-dir-path.outputs.dir }}
          key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
          restore-keys: ${{ runner.os }}-yarn-

      - name: Install PHP dependencies
        working-directory: ./apps/web
        run: composer install --no-interaction --prefer-dist --optimize-autoloader

      - name: Install Node.js dependencies
        run: yarn install --immutable

      - name: Build assets
        working-directory: ./apps/web
        run: yarn build

      - name: Setup Chrome
        if: matrix.browser == 'chrome'
        uses: browser-actions/setup-chrome@latest
        with:
          chrome-version: stable

      - name: Setup Firefox
        if: matrix.browser == 'firefox'
        uses: browser-actions/setup-firefox@latest
        with:
          firefox-version: latest

      - name: Setup ChromeDriver
        if: matrix.browser == 'chrome'
        uses: nanasess/setup-chromedriver@v2

      - name: Setup GeckoDriver
        if: matrix.browser == 'firefox'
        uses: browser-actions/setup-geckodriver@latest

      - name: Create environment file
        working-directory: ./apps/web
        run: |
          cp .env.example .env
          echo "APP_ENV=testing" >> .env
          echo "DB_HOST=127.0.0.1" >> .env
          echo "DB_PORT=3306" >> .env
          echo "DB_DATABASE=esra_bibel_test" >> .env
          echo "DB_USERNAME=root" >> .env
          echo "DB_PASSWORD=secret" >> .env
          echo "REDIS_HOST=127.0.0.1" >> .env
          echo "REDIS_PORT=6379" >> .env
          echo "DUSK_HEADLESS=true" >> .env
          echo "DUSK_DEFAULT_BROWSER=${{ matrix.browser }}" >> .env

      - name: Generate application key
        working-directory: ./apps/web
        run: php artisan key:generate

      - name: Clear config cache
        working-directory: ./apps/web
        run: php artisan config:clear

      - name: Run database migrations
        working-directory: ./apps/web
        run: php artisan migrate:fresh --force

      - name: Seed test data
        working-directory: ./apps/web
        run: |
          php artisan bible:parse Markus --output --env=testing
          php artisan bible:parse Lukas --output --env=testing

      - name: Start Chrome Driver
        if: matrix.browser == 'chrome'
        run: |
          chromedriver --port=9515 &
          sleep 3

      - name: Start Gecko Driver
        if: matrix.browser == 'firefox'
        run: |
          geckodriver --port=4444 &
          sleep 3

      - name: Start Laravel server
        working-directory: ./apps/web
        run: |
          php artisan serve --host=127.0.0.1 --port=8000 &
          sleep 5

      - name: Run Browser Tests
        working-directory: ./apps/web
        run: |
          export DUSK_HEADLESS=true
          export DUSK_DEFAULT_BROWSER=${{ matrix.browser }}
          php artisan dusk --without-tty
        env:
          APP_URL: http://127.0.0.1:8000

      - name: Upload Screenshots
        uses: actions/upload-artifact@v3
        if: failure()
        with:
          name: screenshots-${{ matrix.browser }}-${{ github.run_id }}
          path: apps/web/tests/Browser/screenshots/
          retention-days: 7

      - name: Upload Console Logs
        uses: actions/upload-artifact@v3
        if: failure()
        with:
          name: console-logs-${{ matrix.browser }}-${{ github.run_id }}
          path: apps/web/tests/Browser/console/
          retention-days: 7

      - name: Upload Source Code
        uses: actions/upload-artifact@v3
        if: failure()
        with:
          name: source-${{ matrix.browser }}-${{ github.run_id }}
          path: apps/web/tests/Browser/source/
          retention-days: 7

  # Parallel test execution job
  parallel-browser-tests:
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: secret
          MYSQL_DATABASE: esra_bibel_test
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3

      redis:
        image: redis:alpine
        ports:
          - 6379:6379

      selenium-hub:
        image: selenium/hub:4.15.0
        ports:
          - 4444:4444

      chrome:
        image: selenium/node-chrome:4.15.0
        env:
          HUB_HOST: selenium-hub
          HUB_PORT: 4444

      firefox:
        image: selenium/node-firefox:4.15.0
        env:
          HUB_HOST: selenium-hub
          HUB_PORT: 4444

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: 8.3
          extensions: dom, curl, libxml, mbstring, zip, pcntl, pdo, sqlite, pdo_sqlite, bcmath, soap, intl, gd, exif, iconv

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Install dependencies and build
        run: |
          cd apps/web
          composer install --no-interaction --prefer-dist --optimize-autoloader
          cd ../..
          yarn install --immutable
          cd apps/web
          yarn build

      - name: Setup environment and database
        working-directory: ./apps/web
        run: |
          cp .env.example .env
          echo "APP_ENV=testing" >> .env
          echo "DB_HOST=127.0.0.1" >> .env
          echo "DB_DATABASE=esra_bibel_test" >> .env
          echo "DB_USERNAME=root" >> .env
          echo "DB_PASSWORD=secret" >> .env
          echo "DUSK_DRIVER_URL=http://localhost:4444/wd/hub" >> .env
          php artisan key:generate
          php artisan migrate:fresh --force
          php artisan bible:parse Markus --output --env=testing
          php artisan bible:parse Lukas --output --env=testing

      - name: Start Laravel server
        working-directory: ./apps/web
        run: php artisan serve --host=127.0.0.1 --port=8000 &

      - name: Wait for services
        run: sleep 10

      - name: Run parallel browser tests
        working-directory: ./apps/web
        run: bash scripts/run-browser-tests.sh -b chrome,firefox --parallel --headless -e ci
