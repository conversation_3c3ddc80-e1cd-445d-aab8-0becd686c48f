version: '3.8'

services:
  # Selenium Hub for coordinating browser tests
  selenium-hub:
    image: selenium/hub:4.15.0
    container_name: selenium-hub
    ports:
      - "4444:4444"
      - "4442:4442"
      - "4443:4443"
    environment:
      - GRID_MAX_SESSION=16
      - GRID_BROWSER_TIMEOUT=300
      - GRID_TIMEOUT=300
    networks:
      - browser-testing

  # Chrome browser node
  chrome:
    image: selenium/node-chrome:4.15.0
    container_name: selenium-chrome
    shm_size: 2gb
    depends_on:
      - selenium-hub
    environment:
      - HUB_HOST=selenium-hub
      - HUB_PORT=4444
      - NODE_MAX_INSTANCES=4
      - NODE_MAX_SESSION=4
    volumes:
      - /dev/shm:/dev/shm
    networks:
      - browser-testing

  # Firefox browser node
  firefox:
    image: selenium/node-firefox:4.15.0
    container_name: selenium-firefox
    shm_size: 2gb
    depends_on:
      - selenium-hub
    environment:
      - HUB_HOST=selenium-hub
      - HUB_PORT=4444
      - NODE_MAX_INSTANCES=4
      - NODE_MAX_SESSION=4
    volumes:
      - /dev/shm:/dev/shm
    networks:
      - browser-testing

  # Edge browser node (optional)
  edge:
    image: selenium/node-edge:4.15.0
    container_name: selenium-edge
    shm_size: 2gb
    depends_on:
      - selenium-hub
    environment:
      - HUB_HOST=selenium-hub
      - HUB_PORT=4444
      - NODE_MAX_INSTANCES=2
      - NODE_MAX_SESSION=2
    volumes:
      - /dev/shm:/dev/shm
    networks:
      - browser-testing

  # VNC viewer for debugging (optional)
  chrome-debug:
    image: selenium/node-chrome:4.15.0-20231025
    container_name: selenium-chrome-debug
    shm_size: 2gb
    ports:
      - "5900:5900"
    depends_on:
      - selenium-hub
    environment:
      - HUB_HOST=selenium-hub
      - HUB_PORT=4444
      - NODE_MAX_INSTANCES=1
      - NODE_MAX_SESSION=1
      - START_XVFB=false
    volumes:
      - /dev/shm:/dev/shm
    networks:
      - browser-testing

  firefox-debug:
    image: selenium/node-firefox:4.15.0-20231025
    container_name: selenium-firefox-debug
    shm_size: 2gb
    ports:
      - "5901:5900"
    depends_on:
      - selenium-hub
    environment:
      - HUB_HOST=selenium-hub
      - HUB_PORT=4444
      - NODE_MAX_INSTANCES=1
      - NODE_MAX_SESSION=1
      - START_XVFB=false
    volumes:
      - /dev/shm:/dev/shm
    networks:
      - browser-testing

  # Test application container
  app:
    build:
      context: ../../
      dockerfile: docker/browser-testing/Dockerfile
    container_name: browser-test-app
    ports:
      - "8000:8000"
    environment:
      - APP_ENV=testing
      - DB_HOST=mysql
      - REDIS_HOST=redis
      - SELENIUM_HUB_URL=http://selenium-hub:4444/wd/hub
    depends_on:
      - mysql
      - redis
      - selenium-hub
    volumes:
      - ../../:/var/www/html
    networks:
      - browser-testing
    command: php artisan serve --host=0.0.0.0 --port=8000

  # MySQL for testing
  mysql:
    image: mysql:8.0
    container_name: browser-test-mysql
    environment:
      MYSQL_ROOT_PASSWORD: secret
      MYSQL_DATABASE: esra_bibel_test
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    networks:
      - browser-testing

  # Redis for testing
  redis:
    image: redis:alpine
    container_name: browser-test-redis
    ports:
      - "6379:6379"
    networks:
      - browser-testing

  # Test runner container
  test-runner:
    build:
      context: ../../
      dockerfile: docker/browser-testing/Dockerfile
    container_name: browser-test-runner
    depends_on:
      - app
      - selenium-hub
      - chrome
      - firefox
    environment:
      - APP_ENV=testing
      - DB_HOST=mysql
      - REDIS_HOST=redis
      - DUSK_DRIVER_URL=http://selenium-hub:4444/wd/hub
      - APP_URL=http://app:8000
    volumes:
      - ../../:/var/www/html
      - ./test-results:/var/www/html/test-results
    networks:
      - browser-testing
    working_dir: /var/www/html
    command: tail -f /dev/null  # Keep container running

networks:
  browser-testing:
    driver: bridge

volumes:
  mysql_data:
