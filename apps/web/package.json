{"name": "@esbo/web", "private": true, "version": "1.0.0-beta.12", "type": "module", "scripts": {"build": "node scripts/export-version.js && vite build", "dev": "node scripts/export-version.js && vite & php artisan serve", "lint": "eslint resources/js --ext .js,.ts,.vue --fix", "test": "vitest run", "test:coverage": "vitest run --coverage", "test:watch": "vitest", "test:browser": "bash scripts/run-browser-tests.sh", "test:browser:chrome": "bash scripts/run-browser-tests.sh -b chrome", "test:browser:firefox": "bash scripts/run-browser-tests.sh -b firefox", "test:browser:safari": "bash scripts/run-browser-tests.sh -b safari", "test:browser:all": "bash scripts/run-browser-tests.sh -b chrome,firefox,safari", "test:browser:headless": "bash scripts/run-browser-tests.sh --headless", "test:browser:gui": "bash scripts/run-browser-tests.sh --no-headless", "test:browser:parallel": "bash scripts/run-browser-tests.sh --parallel", "test:browser:docker": "bash scripts/run-browser-tests.sh --docker", "test:browser:ci": "bash scripts/run-browser-tests.sh -e ci -b chrome,firefox --headless", "setup:browser-drivers": "bash scripts/setup-browser-drivers.sh", "data:refresh": "php artisan db:refresh && scripts/import/process_bible_books.sh --force-parse", "clean": "rimraf .turbo node_modules public/build", "docs:api": "php artisan scribe:generate && mkdir -p ../../docs/static/api-docs && cp -R public/docs/* ../../docs/static/api-docs/ && mkdir -p ../../docs/web/api", "import:all": "scripts/import/process_bible_books.sh", "import:force": "scripts/import/process_bible_books.sh --force-parse", "import:book": "bash scripts/import/process_bible_books.sh --force-parse --book", "version:export": "node scripts/export-version.js", "changelog:new": "php artisan changelog:new"}, "dependencies": {"@esbo/enums": "workspace:*", "@esbo/types": "workspace:*", "@floating-ui/vue": "^1.1.7", "@inertiajs/vue3": "^2.0.5", "@sentry/vue": "^9.12.0", "@vueuse/core": "^13.5.0", "abort-controller": "^3.0.0", "lucide-vue-next": "^0.487.0", "pinia": "^3.0.1", "vue": "^3.5.13"}, "devDependencies": {"@pinia/testing": "^1.0.0", "@rushstack/eslint-patch": "^1.8.0", "@sentry/vite-plugin": "^3.3.1", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.1.0", "@tailwindcss/typography": "^0.5.16", "@testing-library/jest-dom": "^6.6.3", "@types/node": "^22.13.10", "@typescript-eslint/eslint-plugin": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "@vitejs/plugin-vue": "^5.2.1", "@vue/compiler-sfc": "^3.5.13", "@vue/eslint-config-prettier": "^10.0.0", "@vue/eslint-config-typescript": "^14.5.0", "@vue/test-utils": "^2.4.6", "autoprefixer": "^10.4.21", "eslint": "^9.10.0", "eslint-plugin-vue": "^9.33.0", "jsdom": "^25.0.1", "laravel-vite-plugin": "^1.2.0", "postcss": "^8.5.3", "prettier": "^3.5.3", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-tailwindcss": "^0.6.11", "rimraf": "^5.0.10", "tailwind-scrollbar": "^4.0.1", "tailwindcss": "^4.1.0", "typescript": "^5.8.2", "vite": "^6.2.1", "vitest": "^1.6.1", "vue-tsc": "^2.2.8"}}