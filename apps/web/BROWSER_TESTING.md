# Cross-Browser Testing Setup

This document describes the comprehensive cross-browser testing setup for the Esra Bibel web application, providing future-proof acceptance testing across Chrome, Firefox, and Safari.

## Overview

Our browser testing strategy includes:
- **Multi-browser support**: Chrome, Firefox, Safari (and Edge)
- **Flexible execution**: Local, Docker, and CI/CD environments
- **Parallel testing**: Run tests across multiple browsers simultaneously
- **Cloud integration**: Ready for BrowserStack, Sauce Labs, LambdaTest
- **Comprehensive reporting**: Console, HTML, and JUnit formats

## Quick Start

### 1. Setup Browser Drivers

```bash
# Install browser drivers automatically
npm run setup:browser-drivers

# Or manually install drivers
./scripts/setup-browser-drivers.sh
```

### 2. Run Basic Tests

```bash
# Run tests in default browsers (Chrome + Firefox)
npm run test:browser

# Run in specific browser
npm run test:browser:chrome
npm run test:browser:firefox
npm run test:browser:safari

# Run across all browsers
npm run test:browser:all
```

### 3. Advanced Usage

```bash
# Run with <PERSON><PERSON> (non-headless)
npm run test:browser:gui

# Run in parallel
npm run test:browser:parallel

# Run in Docker
npm run test:browser:docker

# Run for CI/CD
npm run test:browser:ci
```

## Architecture

### Core Components

1. **DuskMultiBrowserTestCase**: Extended base class supporting multiple browsers
2. **CrossBrowserTesting Trait**: Utilities for cross-browser test execution
3. **Browser Configuration**: Centralized browser settings and capabilities
4. **Test Runner Scripts**: Automated execution across environments

### File Structure

```
apps/web/
├── tests/
│   ├── Browser/
│   │   ├── CrossBrowserExampleTest.php
│   │   └── ...
│   ├── Traits/
│   │   └── CrossBrowserTesting.php
│   ├── DuskMultiBrowserTestCase.php
│   └── DuskTestCase.php (existing)
├── config/
│   └── browser-testing.php
├── scripts/
│   ├── setup-browser-drivers.sh
│   └── run-browser-tests.sh
├── docker/
│   └── browser-testing/
│       ├── docker-compose.yml
│       └── Dockerfile
└── .github/
    └── workflows/
        └── browser-tests.yml
```

## Browser Support

### Supported Browsers

| Browser | Local | Docker | CI/CD | Cloud |
|---------|-------|--------|-------|-------|
| Chrome  | ✅    | ✅     | ✅    | ✅    |
| Firefox | ✅    | ✅     | ✅    | ✅    |
| Safari  | ✅*   | ❌     | ❌    | ✅    |
| Edge    | ✅    | ✅     | ✅    | ✅    |

*Safari only available on macOS

### Browser Configuration

Each browser can be configured in `config/browser-testing.php`:

```php
'browsers' => [
    'chrome' => [
        'enabled' => env('DUSK_CHROME_ENABLED', true),
        'driver_url' => env('DUSK_CHROME_DRIVER_URL', 'http://localhost:9515'),
        'options' => [...],
    ],
    // ... other browsers
],
```

## Writing Cross-Browser Tests

### Basic Cross-Browser Test

```php
<?php

use Tests\DuskMultiBrowserTestCase;
use Tests\Traits\CrossBrowserTesting;

class MyTest extends DuskMultiBrowserTestCase
{
    use CrossBrowserTesting;

    public function test_feature_works_across_browsers()
    {
        $this->runAcrossAllBrowsers(function (Browser $browser) {
            $browser->visit('/page')
                    ->assertSee('Expected Content');
        });
    }
}
```

### Browser-Specific Tests

```php
public function test_chrome_specific_feature()
{
    $this->onlyInBrowsers(['chrome'], function (Browser $browser) {
        // Chrome-specific test logic
    });
}

public function test_skip_safari()
{
    $this->skipInBrowsers(['safari'], function (Browser $browser) {
        // Test that doesn't work well in Safari
    });
}
```

### Responsive Testing

```php
public function test_responsive_design()
{
    $viewports = $this->getCommonViewports();
    
    $this->testResponsiveAcrossBrowsers($viewports, function (Browser $browser, $viewport) {
        $browser->visit('/page');
        
        if ($viewport['width'] <= 768) {
            // Mobile assertions
        } else {
            // Desktop assertions
        }
    });
}
```

## Environment Configuration

### Local Development

```bash
# .env
DUSK_HEADLESS=false
DUSK_CHROME_ENABLED=true
DUSK_FIREFOX_ENABLED=true
DUSK_SAFARI_ENABLED=true
```

### CI/CD Environment

```bash
# .env
DUSK_HEADLESS=true
DUSK_CHROME_ENABLED=true
DUSK_FIREFOX_ENABLED=true
DUSK_SAFARI_ENABLED=false
```

### Docker Environment

```bash
# Use Docker Compose for isolated testing
docker-compose -f docker/browser-testing/docker-compose.yml up -d
```

## Cloud Testing Integration

### BrowserStack Setup

```bash
# .env
BROWSERSTACK_ENABLED=true
BROWSERSTACK_USERNAME=your_username
BROWSERSTACK_ACCESS_KEY=your_access_key
```

### Sauce Labs Setup

```bash
# .env
SAUCELABS_ENABLED=true
SAUCELABS_USERNAME=your_username
SAUCELABS_ACCESS_KEY=your_access_key
```

## Test Execution Options

### Command Line Options

```bash
./scripts/run-browser-tests.sh [OPTIONS]

Options:
  -b, --browsers BROWSERS     Comma-separated list of browsers
  -h, --headless             Run in headless mode
  --no-headless              Run with GUI
  -p, --parallel             Run tests in parallel
  -e, --environment ENV      Test environment (local,ci,testing)
  -f, --filter FILTER        Filter tests by name
  -v, --verbose              Verbose output
  -d, --docker               Run in Docker
  -r, --report FORMAT        Report format (console,html,junit)
```

### NPM Scripts

```bash
npm run test:browser                # Default: Chrome + Firefox
npm run test:browser:chrome         # Chrome only
npm run test:browser:firefox        # Firefox only
npm run test:browser:safari         # Safari only (macOS)
npm run test:browser:all            # All available browsers
npm run test:browser:headless       # Headless mode
npm run test:browser:gui            # With GUI
npm run test:browser:parallel       # Parallel execution
npm run test:browser:docker         # Docker environment
npm run test:browser:ci             # CI/CD optimized
```

## Troubleshooting

### Common Issues

1. **Driver not found**: Run `npm run setup:browser-drivers`
2. **Port conflicts**: Check if drivers are already running
3. **Safari permissions**: Run `sudo safaridriver --enable`
4. **Docker issues**: Ensure Docker is running and ports are available

### Debug Mode

```bash
# Run with verbose output and GUI
./scripts/run-browser-tests.sh -v --no-headless -b chrome

# Take screenshots on failure
export DUSK_SCREENSHOTS_ON_FAILURE=true
```

### Performance Optimization

1. **Parallel execution**: Use `-p` flag for faster test runs
2. **Browser selection**: Test only necessary browsers during development
3. **Docker**: Use containers for consistent, isolated testing
4. **Cloud services**: Offload testing to cloud providers for scale

## CI/CD Integration

### GitHub Actions

The included workflow (`.github/workflows/browser-tests.yml`) provides:
- Matrix testing across browsers
- Parallel execution
- Artifact collection (screenshots, logs)
- Caching for faster builds

### GitLab CI

Update your `.gitlab-ci.yml`:

```yaml
browser-tests:
  stage: test
  script:
    - cd apps/web
    - bash scripts/run-browser-tests.sh -e ci -b chrome,firefox
  artifacts:
    when: on_failure
    paths:
      - apps/web/tests/Browser/screenshots/
      - apps/web/tests/Browser/console/
```

## Best Practices

1. **Test Design**: Write browser-agnostic tests when possible
2. **Error Handling**: Use try-catch for browser-specific behaviors
3. **Timeouts**: Adjust timeouts based on browser performance
4. **Screenshots**: Capture screenshots on failures for debugging
5. **Parallel Testing**: Use parallel execution for faster feedback
6. **Cloud Testing**: Leverage cloud services for comprehensive coverage

## Future Enhancements

- **Visual regression testing**: Compare screenshots across browsers
- **Performance testing**: Measure load times across browsers
- **Accessibility testing**: Automated a11y checks
- **Mobile browser testing**: iOS Safari, Chrome Mobile
- **API mocking**: Consistent test data across environments
