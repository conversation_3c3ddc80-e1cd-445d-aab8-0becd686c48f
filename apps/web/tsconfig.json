{"compilerOptions": {"allowJs": true, "module": "ESNext", "lib": ["ESNext", "DOM", "DOM.Iterable"], "moduleResolution": "bundler", "jsx": "preserve", "strict": true, "isolatedModules": true, "target": "ESNext", "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "skipLibCheck": true, "baseUrl": ".", "sourceMap": true, "paths": {"@/*": ["./resources/js/*"], "@/Components/*": ["./resources/js/Components/*"], "@/Layouts/*": ["./resources/js/Layouts/*"], "@/Pages/*": ["./resources/js/Pages/*"], "@/types/*": ["./resources/js/types/*"], "~/*": ["./resources/*"], "ziggy-js": ["./vendor/tightenco/ziggy"]}, "types": ["vite/client", "vue", "@types/node", "vitest/globals", "@pinia/testing"]}, "include": ["resources/js/**/*.ts", "resources/js/**/*.d.ts", "resources/js/**/*.tsx", "resources/js/**/*.vue", "resources/js/types/shims-vue.d.ts", "resources/js/types/components.d.ts", "resources/js/types/vite-env.d.ts"], "exclude": ["node_modules", "public"], "references": [{"path": "./tsconfig.node.json"}]}