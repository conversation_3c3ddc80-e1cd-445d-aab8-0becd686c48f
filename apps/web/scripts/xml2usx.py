from lxml import etree
import re
from typing import Optional, Dict, Any
import os
import sys
import json
from pathlib import Path
import time
import argparse
from html import unescape

class USXTransformer:
    def __init__(self, book_common_name: str, book_metadata: Optional[Dict[str, Any]] = None, verbose: bool = False, verse_limit: Optional[int] = None):
        self.book_common_name = book_common_name
        self.book_metadata = book_metadata
        self.footnote_letters = 'abcdefghijklmnopqrstuvwxyz'
        self.footnote_count = 0
        self.variant_counts = {}  # Counter per variant type
        self.variant_stack = []  # Stack to track nested variant IDs
        self.current_xot = None  # Track current xot variant
        self.pending_footnote = None
        self.pending_variant = None
        self.verbose = verbose
        self.verse_limit = verse_limit
        self.verse_count = 0  # For counting verses processed
        self.current_chapter = "1"  # Current chapter number for references
        self.current_verse_number = "1"  # Current verse number for references
        self.book_name = None  # Will be set when processing the book name
        # Statistics
        self.stats = {
            'verses_processed': 0,
            'footnotes_processed': 0,
            'references_processed': 0
        }
        self.processed_quotes = set()  # Track processed quotes globally
        self.current_quote = None  # Track the current quote being processed
        # Mapping of XML character styles to USX char styles
        self.char_style_map = {
            'CharacterStyle/$ID/[No character style]': 'no',     # normal text
            'CharacterStyle/Betonung': 'em',                     # emphasis
            'CharacterStyle/Direktes Zitat aus dem AT od. Paraphrase': 'xot',  # Old Testament quote
            'CharacterStyle/Eckige Klammer oben': 'va',          # square bracket above (keep as normal) => Text Variante
            'CharacterStyle/Fußnotenverweis': 'fm',              # footnote marker
            'CharacterStyle/Fußnotenzahl im Bibeltext': 'fm',    # footnote number in bible text
            'CharacterStyle/Fußnotenzahl vor Variante': 'fm',    # footnote number before variant
            'CharacterStyle/Kapitelnummer': 'c',                 # chapter number
            'CharacterStyle/Kursiv in den Fußnoten': 'it',       # italic in footnotes
            'CharacterStyle/Versnummer': 'v',                    # verse number
            'CharacterStyle/Wort nicht im Grundtext': 'add',     # word not in original text
            'CharacterStyle/Wort nicht im Grundtext [Fn.]': 'add'  # word not in original text (in footnote)
        }
        self.book_codes = {
            # Old Testament
            "1. Mose": "GEN", "2. Mose": "EXO", "3. Mose": "LEV", "4. Mose": "NUM", "5. Mose": "DEU",
            "Josua": "JOS", "Richter": "JDG", "Ruth": "RUT",
            "1. Samuel": "1SA", "2. Samuel": "2SA",
            "1. Könige": "1KI", "2. Könige": "2KI",
            "1. Chronik": "1CH", "2. Chronik": "2CH",
            "Esra": "EZR", "Nehemia": "NEH", "Esther": "EST",
            "Hiob": "JOB", "Psalmen": "PSA", "Sprüche": "PRO", "Prediger": "ECC",
            "Hohelied": "SNG", "Jesaja": "ISA", "Jeremia": "JER",
            "Klagelieder": "LAM", "Hesekiel": "EZK", "Daniel": "DAN",
            "Hosea": "HOS", "Joel": "JOL", "Amos": "AMO",
            "Obadja": "OBA", "Jona": "JON", "Micha": "MIC",
            "Nahum": "NAM", "Habakuk": "HAB", "Zephania": "ZEP",
            "Haggai": "HAG", "Sacharja": "ZEC", "Maleachi": "MAL",
            # New Testament
            "Matthäus": "MAT", "Markus": "MRK", "Lukas": "LUK", "Johannes": "JHN",
            "Apostelgeschichte": "ACT", "Römer": "ROM",
            "1. Korinther": "1CO", "2. Korinther": "2CO",
            "Galater": "GAL", "Epheser": "EPH", "Philipper": "PHP",
            "Kolosser": "COL",
            "1. Thessalonicher": "1TH", "2. Thessalonicher": "2TH",
            "1. Timotheus": "1TI", "2. Timotheus": "2TI",
            "Titus": "TIT", "Philemon": "PHM", "Hebräer": "HEB",
            "Jakobus": "JAS",
            "1. Petrus": "1PE", "2. Petrus": "2PE",
            "1. Johannes": "1JN", "2. Johannes": "2JN", "3. Johannes": "3JN",
            "Judas": "JUD", "Offenbarung": "REV"
        }
        # Track current OT-quote depth to decide when to open / close xot variant
        self.xot_quote_depth: int = 0
        self.variant_count = 0

    def _get_usx_char_style(self, xml_style):
        """Convert XML character style to USX char style"""
        return self.char_style_map.get(xml_style, 'no')  # default to 'normal' if style not found

    def debug(self, message: str):
        if self.verbose:
            print(f"DEBUG: {message}")

    def list_character_styles(self, xml_file):
        """List all unique character styles in the XML file"""
        tree = etree.parse(xml_file)
        root = tree.getroot()
        styles = set()
        for style in root.xpath("//@AppliedCharacterStyle"):
            styles.add(style)
        return sorted(list(styles))

    def transform(self, xml_content: bytes, book_name: str) -> str:
        self.debug("Starting XML transformation...")
        # Store styles for later output
        self.found_styles = set()

        self.book_name = book_name  # Store the book name for reference processing

        # Parse input XML as bytes
        parser = etree.XMLParser(remove_blank_text=True)
        root = etree.fromstring(xml_content, parser)
        self.debug("XML parsed successfully")

        # Collect styles during parsing but don't output yet
        for style in root.xpath("//@AppliedCharacterStyle"):
            self.found_styles.add(style)

        # Create USX output
        usx = etree.Element("usx", version="3.0")

        # Get book code
        book_code = self.book_codes.get(book_name)
        if not book_code:
            raise ValueError(f"Unknown book name: {book_name}")

        # 1. Add book identification (required)
        book = etree.SubElement(usx, "book", code=book_code, style="id")

        # 2. Add book titles (at least one required)
        title = self._find_title(root)
        if title:
            para = etree.SubElement(usx, "para", style="mt")
            para.text = title
            # Add toc1 entry
            toc1 = etree.SubElement(usx, "para", style="mt1")
            toc1.text = book_name
            # Add toc2 entry with alternate names
            toc2 = etree.SubElement(usx, "para", style="mt2")
            toc2.text = title
        else:
            # Add minimal required title if none found
            para = etree.SubElement(usx, "para", style="mt")
            para.text = book_name

        # Add alternate names if available from metadata
        if self.book_metadata and 'alternate_names' in self.book_metadata:
            alternate_names = self.book_metadata['alternate_names']
            if alternate_names:
                para_toc2 = etree.SubElement(usx, "para", style="mt2")
                para_toc2.text = '; '.join(alternate_names)
                self.debug(f"Added alternate names: {', '.join(alternate_names)}")

        # Process main content
        current_para = None

        # Process all ParagraphStyleRange elements containing Normal style
        normal_ranges = root.xpath(".//ParagraphStyleRange[contains(@AppliedParagraphStyle, 'Normal')]")
        self.debug(f"Found {len(normal_ranges)} paragraph ranges to process")

        for para_range in normal_ranges:
            if not self._process_paragraph_range(para_range, book):
                self.debug(f"Stopped processing at verse {self.current_verse_number} (limit: {self.verse_limit})")
                break

        # First generate the XML string with declaration
        xml_string = etree.tostring(usx, pretty_print=True, encoding='utf-8',
                                  xml_declaration=True, standalone=True)

        print("\nCharacter styles found:")
        print("--------------------------------------------------")
        for style in sorted(self.found_styles):
            print(f"- {style}")

        # Then decode it to a string
        return xml_string.decode('utf-8')

    def _find_title(self, root) -> Optional[str]:
        title_element = root.xpath(".//ParagraphStyleRange[contains(@AppliedParagraphStyle, 'Title')]")
        if title_element:
            return ''.join(title_element[0].xpath(".//Content/text()"))
        return None

    def _handle_variant_start(self, char_range, parent_element, current_para, variant_type):
        """Handle the start of a variant section."""
        # Initialize counter for this variant type if not exists
        if variant_type not in self.variant_counts:
            self.variant_counts[variant_type] = 0

        self.variant_counts[variant_type] += 1
        variant_id = f"{variant_type}{self.variant_counts[variant_type]}"
        # PUSH onto the variant stack so we can close it later
        self.variant_stack.append((variant_type, variant_id))

        # Create milestone element
        ms_elem = etree.SubElement(current_para or parent_element, "ms")
        ms_elem.set("type", variant_type)
        ms_elem.set("sid", variant_id)

        if variant_type == 'va':
            ms_elem.tail = "⌜"
        return current_para, ms_elem

    def _handle_variant_end(self, char_range, current_para, variant_type):
        """Handle the end of a variant section."""
        if not self.variant_stack:
            return current_para

        # Get the last variant of this type
        last_variant = next((item for item in reversed(self.variant_stack)
                           if item[0] == variant_type), None)

        if last_variant:
            self.variant_stack.remove(last_variant)
            _, variant_id = last_variant

            # Create milestone element with eid and type
            ms_elem = etree.SubElement(current_para, "ms")
            ms_elem.set("type", variant_type)
            ms_elem.set("eid", variant_id)

            if variant_type == 'va':
                ms_elem.tail = "⌝"

        return current_para

    def _format_text_content(self, text):
        """Format text content to ensure proper spacing after punctuation marks and handle quotation marks.

        Args:
            text: The text to format

        Returns:
            Formatted text with proper spacing and proper quotation marks
        """
        if text is None:
            return ""
        # Remove extra spaces, but preserve single spaces
        return ' '.join(text.split())

    def _handle_quote_content(self, text):
        """Handle quote content specifically to avoid repetition."""
        if not text:
            return ""
        # Remove quotes and normalize
        text = text.replace('»', '').replace('«', '').strip()
        # Add back single set of quotes
        return f"»{text}«"

    def _process_paragraph_range(self, para_range, parent_element):
        current_para = None
        current_verse = None  # Track current verse for proper closing
        pending_variant_milestone = None  # Track if a variant milestone needs to be created
        pending_footnote_element = None  # Store footnote to be attached before verse
        chapter_changed = False  # Track if chapter number has changed

        def has_content(elem):
            """Helper function to check if an element has any content"""
            # Check if element has text
            if elem.text and elem.text.strip():
                return True
            # Check if element has non-empty children
            if len(elem) > 0:
                return True
            # Check if any child has tail text
            for child in elem:
                if child.tail and child.tail.strip():
                    return True
            return False

        # Pre-scan for footnotes that need to be attached to variants and verses
        for char_range in para_range.xpath(".//CharacterStyleRange"):
            style = char_range.get('AppliedCharacterStyle')

            # Handle footnotes that need to be attached to variants
            if style == 'CharacterStyle/Fußnotenzahl vor Variante':
                footnote = char_range.find('Footnote')
                if footnote is not None:
                    # Process the footnote immediately and store it
                    note_elem = self._process_footnote_content(footnote, parent_element)
                    if note_elem is not None:
                        pending_footnote_element = note_elem
                        self.debug("Pre-processed footnote for variant")
                    char_range.set('processed', 'true')

        # Now process all elements in order
        for char_range in para_range.xpath(".//CharacterStyleRange"):
            try:
                # Skip if this element or any of its ancestors have been processed
                if (char_range.get('processed') == 'true' or
                    any(p.get('processed') == 'true' for p in char_range.iterancestors())):
                    continue

                style = char_range.get('AppliedCharacterStyle')

                # Update chapter number and create new chapter element
                if style == 'CharacterStyle/Kapitelnummer':
                    content_elem = char_range.find('Content')
                    if content_elem is not None and content_elem.text is not None:
                        chapter_num = content_elem.text.strip()
                        chapter_changed = self.current_chapter != chapter_num
                        self.current_chapter = chapter_num

                        # Close previous chapter if exists
                        if chapter_changed and self.current_chapter:
                            # Close current verse if open
                            if current_verse is not None:
                                # Output any buffered variant text before closing verse
                                verse_eid = etree.SubElement(current_para or parent_element, "verse")
                                verse_eid.set("eid", f"{self.book_codes.get(self.book_name, self.book_name)} {self.current_chapter}:{self.current_verse_number}")
                                current_verse = None

                            # Remove empty paragraph if exists
                            if current_para is not None and not has_content(current_para):
                                parent_element.remove(current_para)

                            chapter_eid = etree.SubElement(parent_element, "chapter")
                            chapter_eid.set("eid", f"{self.book_codes.get(self.book_name, self.book_name)} {self.current_chapter}")

                        # Create new chapter
                        self.current_chapter = chapter_num
                        chapter = etree.SubElement(parent_element, "chapter",
                                                number=chapter_num,
                                                style="c",
                                                sid=f"{self.book_codes.get(self.book_name, self.book_name)} {chapter_num}")

                        # Create first verse of the chapter (verse 1)
                        current_para = etree.SubElement(parent_element, "para", style="pi1")
                        current_verse = etree.SubElement(current_para, "verse",
                                                    number="1",
                                                    style="v",
                                                    sid=f"{self.book_codes.get(self.book_name, self.book_name)} {chapter_num}:1")
                        self.current_verse_number = "1"
                        self.stats['verses_processed'] += 1
                    continue

                # Process <Br /> tag and verse numbers that might appear with it
                if char_range.find('Br') is not None:
                    style = char_range.get('AppliedCharacterStyle')

                    # If this is a verse number with a Br, process it as a verse number
                    if style == 'CharacterStyle/Versnummer':
                        content_elem = char_range.find('Content')
                        if content_elem is not None and content_elem.text is not None:
                            verse_num = content_elem.text.strip()

                            # Close previous verse if exists
                            if current_verse is not None:
                                verse_eid = etree.SubElement(current_para or parent_element, "verse")
                                verse_eid.set("eid", f"{self.book_codes.get(self.book_name, self.book_name)} {self.current_chapter}:{self.current_verse_number}")
                                current_verse = None

                            # Create new paragraph for the new verse
                            current_para = etree.SubElement(parent_element, "para", style="pi1")

                            # Create new verse
                            self.current_verse_number = verse_num
                            current_verse = etree.SubElement(current_para, "verse",
                                                        number=verse_num,
                                                        style="v",
                                                        sid=f"{self.book_codes.get(self.book_name, self.book_name)} {self.current_chapter}:{verse_num}")
                            self.stats['verses_processed'] += 1
                            char_range.set('processed', 'true')
                        continue

                    # Otherwise process as regular Br tag
                    content_elems = char_range.findall('Content')
                    if content_elems:
                        text = ''.join(''.join(elem.xpath('.//text()')) for elem in content_elems)
                        if text:
                            if current_para is not None:
                                if len(current_para) > 0:
                                    if current_para[-1].tail is None:
                                        current_para[-1].tail = text
                                    else:
                                        current_para[-1].tail += text
                                else:
                                    if current_para.text is None:
                                        current_para.text = text
                                    else:
                                        current_para.text += text

                    # Close current verse if open
                    if current_verse is not None:
                        verse_eid = etree.SubElement(current_para or parent_element, "verse")
                        verse_eid.set("eid", f"{self.book_codes.get(self.book_name, self.book_name)} {self.current_chapter}:{self.current_verse_number}")
                        current_verse = None

                    # Remove empty paragraph if exists
                    if current_para is not None and not has_content(current_para):
                        parent_element.remove(current_para)

                    # Create new paragraph
                    current_para = etree.SubElement(parent_element, "para", style="pi1")
                    continue

                # Check for variant markers before verse numbers
                if style == 'CharacterStyle/Eckige Klammer oben':
                    content_elem = char_range.find('Content')
                    text = content_elem.text
                    if text and text.startswith('⌜'):  # Start of variant
                        self.variant_count += 1
                        current_para, pending_variant_milestone = self._handle_variant_start(char_range, parent_element, current_para, 'va')
                    elif text and text.startswith('⌝'):  # End of variant
                        current_para = self._handle_variant_end(char_range, current_para, 'va')
                    else:
                        # Regular text within the quote
                        self._add_text_or_element(current_para, self._format_text_content(content.text))
                    char_range.set('processed', 'true')
                    continue
                elif style == 'CharacterStyle/Direktes Zitat aus dem AT od. Paraphrase':
                    content_elem = char_range.find('Content')
                    if content_elem is not None and content_elem.text:
                        text = content_elem.text
                        opens = text.count('»')
                        closes = text.count('«')

                        # START variant if depth currently 0 and we see an opening quote
                        if self.xot_quote_depth == 0 and opens > 0:
                            current_para, _ = self._handle_variant_start(char_range, parent_element, current_para, 'xot')

                        # Add the quote text
                        self._add_text_or_element(current_para, self._handle_quote_content(text))

                        # Update depth after writing text
                        self.xot_quote_depth += opens - closes

                        # END variant if depth returns to 0
                        if self.xot_quote_depth == 0 and any(v[0] == 'xot' for v in self.variant_stack):
                            current_para = self._handle_variant_end(char_range, current_para, 'xot')
                    continue
                # Process verse numbers
                if style == 'CharacterStyle/Versnummer':
                    content_elem = char_range.find('Content')
                    if content_elem is not None and content_elem.text is not None:
                        verse_num = content_elem.text.strip()  # Add strip() to handle any whitespace
                        self.verse_count = int(verse_num)  # For verse limit

                        # Check if we've hit the verse limit
                        if self.verse_limit and self.verse_count > self.verse_limit:
                            return False

                        # Close previous verse if exists
                        if current_verse is not None:
                            verse_eid = etree.SubElement(current_para or parent_element, "verse")
                            verse_eid.set("eid", f"{self.book_codes.get(self.book_name, self.book_name)} {self.current_chapter}:{self.current_verse_number}")
                            current_verse = None

                        # Ensure paragraph is open before a new verse
                        if current_para is None:
                            current_para = etree.SubElement(parent_element, "para", style="pi1")

                        # Create new verse
                        self.current_verse_number = verse_num
                        current_verse = etree.SubElement(current_para, "verse",
                                                    number=verse_num,
                                                    style="v",
                                                    sid=f"{self.book_codes.get(self.book_name, self.book_name)} {self.current_chapter}:{verse_num}")

                        # If we have a pending footnote, insert it right after the verse element
                        if pending_footnote_element is not None:
                            current_para.append(pending_footnote_element)
                            pending_footnote_element = None

                        self.stats['verses_processed'] += 1
                        char_range.set('processed', 'true')
                        continue

                # Process footnotes and references
                if 'Fußnoten' in style or 'Fußnotenverweis' in style:
                    footnote = self._process_footnote(char_range, current_para or parent_element)
                    if footnote is not None and current_para is not None:
                        current_para.append(footnote)
                    char_range.set('processed', 'true')
                    continue

                # Process regular text content
                content_elems = char_range.findall('Content')
                if content_elems:
                    # Combine text from all Content elements
                    text_parts = []
                    for i, elem in enumerate(content_elems):
                        cleaned = re.sub(r'\u003c\?ACE \d+\?\u003e', '', ''.join(elem.xpath('.//text()')))
                        if i == 0:  # First element
                            cleaned = cleaned.lstrip()
                        if i == len(content_elems) - 1:  # Last element
                            cleaned = cleaned.rstrip()
                        text_parts.append(cleaned)

                    text = ''.join(text_parts)

                    usx_style = self._get_usx_char_style(style)

                    # Process regular text
                    if text.strip():
                        if current_para is not None:
                            if usx_style != 'no':
                                # Always create a char element for styled text
                                char = etree.SubElement(current_para, "char", style=usx_style)
                                char.text = text
                            else:
                                # Add unstyled text to the appropriate place
                                self._add_text_or_element(current_para, text)

                char_range.set('processed', 'true')  # Mark as processed

            except Exception as e:
                self.debug(f"Error processing character range: {e}")
                continue

        # Final cleanup
        if current_verse is not None:
            verse_eid = etree.SubElement(current_para or parent_element, "verse")
            verse_eid.set("eid", f"{self.book_codes.get(self.book_name, self.book_name)} {self.current_chapter}:{self.current_verse_number}")

        # Remove final empty paragraph if exists
        if current_para is not None and not has_content(current_para):
            parent_element.remove(current_para)

        return True


    def _process_char_range(self, char_range, current_para=None, parent_element=None):
        """Process a character style range.

        The method now uses a small dispatcher so that each concrete style is
        delegated to a specialised handler.  This eliminates the large if/elif
        chain and avoids duplicated logic between quote handling, bracket
        variants, footnotes and plain text.
        """
        style = char_range.get('AppliedCharacterStyle', '')

        # Exact match handlers -------------------------------------------------
        exact_handlers = {
            'CharacterStyle/Direktes Zitat aus dem AT od. Paraphrase': self._handle_ot_quote,
            'CharacterStyle/Eckige Klammer oben': self._handle_variant_bracket,
            'CharacterStyle/Fußnotenzahl vor Variante': self._handle_pre_variant_footnote,
        }

        handler = exact_handlers.get(style)

        # Fallback to contains-match footnote handler
        if handler is None and ('Fußnoten' in style or 'Fußnotenverweis' in style):
            handler = self._handle_regular_footnote

        # Default handler for normal text
        if handler is None:
            handler = self._handle_plain_text

        return handler(char_range, current_para, parent_element)

    # ──────────────────────────────────────────────────────────────────────
    # Helper handlers – each returns True once the node is fully processed.
    # ──────────────────────────────────────────────────────────────────────

    def _handle_ot_quote(self, char_range, current_para, parent_element):
        """Handle Old-Testament quotes that are marked with special quotation
        chars.  Keeps track of nested depth and emits ms milestones."""
        if current_para is None:
            return False  # We need a paragraph context

        content_elem = char_range.find('Content')
        if content_elem is None or not content_elem.text:
            return False

        text = content_elem.text
        opens = text.count('»')
        closes = text.count('«')

        # Start variant if depth == 0 and at least one opening quote
        if self.xot_quote_depth == 0 and opens > 0:
            self.variant_counts.setdefault('xot', 0)
            self.variant_counts['xot'] += 1
            variant_id = f"xot{self.variant_counts['xot']}"
            self.current_xot = variant_id

            ms_elem = etree.SubElement(current_para, 'ms', type='xot', sid=variant_id)
            _ = ms_elem  # suppress linter unused-var

        # Write quote text (already handled for nested quotes as well)
        self._add_text_or_element(current_para, self._handle_quote_content(text))

        # Update depth and possibly close the variant
        self.xot_quote_depth += opens - closes
        if self.xot_quote_depth == 0 and self.current_xot:
            etree.SubElement(current_para, 'ms', eid=self.current_xot)
            self.current_xot = None

        char_range.set('processed', 'true')
        return True

    def _handle_variant_bracket(self, char_range, current_para, parent_element):
        """Handle ⌜ / ⌝ markers that delimit a text variant."""
        if current_para is None:
            return False
        text = char_range.findtext('Content', default='')
        if text == '⌜':  # start
            self.variant_count += 1
            variant_id = f"va{self.variant_count}"
            self.variant_stack.append(variant_id)
            etree.SubElement(current_para, 'ms', sid=variant_id, type='va')
            self._add_text_or_element(current_para, text)
        elif text == '⌝':  # end
            variant_id = self.variant_stack.pop() if self.variant_stack else f"va{self.variant_count}"
            etree.SubElement(current_para, 'ms', eid=variant_id)
            self._add_text_or_element(current_para, text)
        else:
            return False
        char_range.set('processed', 'true')
        return True

    def _handle_pre_variant_footnote(self, char_range, current_para, parent_element):
        """Footnote that appears *before* a variant – we pre-process and mark
        it so later variant handling can attach it."""
        footnote = char_range.find('Footnote')
        if footnote is not None:
            note = self._process_footnote_content(footnote, current_para or parent_element)
            if note is not None and current_para is not None:
                current_para.append(note)
        char_range.set('processed', 'true')
        return True

    def _handle_regular_footnote(self, char_range, current_para, parent_element):
        note = self._process_footnote(char_range, current_para or parent_element)
        if note is not None and current_para is not None:
            current_para.append(note)
        char_range.set('processed', 'true')
        return True

    def _handle_plain_text(self, char_range, current_para, parent_element):
        """Default processing branch for plain or styled text."""
        content_elems = char_range.findall('Content')
        if not content_elems:
            return False

        # Collect and clean text (strip ACE tags etc.)
        text_parts = []
        for i, elem in enumerate(content_elems):
            cleaned = re.sub(r'\u003c\?ACE \d+\?\u003e', '', ''.join(elem.xpath('.//text()')))
            if i == 0:  # First element
                cleaned = cleaned.lstrip()
            if i == len(content_elems) - 1:  # Last element
                cleaned = cleaned.rstrip()
            text_parts.append(cleaned)

        text = ''.join(text_parts)

        if not text:
            return False

        usx_style = self._get_usx_char_style(char_range.get('AppliedCharacterStyle', ''))
        if current_para is not None:
            if usx_style != 'no':
                char_elem = etree.SubElement(current_para, 'char', style=usx_style)
                char_elem.text = text
            else:
                processed = self._process_reference_patterns(text)
                self._add_processed_text(current_para, processed)
        char_range.set('processed', 'true')
        return True

    def _process_footnote(self, char_range, parent_element):
        """Process a footnote element."""
        footnote = char_range.find('Footnote')
        if footnote is None:
            return None

        # Check if this is a footnote that should be attached to a variant
        style = char_range.get('AppliedCharacterStyle', '')
        if style == 'CharacterStyle/Fußnotenzahl vor Variante':
            # Store this footnote to be attached to the next variant
            self.pending_footnote = footnote
            return None

        # Create note element and continue with normal footnote processing
        note = etree.SubElement(parent_element, "note", style="f")

        # Add verse reference
        if self.current_chapter and self.current_verse_number:
            ref_text = f"{self.current_chapter},{self.current_verse_number}"
            note.append(etree.XML(f'<char style="fr">{ref_text}: </char>'))

        footnote_letter = self.footnote_letters[self.footnote_count % len(self.footnote_letters)]
        note.set("caller", footnote_letter)

        content_added = False
        last_element = None  # Track the last element added

        for para in footnote.xpath(".//ParagraphStyleRange"):
            # Process each CharacterStyleRange in order
            char_ranges = para.xpath(".//CharacterStyleRange")

            for cr in char_ranges:
                text_elem = cr.find('Content')
                if text_elem is not None:
                    text = ''.join(text_elem.xpath('.//text()'))
                    text = re.sub(r'<\?ACE \d+\?>', '', text)
                    if text.strip():
                        style = cr.get('AppliedCharacterStyle')
                        content_added = True

                        if style != 'CharacterStyle/$ID/[No character style]':
                            # Styled text - add as char element without processing references
                            usx_style = self._get_usx_char_style(style)

                            if usx_style != 'no':
                                # Always create a char element for styled text
                                char = etree.SubElement(note, "char", style=usx_style)
                                char.text = text
                                last_element = char
                            else:
                                # Add unstyled text to the appropriate place
                                self._add_text_or_element(note, text)
                        else:
                            # Regular text - process references
                            content_added = True
                            processed_text = self._process_reference_patterns(text)
                            if isinstance(processed_text, list):
                                for part in processed_text:
                                    if isinstance(part, str):
                                        if last_element is not None:
                                            if last_element.tail is None:
                                                last_element.tail = part
                                            else:
                                                last_element.tail += part
                                        else:
                                            if note.text is None:
                                                note.text = part
                                            else:
                                                note.text += part
                                    else:  # Element
                                        note.append(part)
                                        last_element = part
                            else:
                                # Single string
                                if last_element is not None:
                                    if last_element.tail is None:
                                        last_element.tail = processed_text
                                    else:
                                        last_element.tail += processed_text
                                else:
                                    if note.text is None:
                                        note.text = processed_text
                                    else:
                                        note.text += processed_text

        if content_added:
            self.footnote_count += 1
            self.stats['footnotes_processed'] += 1
            footnote.set('processed', 'true')
            self.debug("Footnote processed and marked")
        else:
            parent_element.remove(note)

        return note

    def _process_footnote_content(self, footnote, parent_element):
        """Process the content of a footnote and create a note element."""
        # Create note element
        note = etree.Element("note")
        note.set("style", "f")
        note.set("caller", "o")  # Use 'o' as caller for variant footnotes

        content_added = False

        for para in footnote.xpath(".//ParagraphStyleRange"):
            # Process each CharacterStyleRange in order
            char_ranges = para.xpath(".//CharacterStyleRange")

            for cr in char_ranges:
                text_elem = cr.find('Content')
                if text_elem is not None and text_elem.text:
                    style = cr.get('AppliedCharacterStyle', '')

                    # Process any references in the text
                    processed_text = self._process_reference_patterns(text_elem.text)
                    if isinstance(processed_text, list):
                        for part in processed_text:
                            self._add_text_or_element(note, part)
                    else:
                        self._add_text_or_element(note, processed_text)

                    content_added = True

        if content_added:
            self.footnote_count += 1
            self.stats['footnotes_processed'] += 1
            footnote.set('processed', 'true')
            return note

        return None

    def _process_reference_patterns(self, text):
        """Process all reference patterns in a text."""
        if not text:
            return text

        try:
            result = []
            current_pos = 0

            self.debug(f"\nProcessing text: '{text}'")

            # Define all patterns and their processing order
            patterns = [
                # Book references (e.g., "Röm 5,8") - should be first to avoid partial matches
                (r'([A-Za-zäöüß]+)\s+(\d+,\d+)(?!\d)', 'book_ref'),

                # Verse lists (e.g., "Versen 1.2.4")
                (r'(?:Versen?|Verse)\s+([\d.]+)', 'verse_list'),

                # V. N references
                (r'V\.\s*(\d+)', 'v_ref'),

                # References with "siehe" prefix - process before direct refs
                (r'siehe\s+(\d+,\d+)', 'siehe_ref'),

                # Direct verse references (e.g., "17,24") - but not if preceded by "siehe"
                (r'(?<!siehe\s)(?<!,)(\d+,\d+)(?=[\s).]|$)', 'direct_ref'),
            ]

            # Find all matches for all patterns
            matches = []
            processed_positions = set()

            for pattern, ref_type in patterns:
                for match in re.finditer(pattern, text):
                    start, end = match.span()

                    # Skip if this position has already been processed
                    if any(start < p[1] and end > p[0] for p in processed_positions):
                        continue

                    self.debug(f"Found {ref_type} match: '{match.group(0)}' at positions {start}-{end}")
                    processed = None

                    if ref_type == 'verse_list':
                        processed = self._process_reference(match.group(0), self.current_chapter)
                    elif ref_type == 'v_ref':
                        processed = self._process_reference(match.group(0), self.current_chapter)
                    elif ref_type == 'direct_ref':
                        processed = self._process_reference(match.group(0), self.current_chapter)
                    elif ref_type == 'book_ref':
                        processed = self._process_reference(match.group(0), None)
                    elif ref_type == 'siehe_ref':
                        # Handle siehe references directly without using _process_reference
                        ref_elem = etree.Element("ref")
                        ref_elem.text = match.group(0)  # Full text with "siehe"
                        ref_elem.set("loc", f"{self.book_name} {match.group(1)}")  # Just numbers
                        processed = ref_elem

                    if processed is not None:
                        matches.append((start, end, processed))
                        processed_positions.add((start, end))

            # Sort matches by start position
            matches.sort(key=lambda x: x[0])

            # Process matches in order
            for start, end, processed in matches:
                # Add text before the match
                if start > current_pos:
                    before_text = text[current_pos:start]
                    if before_text:
                        result.append(before_text)

                # Add the processed reference
                result.append(processed)
                current_pos = end

            # Add remaining text
            if current_pos < len(text):
                remaining = text[current_pos:]
                if remaining:
                    result.append(remaining)

            self.stats['references_processed'] += 1
            return result

        except Exception as e:
            self.debug(f"Error processing references: {str(e)}")
            return [text]

    def _process_reference(self, text, current_chapter, verse_ref=None):
        """Process a single reference and return the USX ref element."""
        # Clean up the reference text
        ref_text = text.strip()

        # Create the reference element
        ref_elem = etree.Element("ref")
        ref_elem.text = ref_text

        # If verse_ref is provided, use it for the loc attribute
        if verse_ref:
            ref_elem.set("loc", f"{self.book_codes.get(self.book_name, self.book_name)} {verse_ref}")
            return ref_elem

        # Handle different reference formats
        if ref_text.startswith('V.'):
            # V. N format
            verse_num = re.search(r'V\.\s*(\d+)', ref_text).group(1)
            ref_elem.set("loc", f"{self.book_codes.get(self.book_name, self.book_name)} {current_chapter},{verse_num}")
        elif ref_text.startswith('Verse') or ref_text.startswith('Versen'):
            # Verse list format
            verse_list = re.search(r'(?:Versen?|Verse)\s+([\d.]+)', ref_text).group(1)
            ref_elem.set("loc", f"{self.book_codes.get(self.book_name, self.book_name)} {current_chapter},{verse_list}")
        elif re.match(r'\d+,\d+', ref_text):
            # Direct verse reference
            ref_elem.set("loc", f"{self.book_codes.get(self.book_name, self.book_name)} {ref_text}")
        elif ref_text.startswith('siehe '):
            # For "siehe" references, keep "siehe" in display but remove from loc
            clean_ref = ref_text[6:].strip()  # Remove "siehe " and any extra whitespace
            ref_elem.set("loc", f"{self.book_codes.get(self.book_name, self.book_name)} {clean_ref}")
            return ref_elem  # Important: return here to prevent further processing
        elif re.match(r'[A-Za-zäöüß]+\s+\d+,\d+', ref_text):
            # Book reference - use as is
            ref_elem.set("loc", ref_text)
        else:
            # Not a reference, return as is
            return text

        self.debug(f"Created reference: {etree.tostring(ref_elem, encoding='unicode')}")
        return ref_elem

    def _add_text_or_element(self, parent, content):
        """Helper to add text or element to parent node."""
        if isinstance(content, str):
            if len(parent) > 0:
                if parent[-1].tail is None:
                    parent[-1].tail = content
                else:
                    parent[-1].tail += content
            else:
                if parent.text is None:
                    parent.text = content
                else:
                    parent.text += content
        else:
            parent.append(content)

    def _add_processed_text(self, parent, processed_text):
        """Helper to add processed text or elements to parent node."""
        if not processed_text:
            return

        self.debug(f"Adding processed text: {processed_text}")

        # Keep track of last element to handle text between elements
        last_tail = None

        for part in processed_text:
            if isinstance(part, str):
                if last_tail is not None:
                    last_tail.tail = (last_tail.tail or '') + part
                else:
                    parent.text = (parent.text or '') + part
            elif isinstance(part, etree._Element):
                parent.append(part)
                last_tail = part

def load_bible_metadata() -> Dict[str, Any]:
    try:
        # Get the Laravel root directory (3 levels up from the script)
        laravel_root = Path(__file__).parent.parent.parent

        # Construct path to the JSON file
        json_path = laravel_root / 'database' / 'data' / 'bible_books.json'

        with open(json_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Warning: Could not load metadata from bible_books.json: {str(e)}")
        return {}

def find_book_metadata(metadata: Dict[str, Any], book_name: str) -> Optional[Dict[str, Any]]:
    # First try direct match
    if book_name in metadata:
        return metadata[book_name]

    # Then try matching against common_names
    for key, value in metadata.items():
        if 'common_names' in value and book_name in value['common_names']:
            return value

    # Then try matching against alternate_names
    for key, value in metadata.items():
        if 'alternate_names' in value and book_name in value['alternate_names']:
            return value

    # Then try matching against abbreviations
    for key, value in metadata.items():
        if 'abbreviations' in value and book_name in value['abbreviations']:
            return value

    return None

def main():
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Transform XML to USX format')
    parser.add_argument('source_xml_path', help='Path to the source XML file')
    parser.add_argument('book_name', help='Name of the book')
    parser.add_argument('-v', '--verbose', action='store_true', help='Enable verbose output')
    parser.add_argument('--verse-limit', type=int, help='Stop processing after this verse number')
    args = parser.parse_args()

    # Check if source file exists
    if not os.path.exists(args.source_xml_path):
        print(f"Error: Source file '{args.source_xml_path}' does not exist.")
        sys.exit(1)

    try:
        start_time = time.time()

        if args.verbose:
            print(f"Starting transformation of {args.source_xml_path}")
            print(f"Book name: {args.book_name}")
            if args.verse_limit:
                print(f"Will stop after verse {args.verse_limit}")

        # Load metadata
        metadata = load_bible_metadata()
        book_metadata = find_book_metadata(metadata, args.book_name) if metadata else None

        # Read source XML as bytes
        with open(args.source_xml_path, 'rb') as f:
            xml_content = f.read()

        if args.verbose:
            print("XML file read successfully")

        # Transform content
        transformer = USXTransformer(args.book_name, book_metadata, args.verbose, args.verse_limit)
        result = transformer.transform(xml_content, args.book_name)

        # Get the directory of the source file
        output_dir = os.path.dirname(args.source_xml_path)

        # Create output filename based on book name in the same directory as source
        output_filename = os.path.join(output_dir, f"{args.book_name}.usx")

        # Write the result to file
        with open(output_filename, 'w', encoding='utf-8') as f:
            f.write(result)

        # Calculate execution time
        execution_time = time.time() - start_time

        # Print summary
        print("\nTransformation Summary:")
        print("-" * 50)
        print(f"Input file: {args.source_xml_path}")
        print(f"Output file: {output_filename}")
        print(f"Book name: {args.book_name}")
        print("\nStatistics:")
        print(f"Verses processed: {transformer.stats['verses_processed']}")
        print(f"Footnotes processed: {transformer.stats['footnotes_processed']}")
        print(f"References processed: {transformer.stats['references_processed']}")
        print(f"\nExecution time: {execution_time:.2f} seconds")
        print("-" * 50)

    except Exception as e:
        print(f"Error: {str(e)}")
        if args.verbose:
            import traceback
            print("\nFull traceback:")
            traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
