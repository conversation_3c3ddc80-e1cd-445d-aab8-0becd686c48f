#!/bin/bash

# Cross-Browser Test Runner Script
# This script runs browser tests across multiple browsers with various options

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
BROWSERS="chrome,firefox"
HEADLESS=true
PARALLEL=false
ENVIRONMENT="local"
FILTER=""
VERBOSE=false
DOCKER=false
REPORT_FORMAT="console"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    cat << EOF
Cross-Browser Test Runner

Usage: $0 [OPTIONS]

OPTIONS:
    -b, --browsers BROWSERS     Comma-separated list of browsers (chrome,firefox,safari,edge)
                               Default: chrome,firefox
    -h, --headless             Run browsers in headless mode (default: true)
    --no-headless              Run browsers with GUI
    -p, --parallel             Run tests in parallel across browsers
    -e, --environment ENV      Test environment (local,ci,testing) Default: local
    -f, --filter FILTER        Filter tests by name or group
    -v, --verbose              Verbose output
    -d, --docker               Run tests in Docker containers
    -r, --report FORMAT        Report format (console,html,junit) Default: console
    --help                     Show this help message

EXAMPLES:
    $0                                          # Run with default settings
    $0 -b chrome,firefox --no-headless         # Run Chrome and Firefox with GUI
    $0 -b safari -e local -v                   # Run Safari locally with verbose output
    $0 -d -p -b chrome,firefox,edge            # Run in Docker with parallel execution
    $0 -f "test_homepage" -b chrome             # Run specific test in Chrome only

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -b|--browsers)
            BROWSERS="$2"
            shift 2
            ;;
        -h|--headless)
            HEADLESS=true
            shift
            ;;
        --no-headless)
            HEADLESS=false
            shift
            ;;
        -p|--parallel)
            PARALLEL=true
            shift
            ;;
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -f|--filter)
            FILTER="$2"
            shift 2
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -d|--docker)
            DOCKER=true
            shift
            ;;
        -r|--report)
            REPORT_FORMAT="$2"
            shift 2
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Function to setup environment
setup_environment() {
    print_status "Setting up test environment: $ENVIRONMENT"
    
    # Copy environment file
    if [[ ! -f .env ]]; then
        cp .env.example .env
    fi
    
    # Set environment-specific variables
    case $ENVIRONMENT in
        "ci")
            export DUSK_HEADLESS=true
            export APP_ENV=testing
            export DB_DATABASE=esra_bibel_test
            ;;
        "local")
            export DUSK_HEADLESS=$HEADLESS
            export APP_ENV=testing
            ;;
        "testing")
            export DUSK_HEADLESS=true
            export APP_ENV=testing
            ;;
    esac
    
    # Set browser configuration
    IFS=',' read -ra BROWSER_ARRAY <<< "$BROWSERS"
    for browser in "${BROWSER_ARRAY[@]}"; do
        case $browser in
            "chrome")
                export DUSK_CHROME_ENABLED=true
                ;;
            "firefox")
                export DUSK_FIREFOX_ENABLED=true
                ;;
            "safari")
                export DUSK_SAFARI_ENABLED=true
                ;;
            "edge")
                export DUSK_EDGE_ENABLED=true
                ;;
        esac
    done
}

# Function to start browser drivers
start_drivers() {
    if [[ "$DOCKER" == "true" ]]; then
        print_status "Starting Docker containers for browser testing..."
        docker-compose -f docker/browser-testing/docker-compose.yml up -d
        sleep 10  # Wait for containers to be ready
    else
        print_status "Starting local browser drivers..."
        
        # Start ChromeDriver if Chrome is enabled
        if [[ "$BROWSERS" == *"chrome"* ]]; then
            if ! pgrep -f "chromedriver" > /dev/null; then
                chromedriver --port=9515 &
                sleep 2
            fi
        fi
        
        # Start GeckoDriver if Firefox is enabled
        if [[ "$BROWSERS" == *"firefox"* ]]; then
            if ! pgrep -f "geckodriver" > /dev/null; then
                geckodriver --port=4444 &
                sleep 2
            fi
        fi
        
        # Safari driver is managed by the system on macOS
        if [[ "$BROWSERS" == *"safari"* ]] && [[ "$(uname)" == "Darwin" ]]; then
            if ! pgrep -f "safaridriver" > /dev/null; then
                safaridriver --port=10444 &
                sleep 2
            fi
        fi
    fi
}

# Function to stop browser drivers
stop_drivers() {
    if [[ "$DOCKER" == "true" ]]; then
        print_status "Stopping Docker containers..."
        docker-compose -f docker/browser-testing/docker-compose.yml down
    else
        print_status "Stopping local browser drivers..."
        pkill -f "chromedriver" || true
        pkill -f "geckodriver" || true
        pkill -f "safaridriver" || true
    fi
}

# Function to prepare test database
prepare_database() {
    print_status "Preparing test database..."
    
    if [[ "$DOCKER" == "true" ]]; then
        docker-compose -f docker/browser-testing/docker-compose.yml exec app php artisan db:refresh-all --env=testing
        docker-compose -f docker/browser-testing/docker-compose.yml exec app php artisan bible:parse Markus --output --env=testing
        docker-compose -f docker/browser-testing/docker-compose.yml exec app php artisan bible:parse Lukas --output --env=testing
    else
        php artisan db:refresh-all --env=testing
        php artisan bible:parse Markus --output --env=testing
        php artisan bible:parse Lukas --output --env=testing
    fi
}

# Function to build assets
build_assets() {
    print_status "Building frontend assets..."
    
    if [[ "$DOCKER" == "true" ]]; then
        docker-compose -f docker/browser-testing/docker-compose.yml exec app npm run build
    else
        npm run build
    fi
}

# Function to run tests
run_tests() {
    print_status "Running browser tests..."
    
    # Build test command
    TEST_CMD="php artisan dusk"
    
    if [[ -n "$FILTER" ]]; then
        TEST_CMD="$TEST_CMD --filter=$FILTER"
    fi
    
    if [[ "$VERBOSE" == "true" ]]; then
        TEST_CMD="$TEST_CMD -vvv"
    fi
    
    # Set report format
    case $REPORT_FORMAT in
        "html")
            TEST_CMD="$TEST_CMD --log-html=test-results/browser-tests.html"
            ;;
        "junit")
            TEST_CMD="$TEST_CMD --log-junit=test-results/browser-tests.xml"
            ;;
    esac
    
    # Create results directory
    mkdir -p test-results
    
    if [[ "$DOCKER" == "true" ]]; then
        docker-compose -f docker/browser-testing/docker-compose.yml exec app $TEST_CMD
    else
        if [[ "$PARALLEL" == "true" ]]; then
            # Run tests in parallel for each browser
            IFS=',' read -ra BROWSER_ARRAY <<< "$BROWSERS"
            pids=()
            
            for browser in "${BROWSER_ARRAY[@]}"; do
                (
                    export DUSK_DEFAULT_BROWSER=$browser
                    $TEST_CMD
                ) &
                pids+=($!)
            done
            
            # Wait for all parallel processes
            for pid in "${pids[@]}"; do
                wait $pid
            done
        else
            $TEST_CMD
        fi
    fi
}

# Function to generate test report
generate_report() {
    print_status "Generating test report..."
    
    case $REPORT_FORMAT in
        "html")
            print_success "HTML report generated: test-results/browser-tests.html"
            ;;
        "junit")
            print_success "JUnit report generated: test-results/browser-tests.xml"
            ;;
        "console")
            print_success "Test results displayed in console"
            ;;
    esac
}

# Function to cleanup
cleanup() {
    print_status "Cleaning up..."
    stop_drivers
}

# Trap to ensure cleanup on exit
trap cleanup EXIT

# Main execution
main() {
    print_status "Starting cross-browser test execution..."
    print_status "Browsers: $BROWSERS"
    print_status "Headless: $HEADLESS"
    print_status "Environment: $ENVIRONMENT"
    print_status "Docker: $DOCKER"
    
    setup_environment
    start_drivers
    prepare_database
    build_assets
    run_tests
    generate_report
    
    print_success "Cross-browser testing completed!"
}

# Run main function
main "$@"
