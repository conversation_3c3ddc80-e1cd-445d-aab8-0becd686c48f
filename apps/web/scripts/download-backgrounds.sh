#!/bin/bash

# Create the backgrounds directory if it doesn't exist
mkdir -p public/images/backgrounds

# Download specific high-quality Bible-themed images
curl -L "https://images.pexels.com/photos/267559/pexels-photo-267559.jpeg" -o "public/images/backgrounds/bible-1.jpg"
curl -L "https://images.pexels.com/photos/1112048/pexels-photo-1112048.jpeg" -o "public/images/backgrounds/bible-2.jpg"
curl -L "https://images.pexels.com/photos/415571/pexels-photo-415571.jpeg" -o "public/images/backgrounds/bible-3.jpg"
curl -L "https://images.pexels.com/photos/268941/pexels-photo-268941.jpeg" -o "public/images/backgrounds/bible-4.jpg"
curl -L "https://images.pexels.com/photos/5989925/pexels-photo-5989925.jpeg" -o "public/images/backgrounds/bible-5.jpg"

echo "Background images downloaded successfully!"
