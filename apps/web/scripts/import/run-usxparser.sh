#!/bin/bash

# Get the directory where the script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$( cd "$SCRIPT_DIR/../.." && pwd )"

# Path to the virtual environment in the project root
VENV_PATH="${PROJECT_ROOT}/venv"

# Function to handle errors
handle_error() {
    echo "Error: $1" >&2
    exit 1
}

# Create virtual environment if it doesn't exist
if [ ! -d "$VENV_PATH" ]; then
    echo "Creating Python virtual environment..."
    python3 -m venv "$VENV_PATH" || handle_error "Failed to create virtual environment"
fi

# Activate virtual environment
source "$VENV_PATH/bin/activate" || handle_error "Failed to activate virtual environment"

# Install/upgrade pip and wheel
python3 -m pip install --upgrade pip wheel || handle_error "Failed to upgrade pip and wheel"

# Install lxml first (it's a special case as it might need system dependencies)
echo "Installing lxml..."
pip install lxml || handle_error "Failed to install lxml. You might need to install system dependencies first:
On macOS: brew install libxml2 libxslt
On Ubuntu/Debian: sudo apt-get install python3-lxml
On RHEL/CentOS: sudo yum install python3-lxml"

# Install the package in development mode if not already installed
if ! python3 -c "import usxparser" 2>/dev/null; then
    echo "Installing usxparser module..."
    cd "$SCRIPT_DIR" || handle_error "Failed to change directory"
    pip install -e . || handle_error "Failed to install usxparser module"
fi

# Add the script directory to PYTHONPATH
export PYTHONPATH="$SCRIPT_DIR:$PYTHONPATH"

# Run the parser CLI with all arguments passed to this script
python3 "${SCRIPT_DIR}/usxparser/cli.py" "$@"

# Deactivate virtual environment
deactivate
