#!/bin/bash

# Base directory for bible texts
BIBLE_DIR="/Applications/MAMP/htdocs/ebtc/esra-bibel/apps/web/bibeltext"
WEB_DIR="/Applications/MAMP/htdocs/ebtc/esra-bibel/apps/web"

# Default settings
FORCE_PARSE=false
SPECIFIC_BOOK=""
USX_ONLY=false
LARAVEL_ONLY=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --force-parse)
            FORCE_PARSE=true
            shift
            ;;
        --book)
            if [[ -n "$2" && "$2" != --* ]]; then
                SPECIFIC_BOOK="$2"
                shift 2
            else
                echo "Error: --book requires a book name argument."
                echo "Usage: $0 [--force-parse] [--book BOOK_NAME] [--usx-only]"
                exit 1
            fi
            ;;
        --usx-only)
            if [ "$LARAVEL_ONLY" = true ]; then
                echo "Error: Cannot use --usx-only and --laravel-only together"
                exit 1
            fi
            USX_ONLY=true
            shift
            ;;
        --laravel-only)
            if [ "$USX_ONLY" = true ]; then
                echo "Error: Cannot use --usx-only and --laravel-only together"
                exit 1
            fi
            LARAVEL_ONLY=true
            shift
            ;;
        *)
            echo "Unknown option: $1"
            echo "Usage: $0 [--force-parse] [--book BOOK_NAME] [--usx-only]"
            exit 1
            ;;
    esac
done

# --- USX generation: parallel Python CLI ---
if [ "$LARAVEL_ONLY" = false ]; then
    cd "$WEB_DIR/scripts/import"
    # Activate virtualenv and set module path
    export PYTHONPATH="$WEB_DIR/scripts/import"
    echo "Generating USX${SPECIFIC_BOOK:+ for $SPECIFIC_BOOK} using module CLI..."
    if [ -n "$SPECIFIC_BOOK" ]; then
        # Point to the book's XML file
        xml_file="$BIBLE_DIR/$SPECIFIC_BOOK/$SPECIFIC_BOOK.xml"
        if [ ! -f "$xml_file" ]; then
            echo "Error: XML file not found for $SPECIFIC_BOOK at $xml_file" >&2
            exit 1
        fi
        echo "Parsing XML for $SPECIFIC_BOOK..."
        python3 -m usxparser.cli "$xml_file" "$SPECIFIC_BOOK"
    else
        echo "Parsing all books in parallel..."
        python3 -m usxparser.cli "$BIBLE_DIR" --parallel --max-workers 4
    fi
    if [ $? -ne 0 ]; then
        echo "Error: Python parser failed. Aborting."
        exit 1
    fi
else
    echo "Skipping Python parsing as --laravel-only was specified"
fi

# --- Laravel import: sequential job loop ---
if [ "$USX_ONLY" = true ]; then
    echo "Python parsing complete. Skipping Laravel import as --usx-only was specified."
    exit 0
fi

set -u pipefail  # treat unset vars as errors, capture errors manually
if [ -n "$SPECIFIC_BOOK" ]; then
    book_dirs=("$BIBLE_DIR/$SPECIFIC_BOOK")
else
    book_dirs=("$BIBLE_DIR"/*)
fi
for book_dir in "${book_dirs[@]}"; do
    book_name=$(basename "$book_dir")
    usx_file="$book_dir/$book_name.usx"
    if [ ! -f "$usx_file" ]; then
        echo "[Laravel] No USX for $book_name, skipping."
        continue
    fi
    cd "$WEB_DIR"
    echo "[Laravel] Importing $book_name..."
    # Run artisan directly to display progress
    php artisan bible:parse "$book_name" --optimize-tables
    rc=$?
    if [ $rc -ne 0 ]; then
        echo "[Laravel] ERROR: $book_name failed with exit code $rc." >&2
        exit $rc
    fi
    echo "[Laravel] Done: $book_name"
done

# Summary output
echo -e "\n================ SUMMARY ================"
echo "Total time elapsed: $(($(date +%s) - $(date +%s))) seconds"
echo "Books processed: $(ls -d "$BIBLE_DIR"/*/ | wc -l)"
echo "Books parsed: $(ls -d "$BIBLE_DIR"/*/ | wc -l)"
echo -e "========================================\n"

echo "Bible text processing complete!"
