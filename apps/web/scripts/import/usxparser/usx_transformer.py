"""
USXTransformer: Main transformation logic for XML→USX Bible text.
Split from CLI and constants for maintainability.
"""
# NOTE: All imports are absolute for CLI compatibility
from __future__ import annotations
import logging
import re
from typing import Any, Dict, List, Optional
from lxml import etree
from lxml.builder import E
from usxparser.style_maps import CHAR_STYLE_MAP, load_book_codes
from usxparser.utils import clean_text, format_text_content
from usxparser.footnotes import FootnoteProcessor
from usxparser.variant_handler import VariantHandler
from usxparser.style_maps import CharStyle
from usxparser.parser_state import ParserState
from usxparser.references import ReferenceProcessor
from usxparser.handle_element import ElementHandler

class USXTransformer:
    def __init__(self, book_common_name: str, book_metadata: Optional[Dict[str, Any]] = None, verbose: bool = False, verse_limit: Optional[int] = None):
        self.stopped_due_to_limit = False  # Flag to stop all output after verse limit
        self.book_common_name = book_common_name
        self.book_name = book_common_name  # Initialize book_name in __init__
        self.book_metadata = book_metadata
        self.footnote_letters = 'abcdefghijklmnopqrstuvwxyz'
        self.footnote_count = 0
        self.book_codes = load_book_codes()  # Load book codes before creating processors
        self.variant_handler = VariantHandler()
        self.footnote_processor = FootnoteProcessor(self)
        self.reference_processor = ReferenceProcessor(self)
        self.pending_footnote = None
        self.pending_variant = None
        self.verbose = verbose
        self.verse_limit = verse_limit
        # Statistics
        self.stats = {
            'verses_processed': 0,
            'footnotes_processed': 0,
            'references_processed': 0
        }
        self.processed_quotes = set()  # Track processed quotes globally
        self.current_quote = None  # Track the current quote being processed
        self.char_style_map = CHAR_STYLE_MAP
        self.logger = logging.getLogger(__name__)
        if verbose:
            self.logger.setLevel(logging.DEBUG)
        self.is_pericope = False
        self.element_handler = ElementHandler(self)

    def debug(self, message: str) -> None:
        if self.verbose:
            self.logger.debug(message)

    def list_character_styles(self, xml_file):
        """List all unique character styles in the XML file"""
        tree = etree.parse(xml_file)
        root = tree.getroot()
        styles = set()
        for style in root.xpath("//@AppliedCharacterStyle"):
            styles.add(style)
        return sorted(list(styles))

    def _get_usx_char_style(self, xml_style):
        """Convert XML character style to USX char style"""
        return self.char_style_map.get(xml_style, 'no')  # default to 'normal' if style not found

    def _process_footnote_content(self, footnote, parent_element):
        """Process the content of a footnote and create a note element."""
        return self.footnote_processor.process_footnote_content(footnote, parent_element)

    def _process_reference(self, text, current_chapter, verse_ref=None):
        """Process a single reference and return the USX ref element."""
        # DEPRECATED: Use ReferenceProcessor
        return self.reference_processor._process_reference(text, current_chapter, verse_ref)

    def _process_reference_patterns(self, text):
        """Process all reference patterns in a text."""
        # DEPRECATED: Use ReferenceProcessor
        return self.reference_processor._process_reference_patterns(text)

    def _add_text_or_element(self, parent, content):
        """Helper to add text or element to parent node."""
        if isinstance(content, str):
            # Apply format_text_content after concatenation to ensure proper spacing.
            if len(parent) > 0:
                current_tail = parent[-1].tail or ""
                new_tail = format_text_content(current_tail + content)
                parent[-1].tail = new_tail if new_tail else None # Use None for empty string
            else:
                current_text = parent.text or ""
                new_text = format_text_content(current_text + content)
                parent.text = new_text if new_text else None # Use None for empty string

        elif isinstance(content, etree._Element):
            # Ensure that if we append an element, its tail doesn't inadvertently merge
            # with the previous element's tail if it was just spaces.
            # This is mostly handled by how tails are processed in _process_paragraph_range.
            parent.append(content)
        # else: Consider logging a warning for unhandled content types

    def _add_processed_text(self, parent, processed_text):
        """Helper to add processed text or elements to parent node."""
        if not processed_text:
            return

        self.debug(f"Adding processed text: {processed_text}")

        # Keep track of last element to handle text between elements
        last_tail = None

        for part in processed_text:
            if isinstance(part, str):
                if last_tail is not None:
                    last_tail.tail = (last_tail.tail or '') + part
                else:
                    parent.text = (parent.text or '') + part
            elif isinstance(part, etree._Element):
                parent.append(part)
                last_tail = part

    def _find_title(self, root):
        # Find the first book title (mt) paragraph
        title_elem = root.find(
            ".//ParagraphStyleRange[@AppliedParagraphStyle='ParagraphStyle/Title']"
        )
        if title_elem is not None:
            parts: list[str] = []
            for node in title_elem.iter():
                tag = node.tag.lower()
                if tag == 'content' and node.text:
                    parts.append(node.text)
                elif tag == 'br':
                    parts.append(' ')
            title_text = ''.join(parts).strip()
            if title_text:
                return title_text
        return None

    def transform(self, xml_content: bytes) -> str:
        self.debug("Starting XML transformation...")
        # Store styles for later output
        self.found_styles = set()

        # Parse input XML as bytes
        parser = etree.XMLParser(remove_blank_text=True)
        root = etree.fromstring(xml_content, parser)
        self.debug("XML parsed successfully")

        # Collect styles during parsing but don't output yet
        for style in root.xpath("//@AppliedCharacterStyle"):
            self.found_styles.add(style)

        # Create USX output
        usx = etree.Element("usx", version="3.0")

        # Get book code
        book_code = self.book_codes.get(self.book_name)
        if not book_code:
            raise ValueError(f"Unknown book name: {self.book_name}")

        # 1. Add book identification (required)
        # Generate a self-closing <book ... /> tag (DO NOT use as a wrapper)
        etree.SubElement(usx, "book", code=book_code, style="id")

        # 2. Add book titles (at least one required)
        title = self._find_title(root)
        if title:
            para = etree.SubElement(usx, "para", style="mt")
            para.text = title
            # Add toc1 entry
            toc1 = etree.SubElement(usx, "para", style="mt1")
            toc1.text = self.book_name
            # Add toc2 entry with alternate names
            toc2 = etree.SubElement(usx, "para", style="mt2")
            toc2.text = title
        else:
            # Add minimal required title if none found
            para = etree.SubElement(usx, "para", style="mt")
            para.text = self.book_name

        # Add alternate names if available from metadata
        if self.book_metadata and 'alternate_names' in self.book_metadata:
            alternate_names = self.book_metadata['alternate_names']
            if alternate_names:
                para_toc2 = etree.SubElement(usx, "para", style="mt2")
                para_toc2.text = '; '.join(alternate_names)
                self.debug(f"Added alternate names: {', '.join(alternate_names)}")

        # Process main content
        parser_state = ParserState()
        current_para = None

        # Process all ParagraphStyleRange elements containing Normal style
        normal_ranges = root.xpath(".//ParagraphStyleRange[contains(@AppliedParagraphStyle, 'Normal')]")
        self.debug(f"Found {len(normal_ranges)} paragraph ranges to process")

        # Track if we've reached the verse limit
        reached_limit = False

        for para_range in normal_ranges:
            # Skip processing if we've already reached the limit
            if reached_limit:
                continue

            if not self._process_paragraph_range(para_range, usx, parser_state):
                self.debug(f"Stopped processing at verse {parser_state.verse} (limit: {self.verse_limit})")
                reached_limit = True

                # If we're stopping due to verse limit, make sure we only include chapter 1
                if self.verse_limit is not None and parser_state.chapter == "1":
                    # Remove any chapters after chapter 1
                    for chapter in usx.findall('.//chapter[@number]'):
                        if chapter.get('number') != "1":
                            usx.remove(chapter)
                    break

        # If we have a verse limit, ensure we only include up to that verse in chapter 1
        if self.verse_limit is not None:
            self.debug(f"Post-processing to ensure only first {self.verse_limit} verses of chapter 1 are included")

            # Remove any chapters after chapter 1
            for chapter in list(usx.findall('.//chapter[@number]')):
                if chapter.get('number') != "1":
                    parent = chapter.getparent()
                    if parent is not None:
                        parent.remove(chapter)

            # Remove any verses after verse 5 in chapter 1
            for verse in list(usx.findall('.//verse[@number]')):
                verse_num = verse.get('number')
                if verse_num and verse_num.isdigit() and int(verse_num) > self.verse_limit:
                    verse_para = verse.getparent()
                    if verse_para is not None:
                        parent = verse_para.getparent()
                        if parent is not None:
                            parent.remove(verse_para)
                        # Remove the verse itself
                        parent.remove(verse)

            # Remove any empty paragraphs
            for para in list(usx.findall('.//para')):
                if len(para) == 0 and (para.text is None or para.text.strip() == ""):
                    para.getparent().remove(para)

        # Post-process verse end tags that appear in wrong locations
        self._post_process_verse_end_tags(usx)

        # First generate the XML string with declaration
        xml_string = etree.tostring(usx, pretty_print=True, encoding='utf-8',
                                   xml_declaration=True, standalone=True)

        print("\nCharacter styles found:")
        print("--------------------------------------------------")
        for style in sorted(self.found_styles):
            print(f"- {style}")

        # Then decode it to a string
        return xml_string.decode('utf-8')

    def _post_process_verse_end_tags(self, root: etree._Element) -> None:
        """
        Post-process the XML to fix verse end tags that appear after a new para tag.
        Only move verse end tags when:
        1. They are the first element in a paragraph
        2. They have content after them (either more elements or text)
        3. There is no text content before them in the paragraph
        All other verse end tags stay where they are.
        TODO: This is a workaround for a bug in the USX parser. It should be removed when the bug is fixed.
        """
        for para in root.xpath(".//para"):
            if len(para) == 0:
                continue

            # Check if there's any text directly in the paragraph before the first element
            if para.text and para.text.strip():
                continue

            first_elem = para[0]
            if (first_elem.tag == 'verse' and first_elem.get('eid') is not None and
                (len(para) > 1 or (first_elem.tail and first_elem.tail.strip()))):
                prev_para = para.getprevious()
                if prev_para is not None and prev_para.tag == 'para':
                    first_elem.getparent().remove(first_elem)
                    prev_para.append(first_elem)

    def _handle_quote_content(self, text: str) -> str:
        """
        Returns the text content for an xot (OT quote) variant. This is a stub for now,
        but can be extended for additional logic such as formatting or annotation.
        """
        return text

    def _process_paragraph_range(self, para_range: etree._Element, parent_element: etree._Element, parser_state: ParserState) -> bool:
        """Process a paragraph range and appends the result to the parent_element.
        All parsing state is managed via parser_state.
        """
        # Check if we've already reached the verse limit
        if self.verse_limit is not None and parser_state.total_verses >= self.verse_limit:
            self.debug(f"Skipping processing as we've reached total verse limit {self.verse_limit} (total verses: {parser_state.total_verses})")
            return False

        # Don't auto-close verses at paragraph boundaries since they may span multiple paragraphs
        # Verses will be closed when we encounter a new verse number or at chapter boundaries
        parser_state.current_para = None
        pending_variant_milestone = None  # Track if a variant milestone needs to be created
        pending_footnote_element = None  # Store footnote to be attached before verse

        # --- Poetic Paragraph Style Mapping ---
        POETIC_STYLE_MAP = {
            "ParagraphStyle/Normal zweite Zeile": "q2",
            "ParagraphStyle/Normal dritte Zeile": "q3",
            "ParagraphStyle/Normal vierte Zeile": "q4",
            "ParagraphStyle/Normal fünfte Zeile": "q5",
            # Extend as needed
        }
        para_xml_style = para_range.get('AppliedParagraphStyle', '')
        poetic_style = POETIC_STYLE_MAP.get(para_xml_style)
        if poetic_style:
            # This is a poetic line (q2, q3, ...)
            vid = f"{self.book_codes.get(self.book_name, self.book_name)} {parser_state.chapter}:{parser_state.verse}"
            self.element_handler.set_current_para(
                parser_state, etree.SubElement(parent_element, 'para', style=poetic_style, vid=vid)
            )
        else:
            # Lookahead for next poetic paragraph
            next_para = para_range.getnext()
            next_style = next_para.get('AppliedParagraphStyle', '') if next_para is not None else ''
            if POETIC_STYLE_MAP.get(next_style):
                # Current paragraph should be q1
                self.element_handler.set_current_para(
                    parser_state, etree.SubElement(parent_element, 'para', style="q1")
                )
            else:
                self.element_handler.set_current_para(
                    parser_state, etree.SubElement(parent_element, 'para', style="p")
                )
            self.is_pericope = True

        def has_content(elem):
            """Helper function to check if an element has any content"""
            if elem.text and elem.text.strip():
                return True
            if len(elem) > 0:
                return True
            for child in elem:
                if child.tail and child.tail.strip():
                    return True
            return False

        # Pre-scan for footnotes that need to be attached to variants and verses
        for char_range in para_range.xpath(".//CharacterStyleRange"):
            style = char_range.get('AppliedCharacterStyle')
            if style == CharStyle.FOOTNOTE_NR_VARIANT:
                footnote = char_range.find('Footnote')
                if footnote is not None:
                    note_elem = self._process_footnote_content(footnote, parent_element)
                    if note_elem is not None:
                        pending_footnote_element = note_elem
                        self.debug("Pre-processed footnote for variant")
                    char_range.set('processed', 'true')

        # Now process all elements in order
        for char_range in para_range.xpath(".//CharacterStyleRange"):
            try:
                if (char_range.get('processed') == 'true' or
                    any(p.get('processed') == 'true' for p in char_range.iterancestors())):
                    continue

                style = char_range.get('AppliedCharacterStyle')
                # Only close the verse if we're about to process a new verse number
                next_elem = char_range.getnext()
                next_is_verse = (next_elem is not None and
                                next_elem.get('AppliedCharacterStyle') == CharStyle.VERSE_NR)

                # Update chapter number and create new chapter element
                if style == CharStyle.CHAPTER_NR:
                    content_elem = char_range.find('Content')
                    if content_elem is not None and content_elem.text is not None:
                        chapter_num = content_elem.text.strip()
                        prev_chapter = parser_state.chapter
                        parser_state.chapter = chapter_num

                        # Close previous chapter if exists
                        if prev_chapter:
                            # Close verse if it exists
                            self.element_handler.handle_verse_end(parser_state, parent_element, prev_chapter)
                            if parser_state.current_para is not None and not has_content(parser_state.current_para):
                                parent_element.remove(parser_state.current_para)
                            # Create new chapter element
                            chapter = etree.SubElement(parent_element, "chapter",
                                number=chapter_num,
                                style="c",
                                sid=f"{self.book_codes.get(self.book_name, self.book_name)} {chapter_num}")
                            # Create first verse of the chapter (verse 1)
                            self.element_handler.set_current_para(parser_state, etree.SubElement(parent_element, "para", style="p"))
                            self.element_handler.handle_verse_start("1", parser_state)
                            self.stats['verses_processed'] += 1
                            char_range.set('processed', 'true')
                            continue

                # Process <Br /> tag and verse numbers that might appear with it
                if char_range.find('Br') is not None:
                    style = char_range.get('AppliedCharacterStyle')
                    self.is_pericope = True # starts a new pericope

                    if style == CharStyle.VERSE_NR:
                        content_elem = char_range.find('Content')
                        if content_elem is not None and content_elem.text is not None:
                            verse_num = content_elem.text.strip()

                            # Handle character range content
                            try:
                                # Create paragraph if needed
                                if parser_state.current_para is None:
                                    self.element_handler.set_current_para(parser_state, etree.SubElement(parent_element, 'para', style='p'))

                                # Handle character range based on style
                                if style == 'CharacterStyle/Versnummer':
                                    verse_num = content_elem.text.strip()
                                    # Close previous verse if exists
                                    if parser_state.current_verse is not None:
                                        self.debug(f"Closing verse {parser_state.verse} before starting verse {verse_num}")
                                        #verse_eid = etree.Element("verse")
                                        #verse_eid.set("eid", f"{self.book_codes.get(self.book_name, self.book_name)} {parser_state.chapter}:{parser_state.verse}")
                                        #parser_state.current_para.append(verse_eid)
                                        #parser_state.current_verse = None
                                        self.element_handler.handle_verse_end(parser_state, parser_state.current_para)

                                    # Create new verse
                                    parser_state.verse = verse_num
                                    self.element_handler.handle_verse_start(verse_num, parser_state)
                                    self.stats['verses_processed'] += 1
                                    char_range.set('processed', 'true')
                                    continue
                                elif style == 'CharacterStyle/Fußnotenverweis im Text':
                                    # Handle footnote reference
                                    if pending_footnote_element is not None:
                                        # Add footnote before verse if needed
                                        if parser_state.current_verse is not None:
                                            parser_state.current_verse.addprevious(pending_footnote_element)
                                            pending_footnote_element = None
                                    continue
                                else:
                                    # Otherwise process as regular Br tag
                                    content_elems = char_range.findall('Content')
                                    if content_elems:
                                        text = ''.join(''.join(elem.xpath('.//text()')) for elem in content_elems)
                                        if text:
                                            if parser_state.current_para is not None:
                                                if len(parser_state.current_para) > 0:
                                                    if parser_state.current_para[-1].tail is None:
                                                        parser_state.current_para[-1].tail = text
                                                    else:
                                                        parser_state.current_para[-1].tail += text
                                                else:
                                                    if parser_state.current_para.text is None:
                                                        parser_state.current_para.text = text
                                                    else:
                                                        parser_state.current_para.text += text

                                # Only close the verse if the next element is a verse number
                                # Close verse if next element is a verse number
                                if next_is_verse:
                                    self.element_handler.handle_verse_end(parser_state, parent_element)
                                if parser_state.current_para is not None and not has_content(parser_state.current_para):
                                    parent_element.remove(parser_state.current_para)

                                # Create a new paragraph with appropriate style
                                para_xml_style = char_range.getparent().get('AppliedParagraphStyle')
                                if para_xml_style == 'ParagraphStyle/Normal zweite Zeile':
                                    # Map poetic second lines: q2
                                    vid = f"{self.book_codes.get(self.book_name, self.book_name)} {parser_state.chapter}:{parser_state.verse}"
                                    self.element_handler.set_current_para(parser_state, etree.SubElement(parent_element, 'para', style='q2', vid=vid))
                                else:
                                    # Default paragraph style
                                    self.element_handler.set_current_para(parser_state, etree.SubElement(parent_element, 'para', style="p"))
                                continue
                            except Exception as e:
                                self.debug(f"Error processing character range: {e}")
                                continue
                    # Otherwise process as regular Br tag
                    content_elems = char_range.findall('Content')
                    if content_elems:
                        text = ''.join(''.join(elem.xpath('.//text()')) for elem in content_elems)
                        if text:
                            if parser_state.current_para is not None:
                                if len(parser_state.current_para) > 0:
                                    if parser_state.current_para[-1].tail is None:
                                        parser_state.current_para[-1].tail = text
                                    else:
                                        parser_state.current_para[-1].tail += text
                                else:
                                    if parser_state.current_para.text is None:
                                        parser_state.current_para.text = text
                                    else:
                                        parser_state.current_para.text += text

                    # Only close the verse if the next element is a verse number
                    # Close verse if next element is a verse number
                    if next_is_verse:
                        self.element_handler.handle_verse_end(parser_state, parent_element)
                    if parser_state.current_para is not None and not has_content(parser_state.current_para):
                        parent_element.remove(parser_state.current_para)

                    # Create a new paragraph with appropriate style
                    para_xml_style = char_range.getparent().get('AppliedParagraphStyle')
                    if para_xml_style == 'ParagraphStyle/Normal zweite Zeile':
                        # Map poetic second lines: q2
                        vid = f"{self.book_codes.get(self.book_name, self.book_name)} {parser_state.chapter}:{parser_state.verse}"
                        self.element_handler.set_current_para(parser_state, etree.SubElement(parent_element, 'para', style='q2', vid=vid))
                    else:
                        # Default paragraph style
                        self.element_handler.set_current_para(parser_state, etree.SubElement(parent_element, 'para', style="p"))
                    continue

                # Check for variant markers before verse numbers
                if style == CharStyle.BRACKET:
                    content_elem = char_range.find('Content')
                    text = content_elem.text if content_elem is not None else None
                    if text and text.startswith('⌜'):  # Start of variant
                        parser_state.variant_count = getattr(parser_state, 'variant_count', 0) + 1
                        self.element_handler.handle_variant_start(char_range, parent_element, parser_state.current_para, 'va')
                    elif text and text.startswith('⌝'):  # End of variant
                        self.element_handler.handle_variant_end(char_range, parser_state.current_para, 'va')
                    else:
                        self._add_text_or_element(parser_state.current_para, text)
                    char_range.set('processed', 'true')
                    continue
                elif style == CharStyle.XOT:
                    content_elem = char_range.find('Content')
                    if content_elem is not None and content_elem.text:
                        text = content_elem.text
                        opens = text.count('»')
                        closes = text.count('«')
                        parser_state.xot_quote_depth = getattr(parser_state, 'xot_quote_depth', 0)
                        # START variant if depth currently 0 and we see an opening quote
                        if parser_state.xot_quote_depth == 0 and opens > 0:
                            self.element_handler.handle_variant_start(char_range, parent_element, parser_state.current_para, 'xot')
                        # Add the quote text
                        self._add_text_or_element(parser_state.current_para, self._handle_quote_content(text))
                        # Update depth after writing text
                        parser_state.xot_quote_depth += opens - closes
                        # END variant if depth returns to 0
                        parser_state.variant_stack = getattr(parser_state, 'variant_stack', [])
                        if parser_state.xot_quote_depth == 0 and closes > 0:
                            self.element_handler.handle_variant_end(char_range, parser_state.current_para, 'xot')
                    continue
                # Process verse numbers
                if style == CharStyle.VERSE_NR or style == CharStyle.VERSE_NR_PERICOPE:
                    content_elem = char_range.find('Content')
                    if content_elem is not None and content_elem.text is not None:
                        verse_num = content_elem.text.strip()
                        parser_state.verse_count = int(verse_num)
                        # Increment total verses counter
                        parser_state.total_verses += 1
                        # Stop processing if we've hit the verse limit
                        if self.verse_limit is not None and parser_state.total_verses >= self.verse_limit:
                            # Make sure to close the last verse
                            if parser_state.current_verse is not None:
                                self.debug(f"Closing final verse {parser_state.verse} before stopping at limit")
                                # Create paragraph if needed
                                if parser_state.current_para is None:
                                    self.element_handler.set_current_para(parser_state, etree.SubElement(parent_element, 'para', style='p'))
                                # Add verse end tag at the end of the current paragraph
                                verse_eid = etree.Element("verse")
                                verse_eid.set("eid", f"{self.book_codes.get(self.book_name, self.book_name)} {parser_state.chapter}:{parser_state.verse}")
                                parser_state.current_para.append(verse_eid)
                                parser_state.current_verse = None
                            self.debug(f"Stopped processing at verse {verse_num} (limit: {self.verse_limit})")
                            return True
                        # Ensure paragraph is open before handling verses
                        if parser_state.current_para is None:
                            self.debug("Creating new paragraph for verse")
                            self.element_handler.set_current_para(parser_state, etree.SubElement(parent_element, "para", style="p"))

                        # Close previous verse before starting new verse
                        if parser_state.current_verse is not None:
                            self.debug(f"Closing verse {parser_state.verse} before starting verse {verse_num}")
                            # Create paragraph if needed
                            if parser_state.current_para is None:
                                self.element_handler.set_current_para(parser_state, etree.SubElement(parent_element, 'para', style='p'))
                            # Pass the new verse's sid so we can place the end tag correctly
                            #new_verse_sid = f"{self.book_codes.get(self.book_name, self.book_name)} {parser_state.chapter}:{verse_num}"
                            # Add verse end tag before starting new verse
                            verse_eid = etree.Element("verse")
                            verse_eid.set("eid", f"{self.book_codes.get(self.book_name, self.book_name)} {parser_state.chapter}:{parser_state.verse}")
                            if parser_state.current_para is not None:
                                #self.element_handler.handle_verse_end(parser_state, parser_state.current_para)
                                parser_state.current_para.append(verse_eid)
                                if style == CharStyle.VERSE_NR_PERICOPE:
                                    parser_state.current_para.set("category", "pericope")
                            parser_state.current_verse = None

                        # Create new verse
                        self.debug(f"Starting verse {verse_num}")
                        parser_state.verse = verse_num
                        self.element_handler.handle_verse_start(verse_num, parser_state)

                        # If we have a pending footnote, insert it right after the verse element
                        if pending_footnote_element is not None:
                            parser_state.current_para.append(pending_footnote_element)
                            pending_footnote_element = None

                        self.stats['verses_processed'] += 1
                        char_range.set('processed', 'true')
                        continue
                # Process footnotes and references
                if style == CharStyle.FOOTNOTE_REF or style == CharStyle.FOOTNOTE_NR:
                    # Create paragraph if needed for footnote
                    if parser_state.current_para is None:
                        self.element_handler.set_current_para(parser_state, etree.SubElement(parent_element, 'para', style='p'))
                    footnote = self.element_handler.handle_regular_footnote(char_range, parser_state.current_para, parent_element, parser_state)
                    char_range.set('processed', 'true')
                    continue
                # Process regular text content
                content_elems = char_range.findall('Content')
                if content_elems:
                    text = ''.join(''.join(elem.xpath('.//text()')) for elem in content_elems)
                    usx_style = self.char_style_map.get(style, 'no')
                    if text.strip():
                        if parser_state.current_para is not None:
                            if usx_style != 'no':
                                char = etree.SubElement(parser_state.current_para, "char", style=usx_style)
                                char.text = text
                            else:
                                self._add_text_or_element(parser_state.current_para, text)
                char_range.set('processed', 'true')  # Mark as processed
            except Exception as e:
                self.debug(f"Error processing character range: {e}")
                if 'current_para' in str(e):
                    # If the error is related to current_para not being initialized
                    self.debug("Creating new paragraph for error recovery")
                    self.element_handler.set_current_para(parser_state, etree.SubElement(parent_element, 'para', style='p'))
                    try:
                        # Retry processing with new paragraph
                        if style == CharStyle.FOOTNOTE_REF or style == CharStyle.FOOTNOTE_NR:
                            footnote = self.element_handler.handle_regular_footnote(char_range, parser_state.current_para, parent_element, parser_state)
                        else:
                            content_elems = char_range.findall('Content')
                            if content_elems:
                                text = ''.join(''.join(elem.xpath('.//text()')) for elem in content_elems)
                                usx_style = self.char_style_map.get(style, 'no')
                                if text.strip():
                                    if usx_style != 'no':
                                        char = etree.SubElement(parser_state.current_para, "char", style=usx_style)
                                        char.text = text
                                    else:
                                        self._add_text_or_element(parser_state.current_para, text)
                        char_range.set('processed', 'true')
                    except Exception as retry_e:
                        self.debug(f"Error during retry: {retry_e}")
                continue
        # Final cleanup
        if parser_state.current_para is not None and not has_content(parser_state.current_para):
            parent_element.remove(parser_state.current_para)
        # Remove any inline closing <verse eid> tags in poetic paras
        if parser_state.current_para is not None:
            usx_para_style = parser_state.current_para.get('style', '')
            if usx_para_style.startswith('q'):
                for v in list(parser_state.current_para.findall('verse')):
                    if v.get('eid') and v.get('number') is None:
                        parser_state.current_para.remove(v)
        # Explicit verse end for non-q paras
        if next_is_verse:
            parser_state.close_verse(parent_element, self.book_codes, self.book_name)
        return True
