import re
import logging
import unicodedata
from lxml import etree

class ReferenceProcessor:
    PATTERNS = [
        # (type, pattern, priority)
        ('in_ref', r'(?<![\w\d])(in\s+\d+,\d+)', 0),
        ('verse_range', r'(?:die\s+)?(Vers(?:en?|e))\s+(\d+)\s+bis\s+(\d+)', 1),
        ('verse_range_inline', r'(\d+)\s+bis\s+(\d+)', 2),
        ('book_ref', r'([A-Za-zäöüß]+|[1-3]\s*[.]?\s*[A-Za-zäöüß]+)\s+(\d+[,\d-]*)', 3),
        ('vers_ref', r'(Vers(?:en)?)\s+(\d+,\d+)', 4),
        ('verse_list', r'(Vers(?:en?|e))\s+([\d.]+)', 5),
        ('v_ref', r'V\.\s*(\d+)', 6),
        ('siehe_ref', r'(siehe)\s+(\d+[,\d]*)', 7),
        ('direct_ref', r'(\d+,\d+)', 8),
        ('verse_num', r'(?<![,\d])(\d+)(?![,\d])', 9),
    ]

    STOP_WORDS = {
        'und', 'bis', 'oder', 'siehe', 'die', 'den', 'das', 'dem', 'der', 'des',
        'ein', 'eine', 'einer', 'einem', 'eines', 'auch', 'in', 'verglichen', 'vergleiche'
    }

    def __init__(self, usx):
        self.usx = usx
        self.logger = logging.getLogger(__name__)
        self.book_name = usx.book_name
        self.book_codes = usx.book_codes

    def _normalize(self, s):
        s = unicodedata.normalize('NFKD', s).encode('ASCII', 'ignore').decode('ASCII').lower()
        s = re.sub(r'[\s\.\-]', '', s)
        return s.strip()

    def _process_reference(self, text, current_chapter, ref_type=None):
        """
        Process a single reference string and return a <ref> element or "Not a reference".
        If ref_type is not given, try to auto-detect it.
        """
        if ref_type is None:
            for _type, pattern, _priority in self.PATTERNS:
                match = re.search(pattern, text, re.IGNORECASE)
                if match:
                    return self._build_ref_element(_type, match, current_chapter)
            # No pattern matched, fallback
            return None
        else:
            for _type, pattern, _priority in self.PATTERNS:
                if _type == ref_type:
                    match = re.search(pattern, text, re.IGNORECASE)
                    if match:
                        return self._build_ref_element(_type, match, current_chapter)
            return None
    def _get_short_book_code(self, book_name):
        # Your codes dict is long name -> short code, so invert it
        codes = getattr(self.usx, 'book_codes', None) or getattr(self, 'book_codes', None) or {}
        codes = {v: k for k, v in codes.items()}  # Now short code -> long name
        if not codes:
            return 'UNK'
        normalized_input = self._normalize(book_name)
        for short_code, long_name in codes.items():
            if normalized_input == self._normalize(short_code):
                return short_code
            if normalized_input == self._normalize(long_name):
                return short_code
        for short_code, long_name in codes.items():
            if normalized_input in self._normalize(short_code) or normalized_input in self._normalize(long_name):
                return short_code
        if codes:
            return next(iter(codes.keys()))
        return 'UNK'

    def _process_reference_patterns(self, text, current_chapter=None):
        """
        Parse references in text and return a list of alternating text and <ref> elements.
        """
        if not text:
            return [text]

        # Find all matches, record their positions, type, and match object
        all_matches = []
        for ref_type, pattern, priority in self.PATTERNS:
            for match in re.finditer(pattern, text, re.IGNORECASE):
                # For book_ref, ignore stop words as book names
                if ref_type == 'book_ref':
                    book_candidate = match.group(1).strip().lower()
                    if book_candidate in self.STOP_WORDS:
                        continue
                all_matches.append({
                    "start": match.start(),
                    "end": match.end(),
                    "type": ref_type,
                    "priority": priority,
                    "match": match,
                })

        # Sort by start, then by longest match, then by priority
        all_matches.sort(key=lambda m: (m['start'], -(m['end']-m['start']), m['priority']))

        # Remove overlapping matches (keep the highest priority/longest at each position)
        non_overlapping = []
        last_end = 0
        for m in all_matches:
            if m['start'] >= last_end:
                non_overlapping.append(m)
                last_end = m['end']

        # Build result: alternate text and <ref> elements
        result = []
        last_pos = 0
        for m in non_overlapping:
            if m['start'] > last_pos:
                result.append(text[last_pos:m['start']])
            ref_elem = self._build_ref_element(m['type'], m['match'], current_chapter)
            result.append(ref_elem)
            last_pos = m['end']
        if last_pos < len(text):
            result.append(text[last_pos:])

        return result

    def _build_ref_element(self, ref_type, match, current_chapter):
        ref_elem = etree.Element("ref")
        book_name = getattr(self.usx, 'book_name', None) or self.book_name
        short_code = self._get_short_book_code(book_name)

        logging.debug(f"Reference: {match.group(0)}, ref_type: {ref_type}, book: {book_name}, short_code: {short_code}, chapter: {current_chapter}")

        if ref_type == 'verse_range':
            prefix = match.group(1)
            start_verse = match.group(2)
            end_verse = match.group(3)
            ref_elem.text = f"{prefix} {start_verse} bis {end_verse}"
            ref_elem.set("loc", f"{short_code} {current_chapter},{start_verse}-{end_verse}")
        elif ref_type == 'verse_range_inline':
            start_verse = match.group(1)
            end_verse = match.group(2)
            ref_elem.text = f"{start_verse} bis {end_verse}"
            ref_elem.set("loc", f"{short_code} {current_chapter},{start_verse}-{end_verse}")
        elif ref_type == 'book_ref':
            book = match.group(1)
            verse = match.group(2)
            short_code = self._get_short_book_code(book)
            ref_elem.text = f"{book} {verse}"
            ref_elem.set("loc", f"{short_code} {verse}")
        elif ref_type == 'vers_ref':
            ref_elem.text = match.group(0)
            ref_elem.set("loc", f"{short_code} {current_chapter},{match.group(2)}")
        elif ref_type == 'verse_list':
            ref_elem.text = match.group(0)
            ref_elem.set("loc", f"{short_code} {current_chapter},{match.group(2)}")
        elif ref_type == 'v_ref':
            verse_num = match.group(1)
            ref_elem.text = f"V. {verse_num}"
            ref_elem.set("loc", f"{short_code} {current_chapter},{verse_num}")
        elif ref_type == 'siehe_ref':
            verse = match.group(2)
            ref_elem.text = match.group(0)
            if ',' not in verse and current_chapter is not None:
                verse = f"{current_chapter},{verse}"
            ref_elem.set("loc", f"{short_code} {verse}")
        elif ref_type == 'direct_ref':
            logging.debug(f"Direct reference: {match.group(0)}, ref_type: {ref_type}, book: {book_name}, short_code: {short_code}, chapter: {current_chapter}")
            verse = match.group(1)
            ref_elem.text = verse
            ref_elem.set("loc", f"{short_code} {verse}")
            logging.debug(f"Created reference: {etree.tostring(ref_elem, encoding='unicode')}")
        elif ref_type == 'verse_num':
            verse_num = match.group(1)
            ref_elem.text = verse_num
            ref_elem.set("loc", f"{short_code} {current_chapter},{verse_num}")
        elif ref_type == 'in_ref':
            ref_elem.text = match.group(1)
            # Extract the numeric part after "in"
            chapter_verse = re.sub(r'^in\s+', '', match.group(1), flags=re.IGNORECASE).strip()
            ref_elem.set("loc", f"{short_code} {chapter_verse}")
        else:
            logging.debug(f"Unknown reference type: {ref_type}, match: {match}")
            ref_elem.text = match.group(0)
        return ref_elem

    @staticmethod
    def result_to_string(result):
        parts = []
        for part in result:
            if isinstance(part, str):
                parts.append(part)
            else:
                parts.append(etree.tostring(part, encoding='unicode'))
        return ''.join(parts)
