# NOTE: All imports are absolute for CLI compatibility
# This is necessary to ensure compatibility with the command-line interface
import re
from typing import Dict

_ACE_CLEAN_RE = re.compile(r'<\?ACE \d+\?>')

def clean_text(txt: str) -> str:
    """Remove ACE artefacts from text."""
    return _ACE_CLEAN_RE.sub('', txt)

def format_text_content(text: str) -> str:
    """Format text content by removing special characters and normalizing whitespace.

    Args:
        text (str): Text to format

    Returns:
        str: Formatted text
    """
    if not text:
        return "" # Return empty string for None or empty input

    # Replace special characters with their ASCII equivalents
    replacements: Dict[str, str] = {
        '\ufeff': '',  # Zero-width no-break space (BOM)
        '\u200b': '',  # Zero-width space
        '\u200c': '',  # Zero-width non-joiner
        '\u200d': '',  # Zero-width joiner
        '\u2060': ''   # Word joiner
    }

    text_after_zwc_removal = text
    for old, new in replacements.items():
        text_after_zwc_removal = text_after_zwc_removal.replace(old, new)

    if not text_after_zwc_removal: # If only ZWC were present
        return ""

    # Check if the string (after ZWC removal) was purely whitespace
    if text_after_zwc_removal.isspace():
        return ' ' # Reduce any sequence of pure whitespace to a single space

    # Preserve leading space if original (after ZWC) had one
    original_had_leading_space = text_after_zwc_removal.startswith(' ')
    # Preserve trailing space if original (after ZWC) had one
    original_had_trailing_space = text_after_zwc_removal.endswith(' ')

    # Normalize internal whitespace: split by any whitespace and join with a single space.
    # This also strips all leading/trailing spaces.
    normalized_text = ' '.join(text_after_zwc_removal.split())

    # Restore leading space if it was stripped and original had it
    if original_had_leading_space and not normalized_text.startswith(' '):
        normalized_text = ' ' + normalized_text

    # Restore trailing space if it was stripped and original had it
    # and the text is not just a single space (which would be handled by isspace() above)
    if original_had_trailing_space and not normalized_text.endswith(' '):
        normalized_text = normalized_text + ' '

    return normalized_text
