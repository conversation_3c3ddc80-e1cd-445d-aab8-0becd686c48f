"""Integration tests for the USX parser."""
import os
import sys
import unittest
from unittest.mock import MagicMock
from lxml import etree

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import the module we're testing
from .references import ReferenceProcessor

class MockUSX:
    def __init__(self, book_name, book_codes):
        self.book_name = book_name
        self.book_codes = book_codes
        self.parser_state = type('', (), {'current_chapter': None})()
        self.stats = type('', (), {'references_processed': 0})()
        
    def debug(self, message):
        print(f"DEBUG:{message}")

class TestIntegration(unittest.TestCase):
    """Integration tests for reference processing."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.usx = MockUSX('Epheser', {'Eph': 'Epheser', 'Röm': 'Römer', '1Thes': '1.<PERSON><PERSON><PERSON><PERSON><PERSON>', 'Jak': '<PERSON><PERSON>'})
        self.processor = ReferenceProcessor(self.usx)
    
    def test_footnote_reference_processing(self):
        """Test that footnote references are processed correctly."""
        # Sample footnote content with references
        footnote_content = """
        Siehe auch Vers 3,8 und Röm 1,28. 
        In 3,8 wird dies näher erläutert. 
        Vergleiche auch 1Thes 4,3 und Jak 1,27.
        """
        
        # Process the content with chapter 4 as context
        processed = self.processor._process_reference_patterns(footnote_content, current_chapter=4)
        
        # Convert to string for easier assertion
        result = "".join(etree.tostring(e, encoding='unicode') if hasattr(e, 'tag') else str(e) 
                         for e in processed if e is not None)
        
        # Verify no stray <char style="fr"> elements
        self.assertNotIn('<char style="fr">', result, 
                        "Found stray <char style=\"fr\"> in processed output")
        
        # Verify that references are properly formatted
        self.assertIn('<ref loc="Eph 3,8">Vers 3,8</ref>', result)
        self.assertIn('<ref loc="Röm 1,28">Röm 1,28</ref>', result)
        self.assertIn('<ref loc="Eph 3,8">In 3,8</ref>', result)
        self.assertIn('<ref loc="1Thes 4,3">1Thes 4,3</ref>', result)
        self.assertIn('<ref loc="Jak 1,27">Jak 1,27</ref>', result)
    
    def test_verse_ranges(self):
        """Test that verse ranges are processed correctly."""
        # Enable debug output
        import sys
        import logging
        logging.basicConfig(stream=sys.stdout, level=logging.DEBUG, 
                          format='%(levelname)s:%(message)s',
                          force=True)
        
        # Debug processor initialization
        print("\n=== Processor Initialization ===")
        print(f"Book name: {self.processor.book_name}")
        print(f"Book codes: {self.processor.book_codes}")
        print(f"Current chapter: {self.processor.usx.parser_state.current_chapter if hasattr(self.processor.usx, 'parser_state') else 'None'}")
        
        # Enable debug logging for the processor
        self.processor.usx.debug = lambda msg: print(f"DEBUG:{msg}")
        
        # Test with 'bis' in the range
        content = "Vergleiche die Verse 12 bis 15 und 20 bis 25."
        print("\n=== Processing content:", repr(content))  # Debug
        
        # Print debug info about processor's patterns
        print("\n=== Processor Patterns ===")
        print("Patterns are defined in the _process_reference_patterns method")
        print(f"Processor class: {self.processor.__class__.__name__}")
        print(f"Book name: {self.processor.book_name}")
        print(f"Book codes: {self.processor.book_codes}")
        
        # Print the content being processed
        print(f"\n=== Processing content ===\n{content}")
        
        # Print the patterns that will be used
        print("\n=== Patterns that should match ===")
        patterns_to_try = [
            (r'(Vers(?:en?|e)|die Vers(?:en?|e))\s+(\d+)\s+bis\s+(\d+)', 'verse_range'),
            (r'(\d+)\s+bis\s+(\d+)', 'verse_range_inline'),
        ]
        
        import re
        for pattern, ref_type in patterns_to_try:
            print(f"\nTrying pattern: {pattern} ({ref_type})")
            for match in re.finditer(pattern, content):
                print(f"  - Match: {match.group(0)!r}")
                print(f"    Groups: {match.groups()}")
                print(f"    Span: {match.span()}")
        
        print("\n=== Starting processor ===")
        
        # Process the content
        processed = self.processor._process_reference_patterns(content, current_chapter=4)
        
        # Print the processed output for debugging
        print("\n=== Processing Results ===")
        result_parts = []
        for i, e in enumerate(processed):
            if e is not None:
                if hasattr(e, 'tag'):
                    part = etree.tostring(e, encoding='unicode')
                    print(f"  [{i}] Element <{e.tag}>: {part}")
                    result_parts.append(part)
                else:
                    print(f"  [{i}] Raw text: {repr(e)}")
                    result_parts.append(str(e))
        
        result = "".join(result_parts)
        print("\n=== Final result ===")
        print(repr(result))
        
        # Check for expected patterns
        expected1 = '<ref loc="Eph 4,12-15">Verse 12 bis 15</ref>'
        expected2 = '<ref loc="Eph 4,20-25">20 bis 25</ref>'
        
        print(f"\n=== Checking for expected patterns ===")
        print(f"Looking for: {expected1}")
        print(f"In result: {expected1 in result}")
        print(f"Looking for: {expected2}")
        print(f"In result: {expected2 in result}")
        
        self.assertIn(expected1, result)
        self.assertIn(expected2, result)

if __name__ == '__main__':
    unittest.main()
