# NOTE: All imports are absolute for CLI compatibility
# Absolute imports are used for CLI compatibility
from enum import Enum
import json
from pathlib import Path
from typing import Dict

class CharStyle(str, Enum):
    NO = 'CharacterStyle/$ID/[No character style]'
    EMPHASIS = 'CharacterStyle/Betonung'
    XOT = 'CharacterStyle/Direktes Zitat aus dem AT od. Paraphrase'
    BRACKET = 'CharacterStyle/Eckige <PERSON>lammer oben'
    FOOTNOTE_REF = 'CharacterStyle/Fußnotenverweis'
    FOOTNOTE_NR = 'CharacterStyle/Fußnotenzahl im Bibeltext'
    FOOTNOTE_NR_VARIANT = 'CharacterStyle/Fußnotenzahl vor Variante'
    CHAPTER_NR = 'CharacterStyle/Kapitelnummer'
    ITALIC_FOOTNOTE = 'CharacterStyle/Kursiv in den Fußnoten'
    VERSE_NR = 'CharacterStyle/Versnummer'
    VERSE_NR_PERICOPE = 'CharacterStyle/Versnummer bold unterstrichen'
    ADD = 'CharacterStyle/Wort nicht im Grundtext'
    ADD_FN = 'CharacterStyle/Wort nicht im Grundtext [Fn.]'

# Mapping from CharStyle to USX char style
CHAR_STYLE_MAP = {
    CharStyle.NO: 'no',
    CharStyle.EMPHASIS: 'em',
    CharStyle.XOT: 'xot',
    CharStyle.BRACKET: 'va',
    CharStyle.FOOTNOTE_REF: 'fm',
    CharStyle.FOOTNOTE_NR: 'fm',
    CharStyle.FOOTNOTE_NR_VARIANT: 'fm',
    CharStyle.CHAPTER_NR: 'c',
    CharStyle.ITALIC_FOOTNOTE: 'it',
    CharStyle.VERSE_NR: 'v',
    CharStyle.VERSE_NR_PERICOPE: 'v',
    CharStyle.ADD: 'add',
    CharStyle.ADD_FN: 'add',
}

def load_book_codes(json_path: str = None) -> Dict[str, str]:
    """Load book codes from external JSON file."""
    if json_path is None:
        json_path = str(Path(__file__).parent / 'book_codes.json')
    with open(json_path, encoding='utf-8') as f:
        return json.load(f)
