import pytest
from lxml import etree
from unittest.mock import MagicMock
from usxparser.references import ReferenceProcessor

class MockUSX:
    def __init__(self):
        self.book_name = "Epheser"
        self.book_codes = {
            "Epheser": "Eph", 
            "Roemer": "Rom", 
            "1.Mo<PERSON>": "1Mo", 
            "2.Mose": "2Mo",
            "3.Mose": "3Mo",
            "4.Mose": "4Mo",
            "5.Mose": "5Mo"
        }
        self.stats = {'references_processed': 0}
        self.parser_state = MagicMock()
        self.parser_state.current_chapter = 1
        
    def debug(self, message):
        pass

@pytest.fixture
def reference_processor():
    return ReferenceProcessor(MockUSX())

def test_process_v_reference(reference_processor):
    """Test processing V. N format references"""
    result = reference_processor._process_reference("V. 18", 1)
    assert result is not None
    assert result.get("loc") == "Eph 1,18"
    assert result.text == "V. 18"

def test_process_v_reference_no_chapter(reference_processor):
    """Test V. N format with explicit chapter"""
    result = reference_processor._process_reference("V. 18", 3)
    assert result is not None
    assert result.get("loc") == "Eph 3,18"

def test_process_verse_list(reference_processor):
    """Test verse list format"""
    result = reference_processor._process_reference("Verse 1.2.4", 1)
    assert result is not None
    assert result.get("loc") == "Eph 1,1.2.4"
    assert result.text == "Verse 1.2.4"

def test_process_direct_reference(reference_processor):
    """Test direct verse reference format"""
    result = reference_processor._process_reference("17,24", 2)
    assert result is not None
    assert result.get("loc") == "Eph 2,17,24"
    assert result.text == "17,24"

def test_process_siehe_reference(reference_processor):
    """Test 'siehe' reference format"""
    result = reference_processor._process_reference("siehe 3,16", 1)
    assert result is not None
    assert result.get("loc") == "Eph 3,16"
    assert result.text == "siehe 3,16"

def test_process_book_reference(reference_processor):
    """Test book reference format"""
    result = reference_processor._process_reference("Roemer 5,8", None)
    assert result is not None
    assert result.get("loc") == "Rom 5,8"
    assert result.text == "Roemer 5,8"

def test_process_book_reference_with_number(reference_processor):
    """Test book reference with book number"""
    result = reference_processor._process_reference("1.Mose 1,1", None)
    assert result is not None
    assert result.get("loc") == "1Mo 1,1"
    assert result.text == "1.Mose 1,1"

def test_process_invalid_reference(reference_processor):
    """Test that invalid references return the original text"""
    result = reference_processor._process_reference("Not a reference", 1)
    assert result == "Not a reference"

def test_process_reference_patterns_v_format(reference_processor):
    """Test processing text with V. N pattern"""
    result = reference_processor._process_reference_patterns("See V. 18 for more", 1)
    assert len(result) == 3  # ["See ", ref_element, " for more"]
    assert hasattr(result[1], 'tag') and result[1].tag == 'ref'
    assert result[1].get("loc") == "Eph 1,18"

def test_process_reference_patterns_direct_ref(reference_processor):
    """Test processing text with direct reference pattern"""
    result = reference_processor._process_reference_patterns("See 1,2 for details", 3)
    assert len(result) == 3  # ["See ", ref_element, " for details"]
    assert hasattr(result[1], 'tag') and result[1].tag == 'ref'
    assert result[1].get("loc") == "Eph 3,1,2"

def test_process_reference_patterns_multiple_refs(reference_processor):
    """Test processing text with multiple references"""
    result = reference_processor._process_reference_patterns("Compare V. 1 with 1,2", 2)
    assert len(result) == 5  # ["Compare ", ref1, " with ", ref2, ""]
    assert hasattr(result[1], 'tag') and result[1].tag == 'ref'
    assert hasattr(result[3], 'tag') and result[3].tag == 'ref'
    assert result[1].get("loc") == "Eph 2,1"
    assert result[3].get("loc") == "Eph 2,1,2"
