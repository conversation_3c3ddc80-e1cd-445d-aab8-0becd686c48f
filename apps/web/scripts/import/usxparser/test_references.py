import pytest
import re
from lxml import etree
from usxparser.references import ReferenceProcessor
from unittest.mock import MagicMock

class MockUSX:
    def __init__(self, book_name):
        self.book_name = book_name
        self.book_codes = {"Epheser": "<PERSON><PERSON>", "<PERSON>ö<PERSON>": "<PERSON><PERSON><PERSON>", "1Thessal<PERSON>her": "1Thes", "Jakob<PERSON>": "Jak"}
        self.debug_log = []
        self.stats = {'references_processed': 0}
    
    def debug(self, msg):
        self.debug_log.append(msg)

class ParserState:
    def __init__(self, current_chapter=None):
        self.current_chapter = current_chapter

# Test data
SAMPLE_FOOTNOTE = '''
<CharacterStyleRange AppliedCharacterStyle="CharacterStyle/Fußnotenzahl im Bibeltext">
    <Footnote>
        <ParagraphStyleRange AppliedParagraphStyle="ParagraphStyle/Footnote text">
            <CharacterStyleRange AppliedCharacterStyle="CharacterStyle/$ID/[No character style]">
                <Content>»ablegen müsst« (V. 22), »erneuert werden müsst« (V. 23) und »anziehen müsst« (V. 24) sind jeweils ein Infinitiv Aor., ein epexegetischer (erklärender) Infinitiv, wie auch in 3,8; Röm 1,28; 1Thes 4,3; Jak 1,27; siehe BDR § 394. Dass diese Infinitive nicht indikativisch, sondern modal zu verstehen sind, ergibt sich aus dem Appell, welcher aus den Versen 17 bis 20 spricht.</Content>
            </CharacterStyleRange>
        </ParagraphStyleRange>
    </Footnote>
</CharacterStyleRange>
'''

def create_processor(chapter=1):
    usx = MockUSX("Epheser")
    usx.parser_state = ParserState(current_chapter=chapter)
    return ReferenceProcessor(usx)

def test_verse_references():
    """Test basic verse reference patterns."""
    processor = create_processor(chapter=4)
    
    # Test V. N format
    result = processor._process_reference_patterns("V. 22", 4)
    print("\n[DEBUG LOG] test_verse_references (V. 22):\n" + "\n".join(processor.usx.debug_log))
    assert len(result) == 3  # Text before, ref, text after
    assert result[1].tag == "ref"
    assert result[1].get("loc") == "Eph 4,22"
    assert result[1].text == "V. 22"
    processor.usx.debug_log.clear()
    
    # Test Versen N bis M format
    result = processor._process_reference_patterns("Versen 17 bis 20", 4)
    print("\n[DEBUG LOG] test_verse_references (Versen 17 bis 20):\n" + "\n".join(processor.usx.debug_log))
    assert len(result) == 3  # Text before, ref, text after
    assert result[1].tag == "ref"
    assert result[1].get("loc") == "Eph 4,17-20"
    assert result[1].text == "Versen 17 bis 20"
    processor.usx.debug_log.clear()

def test_book_references():
    """Test references to other books."""
    processor = create_processor()
    
    # Test simple book reference
    result = processor._process_reference_patterns("Röm 1,28")
    print("\n[DEBUG LOG] test_book_references (Röm 1,28):\n" + "\n".join(processor.usx.debug_log))
    assert len(result) == 3  # Text before, ref, text after
    assert result[1].tag == "ref"
    assert result[1].get("loc") == "Röm 1,28"
    processor.usx.debug_log.clear()
    
    # Test book with number
    result = processor._process_reference_patterns("1Thes 4,3")
    print("\n[DEBUG LOG] test_book_references (1Thes 4,3):\n" + "\n".join(processor.usx.debug_log))
    assert len(result) == 3  # Text before, ref, text after
    assert result[1].tag == "ref"
    # The book code mapping is correct in the test processor
    assert result[1].get("loc") == "1Thes 4,3"
    processor.usx.debug_log.clear()

def test_siehe_references():
    """Test references with 'siehe' prefix."""
    processor = create_processor()
    
    result = processor._process_reference_patterns("siehe 3,8")
    print("\n[DEBUG LOG] test_siehe_references (siehe 3,8):\n" + "\n".join(processor.usx.debug_log))
    assert len(result) == 3  # Text before, ref, text after
    assert result[1].tag == "ref"
    assert result[1].get("loc") == "Eph 3,8"
    assert result[1].text == "siehe 3,8"
    processor.usx.debug_log.clear()

def test_verse_range_patterns():
    """Test specific verse range patterns that are failing in integration."""
    processor = create_processor(chapter=4)
    
    # Test 1: Verse range with 'Verse' prefix
    text = "Verse 12 bis 15"
    result = processor._process_reference_patterns(text, 4)
    
    # Debug output
    print("\n=== Test 1: Verse range with 'Verse' prefix ===")
    print(f"Input: {text}")
    print(f"Result: {result}")
    if len(result) > 1 and hasattr(result[1], 'tag') and result[1].tag == 'ref':
        print(f"  Found ref: loc={result[1].get('loc')}, text={result[1].text}")
    
    # Test 2: Verse range with 'Vers' prefix
    text = "Vers 3,8"
    result = processor._process_reference_patterns(text, 4)
    
    print("\n=== Test 2: Verse reference with 'Vers' prefix ===")
    print(f"Input: {text}")
    print(f"Result: {result}")
    if len(result) > 1 and hasattr(result[1], 'tag') and result[1].tag == 'ref':
        print(f"  Found ref: loc={result[1].get('loc')}, text={result[1].text}")
    
    # Test 3: Inline verse range
    text = "12 bis 15"
    result = processor._process_reference_patterns(text, 4)
    
    print("\n=== Test 3: Inline verse range ===")
    print(f"Input: {text}")
    print(f"Result: {result}")
    if len(result) > 1 and hasattr(result[1], 'tag') and result[1].tag == 'ref':
        print(f"  Found ref: loc={result[1].get('loc')}, text={result[1].text}")

def test_footnote_processing():
    """Test processing of a complete footnote."""
    from lxml import etree
    
    # Parse the sample footnote
    root = etree.fromstring(SAMPLE_FOOTNOTE)
    content = root.xpath(".//Content")[0].text
    
    # Process the content
    processor = create_processor(chapter=4)
    processed = processor._process_reference_patterns(content, 4)
    
    # Convert to string for easier assertion
    result = "".join(
        etree.tostring(e, encoding="unicode") if hasattr(e, 'tag') else e 
        for e in processed
    )
    
    # Check for expected patterns
    assert '<ref loc="Eph 4,22">V. 22</ref>' in result
    assert '<ref loc="Eph 4,23">V. 23</ref>' in result
    assert '<ref loc="Eph 4,24">V. 24</ref>' in result
    assert '<ref loc="Eph 3,8">in 3,8</ref>' in result
    assert '<ref loc="Röm 1,28">Röm 1,28</ref>' in result
    assert '<ref loc="1Thes 4,3">1Thes 4,3</ref>' in result
    assert '<ref loc="Jak 1,27">Jak 1,27</ref>' in result
    assert '<ref loc="Eph 4,17-20">Versen 17 bis 20</ref>' in result
    
    # Ensure no stray char elements
    assert '<char style="fr">' not in result
