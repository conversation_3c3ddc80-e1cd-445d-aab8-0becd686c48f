from dataclasses import dataclass, field
from typing import List

@dataclass
class ParserState:
    # State tracking
    chapter: str = "1"
    verse: str = "1"
    xot_depth: int = 0
    variant_stack: List[str] = field(default_factory=list)
    variant_count: int = 0
    verse_count: int = 0
    total_verses: int = 0  # Track total verses across all chapters

    # Current element tracking
    current_verse: object = None  # lxml Element
    current_para: object = None   # lxml Element
    current_chapter: object = None # lxml Element
