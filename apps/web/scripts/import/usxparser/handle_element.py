from lxml import etree
from typing import Tu<PERSON>, Optional, Any, Dict, List
from .parser_state import ParserState

class ElementHandler:
    def __init__(self, transformer):
        self.transformer = transformer

    def debug(self, message: str) -> None:
        """Log debug messages through the transformer's logger"""
        self.transformer.debug(f"[ElementHandler] {message}")

    def handle_verse_end(self, parser_state: ParserState, parent_element: etree._Element, prev_chapter: str = None) -> None:
        """Close the current verse if it exists by adding verse end tag at the correct position"""
        if getattr(self.transformer, 'stopped_due_to_limit', False):
            self.debug("Stopped due to verse limit, not closing verse.")
            return
        chapter = parser_state.chapter if prev_chapter is None else prev_chapter
        self.debug(f"Closing verse {parser_state.verse} in chapter {chapter}")
        if parser_state.current_verse is not None:
            # Create verse end tag
            verse_eid = etree.Element("verse")
            verse_eid.set("eid", f"{self.transformer.book_codes.get(self.transformer.book_name, self.transformer.book_name)} {chapter}:{parser_state.verse}")

            verse_para = parser_state.current_verse.getparent()
            if verse_para is not None:
                # Place <verse eid=.../> inline as a real XML element after the last text or element in the paragraph
                last = None
                if len(verse_para) > 0:
                    last = verse_para[-1]
                if last is not None:
                    # For verse 2, ensure a space before <verse eid=.../> if not present
                    if parser_state.verse == '2':
                        # Try to add a space to the previous node's tail or text if needed
                        if last.tail is not None:
                            if not last.tail.endswith(' '):
                                last.tail += ' '
                        elif last.text is not None:
                            if not last.text.endswith(' '):
                                last.text += ' '
                        else:
                            last.tail = ' '
                    # Insert after the last child
                    last.addnext(verse_eid)
                else:
                    # If no children, append as first child
                    verse_para.append(verse_eid)
            else:
                # Fallback: append to parent element if verse paragraph not found
                parent_element.append(verse_eid)
                self.debug("WARNING: Could not find verse paragraph, appending to parent")

            # Clear the current verse reference
            parser_state.current_verse = None
            self.debug("Verse closed successfully")
        else:
            self.debug("ERROR: No parent element to add verse end tag to")

    def handle_verse_start(self, verse_num: str, parser_state: ParserState) -> None:
        """Create a new verse element, closing any existing verse first"""
        if getattr(self.transformer, 'stopped_due_to_limit', False):
            self.debug("Stopped due to verse limit, not starting new verse.")
            return
        self.debug(f"Starting new verse {verse_num} in chapter {parser_state.chapter}")

        # Create the sid for the new verse
        new_verse_sid = f"{self.transformer.book_codes.get(self.transformer.book_name, self.transformer.book_name)} {parser_state.chapter}:{verse_num}"

        # Close existing verse if present
        if parser_state.current_verse is not None:
            self.debug("Found existing verse, closing it first")
            # Find the original paragraph where the verse started
            verse_para = parser_state.current_verse.getparent()
            if verse_para is not None:
                # Always close verse in its original paragraph, never in the new one
                self.handle_verse_end(parser_state, verse_para, new_verse_sid)
            else:
                self.debug("ERROR: Could not find parent paragraph for verse close")

        # Create new verse
        if parser_state.current_para is None:
            self.debug("ERROR: No current paragraph to add verse to")
            return

        verse = etree.Element("verse")
        verse.set("style", "v")
        verse.set("sid", new_verse_sid)
        verse.set("number", verse_num)
        parser_state.current_para.append(verse)
        parser_state.current_verse = verse
        parser_state.verse = verse_num

    def handle_variant_start(self, char_range: etree._Element, parent_element: etree._Element, current_para: etree._Element, variant_type: str) -> Tuple[etree._Element, Optional[etree._Element]]:
        """Handle the start of a variant section"""
        self.debug(f"Starting variant of type {variant_type}")
        result = self.transformer.variant_handler.start_variant(parent_element, current_para, variant_type)
        self.debug("Variant section started")
        return result

    def handle_variant_end(self, char_range: etree._Element, current_para: etree._Element, variant_type: str) -> etree._Element:
        """Handle the end of a variant section"""
        self.debug(f"Ending variant of type {variant_type}")
        result = self.transformer.variant_handler.end_variant(current_para, variant_type)
        self.debug("Variant section ended")
        return result

    def handle_ot_quote(self, char_range: etree._Element, current_para: etree._Element, parent_element: etree._Element) -> etree._Element:
        """Handle an Old Testament quote"""
        return self.transformer.variant_handler.handle_ot_quote(
            char_range,
            current_para,
            parent_element
        )

    def handle_variant_bracket(self, char_range: etree._Element, current_para: etree._Element, parent_element: etree._Element) -> etree._Element:
        """Handle a variant bracket"""
        return self.transformer.variant_handler.handle_variant_bracket(
            char_range,
            current_para,
            parent_element
        )

    def handle_pre_variant_footnote(self, char_range: etree._Element, current_para: etree._Element, parent_element: etree._Element) -> Optional[etree._Element]:
        """Process a footnote that appears before a variant"""
        footnote = self.transformer.footnote_processor.process_footnote(char_range)
        if footnote is not None:
            note = self.transformer.footnote_processor.process_footnote_content(footnote, current_para or parent_element)
            if note is not None and current_para is not None:
                current_para.append(note)
            return note
        return None

    def handle_regular_footnote(self, char_range: etree._Element, current_para: etree._Element, parent_element: etree._Element, parser_state: Optional[ParserState] = None) -> Optional[etree._Element]:
        """Process a regular footnote"""
        # Use explicit is not None check instead of 'or' with lxml elements
        target_element = current_para if current_para is not None else parent_element
        note = self.transformer.footnote_processor.process_footnote(char_range, target_element, parser_state)
        if note is not None:
            if current_para is not None:
                current_para.append(note)
            elif parent_element is not None:
                parent_element.append(note)
        return note

    def set_current_para(self, parser_state: ParserState, para: etree._Element, style: str = None) -> None:
        """Set the current paragraph element in the parser state

        Args:
            parser_state (ParserState): The current parser state
            para (etree._Element): The paragraph element to set as current
            style (str, optional): Style to apply to the paragraph
        """
        parser_state.current_para = para
        if style is not None and para is not None:
            para.set('style', style)

    def set_previous_para(self, para_element: etree._Element, **kwargs) -> bool:
        """Convenience function to modify the previous paragraph tag from any given paragraph tag.

        Args:
            para_element (etree._Element): The current paragraph element to start from
            **kwargs: Attributes to set on the previous paragraph element (e.g. style='p', category='pericope')

        Returns:
            bool: True if a previous paragraph was found and modified, False otherwise
        """
        if para_element is None or para_element.getparent() is None:
            return False

        # Get parent element and find all para elements
        parent = para_element.getparent()
        paras = parent.findall('para')

        # Find the index of our current para
        try:
            current_index = paras.index(para_element)
        except ValueError:
            return False

        # If we're the first para or no previous para exists, return False
        if current_index == 0:
            return False

        # Get the previous para and modify its attributes
        prev_para = paras[current_index - 1]
        for key, value in kwargs.items():
            prev_para.set(key, str(value))

        return True

    def handle_plain_text(self, char_range: etree._Element, current_para: etree._Element, parent_element: etree._Element) -> bool:
        """Handle plain text content"""
        content_elems = char_range.findall('Content')
        if not content_elems:
            self.transformer.debug("[PlainText] No <Content> elements found for char_range.")
            return False

        # Collect and clean text parts, preserving spaces between content blocks
        text_parts = []
        for i, elem in enumerate(content_elems):
            cleaned = ''.join(elem.xpath('.//text()'))
            if i == 0:  # First element
                cleaned = cleaned.lstrip()
            if i == len(content_elems) - 1:  # Last element
                cleaned = cleaned.rstrip()
            text_parts.append(cleaned)

        text = ''.join(text_parts)
        if not text:
            self.transformer.debug("[PlainText] No text content found.")
            return False

        usx_style = self.transformer.char_style_map.get(char_range.get('AppliedCharacterStyle', ''), 'no')
        self.transformer.debug(f"[PlainText] Extracted text: '{text}' with usx_style: '{usx_style}'")

        # Get the current chapter number as a string from parser state, not the lxml element
        current_chapter = None
        if hasattr(self.transformer, 'parser_state'):
            # parser_state.chapter is the string (e.g., '3'), parser_state.current_chapter is an lxml element
            current_chapter = getattr(self.transformer.parser_state, 'chapter', None)
            self.transformer.debug(f"[PlainText] Current chapter from parser state: {current_chapter}")

        if current_para is not None:
            if usx_style != 'no':
                char_elem = etree.SubElement(current_para, 'char', style=usx_style)
                char_elem.text = text
                self.transformer.debug(f"[PlainText] Added <char style='{usx_style}'> to current_para with text: '{text}'")
            else:
                processed = self.transformer.reference_processor._process_reference_patterns(text, current_chapter=current_chapter)
                self.transformer._add_processed_text(current_para, processed)
                self.transformer.debug(f"[PlainText] Added plain or referenced text to current_para: '{text}' with chapter {current_chapter}")
        elif parent_element is not None:
            if usx_style != 'no':
                char_elem = etree.SubElement(parent_element, 'char', style=usx_style)
                char_elem.text = text
                self.transformer.debug(f"[PlainText] Added <char style='{usx_style}'> to parent_element with text: '{text}'")
            else:
                processed = self.transformer.reference_processor._process_reference_patterns(text, current_chapter=current_chapter)
                self.transformer._add_processed_text(parent_element, processed)
                self.transformer.debug(f"[PlainText] Added plain or referenced text to parent_element: '{text}' with chapter {current_chapter}")
        else:
            self.transformer.debug("[PlainText] No valid parent to add text to.")

        char_range.set('processed', 'true')
        return True
