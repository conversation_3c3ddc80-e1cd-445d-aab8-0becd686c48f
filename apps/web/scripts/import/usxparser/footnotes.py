# NOTE: All imports are absolute for CLI compatibility
# All internal imports are updated to use absolute imports with the 'usxparser.' prefix for CLI compatibility
"""
FootnoteProcessor: Handles all footnote logic for USX transformation.
"""
from __future__ import annotations
from lxml import etree
from typing import Any, Optional
import logging
import re
from usxparser.style_maps import CHAR_STYLE_MAP, CharStyle
from usxparser.parser_state import ParserState

class FootnoteProcessor:
    def __init__(self, usx):
        self.usx = usx
        self.logger = logging.getLogger(__name__)

    def make_note_from_cr(self, char_range, current_para, parser_state: ParserState = None) -> Optional[etree.Element]:
        """
        Create a <note> element from a CharacterStyleRange (char_range),
        mimicking the working logic from xml2usx.py _process_footnote.
        parser_state: Pass ParserState for chapter/verse context.
        """
        footnote = char_range.find('Footnote')
        if footnote is None:
            return None

        # Check if this is a footnote that should be attached to a variant
        style = char_range.get('AppliedCharacterStyle', '')
        if style == CharStyle.FOOTNOTE_NR_VARIANT:
            self.usx.pending_footnote = footnote
            return None

        note = etree.Element("note", style="f")
        # Use parser_state for chapter/verse context
        chapter = parser_state.chapter if parser_state else None
        verse = parser_state.verse if parser_state else None

        footnote_letter = self.usx.footnote_letters[self.usx.footnote_count % len(self.usx.footnote_letters)]
        note.set("caller", footnote_letter)

        content_added = False
        last_element = None

        for para in footnote.xpath(".//ParagraphStyleRange"):
            char_ranges = para.xpath(".//CharacterStyleRange")
            for cr in char_ranges:
                text_elem = cr.find('Content')
                if text_elem is not None:
                    text = ''.join(text_elem.xpath('.//text()'))
                    text = re.sub(r'<\?ACE \d+\?>', '', text)
                    if text.strip():
                        style = cr.get('AppliedCharacterStyle')
                        content_added = True
                        if style != CharStyle.NO:
                            usx_style = CHAR_STYLE_MAP.get(style, 'no')
                            if usx_style != 'no':
                                char = etree.SubElement(note, "char", style=usx_style)
                                char.text = text
                                last_element = char
                            else:
                                self.usx._add_text_or_element(note, text)
                        else:
                            # Get current chapter from parser_state if available
                            current_chapter = parser_state.chapter if parser_state else None
                            processed_text = self.usx.reference_processor._process_reference_patterns(
                                text,
                                current_chapter=current_chapter
                            )
                            if isinstance(processed_text, list):
                                for part in processed_text:
                                    if isinstance(part, str):
                                        if last_element is not None:
                                            if last_element.tail is None:
                                                last_element.tail = part
                                            else:
                                                last_element.tail += part
                                        else:
                                            if note.text is None:
                                                note.text = part
                                            else:
                                                note.text += part
                                    else:
                                        note.append(part)
                                        last_element = part
                            else:
                                if last_element is not None:
                                    if last_element.tail is None:
                                        last_element.tail = processed_text
                                    else:
                                        last_element.tail += processed_text
                                else:
                                    if note.text is None:
                                        note.text = processed_text
                                    else:
                                        note.text += processed_text
        if content_added:
            self.usx.footnote_count += 1
            self.usx.stats['footnotes_processed'] += 1
            footnote.set('processed', 'true')
            self.usx.debug("Footnote processed and marked")
            return note
        return None

    def process_footnote(self, char_range, parent_element, parser_state: ParserState = None):
        """Process a footnote element (main entry for normal footnotes)."""
        # Use parser_state for chapter/verse context if needed in the future
        note = self.make_note_from_cr(char_range, parent_element, parser_state)
        return note

    def process_footnote_content(self, footnote, parent_element):
        """Process the content of a footnote and create a note element (for variants, etc)."""
        note = etree.Element("note")
        note.set("style", "f")
        note.set("caller", "o")  # Use 'o' as caller for variant footnotes
        content_added = False

        # Get current chapter from parser_state if available
        current_chapter = None
        if hasattr(self.usx, 'parser_state') and self.usx.parser_state:
            current_chapter = self.usx.parser_state.current_chapter

        # Process all text content first
        text_content = []
        for para in footnote.xpath(".//ParagraphStyleRange"):
            for cr in para.xpath(".//CharacterStyleRange"):
                text_elem = cr.find('Content')
                if text_elem is not None and text_elem.text:
                    text_content.append(text_elem.text)

        # Process the combined text for references
        if text_content:
            combined_text = ' '.join(text_content)
            processed_text = self.usx.reference_processor._process_reference_patterns(
                combined_text,
                current_chapter=current_chapter
            )

            # Add processed content to note
            if isinstance(processed_text, list):
                for part in processed_text:
                    self.usx._add_text_or_element(note, part)
            else:
                self.usx._add_text_or_element(note, processed_text)

            content_added = True

        if content_added:
            self.usx.footnote_count += 1
            self.usx.stats['footnotes_processed'] += 1
            footnote.set('processed', 'true')
            return note
        return None
