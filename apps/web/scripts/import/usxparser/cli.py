#!/usr/bin/env python
# NOTE: All imports are absolute for CLI compatibility
"""
CLI for XML→USX Bible text transformation.
Handles argument parsing, file I/O, and logging setup.
"""
import argparse
import logging
import sys
import time
from pathlib import Path
from concurrent.futures import ProcessPoolExecutor, as_completed
import os
from usxparser.usx_transformer import USXTransformer

def process_one_book(xml_path: str, book_name: str, verbose: bool, verse_limit: int | None, log_level: str = 'INFO') -> tuple[str, bool, str]:
    """Process a single book and return (book_name, success, message)."""
    try:
        # Always enable verbose mode when in debug
        is_debug = log_level.upper() == 'DEBUG'
        transformer = USXTransformer(book_name, verbose=verbose or is_debug, verse_limit=verse_limit)
        with open(xml_path, 'rb') as f:
            xml_content = f.read()
        t0 = time.time()
        usx = transformer.transform(xml_content)
        t1 = time.time()
        out_path = Path(xml_path).with_suffix('.usx')
        with open(out_path, 'w', encoding='utf-8') as f:
            f.write(usx)
        msg = f"Output written to {out_path} (Execution time: {t1-t0:.2f}s)"
        return (book_name, True, msg)
    except Exception as e:
        return (book_name, False, f"Error: {str(e)}")

def main():
    parser = argparse.ArgumentParser(description='Transform XML to USX format')
    parser.add_argument('source_xml_path', nargs='?', help='Path to the source XML file (or directory for --parallel)')
    parser.add_argument('book_name', nargs='?', help='Name of the book (ignored for --parallel)')
    parser.add_argument('-v', '--verbose', action='store_true', help='Enable verbose output')
    parser.add_argument('--verse-limit', type=int, help='Stop processing after this verse number')
    parser.add_argument('--log-level', default='INFO', help='Set log level (DEBUG, INFO, WARNING, ERROR)')
    parser.add_argument('--parallel', action='store_true', help='Process all books in parallel (source_xml_path must be a directory)')
    parser.add_argument('--max-workers', type=int, default=4, help='Maximum parallel workers (default: 4)')
    args = parser.parse_args()

    # Set up logging configuration
    log_level = getattr(logging, args.log_level.upper(), logging.INFO)
    logging.basicConfig(level=log_level, format='%(levelname)s: %(message)s')

    # Add file handler for debug logging
    if log_level == logging.DEBUG:
        log_dir = Path('../../storage/logs')
        log_dir.mkdir(parents=True, exist_ok=True)
        file_handler = logging.FileHandler(log_dir / 'transformer.log')
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
        logging.getLogger().addHandler(file_handler)

    if args.parallel:
        # Batch mode: process all books in directory
        parent_dir = args.source_xml_path or os.environ.get("BIBLE_DIR", "bibeltext")
        parent = Path(parent_dir)
        if not parent.is_dir():
            print(f"Directory not found: {parent}", file=sys.stderr)
            sys.exit(1)
        xml_files = list(parent.glob("*/**.xml"))
        if not xml_files:
            print(f"No XML files found in {parent}", file=sys.stderr)
            sys.exit(1)
        print(f"Processing {len(xml_files)} books in parallel (max_workers={args.max_workers})...")
        t0 = time.time()
        results = []
        with ProcessPoolExecutor(max_workers=args.max_workers) as executor:
            futures = {executor.submit(process_one_book, str(xml), xml.stem, args.verbose, args.verse_limit, args.log_level): xml.stem for xml in xml_files}
            for future in as_completed(futures):
                book, ok, msg = future.result()
                results.append((book, ok, msg))
                print(f"[{book}] {'OK' if ok else 'FAIL'}: {msg}")
        t1 = time.time()
        oks = sum(1 for _, ok, _ in results if ok)
        fails = sum(1 for _, ok, _ in results if not ok)
        print(f"\nCompleted {len(results)} books in {t1-t0:.2f}s: {oks} OK, {fails} failed.")
        if fails:
            sys.exit(1)
        return

    # Single-book mode (default)
    if not args.source_xml_path or not args.book_name:
        parser.print_help()
        sys.exit(1)

    book_name = args.book_name
    xml_path = args.source_xml_path
    print(f"Starting transformation of {xml_path}")
    book, ok, msg = process_one_book(xml_path, book_name, args.verbose, args.verse_limit, args.log_level)
    print(msg)
    if not ok:
        sys.exit(1)

if __name__ == '__main__':
    main()
