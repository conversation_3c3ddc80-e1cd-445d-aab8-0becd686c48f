# Bible Text Import Pipeline

This directory contains scripts and modules for importing and transforming Bible text files into USX XML format. There are two main approaches:

- **Modern Python module:** [`usxparser`](./usxparser/) — recommended for all new imports
- **Legacy script:** [`../xml2usx.py`](../xml2usx.py) — for compatibility with older workflows

Both tools convert XML-based source files into standardized USX output, supporting advanced features like text variants and footnotes.

---

## 1. Modern Import: `usxparser` Module

### Features

- Fast, parallelized import of multiple books
- Handles text variants, footnotes, and references
- Extensible and strictly typed (Python 3.10+ recommended)
- CLI and library usage

### Installation & Setup

A Python virtual environment is already provided as `venv/` in this directory. You can activate it directly:

```sh
source venv/bin/activate
```

If you want to recreate the environment (e.g., for a fresh install), you may delete the existing `venv/` folder and run:

```sh
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

> If `requirements.txt` is missing, install dependencies manually (e.g., `lxml`).

### Dependency Management

This project uses modern Python packaging with a [`pyproject.toml`](./pyproject.toml) file, which lists all dependencies and defines the CLI entry point.

To install dependencies and make the CLI available as a command, run (from this directory):

```sh
pip install .
```

Alternatively, you may use `requirements.txt` if present, but `pyproject.toml` is the authoritative source for dependencies and CLI scripts.

- After installation, you can run the CLI as described above, or as a script via `usxparser ...` if installed in your environment.

### Output Validation (Optional)

You can validate the resulting USX files using the provided [`usxValidator.py`](./usxValidator.py) script and the [`usx.rng`](./usx.rng) RelaxNG schema:

```sh
python usxValidator.py
```

- By default, `usxValidator.py` validates `../../bibeltext/Markus/Markus.usx` against the schema.
- Edit the script to validate other USX files as needed.
- **Note:** Validation is not automatically performed after import; you must run this check manually if you want to confirm schema compliance.

### Usage: Command Line

From this directory, run:

#### Single Book

```sh
python -m usxparser.cli path/to/source.xml "Book Name"
```

- `path/to/source.xml`: Path to the XML file
- `"Book Name"`: Name of the book (e.g., `Markus`)

#### Batch Import (Parallel)

```sh
python -m usxparser.cli path/to/xml_dir --parallel
```

- `path/to/xml_dir`: Directory containing XML files (one per book)
- Use `--max-workers N` to set parallelism (default: 4)

#### Options

- `-v`, `--verbose`: Enable verbose output
- `--verse-limit N`: Stop after processing N verses
- `--log-level LEVEL`: Set log level (`DEBUG`, `INFO`, `WARNING`, `ERROR`)
- `--parallel`: Process all books in a directory in parallel
- `--max-workers N`: Number of parallel workers (default: 4)

#### Example

```sh
python -m usxparser.cli bibeltext/Markus.xml Markus --verbose
python -m usxparser.cli bibeltext/ --parallel --max-workers 8
```

### Library Usage

You can also import and use the `USXTransformer` class directly in Python scripts for advanced workflows.

---

## 2. Legacy Import: `xml2usx.py`

This script is retained for compatibility with older pipelines. It supports basic XML→USX conversion, including text variants and footnotes, but does not support parallel import or advanced logging.

### Usage

```sh
python ../xml2usx.py path/to/source.xml "Book Name" [options]
```

- `path/to/source.xml`: Path to the XML file
- `"Book Name"`: Name of the book (e.g., `Markus`)

#### Legacy Options

- `-v`, `--verbose`: Enable verbose output
- `--verse-limit N`: Stop after processing N verses

#### Legacy Example

```sh
python ../xml2usx.py bibeltext/Markus.xml Markus --verbose
```

---

## Notes & Best Practices

- Always use a virtual environment for Python dependencies
- The modern `usxparser` module is recommended for all new imports
- For batch imports, prefer the parallel mode for speed
- USX output files are written alongside the input XML files (same name, `.usx` extension)
- Both tools support error reporting and will print statistics after import

---

## Troubleshooting

- Make sure all dependencies are installed (see `requirements.txt`)
- For encoding issues, ensure your terminal and source files are UTF-8
- For advanced debugging, use the `--verbose` and `--log-level DEBUG` options

---

## License & Contributions

See the main project repository for license and contribution guidelines. For questions or improvements, please open an issue or pull request.

---

# USX Parser

A modular Bible text parser that converts InDesign XML to USX format. This tool is specifically designed for processing Bible text with support for variants, footnotes, and cross-references.

## Installation

### Quick Start (Using the Shell Script)

1. The parser comes with a convenient shell script that handles the virtual environment:
```bash
./run-usxparser.sh [arguments]
```

### Manual Installation

1. Create and activate a Python virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # On Unix/macOS
```

2. Install the package in development mode:
```bash
pip install -e .
```

## Usage

### Using the Shell Script

Process a single book:
```bash
./run-usxparser.sh path/to/book.xml "Book Name" [options]
```

Process all books in parallel:
```bash
./run-usxparser.sh path/to/books/directory --parallel [options]
```

### Available Options

- `-v` or `--verbose`: Enable verbose output
- `--verse-limit N`: Stop processing after verse N
- `--log-level LEVEL`: Set log level (DEBUG, INFO, WARNING, ERROR)
- `--parallel`: Process all books in parallel (source must be a directory)
- `--max-workers N`: Maximum parallel workers (default: 4)

### Examples

Process Ephesians with verbose output:
```bash
./run-usxparser.sh ../bibeltext/Epheser/Epheser.xml "Epheser" -v
```

Process all books in parallel with 8 workers:
```bash
./run-usxparser.sh ../bibeltext --parallel --max-workers 8
```

## Development

### Making Changes

1. The parser is organized into several modules:
   - `cli.py`: Command-line interface
   - `handle_element.py`: Core XML element processing
   - `variant_handler.py`: Handles text variants
   - `footnotes.py`: Processes footnotes
   - `references.py`: Handles cross-references
   - `usx_transformer.py`: Main transformation logic

2. After making changes:
   ```bash
   # Reinstall the package
   pip install -e .
   
   # Or if you're using the shell script, it will use your changes automatically
   ./run-usxparser.sh [arguments]
   ```

### Project Structure

```
usxparser/
├── __init__.py
├── cli.py              # Command-line interface
├── handle_element.py   # Core element processing
├── footnotes.py       # Footnote handling
├── references.py      # Cross-reference processing
├── variant_handler.py # Variant handling
├── usx_transformer.py # Main transformation logic
└── utils.py          # Utility functions
```

### Dependencies

- Python 3.8+
- lxml: XML processing library

## Troubleshooting

1. **XML Parsing Errors**
   - Check if the input XML is well-formed
   - Enable verbose mode (`-v`) for detailed error messages
   - Check the log file in `../../storage/logs/transformer.log` when using DEBUG level

2. **Missing Dependencies**
   - If you get import errors, ensure you're in the virtual environment
   - Run `pip install -e .` to reinstall dependencies

3. **Virtual Environment Issues**
   - If the shell script can't find the virtual environment, check that `venv` exists in the project root
   - You can recreate it with `python -m venv venv`
