#!/bin/bash

# Print colored output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${BLUE}Starting maintenance tasks...${NC}\n"

# Navigate to the project directory
cd "$(dirname "$0")/.."

# Function to run a command and check its status
run_command() {
    echo -e "${BLUE}Running: $1${NC}"
    if eval "$1"; then
        echo -e "${GREEN}✓ Success${NC}\n"
    else
        echo -e "${RED}✗ Failed${NC}\n"
        exit 1
    fi
}

# Clear all caches
run_command "php artisan cache:clear"
run_command "php artisan config:clear"
run_command "php artisan route:clear"
run_command "php artisan view:clear"

# Optimize
run_command "php artisan config:cache"
run_command "php artisan route:cache"
run_command "php artisan view:cache"

# Optimize composer
run_command "composer dump-autoload --optimize"

# Rebuild search index
run_command "php artisan bible:rebuild-search"

# Final optimization
run_command "php artisan optimize"

echo -e "${GREEN}All maintenance tasks completed successfully!${NC}"
