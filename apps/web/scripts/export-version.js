import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { Console } from 'console';
import process from 'process';

// Create a console instance if not available globally
const console = globalThis.console || new Console(process.stdout, process.stderr);

// Ensure process.exit is available
const exitProcess = (code) => process.exit(code);

// Get directory name in ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Path to package.json (relative to this script)
const packageJsonPath = path.resolve(__dirname, '../package.json');
const tsOutputPath = path.resolve(__dirname, '../resources/js/version.ts');
const envOutputPath = path.resolve(__dirname, '../.env.VERSION');

try {
  // Read package.json
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    const version = packageJson.version || '0.0.0';

    console.log(`Exporting version ${version} to TypeScript and .env files...`);

  // Create TypeScript file with version export
  const tsContent = `/**
 * Application version (auto-generated from package.json)
 * DO NOT MODIFY THIS FILE DIRECTLY
 */

export const APP_VERSION = '${version}';

export default APP_VERSION;
`;

  // Create .env.VERSION file for build process
  const envContent = `VERSION=${version}`;

  // Write files
  fs.writeFileSync(tsOutputPath, tsContent);
  fs.writeFileSync(envOutputPath, envContent);

  console.log(`✅ Version ${version} exported successfully to:`);
  console.log(`   - ${tsOutputPath}`);
  console.log(`   - ${envOutputPath}`);
} catch (error) {
  console.error('❌ Error exporting version:', error);
  exitProcess(1);
}
