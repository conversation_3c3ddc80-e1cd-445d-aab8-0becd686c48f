#!/bin/bash
set -e

echo "Deployment started..."

# Pull the latest changes from the git repository
# git pull origin main
# Make sure you're in the project root
cd $PROJECT_PATH

# Install/update PHP dependencies
composer install --no-interaction --prefer-dist --optimize-autoloader --no-dev

# Clear and cache config
php artisan config:clear
php artisan config:cache

# Run database migrations
php artisan migrate --force

# Clear and cache routes
php artisan route:clear
php artisan route:cache

# Clear and cache views
php artisan view:clear
php artisan view:cache

# Restart Docker containers
docker-compose -f docker-compose.prod.yml up -d --build

# Sync Meilisearch indexes
php artisan scout:sync-index-settings

# Restart queue workers
php artisan queue:restart

echo "Deployment finished!"
