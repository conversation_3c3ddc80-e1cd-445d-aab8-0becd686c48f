#!/bin/bash

# Browser Drivers Setup Script
# This script installs and configures browser drivers for cross-browser testing

set -e

echo "🚀 Setting up browser drivers for cross-browser testing..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Detect OS
OS="unknown"
case "$(uname -s)" in
    Darwin*)    OS="macos";;
    Linux*)     OS="linux";;
    CYGWIN*|MINGW*|MSYS*) OS="windows";;
esac

print_status "Detected OS: $OS"

# Create drivers directory
DRIVERS_DIR="./drivers"
mkdir -p "$DRIVERS_DIR"

# Function to download and install ChromeDriver
install_chromedriver() {
    print_status "Installing ChromeDriver..."
    
    if command -v google-chrome &> /dev/null || command -v chromium-browser &> /dev/null; then
        # Get Chrome version
        if command -v google-chrome &> /dev/null; then
            CHROME_VERSION=$(google-chrome --version | grep -oE '[0-9]+\.[0-9]+\.[0-9]+')
        elif command -v chromium-browser &> /dev/null; then
            CHROME_VERSION=$(chromium-browser --version | grep -oE '[0-9]+\.[0-9]+\.[0-9]+')
        fi
        
        print_status "Chrome version: $CHROME_VERSION"
        
        # Download ChromeDriver
        case "$OS" in
            "macos")
                if [[ $(uname -m) == "arm64" ]]; then
                    CHROMEDRIVER_URL="https://chromedriver.storage.googleapis.com/LATEST_RELEASE_${CHROME_VERSION%.*.*}/chromedriver_mac_arm64.zip"
                else
                    CHROMEDRIVER_URL="https://chromedriver.storage.googleapis.com/LATEST_RELEASE_${CHROME_VERSION%.*.*}/chromedriver_mac64.zip"
                fi
                ;;
            "linux")
                CHROMEDRIVER_URL="https://chromedriver.storage.googleapis.com/LATEST_RELEASE_${CHROME_VERSION%.*.*}/chromedriver_linux64.zip"
                ;;
            "windows")
                CHROMEDRIVER_URL="https://chromedriver.storage.googleapis.com/LATEST_RELEASE_${CHROME_VERSION%.*.*}/chromedriver_win32.zip"
                ;;
        esac
        
        curl -L "$CHROMEDRIVER_URL" -o "$DRIVERS_DIR/chromedriver.zip"
        unzip -o "$DRIVERS_DIR/chromedriver.zip" -d "$DRIVERS_DIR"
        rm "$DRIVERS_DIR/chromedriver.zip"
        chmod +x "$DRIVERS_DIR/chromedriver"
        
        print_success "ChromeDriver installed successfully"
    else
        print_warning "Chrome/Chromium not found. Please install Chrome first."
    fi
}

# Function to download and install GeckoDriver (Firefox)
install_geckodriver() {
    print_status "Installing GeckoDriver for Firefox..."
    
    if command -v firefox &> /dev/null; then
        # Get latest GeckoDriver version
        GECKODRIVER_VERSION=$(curl -s https://api.github.com/repos/mozilla/geckodriver/releases/latest | grep -oP '"tag_name": "\K(.*)(?=")')
        
        case "$OS" in
            "macos")
                if [[ $(uname -m) == "arm64" ]]; then
                    GECKODRIVER_URL="https://github.com/mozilla/geckodriver/releases/download/$GECKODRIVER_VERSION/geckodriver-$GECKODRIVER_VERSION-macos-aarch64.tar.gz"
                else
                    GECKODRIVER_URL="https://github.com/mozilla/geckodriver/releases/download/$GECKODRIVER_VERSION/geckodriver-$GECKODRIVER_VERSION-macos.tar.gz"
                fi
                ;;
            "linux")
                GECKODRIVER_URL="https://github.com/mozilla/geckodriver/releases/download/$GECKODRIVER_VERSION/geckodriver-$GECKODRIVER_VERSION-linux64.tar.gz"
                ;;
            "windows")
                GECKODRIVER_URL="https://github.com/mozilla/geckodriver/releases/download/$GECKODRIVER_VERSION/geckodriver-$GECKODRIVER_VERSION-win64.zip"
                ;;
        esac
        
        if [[ "$OS" == "windows" ]]; then
            curl -L "$GECKODRIVER_URL" -o "$DRIVERS_DIR/geckodriver.zip"
            unzip -o "$DRIVERS_DIR/geckodriver.zip" -d "$DRIVERS_DIR"
            rm "$DRIVERS_DIR/geckodriver.zip"
        else
            curl -L "$GECKODRIVER_URL" -o "$DRIVERS_DIR/geckodriver.tar.gz"
            tar -xzf "$DRIVERS_DIR/geckodriver.tar.gz" -C "$DRIVERS_DIR"
            rm "$DRIVERS_DIR/geckodriver.tar.gz"
        fi
        
        chmod +x "$DRIVERS_DIR/geckodriver"
        print_success "GeckoDriver installed successfully"
    else
        print_warning "Firefox not found. Please install Firefox first."
    fi
}

# Function to setup Safari driver (macOS only)
setup_safari_driver() {
    if [[ "$OS" == "macos" ]]; then
        print_status "Setting up Safari WebDriver..."
        
        # Enable Safari WebDriver
        sudo safaridriver --enable
        
        print_success "Safari WebDriver enabled"
    else
        print_warning "Safari WebDriver is only available on macOS"
    fi
}

# Function to install Edge WebDriver
install_edgedriver() {
    print_status "Installing EdgeDriver..."
    
    if command -v microsoft-edge &> /dev/null || command -v msedge &> /dev/null; then
        # Get latest EdgeDriver version
        EDGEDRIVER_VERSION=$(curl -s https://msedgedriver.azureedge.net/LATEST_STABLE)
        
        case "$OS" in
            "macos")
                EDGEDRIVER_URL="https://msedgedriver.azureedge.net/$EDGEDRIVER_VERSION/edgedriver_mac64.zip"
                ;;
            "linux")
                EDGEDRIVER_URL="https://msedgedriver.azureedge.net/$EDGEDRIVER_VERSION/edgedriver_linux64.zip"
                ;;
            "windows")
                EDGEDRIVER_URL="https://msedgedriver.azureedge.net/$EDGEDRIVER_VERSION/edgedriver_win64.zip"
                ;;
        esac
        
        curl -L "$EDGEDRIVER_URL" -o "$DRIVERS_DIR/edgedriver.zip"
        unzip -o "$DRIVERS_DIR/edgedriver.zip" -d "$DRIVERS_DIR"
        rm "$DRIVERS_DIR/edgedriver.zip"
        chmod +x "$DRIVERS_DIR/msedgedriver"
        
        print_success "EdgeDriver installed successfully"
    else
        print_warning "Microsoft Edge not found. Please install Edge first."
    fi
}

# Function to add drivers to PATH
setup_path() {
    print_status "Setting up PATH for browser drivers..."
    
    CURRENT_DIR=$(pwd)
    DRIVERS_PATH="$CURRENT_DIR/$DRIVERS_DIR"
    
    # Add to current session
    export PATH="$DRIVERS_PATH:$PATH"
    
    # Add to shell profile
    SHELL_PROFILE=""
    if [[ -f ~/.zshrc ]]; then
        SHELL_PROFILE="$HOME/.zshrc"
    elif [[ -f ~/.bashrc ]]; then
        SHELL_PROFILE="$HOME/.bashrc"
    elif [[ -f ~/.bash_profile ]]; then
        SHELL_PROFILE="$HOME/.bash_profile"
    fi
    
    if [[ -n "$SHELL_PROFILE" ]]; then
        if ! grep -q "$DRIVERS_PATH" "$SHELL_PROFILE"; then
            echo "export PATH=\"$DRIVERS_PATH:\$PATH\"" >> "$SHELL_PROFILE"
            print_success "Added drivers to PATH in $SHELL_PROFILE"
        fi
    fi
}

# Function to verify installations
verify_installations() {
    print_status "Verifying driver installations..."
    
    if command -v chromedriver &> /dev/null; then
        CHROMEDRIVER_VERSION=$(chromedriver --version)
        print_success "ChromeDriver: $CHROMEDRIVER_VERSION"
    else
        print_error "ChromeDriver not found in PATH"
    fi
    
    if command -v geckodriver &> /dev/null; then
        GECKODRIVER_VERSION=$(geckodriver --version | head -n 1)
        print_success "GeckoDriver: $GECKODRIVER_VERSION"
    else
        print_warning "GeckoDriver not found in PATH"
    fi
    
    if [[ "$OS" == "macos" ]] && command -v safaridriver &> /dev/null; then
        print_success "Safari WebDriver: Available"
    fi
    
    if command -v msedgedriver &> /dev/null; then
        EDGEDRIVER_VERSION=$(msedgedriver --version)
        print_success "EdgeDriver: $EDGEDRIVER_VERSION"
    else
        print_warning "EdgeDriver not found in PATH"
    fi
}

# Main installation process
main() {
    print_status "Starting browser drivers installation..."
    
    # Install drivers
    install_chromedriver
    install_geckodriver
    setup_safari_driver
    # install_edgedriver  # Uncomment if you need Edge support
    
    # Setup PATH
    setup_path
    
    # Verify installations
    verify_installations
    
    print_success "Browser drivers setup completed!"
    print_status "Please restart your terminal or run 'source ~/.zshrc' (or ~/.bashrc) to update PATH"
    
    # Create .env entries
    print_status "Adding environment variables to .env.example..."
    cat >> .env.example << EOF

# Browser Testing Configuration
DUSK_DEFAULT_BROWSER=chrome
DUSK_HEADLESS=true
DUSK_CHROME_ENABLED=true
DUSK_FIREFOX_ENABLED=true
DUSK_SAFARI_ENABLED=false
DUSK_CHROME_DRIVER_URL=http://localhost:9515
DUSK_FIREFOX_DRIVER_URL=http://localhost:4444
DUSK_SAFARI_DRIVER_URL=http://localhost:10444
EOF
    
    print_success "Environment variables added to .env.example"
}

# Run main function
main "$@"
