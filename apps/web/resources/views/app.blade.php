<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <title inertia></title>

    <!-- Fonts -->
    <!-- Preconnect to font domains -->
    <link rel="preconnect" href="https://use.typekit.net" as="font" type="font/woff2" crossorigin>

    <!-- Preload critical fonts -->
    <link rel="preload" href="/fonts/ThanatosText-Book.woff2" as="font" type="font/woff2" crossorigin>

    <!-- Load fonts -->
    <link rel="stylesheet" href="https://use.typekit.net/kzb8yhl.css" media="print" onload="this.media='all'">

    <!-- Fallback for typekit fonts -->
    <noscript>
        <link rel="stylesheet" href="https://use.typekit.net/kzb8yhl.css">
    </noscript>

    <!-- Local font definition -->
    <style>
        @font-face {
            font-family: 'ThanatosText';
            src: url('/fonts/ThanatosText-Book.woff2') format('woff2');
            font-weight: normal;
            font-style: normal;
            font-display: swap;
        }
    </style>

    <!-- Google tag (gtag.js) -->
    @if (app()->environment('production') && config('services.google_analytics.id'))
    <script async src="https://www.googletagmanager.com/gtag/js?id={{ config('services.google_analytics.id') }}"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', '{{ config('services.google_analytics.id') }}');
    </script>
    @endif

    <!-- Scripts -->
    @routes
    @vite(['resources/js/app.ts', "resources/js/Pages/{$page['component']}.vue"])
    @inertiaHead
</head>
<body class="font-sans antialiased">
    @inertia
</body>

</html>
