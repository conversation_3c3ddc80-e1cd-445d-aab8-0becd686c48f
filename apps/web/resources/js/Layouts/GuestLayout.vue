<template>
    <div
        class="bg-surface text-text dark:bg-theme-900 min-h-screen pb-16 md:pb-0"
    >
        <header
            class="dark:bg-theme-800 bg-theme border-theme-100 dark:border-theme-700 sticky top-0 z-50 border-b shadow-xs"
        >
            <NavigationBar
                v-if="!textSettings.focusedMode"
                v-model="textSettings"
            />
            <button
                v-else
                class="bg-theme-100 hover:bg-theme-200 dark:bg-theme-800 dark:hover:bg-theme-700 text-theme-600 hover:text-theme-900 dark:text-theme-400 dark:hover:text-theme-100 fixed top-4 right-4 rounded-full p-2 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-hidden dark:focus:ring-offset-gray-800"
                title="Schließe Fokussiertes Lesen"
                @click="exitFocusedMode"
            >
                <span class="sr-only">Schließe Fokussiertes Lesen</span>
                <Icon
                    name="X"
                    class="h-6 w-6"
                    :aria-label="'Exit focused mode'"
                />
            </button>
        </header>

        <!-- Mobile Bottom Navigation -->
        <MobileBottomNav
            v-if="!textSettings.focusedMode && isMobile"
            @open-settings="openSettings"
        />

        <!-- Off-canvas Sidebar -->
        <BibleNavigationAside
            :is-open="navigationStore.isNavigationAsideOpen"
            :sections="sections"
            @close="navigationStore.closeNavigationAside"
            @select-book="selectBook"
        />

        <!-- Settings Aside -->
        <SettingsAside :is-open="isSettingsOpen" @close="closeSettings" />

        <!-- Page Content -->
        <main
            class="scrollbar scrollbar-track-gray-100 scrollbar-thumb-gray-300 dark:scrollbar-track-gray-800 dark:scrollbar-thumb-gray-600 hover:scrollbar-thumb-gray-400 dark:hover:scrollbar-thumb-gray-500 dark:bg-theme-900 space-y-12 py-8"
        >
            <slot />
        </main>
    </div>
</template>

<script setup lang="ts">
import { Icon } from '@/Components/Icons';
import BibleNavigationAside from '@/Components/Navigation/BibleNavigationAside.vue';
import MobileBottomNav from '@/Components/Navigation/MobileBottomNav.vue';
import NavigationBar from '@/Components/Navigation/NavigationBar.vue';
import SettingsAside from '@/Components/Settings/SettingsAside.vue';
import { useResponsiveStore } from '@/composables/useResponsiveStore';
import { useToggle } from '@vueuse/core';
import { useNavigationStore } from '@/stores/navigationStore';
import { useTextSettingsStore } from '@/stores/textSettingsStore';
import type { PageProps } from '@/types';
import type { BookView } from '@esbo/types';
import { usePage } from '@inertiajs/vue3';
import { computed, ref, watch } from 'vue';

const page = usePage<PageProps>();

const { isMobile } = useResponsiveStore();

// Transform sections data to include required name property
const sections = computed(() => {
    const rawSections = page.props.books?.sections ?? [];
    return rawSections.map((section, index) => ({
        name: `Testament ${index + 1}`,
        books: section.books,
    }));
});

const currentBook = ref<BookView | null>(null);
//const currentChapter = ref<number | null>(null);
//const currentVerse = ref<number | null>(null);
const showChapterSelector = ref(false);
//const showVerseSelector = ref(false);
const availableChapters = ref<number[]>([]);
//const availableVerses = ref<number[]>([]);

// Create a reactive settings object
const textSettings = useTextSettingsStore();
const [isSettingsOpen] = useToggle(false);
const closeSettings = () => { isSettingsOpen.value = false; };

const navigationStore = useNavigationStore();

//const chapterSelectorTitle = computed(() => {
//return currentBook.value
//    ? `${currentBook.value.name} - Kapitel`
//    : 'Kapitel';
//});

//const verseSelectorTitle = computed(() => {
//if (!currentBook.value) return 'Vers';
//if (!currentChapter.value) return `${currentBook.value.name} - Vers`;
//return `${currentBook.value.name} ${currentChapter.value} - Vers`;
//});

//const selectChapter = (chapter: number) => {
//currentChapter.value = chapter;
//showChapterSelector.value = false;
//};

//const selectVerse = (verse: number) => {
//currentVerse.value = verse;
//showVerseSelector.value = false;
//};

const selectBook = (book: BookView) => {
    currentBook.value = book;
    navigationStore.closeNavigationAside();
    availableChapters.value = [...Array(book.chapterCount)].map(
        (_, i) => i + 1,
    );
    showChapterSelector.value = true;
};

const exitFocusedMode = () => {
    textSettings.toggleFocusedMode();
};

const openSettings = () => {
    isSettingsOpen.value = true;
};

// Watch for changes in text settings and update CSS variables
watch(
    textSettings,
    (newSettings) => {
        document.documentElement.style.setProperty(
            '--bible-font-size',
            `${newSettings.fontSize}px`,
        );
        document.documentElement.style.setProperty(
            '--show-verse-numbers',
            newSettings.showVerseNumbers ? 'inline' : 'none',
        );
        document.documentElement.style.setProperty(
            '--show-footnotes',
            newSettings.showFootnotes ? 'block' : 'none',
        );
    },
    { immediate: true },
);
</script>

<style>
:root {
    --bible-font-size: 16px;
    --show-verse-numbers: inline;
    --show-footnotes: block;
}
</style>
