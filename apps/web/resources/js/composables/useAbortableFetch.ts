// resources/js/composables/useAbortableFetch.ts
import { ref, onBeforeUnmount } from 'vue';

export function useAbortableFetch() {
  const controller = ref<AbortController | null>(null);
  const timeout = ref<ReturnType<typeof setTimeout> | null>(null);

  async function fetchWithAbort(url: string, options = {}, timeoutMs = 5000) {
    controller.value?.abort();
    if (timeout.value) clearTimeout(timeout.value);

    controller.value = new AbortController();
    timeout.value = setTimeout(() => controller.value?.abort(), timeoutMs);

    try {
      const response = await window.fetch(url, { ...options, signal: controller.value.signal });
      clearTimeout(timeout.value);
      return response;
    } catch (error) {
      clearTimeout(timeout.value);
      throw error;
    }
  }

  onBeforeUnmount(() => {
    controller.value?.abort();
    if (timeout.value) clearTimeout(timeout.value);
  });

  return { fetchWithAbort };
}
