/**
 * @file Handles Bible verse references and navigation
 * @module composables/useVerseReference
 */
import { useBibleHighlightStore } from '@/stores/bible/bibleHighlightStore';
import { useBibleStore } from '@/stores/bible/bibleSectionStore';
import { useSearchStore } from '@/stores/searchStore';
import type { Section } from '@esbo/types';
import { router } from '@inertiajs/vue3';

interface VerseReference {
    startVerse: number;
    endVerse: number | null;
    raw: string;
    chapter: number;
    book: string;
}

/**
 * Composable for handles bible verse references and navigation
 *
 * @description Handles Bible verse references and navigation
 * @example
 * // Example usage
 * const result = useVerseReference()
 *
 * @returns {Object} The returned object with its properties
 */
export function useVerseReference() {
    const searchStore = useSearchStore();
    const bibleStore = useBibleStore();

    function parseVerseReference(reference: string): VerseReference | null {
        // Extract book, chapter, and verse
        const match = reference.match(
            /\/([A-Za-zäöüÄÖÜß]+)(\d+)(?:,(\d+)(?:-(\d+))?)?$/,
        );
        if (!match) return null;

        const [, book, chapterStr, startVerseStr, endVerseStr] = match;
        if (!startVerseStr) return null;

        const chapter = parseInt(chapterStr);
        const startVerse = parseInt(startVerseStr);
        const endVerse = endVerseStr ? parseInt(endVerseStr) : null;
        const raw = `,${startVerseStr}${endVerseStr ? `-${endVerseStr}` : ''}`;

        return {
            startVerse,
            endVerse,
            raw,
            chapter,
            book,
        };
    }

    /**
     * highlight search terms
     * @param {any} params - Parameters for the function
     * @returns {any} The result of the function
     */
    function highlightSearchTerms(element: Element) {
        const searchQuery = searchStore.query.trim();
        if (!searchQuery) return;

        // Split search query into terms, handling quoted phrases
        const terms = searchQuery.match(/\w+|"[^"]+"/g) || [];
        const searchTerms = terms.map((term) => term.replace(/"/g, '').trim());

        // Get the text content
        const text = element.textContent || '';

        // Create a temporary container
        const tempContainer = document.createElement('div');
        let lastIndex = 0;

        // Find and wrap each search term with <strong>
        searchTerms.forEach((term) => {
            const regex = new RegExp(`(${term})`, 'gi');
            let match;

            while ((match = regex.exec(text)) !== null) {
                // Add text before the match
                tempContainer.appendChild(
                    document.createTextNode(
                        text.substring(lastIndex, match.index),
                    ),
                );

                // Create strong element for the match
                const strong = document.createElement('strong');
                strong.textContent = match[0];
                strong.classList.add('search-term-highlight');
                tempContainer.appendChild(strong);

                lastIndex = regex.lastIndex;
            }
        });

        // Add any remaining text
        if (lastIndex < text.length) {
            tempContainer.appendChild(
                document.createTextNode(text.substring(lastIndex)),
            );
        }

        // Replace the content
        element.innerHTML = tempContainer.innerHTML;
    }

    // Add new functions for navigation
    /**
     * navigate to reference
     * @param {any} params - Parameters for the function
     * @returns {any} The result of the function
     */
    function navigateToReference(
        book: string,
        chapter: number,
        verse?: number,
    ) {
        const bibleHighlightStore = useBibleHighlightStore();
        // Disable scroll handling before navigation
        bibleStore.disableScrollHandling();

        const url = verse
            ? `/${book}${chapter},${verse}`
            : `/${book}${chapter}`;

        router.visit(url, {
            preserveScroll: true, // Prevent automatic scrolling
            onFinish: () => {
                if (verse) {
                    bibleHighlightStore.clearHighlights();
                    bibleStore.updateCurrentVerse(chapter, verse);
                }
                // Re-enable scroll handling after a short delay to ensure all content is loaded
                setTimeout(() => {
                    bibleStore.enableScrollHandling();
                }, 100);
            },
        });
    }

    // Add verse range helper
    function getAvailableVerses(section: Section): number[] {
        if (!section || section.type === 'frontmatter') return [];
        return Array.from({ length: section.verses.length }, (_, i) => i + 1);
    }

    return {
        parseVerseReference,
        highlightSearchTerms,
        navigateToReference,
        getAvailableVerses,
    };
}
