/**
 * @file Manages dropdown UI component state and interactions
 * @module composables/useDropdown
 */
import { onMounted, onUnmounted, ref, Ref } from 'vue';

/**
 * Composable for manages dropdown ui component state and interactions
 * 
 * @description Manages dropdown UI component state and interactions
 * @example
 * // Example usage
 * const result = useDropdown()
 * 
 * @returns {Object} The returned object with its properties
 */
export function useDropdown(containerClass = 'dropdown-container'): {
    isOpen: Ref<boolean>;
    toggleDropdown: () => void;
    setDropdownState: (state: boolean) => void;
} {
    const isOpen = ref(false);

    const closeDropdown = (e: Event) => {
        const target = e.target as HTMLElement;
        // Only close if clicking outside the container and not on a button inside the dropdown
        if (
            !target.closest(`.${containerClass}`) &&
            !target.closest('.dropdown-item')
        ) {
            isOpen.value = false;
        }
    };

    const toggleDropdown = () => {
        isOpen.value = !isOpen.value;
    };

    const setDropdownState = (state: boolean) => {
        isOpen.value = state;
    };

    onMounted(() => {
        document.addEventListener('click', closeDropdown);
    });

    onUnmounted(() => {
        document.removeEventListener('click', closeDropdown);
    });

    return {
        isOpen,
        toggleDropdown,
        setDropdownState,
    };
}
