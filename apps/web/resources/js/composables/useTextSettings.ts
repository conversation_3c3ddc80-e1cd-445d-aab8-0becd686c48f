/**
 * @file Manages text display settings and preferences
 * @module composables/useTextSettings
 */
import { useTextSettingsStore } from '@/stores/textSettingsStore';
import { storeToRefs } from 'pinia';

/**
 * Composable for manages text display settings and preferences
 *
 * @description Manages text display settings and preferences
 * @example
 * // Example usage
 * const result = useTextSettings()
 *
 * @returns {Object} The returned object with its properties
 */
export function useTextSettings() {
    const store = useTextSettingsStore();
    const {
        fontSize,
        showVerseNumbers,
        showChapterNumbers,
        showBookNames,
        flowText,
        showFootnotes,
        focusedMode,
    } = storeToRefs(store);

    return {
        fontSize,
        showVerseNumbers,
        showChapterNumbers,
        showBookNames,
        flowText,
        showFootnotes,
        focusedMode,
        updateSettings: store.updateSettings,
        toggleFocusedMode: store.toggleFocusedMode,
    };
}
