import { ref, computed, onUnmounted } from 'vue'

/**
 * useSheetDrag composable
 * Encapsulates drag/slider logic for bottom sheet resizing.
 *
 * @param options - initial height, min/max, emit close callback
 */
export function useSheetDrag(options: {
  initialHeight: number
  minHeight: number
  maxHeight: number
  onClose: () => void
}) {
  const sheetHeightVh = ref(options.initialHeight)
  const dragging = ref(false)
  const startY = ref(0)
  const startHeight = ref(options.initialHeight)
    const dragOffset = ref(0)
    const closeSheetBottomMargin = 30 // in px

  function adjustHeight(delta: number) {
    let newHeight = sheetHeightVh.value + delta
    newHeight = Math.max(options.minHeight, Math.min(options.maxHeight, newHeight))
    if (newHeight <= options.minHeight + closeSheetBottomMargin) {
      dragOffset.value = 0
      options.onClose()
    } else {
      sheetHeightVh.value = newHeight
      dragOffset.value = 0
    }
  }

  function onDragStart(e: MouseEvent | TouchEvent) {
    dragging.value = true
    startY.value = 'touches' in e ? e.touches[0].clientY : e.clientY
    startHeight.value = sheetHeightVh.value
    document.addEventListener('mousemove', onDragMove)
    document.addEventListener('mouseup', onDragEnd)
    document.addEventListener('touchmove', onDragMove)
    document.addEventListener('touchend', onDragEnd)
  }

  function onDragMove(e: MouseEvent | TouchEvent) {
    if (!dragging.value) return;
    const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY;
    // If the handle is within closeSheetBottomMargin of the bottom, close the sheet
    if (clientY > window.innerHeight - closeSheetBottomMargin) {
      dragging.value = false;
      dragOffset.value = 0;
      options.onClose();
      return;
    }
    const delta = clientY - startY.value;
    let newHeight = startHeight.value - (delta / window.innerHeight) * 100;
    newHeight = Math.max(options.minHeight, Math.min(options.maxHeight, newHeight));
    sheetHeightVh.value = newHeight;
    dragOffset.value = delta;
  }

  function onDragEnd() {
    dragging.value = false
    dragOffset.value = 0
    document.removeEventListener('mousemove', onDragMove)
    document.removeEventListener('mouseup', onDragEnd)
    document.removeEventListener('touchmove', onDragMove)
    document.removeEventListener('touchend', onDragEnd)
  }

  function onHandleKeydown(e: KeyboardEvent) {
    if (e.key === 'ArrowUp') {
      adjustHeight(5)
      e.preventDefault()
    } else if (e.key === 'ArrowDown') {
      adjustHeight(-5)
      e.preventDefault()
    } else if (e.key === 'Escape') {
      options.onClose()
      e.preventDefault()
    }
  }

  const dragStyle = computed(() => {
    return `height: ${sheetHeightVh.value}vh; transition: none;`;
  })

  onUnmounted(() => {
    document.removeEventListener('mousemove', onDragMove)
    document.removeEventListener('mouseup', onDragEnd)
    document.removeEventListener('touchmove', onDragMove)
    document.removeEventListener('touchend', onDragEnd)
  })

  return {
    sheetHeightVh,
    dragging,
    dragOffset,
    dragStyle,
    adjustHeight,
    onDragStart,
    onDragMove,
    onDragEnd,
    onHandleKeydown
  }
}
