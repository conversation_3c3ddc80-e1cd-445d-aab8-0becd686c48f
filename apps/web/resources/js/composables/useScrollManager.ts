/**
 * @file Handles scroll events and position management
 * @module composables/useScrollManager
 */
import { useBibleHighlightStore } from '@/stores/bible/bibleHighlightStore';
import { useBibleMemoryStore } from '@/stores/bible/bibleMemoryStore'; // Import memory store
import { useBibleStore } from '@/stores/bible/bibleSectionStore';
import { getChapterId } from '@/utils/bibleNavigationUtils';
import { logger } from '@/utils/logger';
import type { Section } from '@esbo/types';
import {
    computed,
    nextTick,
    onMounted,
    onUnmounted,
    ref,
    watch,
    type ComponentPublicInstance,
} from 'vue';
import { useDebounceFn } from '@vueuse/core';

export interface ScrollManagerOptions {
    findMostVisibleChapter: () => string | null;
}

/**
 * Composable for handles scroll events and position management
 *
 * @description Handles scroll events and position management
 * @example
 * // Example usage
 * const result = useScrollManager()
 *
 * @returns {Object} The returned object with its properties
 */
export function useScrollManager(options: ScrollManagerOptions) {
    const bibleStore = useBibleStore();
    const highlightStore = useBibleHighlightStore();
    const chapterRefs = ref(new Map<string, HTMLElement>());
    const cachedOffsetHeight = ref<number | null>(null);
    const isScrollEnabled = ref(true);
    const isLocked = ref(false);
    const SCROLL_TIMEOUT = 500;
    const store = useBibleStore();
    const isMobile = computed(() => store.isMobile);

    const lock = () => {
        isLocked.value = true;
        document.body.style.scrollBehavior = 'auto';
    };

    const unlock = () => {
        isLocked.value = false;
        document.body.style.scrollBehavior = '';
    };

    // Keep the existing offset height calculation
    const getScrollOffset = (): number => {
        if (cachedOffsetHeight.value !== null) {
            return cachedOffsetHeight.value;
        }

        try {
            const navbarSelector = isMobile.value
                ? 'nav.sub-navbar'
                : 'nav.main-navbar';
            const navbar = document.querySelector(
                navbarSelector,
            ) as HTMLElement | null;

            if (!navbar) {
                logger.warn('Navbar element ' + navbarSelector + ' not found');
                return 64; // Default navbar height
            }

            const offsetHeight = navbar.offsetHeight;
            cachedOffsetHeight.value = offsetHeight;
            return cachedOffsetHeight.value;
        } catch (error) {
            logger.error('Error calculating scroll offset:', error);
            return 64;
        }
    };

    const resetOffsetCache = () => {
        cachedOffsetHeight.value = null;
    };

    // Chapter ref management
    /**
     * set chapter ref
     * @param {any} params - Parameters for the function
     * @returns {any} The result of the function
     */
    function setChapterRef(
        el: Element | ComponentPublicInstance | null,
        section: Section,
    ) {
        let htmlElement: HTMLElement | null = null;

        if (el instanceof HTMLElement) {
            htmlElement = el;
        } else if (el && '$el' in el && el.$el instanceof HTMLElement) {
            htmlElement = el.$el;
        }

        if (!htmlElement) return;

        const id = getChapterId(section);
        chapterRefs.value.set(id, htmlElement);
    }

    // Maximum number of retries for finding chapter/verse elements
    const MAX_SCROLL_RETRIES = 3;

    // Scroll functions
    async function scrollToVerse(
        verseNumber: number | null,
        chapterNumber: number,
        highlight: boolean = true,
        retryCount: number = 0,
    ): Promise<void> {
        if (!bibleStore.currentBook || !verseNumber) {
            logger.debug('Missing book or verse number');
            return;
        }

        // If we've exceeded max retries, give up
        if (retryCount >= MAX_SCROLL_RETRIES) {
            logger.error(
                `Failed to find chapter/verse after ${MAX_SCROLL_RETRIES} attempts`,
            );
            enableScrollHandling();
            return;
        }

        const chapterId = `${bibleStore.currentBook.slug}${chapterNumber}`;
        console.log(
            `Attempting to scroll to verse: ${chapterId}.${verseNumber} (attempt ${retryCount + 1})`,
        );

        const chapterElement =
            chapterRefs.value.get(chapterId) ||
            document.getElementById(chapterId);

        if (!chapterElement) {
            console.log(`Chapter ${chapterId} not found, retrying...`);
            setTimeout(
                () =>
                    scrollToVerse(
                        verseNumber,
                        chapterNumber,
                        highlight,
                        retryCount + 1,
                    ),
                SCROLL_TIMEOUT,
            );
            return;
        }

        await nextTick();

        // Disable scroll handling before attempting to scroll
        disableScrollHandling();

        try {
            if (highlight) {
                const success = highlightStore.highlightVerseByReference({
                    book: bibleStore.currentBook!.slug,
                    chapter: chapterNumber,
                    verse: verseNumber,
                });
                if (!success) {
                    logger.warn(
                        `Failed to highlight verse: ${chapterId}.${verseNumber}`,
                    );
                    throw new Error('Failed to highlight verse');
                }
            }

            const verseId = `${bibleStore.currentBook!.slug}.${chapterNumber}.${verseNumber}`;
            const verseElement = document.getElementById(verseId);

            if (!verseElement) {
                throw new Error(`Verse element not found: ${verseId}`);
            }

            const offset =
                verseElement.getBoundingClientRect().top +
                window.scrollY -
                getScrollOffset();

            window.scrollTo({
                top: offset,
                behavior: 'smooth',
            });

            // Re-enable scroll handling after animation completes
            setTimeout(enableScrollHandling, SCROLL_TIMEOUT);
        } catch (error) {
            logger.error('Error during scroll operation:', error);
            enableScrollHandling();

            // If the error was due to missing verse element, retry
            if (
                error instanceof Error &&
                error.message.includes('Verse element not found') &&
                retryCount < MAX_SCROLL_RETRIES
            ) {
                setTimeout(
                    () =>
                        scrollToVerse(
                            verseNumber,
                            chapterNumber,
                            highlight,
                            retryCount + 1,
                        ),
                    SCROLL_TIMEOUT,
                );
            }
        }
    }

    async function scrollToVerseRange(
        startVerse: number | null,
        endVerse: number | null,
        chapterNumber: number,
    ): Promise<void> {
        if (!bibleStore.currentBook || !startVerse || !endVerse) return;

        const chapterId = `${bibleStore.currentBook.slug}${chapterNumber}`;
        logger.debug(
            `Attempting to scroll to verse range: ${chapterId}.${startVerse}-${endVerse}`,
        );

        const chapterElement =
            chapterRefs.value.get(chapterId) ||
            document.getElementById(chapterId);
        if (!chapterElement) {
            logger.debug(`Chapter ${chapterId} not found, retrying...`);
            setTimeout(
                () => scrollToVerseRange(startVerse, endVerse, chapterNumber),
                500,
            );
            return;
        }

        await nextTick();

        setTimeout(() => {
            disableScrollHandling();

            const success = highlightStore.toggleVerseHighlight({
                book: bibleStore.currentBook!.slug,
                chapter: chapterNumber,
                verse: startVerse,
                endVerse: endVerse,
            });

            if (!success) {
                logger.error(
                    `Failed to highlight verse range: ${chapterId}.${startVerse}-${endVerse}`,
                );
                enableScrollHandling();
                return;
            }

            const verseId = `${bibleStore.currentBook!.slug}.${chapterNumber}.${startVerse}`;
            const verseElement = document.getElementById(verseId);

            if (verseElement) {
                const offset =
                    verseElement.getBoundingClientRect().top +
                    window.scrollY -
                    getScrollOffset();

                window.scrollTo({
                    top: offset,
                    behavior: 'smooth',
                });

                setTimeout(enableScrollHandling, SCROLL_TIMEOUT);
            } else {
                logger.error(`Verse element not found: ${verseId}`);
                enableScrollHandling();
            }
        }, 300);
    }

    async function scrollToChapter(chapterId: string): Promise<void> {
        if (!chapterId) {
            logger.error('Cannot scroll to chapter: chapterId is undefined');
            enableScrollHandling();
            return;
        }

        logger.debug(`Scrolling to chapter: ${chapterId}`);

        // Wait for next tick to ensure Vue updates are complete
        await nextTick();

        // Use a small delay to ensure content is fully rendered
        setTimeout(() => {
            disableScrollHandling();

            try {
                // Get the navbar offset height
                const navbarOffset = getScrollOffset();
                let targetElement = null;

                // Try to find the element in our refs first
                targetElement = chapterRefs.value.get(chapterId);

                // If not in refs, try by ID
                if (!targetElement) {
                    targetElement = document.getElementById(chapterId);
                }

                // If still not found, try by data-chapter-id attribute (for frontmatter)
                if (!targetElement) {
                    targetElement = document.querySelector(
                        `[data-chapter-id="${chapterId}"]`,
                    );
                }

                if (targetElement) {
                    // Calculate the offset position considering the navbar height
                    const offset =
                        targetElement.getBoundingClientRect().top +
                        window.scrollY -
                        navbarOffset;

                    // Scroll to the element with the offset
                    window.scrollTo({
                        top: offset,
                        behavior: 'smooth',
                    });
                } else {
                    logger.error(`Element not found for chapter: ${chapterId}`);
                }

                // Re-enable scroll handling after animation completes
                setTimeout(enableScrollHandling, SCROLL_TIMEOUT);
            } catch (error) {
                logger.error(`Error scrolling to chapter ${chapterId}:`, error);
                enableScrollHandling();
            }
        }, 100);
    }

    // Lifecycle and scroll handling
    const lastScrollY = ref<number>(0); // Track last scroll position to detect direction

    // Track previous chapter IDs
    let lastIds: string[] = [];
    // Watch for removed chapters to clean up refs/highlights
    watch(
        () => bibleStore.visibleChapters.map((s) => s.id),
        (newIds) => {
            const removed = lastIds.filter((id) => !newIds.includes(id));
            removed.forEach((id) => {
                chapterRefs.value.delete(id);
                highlightStore.clearHighlights();
            });
            lastIds = [...newIds];
        },
        { immediate: true },
    );

    onMounted(() => {
        window.addEventListener('resize', resetOffsetCache);
        lastScrollY.value = window.scrollY;
    });

    onUnmounted(() => {
        window.removeEventListener('resize', resetOffsetCache);
    });

    const enableScrollHandling = () => {
        isScrollEnabled.value = true;
        bibleStore.enableScrollHandling();
        unlock();
    };

    const disableScrollHandling = () => {
        isScrollEnabled.value = false;
        bibleStore.disableScrollHandling();
        lock();
    };

    const handleScroll = useDebounceFn(() => {
        if (!isScrollEnabled.value || !bibleStore.scrollHandlingEnabled) {
            console.log('Skipping scroll handler, not enabled');
            return;
        }

        const mostVisibleChapter = options.findMostVisibleChapter();
        if (!mostVisibleChapter) {
            console.log('No visible chapter found');
            return;
        }

        let i = mostVisibleChapter.length - 1;
        while (i >= 0 && !isNaN(parseInt(mostVisibleChapter[i]))) {
            i--;
        }
        const mostVisibleChapterNumber = parseInt(
            mostVisibleChapter.substring(i + 1),
        );
        const bookSlug = bibleStore.currentBook?.slug || '';

        if (
            bibleStore.isBookAvailable(bookSlug) &&
            bibleStore.currentChapter !== mostVisibleChapterNumber
        ) {
            bibleStore.updateCurrentChapter(mostVisibleChapter);
        }

        // Detect scroll direction and request memory cleanup
        const newY = window.scrollY;
        const direction = newY > lastScrollY.value ? 'next' : 'previous';
        lastScrollY.value = newY;
        if (!bibleStore.isInitialLoad) {
            const memoryStore = useBibleMemoryStore();
            memoryStore.requestCleanup(direction);
        }
    }, 50);

    return {
        chapterRefs,
        setChapterRef,
        handleScroll,
        scrollToVerse,
        scrollToVerseRange,
        scrollToChapter,
        enableScrollHandling,
        disableScrollHandling,
    };
}
