import { onMounted, onBeforeUnmount, nextTick, Ref, ComputedRef } from 'vue';
import type { FootnoteState } from '@esbo/types';

/**
 * Encapsulates event handling for FootnoteTooltip (click-outside, scroll, keydown, focus management).
 * Ensures accessibility and separation of concerns from stack/state logic.
 *
 * @param options - Configuration object
 *   - isMobile: ComputedRef<boolean> indicating mobile/desktop
 *   - currentFootnote: ComputedRef<FootnoteState | null>
 *   - canGoBack: ComputedRef<boolean>
 *   - getActiveEl: () => HTMLElement | null (returns the current tooltip/sheet DOM node)
 *   - onClose: (reason?: string) => void
 *   - goBack: () => void
 */


export function useFootnoteTooltipEvents(options: {
  isMobile: Ref<boolean> | ComputedRef<boolean>;
  currentFootnote: ComputedRef<FootnoteState | null>;
  canGoBack: ComputedRef<boolean>;
  getActiveEl: () => HTMLElement | null;
  onClose: (reason?: string) => void;
  goBack: () => void;
}) {
  let justOpened = false;
  let lastOpenedAt = 0;
  let justWentBack = false;

  function focusPopup() {
    // Focus management for accessibility
    const el = options.getActiveEl();
    if (el && typeof el.focus === 'function') {
      el.focus();
    }
  }

  function onClickOutside(e: MouseEvent) {
    if (!options.currentFootnote.value) return;
    if (options.currentFootnote.value && typeof options.currentFootnote.value === 'object' && 'isClickLocked' in options.currentFootnote.value && (options.currentFootnote.value as { isClickLocked: boolean }).isClickLocked === false) return;
    if (justWentBack) return;
    if (justOpened) return;

    if (lastOpenedAt && e.timeStamp && e.timeStamp - lastOpenedAt < 350) {
      return;
    }
    const tooltipElement = options.getActiveEl();
    if (tooltipElement?.contains(e.target as Node)) return;
    options.onClose('click-outside');
  }
  function onScroll() {
    if (!options.currentFootnote.value) return;
    if (options.isMobile.value) return;
    const now = typeof performance !== 'undefined' ? performance.now() : Date.now();
    if (lastOpenedAt && now - lastOpenedAt < 350) return;
    if (options.currentFootnote.value && typeof options.currentFootnote.value === 'object' && 'isClickLocked' in options.currentFootnote.value && (options.currentFootnote.value as { isClickLocked: boolean }).isClickLocked !== false && !justWentBack) {
      options.onClose('scroll');
    }
  }
  function onKeydown(e: KeyboardEvent) {
    if (!options.currentFootnote.value) return;
    if (e.key === 'Escape') {
      e.preventDefault();
      options.onClose('escape');
    } else if ((e.key === 'Backspace' || e.key === 'ArrowLeft') && options.canGoBack.value) {
      e.preventDefault();
      options.goBack();
    }
  }

  function markOpened() {
    justOpened = true;
    lastOpenedAt = Date.now();
    nextTick(() => {
      justOpened = false;
    });
  }
  function markWentBack() {
    justWentBack = true;
    setTimeout(() => { justWentBack = false; }, 150);
    nextTick(() => focusPopup());
  }

  // Lifecycle hooks
  onMounted(() => {
    document.addEventListener('keydown', onKeydown);
    nextTick(() => {
      setTimeout(() => {
        document.addEventListener('click', onClickOutside);
      }, 50);
    });
    document.addEventListener('scroll', onScroll, true);
  });
  onBeforeUnmount(() => {
    document.removeEventListener('keydown', onKeydown);
    document.removeEventListener('click', onClickOutside);
    document.removeEventListener('scroll', onScroll, true);
  });

  return {
    focusPopup,
    markOpened,
    markWentBack,
  };
}
