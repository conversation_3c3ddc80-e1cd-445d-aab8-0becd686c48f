/**
 * @file Manages search results and search-related functionality
 * @module composables/useSearchResults
 */
import type { GeneralSearchResult, SearchResultsState } from '@esbo/types';
import { router } from '@inertiajs/vue3';
import { computed, ref, watch } from 'vue';

/**
 * Composable for manages search results and search-related functionality
 *
 * @description Manages search results and search-related functionality
 * @example
 * // Example usage
 * const result = useSearchResults()
 *
 * @returns {Object} The returned object with its properties
 */
export function useSearchResults(initialResults: SearchResultsState) {
    const isLoading = ref(false);
    const hasError = ref(false);
    const retryCount = ref(0);
    const currentPage = ref(initialResults.metadata.currentPage);
    const allResults = ref<GeneralSearchResult[]>([]);
    const highlightCache = ref(new Map<string, string>());

    const resetResults = () => {
        allResults.value = [];
        highlightCache.value.clear();
        currentPage.value = 1;
        hasError.value = false;
        retryCount.value = 0;
    };

    // Watch for changes in initialResults
    watch(
        () => initialResults,
        (newResults, oldResults) => {
        // Reset results if it's a new search (query changed)
        if (!oldResults || newResults.metadata.currentPage !== oldResults.metadata.currentPage) {
            resetResults();
        }

        if (
            !newResults ||
            !newResults.data ||
            !Array.isArray(newResults.data) ||
            newResults.data.length === 0
        ) {
            allResults.value = [];
            return;
        }

        // If it's a new search (first page), replace the results
        if (newResults.metadata.currentPage === 1) {
            allResults.value = [...newResults.data];
        }
        // If it's loading more results, append them
        else {
            allResults.value = [...allResults.value, ...newResults.data];
        }

        currentPage.value = newResults.metadata.currentPage;
    },
    { immediate: true, deep: true }
    );

    const hasResults = computed(() => {
        return initialResults.metadata.total > 0 && allResults.value.length > 0;
    });

    function highlightContent(content: string, query: string): string {
        const cacheKey = `${content}-${query}`;
        if (highlightCache.value.has(cacheKey)) {
            return highlightCache.value.get(cacheKey)!;
        }

        if (!query || !content) return content;

        const terms = query
            .split(/\s+/)
            .filter((term) => term.length > 0)
            .map((term) => term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'));

        const regex = new RegExp(`(${terms.join('|')})`, 'gi');
        const highlighted = content.replace(regex, '<mark>$1</mark>');

        highlightCache.value.set(cacheKey, highlighted);
        return highlighted;
    }

    /**
     * load more results
     * @param {any} params - Parameters for the function
     * @returns {any} The result of the function
     */
    function loadMoreResults(query: string, types: string[]) {
        if (
            isLoading.value ||
            hasError.value ||
            currentPage.value >= initialResults.metadata.lastPage
        ) {
            return null;
        }

        try {
            isLoading.value = true;
            const nextPage = currentPage.value + 1;
            console.log(
                'Loading more results for page:',
                nextPage,
                'with types:',
                types,
            );

            return new Promise((resolve) => {
                // Use POST method to avoid query parameters in URL
                router.post(
                    `/search/${encodeURIComponent(query)}`,
                    {
                        types: types,
                        page: nextPage, // Add the page parameter
                    },
                    {
                        preserveState: true,
                        preserveScroll: true,
                        only: ['results'],
                        // eslint-disable-next-line @typescript-eslint/no-explicit-any
                        onSuccess: (page: any) => {
                            if (page.props && page.props.results) {
                                // Update current page
                                currentPage.value =
                                    page.props.results.metadata.currentPage;

                                // Append the new results to allResults
                                if (
                                    page.props.results.data &&
                                    Array.isArray(page.props.results.data)
                                ) {
                                    allResults.value = [
                                        ...allResults.value,
                                        ...page.props.results.data,
                                    ];
                                    console.log(
                                        'Updated allResults with new data:',
                                        allResults.value.length,
                                    );
                                }

                                retryCount.value = 0;
                                hasError.value = false;
                                resolve(page.props.results);
                            } else {
                                console.error('No results found in page props');
                                hasError.value = true;
                                resolve(null);
                            }
                        },
                        onError: (errors) => {
                            console.error(
                                'Failed to load more results:',
                                errors,
                            );
                            hasError.value = true;
                            resolve(null);
                        },
                        onFinish: () => {
                            isLoading.value = false;
                        },
                    },
                );
            });
        } catch (error) {
            console.error('Failed to load more results:', error);
            hasError.value = true;
            isLoading.value = false;
            return null;
        }
    }

    return {
        isLoading,
        hasError,
        currentPage,
        allResults,
        hasResults,
        highlightContent,
        loadMoreResults,
    };
}
