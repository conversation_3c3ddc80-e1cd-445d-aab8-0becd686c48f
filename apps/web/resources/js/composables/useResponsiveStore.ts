import { useMediaQuery } from '@vueuse/core';
import { computed } from 'vue';

/**
 * Responsive state composable using VueUse's useMediaQuery for SSR safety.
 * Returns isMobile as a ref<boolean> (true if viewport is mobile width).
 */
export function useResponsiveStore() {
  const isClient = typeof window !== 'undefined';
  const isMobile = isClient
    ? useMediaQuery('(max-width: 639px)')
    : computed(() => false); // Always false during SSR

  return { isMobile };
}
