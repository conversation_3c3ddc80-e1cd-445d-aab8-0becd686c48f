// resources/js/composables/useFootnoteStack.ts
import { ref, computed } from 'vue';
import type { FootnoteState } from '@esbo/types';

export function useFootnoteStack(initialFootnote: FootnoteState | null) {
  const footnoteStack = ref<FootnoteState[]>(initialFootnote ? [initialFootnote] : []);

  const currentFootnote = computed(() =>
    footnoteStack.value.length > 0
      ? footnoteStack.value[footnoteStack.value.length - 1]
      : null
  );

  function pushFootnote(footnote: FootnoteState) {
    footnoteStack.value.push(footnote);
  }
  function popFootnote() {
    if (footnoteStack.value.length > 1) {
      footnoteStack.value.pop();
    }
  }
  function resetFootnotes() {
    footnoteStack.value = [];
  }
  function setFootnote(footnote: FootnoteState | null) {
    footnoteStack.value = footnote ? [footnote] : [];

  }

  return {
    footnoteStack,
    currentFootnote,
    pushFootnote,
    popFootnote,
    resetFootnotes,
    setFootnote,
  };
}
