/**
 * Composable for accessing the application version
 * This version is automatically generated from package.json
 */

import { APP_VERSION } from '../version';

/**
 * Provides access to the application version
 * @returns The current application version from package.json
 */
export function useAppVersion() {
  // Use the imported version from version.ts (generated from package.json)
  // This is the primary source of the version number

  // Alternatively, we can use the version from Vite's define
  // This is useful in production builds where the version.ts might not be available
    /*let viteVersion: string | null = null;
    try {
        // This will only succeed if __APP_VERSION__ is defined by Vite
        viteVersion = new Function('return typeof __APP_VERSION__ !== "undefined" ? __APP_VERSION__ : null')();
    } catch {
        viteVersion = null;
    }*/
    // Return the version from the most reliable source
    const version = APP_VERSION || '0.0.0';

    return {
        version,

        /**
         * Returns the version with a 'v' prefix (e.g., v1.0.0)
         */
        formattedVersion: `v${version}`,

        /**
         * Returns the major version number
         */
        majorVersion: parseInt(version.split('.')[0], 10),

        /**
         * Returns the minor version number
         */
        minorVersion: parseInt(version.split('.')[1], 10),

        /**
         * Returns the patch version number
         */
        patchVersion: parseInt(version.split('.')[2], 10),

        /**
         * Return any beta, alpha, or other suffix extracts from the version string /'1.0.0-beta.11'/ the suffix is 'beta.11';
         */
        suffixVersion: version.split('-')[1],
    };
}

export default useAppVersion;
