import { onMounted, onUnmounted, ref, type Ref } from 'vue';

export interface Position {
    x: number;
    y: number;
}

export interface SelectedVerse {
    book: string;
    chapter: number;
    verse: number;
    text: string;
}

export interface UseTextSelectionOptions {
    onCopy?: (withVerseNumbers: boolean) => void;
    onShare?: () => void;
    onClose?: () => void;
}

/**
 * Composable for advanced text selection handling in Bible chapter content.
 * Handles extracting selected verses, formatting for copy/share, and context menu positioning.
 * Strictly typed and Vue 3 best practices.
 */
export function useTextSelection(
    menuRef: Ref<HTMLElement | null>,
    options: UseTextSelectionOptions = {},
) {
    // State for menu visibility and position
    const showSelectionMenu = ref(false);
    const menuPosition = ref<Position>({ x: 0, y: 0 });
    const selectedText = ref('');
    const selectedVerses = ref<SelectedVerse[]>([]);

    /**
     * <PERSON>les text selection and shows the context menu if text is selected
     * Extracts selected verses and positions menu.
     */
    const handleTextSelection = (event: MouseEvent) => {
        // Ignore right mouse button (right-click)
        if (event && event.button === 2) return;
        const selection = window.getSelection();
        if (!selection || selection.toString().trim() === '') {
            // No text selected, don't show menu
            return;
        }

        // Get selected text
        selectedText.value = selection.toString().trim();

        // Collect verse references from the selection
        selectedVerses.value = [];
        if (!selection.rangeCount) return;
        const range = selection.getRangeAt(0);
        const startContainer = range.startContainer;
        const endContainer = range.endContainer;

        // Find all verse elements that are part of the selection
        const verseElements = document.querySelectorAll('.verse-unit');
        let inSelection = false;
        verseElements.forEach((verseEl) => {
            if (verseEl.contains(startContainer)) {
                inSelection = true;
            }
            if (inSelection) {
                const verseNum = verseEl.getAttribute('data-verse');
                const reference = verseEl.getAttribute('data-reference');
                if (verseNum && reference) {
                    const [bookChapter, verse] = reference.split(',');
                    const [book, chapter] = bookChapter.split(' ');
                    const verseText = verseEl.textContent?.trim() || '';
                    selectedVerses.value.push({
                        book,
                        chapter: parseInt(chapter),
                        verse: parseInt(verse),
                        text: verseText,
                    });
                }
            }
            if (verseEl.contains(endContainer)) {
                inSelection = false;
            }
        });

        // Position the context menu near the highlighted text
        if (selection && selection.rangeCount > 0) {
            const range = selection.getRangeAt(0);
            const rect = range.getBoundingClientRect();
            const viewportWidth = window.innerWidth;
            const viewportHeight = window.innerHeight;
            const menuWidth = 200;
            const menuHeight = 120;
            const margin = 10;
            let x = rect.left + window.scrollX;
            let y = rect.bottom + window.scrollY;
            if (x < margin) x = margin;
            if (x + menuWidth > viewportWidth + window.scrollX - margin)
                x = viewportWidth + window.scrollX - menuWidth - margin;
            if (y + menuHeight > viewportHeight + window.scrollY - margin) {
                y = y - menuHeight - margin;
                if (y < margin) {
                    y = rect.top + window.scrollY - menuHeight - 8;
                    if (y < margin) y = rect.bottom + window.scrollY + 8;
                }
            }
            menuPosition.value = { x, y };
            showSelectionMenu.value = true;
        }
    };

    /**
     * Type guard to check if the Clipboard API is available
     */
    const canUseClipboard = () => {
        return (
            typeof window !== 'undefined' &&
            'navigator' in window &&
            'clipboard' in window.navigator &&
            typeof window.navigator.clipboard?.writeText === 'function'
        );
    };

    /**
     * Type guard to check if the Web Share API is available
     */
    const canShare = () => {
        return (
            typeof window !== 'undefined' &&
            'navigator' in window &&
            'share' in window.navigator &&
            typeof window.navigator.share === 'function'
        );
    };

    /**
     * Removes leading verse numbers and trims the text
     * @param text The verse text to clean
     * @returns The cleaned text without leading verse numbers
     */
    const cleanVerseText = (text: string): string => {
        return text.replace(/^\s*\d+[ .]*/, '').trim();
    };

    /**
     * Formats a list of verses with a proper reference and cleaned verse numbers.
     * @param verses List of SelectedVerse
     * @returns Formatted string
     */
    function formatVersesWithNumbers(verses: SelectedVerse[]): string {
        if (!verses.length) return '';
        const bookName = verses[0].book;
        const chapterNum = verses[0].chapter;
        const verseNumbers = verses.map((v) => v.verse).sort((a, b) => a - b);
        const minVerse = verseNumbers[0];
        const maxVerse = verseNumbers[verseNumbers.length - 1];
        let reference = `${bookName} ${chapterNum}`;
        if (minVerse === maxVerse) {
            reference += `,${minVerse}`;
        } else {
            reference += `,${minVerse}-${maxVerse}`;
        }
        let result = `${reference}:\n\n`;
        verses.forEach((verse) => {
            // Remove leading verse number (1-100) and optional space/punctuation
            const cleanText = cleanVerseText(verse.text);
            result += `${verse.verse} ${cleanText}\n`;
        });
        return result;
    }

    /**
     * Formats verses into a single text string without verse numbers
     * @param verses Array of verse objects
     * @returns Formatted text with verses joined by spaces
     */
    const formatVersesWithoutNumbers = (verses: SelectedVerse[]): string => {
        const reference = formatVerseReference(verses);
        const versesText = verses
            .map(verse => cleanVerseText(verse.text))
            .join(' ');
        return `${reference}:\n\n${versesText}`;
    };

    const handleCopy = async (withVerseNumbers = false) => {
        try {
            let textToCopy = '';
            if (withVerseNumbers && selectedVerses.value.length > 0) {
                textToCopy = formatVersesWithNumbers(selectedVerses.value);
            } else if (selectedVerses.value.length > 0) {
                textToCopy = formatVersesWithoutNumbers(selectedVerses.value);
            } else {
                textToCopy = selectedText.value;
            }
            // Append website URL
            textToCopy += `\n\nesrabibel.de`;
            if (canUseClipboard()) {
                await window.navigator.clipboard.writeText(textToCopy);
                options.onCopy?.(withVerseNumbers);
            } else {
                console.warn('Clipboard API not available');
            }
        } catch (err) {
            console.error('Failed to copy text:', err);
        }
    };

    /**
     * Shares the selected text using the Web Share API if available; falls back to copy.
     */
    const handleShare = async () => {
        try {
            if (canShare()) {
                const shareText = selectedVerses.value.length > 0
                    ? formatVersesWithoutNumbers(selectedVerses.value)
                    : selectedText.value;

                await window.navigator.share({
                    text: `${shareText}\n\nesrabibel.de`,
                });
                options.onShare?.();
            } else {
                // Fallback to copy if share not available
                await handleCopy(false); // Use false to match the share behavior (no verse numbers)
            }
        } catch (err: unknown) {
            if (err instanceof Error && err.name !== 'AbortError') {
                console.error('Failed to share:', err);
            }
        }
    };

    /**
     * Hides the text selection menu
     */
    const hideMenu = () => {
        showSelectionMenu.value = false;
        options.onClose?.();
    };

    /**
     * Handles right-click (contextmenu) events to show the custom menu at mouse position
     */
    function handleCustomContextMenu(event: MouseEvent) {
        event.preventDefault();
        event.stopPropagation(); // Prevent menu flicker by stopping propagation
        // Do NOT clear selection or selected verses; just reposition and show the menu
        menuPosition.value = {
            x: event.clientX + window.scrollX,
            y: event.clientY + window.scrollY,
        };
        showSelectionMenu.value = true;
        // Do not modify selectedText or selectedVerses here
    }

    function formatVerseReference(verses: SelectedVerse[]): string {
        if (!verses.length) return '';

        const bookName = verses[0].book;
        const chapter = verses[0].chapter;
        const verseNumbers = verses.map(v => v.verse).sort((a, b) => a - b);

        let verseRange = verseNumbers[0].toString();
        if (verseNumbers.length > 1) {
            // Find consecutive ranges
            const ranges: number[][] = [];
            let start = verseNumbers[0];
            let prev = start;

            for (let i = 1; i < verseNumbers.length; i++) {
                if (verseNumbers[i] === prev + 1) {
                    prev = verseNumbers[i];
                } else {
                    ranges.push([start, prev]);
                    start = verseNumbers[i];
                    prev = start;
                }
            }
            ranges.push([start, prev]);

            // Format the ranges
            verseRange = ranges.map(([s, e]) =>
                s === e ? s.toString() : `${s}-${e}`
            ).join(',');
        }

        return `${bookName} ${chapter},${verseRange}`;
    }

    // Handle clicks outside the menu
    const handleClickOutside = (event: MouseEvent) => {
        // Ignore right-click/contextmenu events so menu doesn't close immediately after opening
        if (event.type === 'contextmenu') return;
        const el = menuRef.value;
        if (!el || !(el instanceof HTMLElement)) return;
        if (!el.contains(event.target as Node)) {
            hideMenu();
        }
    };

    // Handle keyboard events
    const handleKeyDown = (event: KeyboardEvent) => {
        if (event.key === 'Escape') {
            hideMenu();
        }
    };

    onMounted(() => {
        document.addEventListener('mousedown', handleClickOutside);
        document.addEventListener('contextmenu', handleClickOutside); // Listen for right-clicks as well
        document.addEventListener('keydown', handleKeyDown);
    });

    onUnmounted(() => {
        document.removeEventListener('mousedown', handleClickOutside);
        document.removeEventListener('contextmenu', handleClickOutside); // Clean up
        document.removeEventListener('keydown', handleKeyDown);
    });

    /**
     * Mobile long-press support (pointer events, touch only)
     * Uses PointerEvent for universal browser support (including Safari).
     */
    let longPressTimer: ReturnType<typeof setTimeout> | null = null;
    let pointerStartX = 0;
    let pointerStartY = 0;
    const LONG_PRESS_DURATION = 1000; // Increased from 500ms to 1000ms for better text selection
    const MOVE_THRESHOLD = 15; // Increased from 10px to 15px for better touch handling

    /**
     * Call on pointerdown: starts long-press timer (touch only)
     */
    function handlePointerDown(event: PointerEvent) {
        if (event.pointerType !== 'touch') return;
        pointerStartX = event.clientX;
        pointerStartY = event.clientY;

        // Clear any existing selection
        const selection = window.getSelection();
        if (selection) {
            selection.removeAllRanges();
        }

        longPressTimer = setTimeout(() => {
            // Check if we have a valid text selection
            const selection = window.getSelection();
            const selectedText = selection?.toString().trim() || '';

            if (selectedText) {
                // If we have selected text, show the menu at the selection
                const range = selection?.getRangeAt(0);
                if (range) {
                    const rect = range.getBoundingClientRect();
                    menuPosition.value = {
                        x: rect.left + window.scrollX,
                        y: rect.bottom + window.scrollY
                    };
                    showSelectionMenu.value = true;
                    event.preventDefault();
                    event.stopPropagation();
                }
            } else {
                // If no text is selected, show menu at touch position
                menuPosition.value = {
                    x: event.clientX + window.scrollX,
                    y: event.clientY + window.scrollY,
                };
                showSelectionMenu.value = true;
            }
        }, LONG_PRESS_DURATION);
    }

    /**
     * Call on pointerup/pointercancel: cancels timer if not long enough (touch only)
     */
    function handlePointerUp(event: PointerEvent) {
        if (event.pointerType !== 'touch') return;

        // Clear any pending long press timer
        if (longPressTimer) {
            clearTimeout(longPressTimer);
            longPressTimer = null;
        }

        // Check if we have a selection after the user lifts their finger
        setTimeout(() => {
            const selection = window.getSelection();
            if (!selection) return;

            const selectedTextValue = selection.toString().trim();
            if (!selectedTextValue) return;

            // Update the ref with the selected text
            selectedText.value = selectedTextValue;

            // Try to get the verse information from the selection
            const range = selection.getRangeAt(0);
            if (!range) return;

            const verseElements = document.querySelectorAll('.verse-unit');
            const verses: SelectedVerse[] = [];

            verseElements.forEach((verseEl) => {
                if (range.intersectsNode(verseEl)) {
                    const verseNum = verseEl.getAttribute('data-verse');
                    const reference = verseEl.getAttribute('data-reference');
                    if (verseNum && reference) {
                        const [bookChapter, verse] = reference.split(',');
                        const [book, chapter] = bookChapter.split(' ');
                        const verseText = verseEl.textContent?.trim() || '';
                        verses.push({
                            book,
                            chapter: parseInt(chapter),
                            verse: parseInt(verse),
                            text: verseText,
                        });
                    }
                }
            });

            if (verses.length > 0) {
                selectedVerses.value = verses;
                // Position the menu at the selection
                const rect = range.getBoundingClientRect();
                menuPosition.value = {
                    x: rect.left + window.scrollX,
                    y: rect.bottom + window.scrollY
                };
                showSelectionMenu.value = true;
            }
        }, 100); // Small delay to ensure selection is complete
    }

    /**
     * Call on pointermove: cancels timer if user moves too far (touch only)
     */
    function handlePointerMove(event: PointerEvent) {
        if (event.pointerType !== 'touch') return;
        if (!longPressTimer) return;
        const dx = event.clientX - pointerStartX;
        const dy = event.clientY - pointerStartY;
        if (Math.sqrt(dx * dx + dy * dy) > MOVE_THRESHOLD) {
            clearTimeout(longPressTimer);
            longPressTimer = null;
        }
    }

    /**
     * Call on pointercancel: always cancels timer (touch only)
     */
    function handlePointerCancel(event: PointerEvent) {
        if (event.pointerType !== 'touch') return;
        if (longPressTimer) {
            clearTimeout(longPressTimer);
            longPressTimer = null;
        }
    }

    return {
        showSelectionMenu,
        menuPosition,
        selectedText,
        selectedVerses,
        handleTextSelection,
        handleCopy,
        handleShare,
        hideMenu,
        canShare,
        canUseClipboard,
        handleCustomContextMenu,
        handlePointerDown,
        handlePointerUp,
        handlePointerMove,
        handlePointerCancel,
    };
}
