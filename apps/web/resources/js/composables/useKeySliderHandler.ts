// SheetKeySliderHandler.ts
// Utility to provide reliable keyboard slider handling for Sheet.vue drag handle
import { Ref, watch } from 'vue';

export interface SheetSliderOptions {
  min: number;
  max: number;
  step: number;
  onAdjust: (delta: number) => void;
  onClose: () => void;
  value: Ref<number>;
}

/**
 * Attach ARIA slider keyboard handling to a given element.
 * Handles up/down/left/right, Home/End, Enter/Space for ARIA slider.
 * Returns a cleanup function to remove the event listeners.
 */
export function useSheetKeySlider(
  el: Ref<HTMLElement|null>,
  opts: SheetSliderOptions
) {
  function handleArrow(e: KeyboardEvent) {
    // DEBUG: log all key events
    console.debug('SheetKeySliderHandler', e.type, e.key, e.code, e.keyCode, e);
    // Handle both key and keyCode for cross-browser support
    if (e.key === 'ArrowUp' || e.code === 'ArrowUp' || e.keyCode === 38) {
      opts.onAdjust(opts.step);
      e.preventDefault();
      return false;
    }
      if (e.key === 'ArrowDown' || e.code === 'ArrowDown' || e.keyCode === 40) {
        console.log('ArrowDown')
      opts.onAdjust(-opts.step);
      e.preventDefault();
      return false;
    }
    if (e.key === 'ArrowRight' || e.code === 'ArrowRight' || e.keyCode === 39) {
      opts.onAdjust(opts.step);
      e.preventDefault();
      return false;
    }
    if (e.key === 'ArrowLeft' || e.code === 'ArrowLeft' || e.keyCode === 37) {
      opts.onAdjust(-opts.step);
      e.preventDefault();
      return false;
    }
    if (e.key === 'Home' || e.code === 'Home') {
      opts.onAdjust(opts.min - opts.value.value);
      e.preventDefault();
      return false;
    }
    if (e.key === 'End' || e.code === 'End') {
      opts.onAdjust(opts.max - opts.value.value);
      e.preventDefault();
      return false;
    }
    if (e.key === 'Enter' || e.key === ' ' || e.code === 'Space' || e.keyCode === 32) {
      opts.onClose();
      e.preventDefault();
      return false;
    }
    // Let Escape and other keys pass through
  }

  function onKeyDown(e: KeyboardEvent) {
    handleArrow(e);
  }
  function onKeyUp(e: KeyboardEvent) {
    handleArrow(e);
  }

  let target: HTMLElement|null = null;
  const cleanup = () => {
    if (target) {
      target.removeEventListener('keydown', onKeyDown);
      target.removeEventListener('keyup', onKeyUp);
    }
  };
  watch(
    () => el.value,
    (val, oldVal) => {
      if (oldVal) {
        oldVal.removeEventListener('keydown', onKeyDown);
        oldVal.removeEventListener('keyup', onKeyUp);
      }
      if (val) {
        target = val;
        val.addEventListener('keydown', onKeyDown);
        val.addEventListener('keyup', onKeyUp);
      }
    },
    { immediate: true }
  );
  return cleanup;
}
