<template>
    <div class="mx-auto max-w-md text-center">
        <div
            class="border-theme-300 hover:border-theme-400 cursor-pointer border-2 border-dashed p-6"
            @dragover.prevent
            @drop.prevent="handleDrop"
            @click="triggerFileInputClick"
        >
            <p class="text-theme-600">
                Drag & Drop your file here or click to select
            </p>
        </div>
        <input
            ref="fileInput"
            type="file"
            class="hidden"
            @change="handleFileSelect"
        />
        <div
            v-if="uploadProgress > 0"
            class="bg-theme-200 relative mt-4 h-2 w-full"
        >
            <div
                class="absolute top-0 left-0 h-full bg-green-500 transition-all"
                :style="{ width: uploadProgress + '%' }"
            ></div>
        </div>
        <p v-if="statusMessage" class="text-theme-700 mt-4">
            {{ statusMessage }}
        </p>

        <div class="mt-8">
            <h2 class="mb-4 text-lg font-bold">Uploaded Files</h2>
            <ul>
                <li v-for="upload in uploads" :key="upload.id" class="mb-2">
                    <span>{{ upload.filename }}</span>
                    <span>{{ upload.book_name }}</span>
                    <span
                        :class="{
                            'text-green-500': upload.status === 'successful',
                            'text-red-500': upload.status !== 'successful',
                        }"
                    >
                        {{ upload.status }}
                    </span>
                </li>
            </ul>
        </div>
    </div>
</template>

<script setup lang="ts">
import { usePage } from '@inertiajs/vue3';
import axios, { AxiosProgressEvent } from 'axios';
import { ref } from 'vue';

interface Upload {
    id: number;
    filename: string;
    book_name: string;
    status: string;
}

const uploadProgress = ref<number>(0);
const statusMessage = ref<string>('');
const fileInput = ref<HTMLInputElement | null>(null);

// First cast to unknown, then to the expected type
const uploads = (usePage().props as unknown as { uploads: Upload[] }).uploads;

// Define common Bible book names
const handleDrop = (event: DragEvent) => {
    const files = event.dataTransfer?.files;
    if (files && files.length) {
        uploadFile(files[0]);
    }
};

const handleFileSelect = (event: Event) => {
    const target = event.target as HTMLInputElement;
    const files = target.files;
    if (files && files.length) {
        uploadFile(files[0]);
    }
};

const triggerFileInputClick = () => {
    fileInput.value?.click();
};

const uploadFile = (file: File) => {
    const formData = new FormData();
    formData.append('file', file);

    axios
        .post('/api/upload', formData, {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
            onUploadProgress: (progressEvent: AxiosProgressEvent) => {
                if (progressEvent.total) {
                    uploadProgress.value = Math.round(
                        (progressEvent.loaded * 100) / progressEvent.total,
                    );
                }
            },
        })
        .then(() => {
            statusMessage.value = 'Upload erfolgreich!';
            uploadProgress.value = 0; // Reset progress after success
        })
        .catch((error) => {
            statusMessage.value = 'Upload fehlgeschlagen: ' + error.message;
        });
};
</script>

<style scoped>
/* Minimal custom CSS if needed */
</style>
