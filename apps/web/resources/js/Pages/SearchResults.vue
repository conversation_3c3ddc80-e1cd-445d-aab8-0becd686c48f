<script setup lang="ts">
import SearchResultItem from '@/Components/Search/SearchResultItem.vue';
import { useSearchResults } from '@/composables/useSearchResults';
import GuestLayout from '@/Layouts/GuestLayout.vue';
import { useSearchSettingsStore } from '@/stores/searchSettingsStore';
import type { SearchResultsState } from '@esbo/types';
import { Head, router } from '@inertiajs/vue3';
import { computed, nextTick, onMounted, onUnmounted, ref, watch } from 'vue';

interface Props {
    query: string;
    results: SearchResultsState;
    filters: {
        types: string[];
    };
}

const props = defineProps<Props>();
const emit = defineEmits<{
    (e: 'update:results', value: Props['results']): void;
}>();

const searchSettingsStore = useSearchSettingsStore();

const {
    isLoading,
    hasError,
    currentPage,
    allResults,
    hasResults,
    highlightContent,
    loadMoreResults,
} = useSearchResults(props.results);

// console.log('props.results:', props.results);

// Get translated types from the store
const translatedTypes = computed(() => {
    return props.filters.types.map((type) => {
        const typeObj = searchSettingsStore.availableSearchTypes.find(
            (t) => t.value === type,
        );
        return {
            value: type,
            label: typeObj ? typeObj.label : type,
        };
    });
});

// Debug output for search results
//console.log('allResults:', allResults);
const pageTitle = computed(() => {
    return (
        'Suche nach "' +
        props.query +
        '"(' +
        props.results.data.length +
        ' Ergebnisse)'
    );
});

// Infinite scroll setup
const observer = ref<IntersectionObserver | null>(null);
const loadingTrigger = ref<HTMLElement | null>(null);

onMounted(() => {
    observer.value = new IntersectionObserver(
        async (entries) => {
            if (entries[0].isIntersecting && !isLoading.value) {
                console.log(
                    'Loading trigger intersecting, loading more results',
                );
                const newResults = await loadMoreResults(
                    props.query,
                    props.filters.types,
                );
                if (newResults) {
                    console.log('New results loaded:', newResults);
                    emit('update:results', newResults as Props['results']);
                }
            }
        },
        {
            rootMargin: '0px 0px 300px 0px', // Increased margin to load earlier
            threshold: 0.1, // Trigger when at least 10% of the element is visible
        },
    );

    //console.log('Props:', props);

    // Use nextTick to ensure DOM is ready before observing
    nextTick(() => {
        if (loadingTrigger.value) {
            console.log('Observing loading trigger element');
            observer.value?.observe(loadingTrigger.value);
        } else {
            console.warn('Loading trigger element not found');
        }
    });
});

onUnmounted(() => {
    if (observer.value && loadingTrigger.value) {
        observer.value.unobserve(loadingTrigger.value);
        observer.value.disconnect();
    }
});

function getTypeColorClasses(type: string): string {
    const typeColors: Record<string, string> = {
        books: 'bg-type-book text-white',
        verses: 'bg-type-verse text-white',
        footnotes: 'bg-type-footnote text-white',
        words: 'bg-type-word text-white',
        metadata: 'bg-type-metadata text-white',
    };

    return (
        typeColors[type] ||
        'bg-theme-100 dark:bg-theme-700 text-theme-800 dark:text-theme-200'
    );
}

// Initialize selected types from props
const selectedTypes = ref<string[]>(props.filters.types || []);

// Update selectedTypes when props change
watch(
    () => props.filters.types,
    (newTypes) => {
        if (newTypes && Array.isArray(newTypes)) {
            selectedTypes.value = [...newTypes];
        }
    },
    { immediate: true },
);

// Watch for changes in selected search types and trigger a new search
// Only when user manually changes types after initial search
watch(
    () => selectedTypes.value,
    (newTypes, oldTypes) => {
        // Skip if this is the initial setting or if types haven't actually changed
        if (
            !props.query ||
            newTypes.length === 0 ||
            JSON.stringify(newTypes) === JSON.stringify(oldTypes)
        ) {
            return;
        }

        // Directly perform a new search with the updated types
        const url = route('search.query', { query: props.query });
        router.post(
            url,
            {
                types: newTypes,
            },
            {
                preserveState: true,
                preserveScroll: true,
                only: ['query', 'results', 'filters'],
            },
        );
    },
    { deep: true },
);
</script>

<template>
    <GuestLayout class="bg-theme-100 dark:bg-theme-900">
        <Head :title="pageTitle" />
        <div class="mt-10 py-10">
            <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
                <div
                    class="dark:bg-theme-800 bg-theme overflow-hidden shadow-xs sm:rounded-lg"
                >
                    <div class="text-theme-900 dark:text-theme-100 p-6">
                        <!-- Search Info -->
                        <div v-if="props.query" class="mb-6">
                            <h2 class="text-lg font-semibold">
                                Suchergebnisse für "{{ props.query }}" in
                                <span
                                    v-for="type in translatedTypes"
                                    :key="type.value"
                                    class="ml-2 inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium"
                                    :class="getTypeColorClasses(type.value)"
                                >
                                    {{ type.label }}
                                </span>
                            </h2>
                            <p
                                class="text-theme-600 dark:text-theme-400 text-sm"
                            >
                                {{ props.results.metadata.total }} Ergebnisse
                                gefunden
                            </p>
                        </div>

                        <!-- Results -->
                        <div v-if="allResults.length > 0" class="space-y-4">
                            <SearchResultItem
                                v-for="result in allResults"
                                :key="result.id"
                                :result="result"
                                :query="props.query"
                                :highlight-content="highlightContent"
                            />
                        </div>

                        <!-- No Results -->
                        <div v-else-if="!hasResults" class="py-4 text-center">
                            <p class="text-theme-600 dark:text-theme-400">
                                Keine Ergebnisse gefunden.
                            </p>
                        </div>

                        <!-- Loading State -->
                        <div
                            v-if="isLoading"
                            class="mt-4 flex items-center justify-center space-x-2"
                        >
                            <div
                                class="border-theme-900 dark:border-theme-100 h-4 w-4 animate-spin rounded-full border-2 border-t-transparent"
                                role="status"
                                aria-label="Lädt..."
                            />
                            <span>Lade weitere Ergebnisse...</span>
                        </div>

                        <!-- Error State -->
                        <div v-if="hasError" class="mt-4 text-center">
                            <p class="text-red-600 dark:text-red-400">
                                Fehler beim Laden weiterer Ergebnisse
                            </p>
                            <button
                                class="mt-2 rounded-md bg-red-600 px-4 py-2 text-sm font-medium text-white hover:bg-red-700 focus:ring-2 focus:ring-red-500 focus:ring-offset-2 focus:outline-hidden"
                                aria-label="Erneut versuchen"
                                @click="
                                    loadMoreResults(
                                        props.query,
                                        props.filters.types,
                                    )
                                "
                            >
                                Erneut versuchen
                            </button>
                        </div>

                        <!-- Loading Trigger for Infinite Scroll -->
                        <div
                            v-if="currentPage < props.results.metadata.lastPage"
                            ref="loadingTrigger"
                            class="py-4 text-center"
                            aria-hidden="true"
                        >
                            <div
                                class="h-8 w-full"
                                data-testid="loading-trigger"
                            ></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </GuestLayout>
</template>
