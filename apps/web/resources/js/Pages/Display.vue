<template>
    <GuestLayout>
        <Head :title="pageTitle" />
        <div class="space-y-12 px-4 py-8 sm:px-6 lg:px-8" :style="contentStyle">
            <template
                v-for="section in visibleChapters"
                :key="getChapterId(section)"
            >
                <template v-if="!bookHasContent(section)">
                    <UnavailableBookNotice
                        :book="section.book"
                        :available-books="availableBooks"
                    />
                </template>
                <template v-else>
                    <ChapterWrapper
                        :ref="(el) => setChapterRef(el, section)"
                        :section="section"
                    />
                </template>
            </template>

            <LoadingSpinner v-if="isLoading" />
            <SentryButton />
        </div>

        <FootnoteTooltip
            v-if="bibleStore.footnoteState "
            :footnote-state="bibleStore.footnoteState"
            :reference-el="bibleStore.footnoteState.referenceEl"
            @close="bibleStore.closeFootnote"
        />

        <ChapterNavButtons v-if="!isMobile" />
    </GuestLayout>
</template>

<script setup lang="ts">
import ChapterWrapper from '@/Components/BibleDisplay/ChapterWrapper.vue';
import FootnoteTooltip from '@/Components/BibleDisplay/FootnoteTooltip.vue';
import UnavailableBookNotice from '@/Components/BibleDisplay/UnavailableBookNotice.vue';
import LoadingSpinner from '@/Components/common/LoadingSpinner.vue';
import ChapterNavButtons from '@/Components/Navigation/ChapterNavButtons.vue';
import { useResponsiveStore } from '@/composables/useResponsiveStore';
import { useScrollManager } from '@/composables/useScrollManager';
import GuestLayout from '@/Layouts/GuestLayout.vue';
import { useBibleStore } from '@/stores/bible/bibleSectionStore';
import { useTextSettingsStore } from '@/stores/textSettingsStore';
import { getChapterId, isChapterSection } from '@/utils/bibleNavigationUtils';
import type { DisplayResponse, Section } from '@esbo/types';
import { Head } from '@inertiajs/vue3';
import { computed, nextTick, onMounted, onUnmounted } from 'vue';
import SentryButton from '@/Components/Navigation/SentryButton.vue';

// Props
const props = defineProps<DisplayResponse>();

// Stores
const bibleStore = useBibleStore();
const textSettings = useTextSettingsStore();

// Get responsive store state
const { isMobile } = useResponsiveStore();

const pageTitle = computed(() => {
    if (!bibleStore.currentBook) {
        return 'ESB';
    }

    if (bibleStore.currentChapter) {
        return `${bibleStore.currentBook.slug} ${bibleStore.currentChapter}`;
    }

    return bibleStore.currentBook.slug;
});

// Available books computed from store
const availableBooks = computed(() =>
    bibleStore.books.availableBooks
        .filter((book) => bibleStore.isBookAvailable(book.slug))
        .sort((a, b) => a.order - b.order),
);

// Check if a book has content
const bookHasContent = (section: Section) => {
    if (!isChapterSection(section)) {
        return true; // Frontmatter sections are always considered to have content
    }
    return bibleStore.isBookAvailable(section.book.slug);
};

// Content style for proper spacing
const contentStyle = computed(() => {
    // Base margin for sidebars
    const sideMargin = !isMobile.value ? '6vw' : '0';

    // Content margin based on text settings
    let contentMargin;
    switch (textSettings.marginSize) {
        case 'margin-wide':
            contentMargin = !isMobile.value ? '10rem' : '2rem';
            break;
        case 'margin-narrow':
            contentMargin = !isMobile.value ? '1rem' : '0.5rem';
            break;
        case 'margin-normal':
        default:
            contentMargin = !isMobile.value ? '4rem' : '1rem';
    }

    return {
        marginLeft: `calc(${sideMargin} + ${contentMargin})`,
        marginRight: `calc(${sideMargin} + ${contentMargin})`,
        'margin-top':
            !textSettings.isInfiniteScrollEnabled && !isMobile.value
                ? '12vh'
                : 'auto',
    };
});

// Visible chapters
const visibleChapters = computed(() => bibleStore.visibleChapters);
const isLoading = computed(
    () => bibleStore.isLoadingNext || bibleStore.isLoadingPrevious,
);

// Scroll management
const {
    chapterRefs,
    setChapterRef,
    handleScroll,
    scrollToVerse,
    scrollToVerseRange,
    scrollToChapter,
    enableScrollHandling,
} = useScrollManager({
    findMostVisibleChapter: () => {
        let maxVisibleHeight = 0;
        let mostVisibleChapterId: string | null = null;

        chapterRefs.value.forEach((el, id) => {
            try {
                if (!el) return;

                const rect = el.getBoundingClientRect();
                const visibleHeight =
                    Math.min(rect.bottom, window.innerHeight) -
                    Math.max(rect.top, 0);

                if (visibleHeight > maxVisibleHeight) {
                    maxVisibleHeight = visibleHeight;
                    mostVisibleChapterId = id;
                }
            } catch (error) {
                console.error('Error calculating visibility:', error);
            }
        });

        return mostVisibleChapterId;
    },
});

import { watch } from 'vue';
// Watch for changes in bibleStore.footnoteState
watch(
    () => bibleStore.footnoteState,
    (_val, _oldVal) => {
        /*const id = _val?.footnote?.id;
        const oldId = _oldVal?.footnote?.id;
        console.log('[Display] footnoteState changed:', { _oldVal, _val, oldId, id });*/
    },
    { immediate: true }
);
// Lifecycle hooks
onMounted(async () => {
    // Initialize the store with the initial data.
    // The store internally checks if it's already initialized for this context.
    bibleStore.initialize(props);

    // Set up scroll listener if not already set (idempotent addEventListener is safe)
    if (textSettings.isInfiniteScrollEnabled) {
        window.addEventListener('scroll', handleScroll);
    }

    // Initial scroll to verse if needed.
    // Ensure this runs after store initialization is potentially complete.
    await initializeDisplay();
});

onUnmounted(() => {
    window.removeEventListener('scroll', handleScroll);
});

// Initialize display
async function initializeDisplay(): Promise<void> {
    // Ensure we have valid navigation data before proceeding
    if (!props.navigation) {
        console.warn('No navigation data available');
        return;
    }

    // Wait for the next tick to ensure DOM is updated
    await nextTick();

    try {
        // Log navigation data for debugging
        //console.log('Initializing display with navigation:', props.navigation);

        // Handle verse range scroll
        if (props.navigation.scrollToVerseRange) {
            const { verseStart, verseEnd, verseRanges, chapter } =
                props.navigation.scrollToVerseRange;

            if (!chapter) {
                throw new Error('Missing chapter in scrollToVerseRange');
            }

            if (verseRanges && verseRanges.length > 0) {
                // Handle multiple verse ranges
                await scrollToVerseRange(verseStart, verseEnd, chapter);
            } else {
                // Handle single verse range
                await scrollToVerseRange(verseStart, verseEnd, chapter);
            }
        }
        // Handle single verse scroll
        else if (props.navigation.scrollToVerse) {
            const { verseStart, chapter } = props.navigation.scrollToVerse;

            if (!chapter || !verseStart) {
                throw new Error('Missing chapter or verse in scrollToVerse');
            }

            await scrollToVerse(verseStart, chapter);
        }
        // Handle chapter scroll
        else if (
            props.navigation.scrollToChapter &&
            textSettings.isInfiniteScrollEnabled
        ) {
            const { book, chapter } = props.navigation.scrollToChapter;

            if (!book || !chapter) {
                throw new Error('Missing book or chapter in scrollToChapter');
            }

            await scrollToChapter(`${book}${chapter}`);
        }
        // Handle frontmatter scroll
        else if (props.navigation.scrollToFrontmatter) {
            const slug = props.navigation.scrollToFrontmatter.slug;

            if (!slug) {
                throw new Error('Missing slug in scrollToFrontmatter');
            }

            await scrollToChapter(slug as string);
        }
        // Default to first verse of first chapter if we have a book
        else if (bibleStore.currentBook && props.navigation.currentBook) {
            await scrollToVerse(1, 1, false);
        }
    } catch (error) {
        console.error('Error during display initialization:', error);
        // Always ensure scroll handling is re-enabled
        enableScrollHandling();
        // Re-throw the error for potential error boundary handling
        throw error;
    }
}
</script>
