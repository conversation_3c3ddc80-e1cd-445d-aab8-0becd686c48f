<template>
  <GuestLayout>
    <Head title="Changelog" />
    <div class="container mx-auto px-6 sm:py-32 py-10">
      <h1 class="text-3xl text-theme-800 font-bold mb-8 dark:text-theme-200">
        Änderungsprotokoll
      </h1>

      <p class="mb-8 text-theme-600 dark:text-theme-400">
        Unsere Webseite ist aktuell als „Beta-Version“ gekennzeichnet. Das bedeutet: Die wichtigsten Funktionen zum Bibellesen stehen bereits zur Verfügung, und wir entwickeln die Seite kontinuierlich weiter. Dein Feedback ist uns dabei sehr wichtig – über den „Fehler melden“ Button (auf Mobilgeräten als Icon dargestellt) kannst du uns Hinweise und Anregungen direkt mitteilen. So wird die Seite gemeinsam noch besser!
      </p>

      <div
        v-for="(release, version) in releases"
        :key="version"
        class="mb-12"
      >
        <div class="flex items-baseline gap-4 mb-4">
          <h2 class="text-2xl font-semibold text-theme-800 dark:text-theme-200">
            {{ version }}
          </h2>
          <span v-if="release.date" class="text-theme-600 dark:text-theme-400">
            {{ release.date }}
          </span>
        </div>

        <div
          v-for="(changes, section) in release.sections"
          :key="section"
          class="mb-6"
        >
          <h3 :class="getSectionClass(section)">
            {{ section }}
          </h3>
          <ul class="list-disc list-inside space-y-2">
            <li
              v-for="change in changes"
              :key="change.id"
              class="text-theme-600 dark:text-theme-400"
            >
              {{ change.content }}
            </li>
          </ul>
        </div>
      </div>
    </div>
  </GuestLayout>
</template>

<script setup lang="ts">
import GuestLayout from '@/Layouts/GuestLayout.vue';
import { Head } from '@inertiajs/vue3';

interface ChangelogEntry {
  id: string;
  content: string;
}

interface Release {
  date: string | null;
  sections: {
    [key: string]: ChangelogEntry[];
  };
}

interface Props {
  releases: {
    [version: string]: Release;
  };
}

const props = defineProps<Props>();
console.log('props', props);

function getSectionClass(section: string | number): string {
  const base = "text-xl font-medium mb-3";
  const lower = String(section).toLowerCase();

  if (lower.includes('hinzugefügt')) {
    return `${base} text-emerald-600 dark:text-emerald-400`;
  } else if (lower.includes('aktualisiert')) {
    return `${base} text-blue-600 dark:text-blue-400`;
  } else if (lower.includes('behoben')) {
    return `${base} text-red-600 dark:text-red-400`;
  } else if (lower.includes('entfernt')) {
    return `${base} text-purple-600 dark:text-purple-400`;
  } else if (lower.includes('sicherheit')) {
    return `${base} text-rose-600 dark:text-rose-400`;
  }
  return `${base} text-theme-800 dark:text-theme-200`;
}
</script>
