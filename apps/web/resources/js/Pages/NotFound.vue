<template>
    <GuestLayout>
        <div class="dark:bg-theme-900 bg-theme min-h-screen">
            <div class="mx-auto max-w-4xl px-4 py-8 sm:px-6 lg:px-8">
                <div class="text-center">
                    <!-- Enhanced SVG illustration for 404 - detailed flat art style -->
                    <div class="mx-auto mb-8 h-96 w-96">
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 512 512"
                            class="h-full w-full"
                        >
                            <!-- Background circle -->
                            <circle
                                cx="256"
                                cy="256"
                                r="200"
                                class="dark:fill-theme-800 fill-theme-400"
                            />

                            <!-- Open book base -->
                            <path
                                d="M156 176c0-8.8 7.2-16 16-16h168c8.8 0 16 7.2 16 16v160c0 8.8-7.2 16-16 16H172c-8.8 0-16-7.2-16-16V176z"
                                class="dark:fill-theme-700 fill-white"
                            />

                            <!-- Left page shadow -->
                            <path
                                d="M156 176v160c0 8.8 7.2 16 16 16h84V160h-84c-8.8 0-16 7.2-16 16z"
                                class="fill-theme-100 dark:fill-theme-600"
                            />

                            <!-- Book spine -->
                            <path
                                d="M148 176c0-13.2 10.8-24 24-24h168c13.2 0 24 10.8 24 24v160c0 13.2-10.8 24-24 24H172c-13.2 0-24-10.8-24-24V176zm16 0v160c0 4.4 3.6 8 8 8h168c4.4 0 8-3.6 8-8V176c0-4.4-3.6-8-8-8H172c-4.4 0-8 3.6-8 8z"
                                class="fill-theme-600 dark:fill-theme-500"
                            />

                            <!-- Page lines -->
                            <g
                                class="stroke-theme-300 dark:stroke-theme-500"
                                stroke-width="2"
                            >
                                <line x1="188" y1="200" x2="324" y2="200" />
                                <line x1="188" y1="220" x2="324" y2="220" />
                                <line x1="188" y1="240" x2="324" y2="240" />
                                <line x1="188" y1="260" x2="280" y2="260" />
                            </g>

                            <!-- 404 text with decorative elements -->
                            <g transform="translate(256 310)">
                                <!-- Decorative swash before -->
                                <path
                                    d="M-80 0c20 0 40-10 60-10"
                                    class="stroke-theme-600 dark:stroke-theme-400"
                                    fill="none"
                                    stroke-width="3"
                                    stroke-linecap="round"
                                />

                                <!-- 404 text -->
                                <text
                                    x="0"
                                    y="0"
                                    text-anchor="middle"
                                    class="fill-theme-900 dark:fill-theme-100 text-5xl font-bold"
                                >
                                    404
                                </text>

                                <!-- Decorative swash after -->
                                <path
                                    d="M20 0c20 0 40 10 60 10"
                                    class="stroke-theme-600 dark:stroke-theme-400"
                                    fill="none"
                                    stroke-width="3"
                                    stroke-linecap="round"
                                />
                            </g>

                            <!-- Bookmark -->
                            <path
                                d="M290 140v80l15-10 15 10v-80z"
                                class="fill-theme-950 dark:fill-theme-200"
                            />
                        </svg>
                    </div>

                    <h1
                        class="text-theme-900 dark:text-theme-100 mb-4 text-4xl font-bold"
                    >
                        Seite nicht gefunden
                    </h1>

                    <p class="text-theme-600 dark:text-theme-400 mb-8 text-xl">
                        Die angeforderte Seite konnte nicht gefunden werden.
                    </p>

                    <div class="space-y-4">
                        <a
                            href="/"
                            class="bg-theme-600 hover:bg-theme-700 inline-block rounded-lg px-6 py-3 text-white transition-colors"
                        >
                            Zur Startseite
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </GuestLayout>
</template>

<script setup lang="ts">
import GuestLayout from '@/Layouts/GuestLayout.vue';
</script>
