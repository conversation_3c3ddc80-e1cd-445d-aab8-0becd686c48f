<template>
    <GuestLayout>
        <Head title="Lesezeichen" />
        <div class="py-5 sm:py-28">
            <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
                <div
                    class="dark:bg-theme-800 overflow-hidden bg-white shadow-sm sm:rounded-lg"
                >
                    <div class="p-6 text-gray-900 dark:text-gray-100">
                        <h1 class="mb-6 text-2xl font-bold">
                            <PERSON><PERSON>
                        </h1>

                        <div
                            v-if="bookmarks.length === 0"
                            class="py-8 text-center"
                        >
                            <p class="text-gray-500 dark:text-gray-400">
                                Keine Lesezeichen gespeichert
                            </p>
                        </div>

                        <ul
                            v-else
                            class="divide-y divide-gray-200 dark:divide-gray-700"
                        >
                            <li
                                v-for="(bookmark, index) in bookmarks"
                                :key="index"
                                class="flex items-center justify-between py-4"
                            >
                                <div>
                                    <button
                                        class="text-theme-600 dark:text-theme-400 hover:text-theme-900 dark:hover:text-theme-200"
                                        @click="navigateToBookmark(bookmark)"
                                    >
                                        {{ formatBookmarkReference(bookmark) }}
                                    </button>
                                </div>
                                <button
                                    class="group text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                                    aria-label="Lesezeichen löschen"
                                    @click="removeBookmark(index)"
                                >
                                    <Icon
                                        name="Trash"
                                        class="hover:animate-wiggle h-5 w-5"
                                    />
                                </button>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </GuestLayout>
</template>

<script setup lang="ts">
import { Icon } from '@/Components/Icons';
import { useLocalStorage } from '@vueuse/core';
import { useVerseReference } from '@/composables/useVerseReference';
import GuestLayout from '@/Layouts/GuestLayout.vue';
import type { Bookmark } from '@esbo/types';
import { Head } from '@inertiajs/vue3';

const { navigateToReference } = useVerseReference();
const BOOKMARKS_KEY = 'esbo-bookmarks';
const bookmarks = useLocalStorage<Bookmark[]>(BOOKMARKS_KEY, []);


const formatBookmarkReference = (bookmark: Bookmark) => {
    return bookmark.verse
        ? `${bookmark.book} ${bookmark.chapter}:${bookmark.verse}`
        : `${bookmark.book} ${bookmark.chapter}`;
};

const navigateToBookmark = (bookmark: Bookmark) => {
    if (bookmark.verse) {
        navigateToReference(bookmark.book, bookmark.chapter, bookmark.verse);
    } else {
        navigateToReference(bookmark.book, bookmark.chapter);
    }
};

const removeBookmark = (index: number) => {
    bookmarks.value.splice(index, 1);
};
</script>
