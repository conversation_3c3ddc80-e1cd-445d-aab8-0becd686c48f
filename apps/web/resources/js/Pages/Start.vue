<template>
    <main class="relative min-h-screen" role="main">
        <Head title="EsraBibel – Die neue, urtextnahe Bibelübersetzung">
            <meta name="description" content="Entdecke die EsraBibel – eine urtextnahe, sprachgenaue Bibelübersetzung. Erfahre mehr über das Projekt, unterstütze die Übersetzungsarbeit und finde Zugang zum Wort Gottes auf eine neue, verständliche Weise." />
            <!-- OpenGraph Meta Tags -->
            <meta property="og:title" content="EsraBibel – Die neue, urtextnahe Bibelübersetzung" />
            <meta property="og:description" content="Entdecke die EsraBibel – eine urtextnahe, sprachgenaue Bibelübersetzung. Erfahre mehr über das Projekt, unterstütze die Übersetzungsarbeit und finde Zugang zum Wort Gottes auf eine neue, verständliche Weise." />
            <meta property="og:type" content="website" />
            <meta property="og:url" content="https://esrabibel.de/" />
            <meta property="og:image" content="https://esrabibel.de/images/ESB_Alle.webp" />
            <!-- Twitter Card Meta Tags -->
            <meta name="twitter:card" content="summary_large_image" />
            <meta name="twitter:title" content="EsraBibel – Die neue, urtextnahe Bibelübersetzung" />
            <meta name="twitter:description" content="Entdecke die EsraBibel – eine urtextnahe, sprachgenaue Bibelübersetzung. Erfahre mehr über das Projekt, unterstütze die Übersetzungsarbeit und finde Zugang zum Wort Gottes auf eine neue, verständliche Weise." />
            <meta name="twitter:image" content="https://esrabibel.de/images/ESB_Alle.webp" />
            <!-- JSON-LD Structured Data is injected programmatically via onMounted in <script setup> -->
        </Head>

        <!-- Background -->
        <div
            :style="{ backgroundColor: COLORS.primary }"
            class="absolute inset-0 z-0"
            aria-hidden="true"
        />

        <!-- Off-canvas Sidebar -->
        <BibleNavigationAside
            v-if="bibleStore.$state.isMobile"
            :is-open="navigationStore.isNavigationAsideOpen"
            :sections="navigationSections"
            @close="navigationStore.closeNavigationAside"
            @select-book="selectBook"
        />

        <!-- Settings Aside -->
        <SettingsAside :is-open="isSettingsOpen" @close="closeSettings" />

        <!-- Search Section -->
        <section
            class="relative z-50 flex flex-col justify-around"
            aria-labelledby="search-title"
        >
            <!-- Logo -->
            <div class="flex h-auto w-full items-center justify-center pt-10">
                <EsraLogo class="h-auto w-70" />
            </div>

            <!-- Search Field -->
            <div class="relative flex flex-col items-center justify-center">
                <div class="mx-auto w-full max-w-4xl justify-items-center">
                    <div
                        class="bg-theme/10 mx-auto max-w-2xl rounded-xl p-6 backdrop-blur-xs"
                    >
                        <BibleSearch class="in-start" />
                    </div>
                    <h1
                        id="search-title"
                        class="mt-4 md:pb-24 sm:pb-18 xs:pb-12 text-center text-2xl font-bold text-white"
                    >
                        Entdecke die
                        <span class="font-thanatos">EsraBibel</span>
                    </h1>
                </div>
            </div>
        </section>

        <section>
            <div class="relative z-10 flex items-center justify-center p-20">
                <nav
                    class="z-10 grid gap-2 shadow-xs sm:grid-cols-2 md:grid-cols-4 md:gap-0"
                    role="navigation"
                    aria-label="EsraBibel Navigation"
                >
                    <button
                        v-for="(btn, index) in navigationButtons"
                        :key="index"
                        class="text-theme-900 border-theme-200 hover:bg-theme-900 focus:ring-theme-700 focus:text-theme-700 dark:bg-theme-800 dark:border-theme-700 dark:hover:bg-theme-700 dark:focus:ring-theme-500 z-10 cursor-pointer border bg-white px-4 py-2 text-sm font-medium hover:text-white focus:z-10 focus:ring-2 dark:text-white dark:hover:text-white dark:focus:text-white"
                        :class="btnGroupClass(index)"
                        :aria-label="btn.label"
                        :aria-title="btn.label"
                        @click="btn.action"
                    >
                        {{ btn.label }}
                    </button>
                </nav>
            </div>
        </section>

        <!-- Book Section -->
        <section
            :style="{ backgroundColor: COLORS.secondary }"
            class="relative"
            aria-label="Logo showcase"
        >
            <div
                class="relative z-10 flex h-auto w-full items-center justify-center p-10 pb-20"
            >
                <EsraTextLogo class="h-auto w-100" />
            </div>

            <!-- Book Images -->
            <div
                class="relative flex h-auto w-full items-center justify-center pt-8"
            >
                <img
                    src="/public/images/ESB_Alle.webp"
                    alt="Alle EsraBibel Logbücher bisher"
                    class="h-auto w-3/4"
                    loading="lazy"
                />
            </div>

            <!-- Support Section -->
            <div
                class="relative flex h-auto w-full flex-col items-center justify-center pt-8 pb-20"
            >
                <h2 class="pb-8 text-center text-4xl font-bold text-white">
                    Das Projekt Bibelübersetzung finanziell unterstützen
                </h2>
                <p class="w-2/3 text-center text-lg text-white">
                    Die Spenden für das EsraBibel-Projekt sollen den
                    Übersetzungs- und Erarbeitungsprozess bis zum fertigen
                    Basistext abdecken. Der Verkauf eines Logbuchs oder einer
                    Bibelausgabe soll dann alle Produktkosten ab dem Basistext
                    tragen. Die Übersetzungsarbeit ist nicht im Produktpreis
                    berücksichtigt. Neben den bereits eingegangenen Spenden
                    benötigen wir 36 Monate lang jeweils 6.250 €, um die
                    EsraBibel fertigzustellen. Deine Spende kannst du auf dieser
                    Seite mit wenigen Klicks per Lastschrift, Überweisung,
                    PayPal oder Kreditkarte senden. Bitte bete für diese Arbeit
                    und erwäge, ob und wie auch du in den nächsten 36 Monaten
                    die Übersetzungsarbeit mittragen und zur Erstellung dieser
                    Bibelübersetzung beitragen kannst. Bitte bringe die
                    Gebetsanliegen regelmäßig vor den Thron der Gnade. Bitte
                    teile diese Anliegen deiner Kleingruppe oder deiner Gemeinde
                    mit und erwägt gemeinsam, diese Arbeit einmalig oder
                    monatlich finanziell zu unterstützen. Und nutze die
                    EsraBibel, lies das Wort Gottes und entdecke die
                    wundervollen Schätze in Gottes unfehlbarem Wort!
                </p>
            </div>
        </section>
    </main>
    <Footer />
</template>

<script setup lang="ts">

import { onMounted } from 'vue';

const jsonLd = {
  "@context": "https://schema.org",
  "@type": "WebSite",
  "name": "EsraBibel",
  "url": "https://esb-online.org/",
  "description": "Entdecke die EsraBibel – eine urtextnahe, sprachgenaue Bibelübersetzung. Erfahre mehr über das Projekt, unterstütze die Übersetzungsarbeit und finde Zugang zum Wort Gottes auf eine neue, verständliche Weise.",
  "inLanguage": "de",
  "publisher": {
    "@type": "Organization",
    "name": "EBTC"
  },
  "image": "https://esb-online.org/images/ESB_Alle.webp"
};

onMounted(() => {
  const script = document.createElement('script');
  script.type = 'application/ld+json';
  script.text = JSON.stringify(jsonLd);
  script.id = 'esrabibel-jsonld';
  // Remove any existing JSON-LD to avoid duplicates
  const old = document.getElementById('esrabibel-jsonld');
  if (old) old.remove();
  document.head.appendChild(script);
});

import EsraLogo from '@/Components/Logos/EsraLogo.vue';
import EsraTextLogo from '@/Components/Logos/EsraTextLogo.vue';
import BibleNavigationAside from '@/Components/Navigation/BibleNavigationAside.vue';
import BibleSearch from '@/Components/Search/BibleSearch.vue';
import SettingsAside from '@/Components/Settings/SettingsAside.vue';
import { useToggle } from '@vueuse/core';
import { useBibleStore } from '@/stores/bible/bibleSectionStore';
import { useNavigationStore } from '@/stores/navigationStore';
import type { BookView, NavigationSection } from '@esbo/types';
import { Head } from '@inertiajs/vue3';
import { computed } from 'vue';
import Footer from '@/Components/Layout/Footer.vue';

// Define color constants
const COLORS = {
    primary: '#c79e88',
    secondary: '#2e6977',
    text: '#50433C',
} as const;

// Initialize stores
const navigationStore = useNavigationStore();
const bibleStore = useBibleStore();
const [isSettingsOpen] = useToggle(false);
const closeSettings = () => { isSettingsOpen.value = false; };


// Transform sections to match NavigationSection type
const navigationSections = computed<NavigationSection[]>(() => {
    return bibleStore.books.sections.map(section => ({
        name: section.name,
        books: section.books.map(book => ({
            id: book.id,
            slug: book.slug,
            name: book.name,
            abbreviation: book.abbreviation,
            testament: book.testament,
            testamentLabel: book.testamentLabel,
            category: book.category,
            searchNames: book.searchNames,
            order: book.order,
            chapterCount: book.chapters?.length ?? 0,
            hasContent: book.hasContent ?? false,
            chapters: book.chapters ?? Array.from({ length: book.chapterCount ?? 0 }, (_, i) => i + 1)
        }))
    }));
});

function selectBook(_book: BookView) {
    navigationStore.closeNavigationAside();
}

// Define component props and types
interface NavigationButton {
    label: string;
    action: () => void;
}

const navigationButtons: NavigationButton[] = [
    {
        label: 'Was ist die EsraBibel?',
        action: () =>
            (window.location.href =
                'https://www.ebtc.org/esrabibel-esb-neue-bibeluebersetzung-urtextnah-und-sprachgenau#interview'),
    },
    {
        label: 'unterstützen',
        action: () =>
            (window.location.href =
                'https://www.ebtc.org/esrabibel-esb-neue-bibeluebersetzung-urtextnah-und-sprachgenau#spenden'),
    },
    {
        label: 'kaufen',
        action: () =>
            (window.location.href =
                'https://www.ebtc.org/esrabibel-esb-neue-bibeluebersetzung-urtextnah-und-sprachgenau#kaufen'),
    },
    {
        label: 'kontaktieren',
        action: () =>
            (window.location.href = 'mailto:<EMAIL>'),
    },
];

function btnGroupClass(index: number) {
    return index === 0
        ? 'md:rounded-l-md'
        : index === navigationButtons.length - 1
          ? 'md:rounded-r-md'
          : '';
}
</script>
