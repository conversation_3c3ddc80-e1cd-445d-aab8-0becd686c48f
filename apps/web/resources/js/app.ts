import '../css/app.css';
import './bootstrap';

import { createInertiaApp } from '@inertiajs/vue3';
import type { ErrorEvent } from '@sentry/core';
import * as Sentry from '@sentry/vue';
import axios from 'axios';
import { resolvePageComponent } from 'laravel-vite-plugin/inertia-helpers';
import { createPinia } from 'pinia';
import { createApp, DefineComponent, h } from 'vue';
import { useTextSettingsStore } from './stores/textSettingsStore';

// Import version or use the one from Vite define
let appVersion = '0.0.0';
try {
        // Try to import from version.ts (generated by export-version.js)
    const versionModule = await import('./version');
    appVersion = versionModule.APP_VERSION;
} catch (e) {
    // Fallback to Vite define if available
    const viteVersion = import.meta.env.VITE_APP_VERSION || null;
    appVersion = viteVersion || appVersion;
    console.warn('Failed to load version from version.ts, using fallback:', appVersion, e);
}

const appName = import.meta.env.VITE_APP_NAME || 'Laravel';

// Store original console methods
/*const originalConsole = {
    log: console.log,
    info: console.info,
    debug: console.debug,
    warn: console.warn,
    error: console.error,
};*/

const isProduction = import.meta.env.MODE === 'production';

// Override console methods to respect environment
/*console.log = (...args: any[]) =>
    logger.shouldLog('log') && originalConsole.log(...args);
console.info = (...args: any[]) =>
    logger.shouldLog('info') && originalConsole.info(...args);
console.debug = (...args: any[]) =>
    logger.shouldLog('debug') && originalConsole.debug(...args);
console.warn = (...args: any[]) =>
    logger.shouldLog('warn') && originalConsole.warn(...args);
console.error = (...args: any[]) =>
    logger.shouldLog('error') && originalConsole.error(...args);*/

// Configure Axios
axios.defaults.baseURL = window.location.origin;
axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';

createInertiaApp({
    title: (title) => {
        // Handle different page types with custom title formats
        if (title === 'Start') {
            return 'EsraBibel';
        } else if (title === 'SearchResults') {
            return 'Suche - ESB';
        } else if (title === 'Display') {
            // For Display page, the title will be set in the component
            return title;
        } else if (title === '404') {
            return '404 - ESB';
        } else {
            return `${title} - ${appName}`;
        }
    },
    resolve: async (name) => {
        const pages = import.meta.glob<DefineComponent>('./Pages/**/*.vue');
        return (await resolvePageComponent(
            `./Pages/${name}.vue`,
            pages,
        )) as DefineComponent;
    },
    setup({ el, App, props, plugin }) {
        const app = createApp({ render: () => h(App, props) });
        const pinia = createPinia();
        app.use(pinia);

        // Register version as a global property
        app.config.globalProperties.$version = appVersion;
        // Also provide the version via provide/inject pattern
        app.provide('appVersion', appVersion);

        // Initialize theme mode
        const textSettings = useTextSettingsStore();

        // Apply theme immediately to avoid FOUC
        const theme =
            localStorage.theme === 'dark' ||
            (!('theme' in localStorage) &&
                window.matchMedia('(prefers-color-scheme: dark)').matches)
                ? 'dark'
                : 'light';
        document.documentElement.setAttribute('data-theme', theme);

        // Apply color theme if not default
        if (textSettings.colorTheme !== 'default') {
            document.documentElement.setAttribute(
                'data-theme',
                textSettings.colorTheme,
            );
        }

        // Then apply full theme settings
        textSettings.applyThemeMode();

        // Add system theme change listener
        const systemThemeQuery = window.matchMedia(
            '(prefers-color-scheme: dark)',
        );
        systemThemeQuery.addEventListener('change', () => {
            if (!('theme' in localStorage)) {
                const newTheme = systemThemeQuery.matches ? 'dark' : 'light';
                document.documentElement.setAttribute('data-theme', newTheme);
                // Re-apply color theme if not default
                if (textSettings.colorTheme !== 'default') {
                    document.documentElement.setAttribute(
                        'data-theme',
                        textSettings.colorTheme,
                    );
                }
            }
        });

        const shouldInitializeSentry = isProduction || window.location.hash === '#sentry';

        // Only initialize Sentry in production
        if (shouldInitializeSentry) {
            Sentry.init({
                app,
                dsn: import.meta.env.VITE_SENTRY_DSN,
                integrations: [
                    Sentry.feedbackIntegration({
                        autoInject: false,
                        colorScheme: 'system',
                        showBranding: false,
                        showEmail: false,
                        addScreenshotButtonLabel: 'Screenshot hinzufügen',
                        addScreenshotButtonAriaLabel: 'Screenshot hinzufügen',
                        removeScreenshotButtonLabel: 'Screenshot entfernen',
                        removeScreenshotButtonAriaLabel: 'Screenshot entfernen',
                        triggerLabel: 'Fehler melden (Beta Version)',
                        triggerAriaLabel: 'Fehler melden (Beta Version)',
                        namePlaceholder: 'Name (optional)',
                        submitButtonLabel: 'Senden',
                        cancelButtonLabel: 'Abbrechen',
                        formTitle: 'Fehler melden (Beta Version)',
                        messageLabel: 'Was ist der Fehler?',
                        isRequiredLabel: '(Pflichtfeld)',
                        messagePlaceholder: 'Beschreibe das Problem...',
                        showDialog: true,
                        enableScreenshot: false,
                    }),
                    Sentry.replayIntegration({
                        maskAllText: false,
                        blockAllMedia: false,
                        unblock: ['.sentry-unblock, [data-sentry-unblock]'],
                        unmask: ['.sentry-unmask, [data-sentry-unmask]'],
                    }),
                ],
                // Performance Monitoring
                tracesSampleRate: 1.0,
                // Session Replay
                replaysSessionSampleRate: 0.1,
                replaysOnErrorSampleRate: 1.0,
                // Disable console logging in production
                beforeSend(event: ErrorEvent) {
                    // Check if it is an exception, and if so, show the report dialog
                    if (event.exception && event.event_id) {
                        Sentry.showReportDialog({ eventId: event.event_id });
                    }
                    return event;
                },
            });
        } else {
            // In development, create a no-op Sentry instance
            const noop = () => {};
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            (window as any).Sentry = {
                captureException: noop,
                captureMessage: noop,
                addBreadcrumb: noop,
            };
        }

        app.use(plugin).mount(el);
    },
    progress: {
        color: '#4B5563',
    },
});
