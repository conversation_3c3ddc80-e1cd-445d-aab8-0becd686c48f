import { defineStore } from 'pinia';

interface NavigationState {
    isNavigationAsideOpen: boolean;
}

interface NavigationActions {
    openNavigationAside(): void;
    closeNavigationAside(): void;
    toggleNavigationAside(): void;
}

type NavigationGetters = {
    navigationAsideOpen(): boolean;
};

export const useNavigationStore = defineStore<
    'navigation',
    NavigationState,
    NavigationGetters,
    NavigationActions
>('navigation', {
    state: (): NavigationState => ({
        isNavigationAsideOpen: false,
    }),

    actions: {
        openNavigationAside() {
            console.log('Opening navigation aside...');
            this.isNavigationAsideOpen = true;
            console.log('Navigation aside state:', this.isNavigationAsideOpen);
        },

        closeNavigationAside() {
            console.log('Closing navigation aside...');
            this.isNavigationAsideOpen = false;
            console.log('Navigation aside state:', this.isNavigationAsideOpen);
        },

        toggleNavigationAside() {
            console.log('Toggling navigation aside...');
            this.isNavigationAsideOpen = !this.isNavigationAsideOpen;
            console.log('Navigation aside state:', this.isNavigationAsideOpen);
        },
    },
    getters: {
        navigationAsideOpen(): boolean {
            return this.isNavigationAsideOpen;
        },
    },
});
