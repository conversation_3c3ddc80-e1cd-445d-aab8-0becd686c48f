/**
 * @file Search configuration and settings
 * @module stores/searchSettingsStore
 */
import { useLocalStorage } from '@vueuse/core';
import { router } from '@inertiajs/vue3';
import { defineStore } from 'pinia';

const STORAGE_KEY = 'esbo-settings-search';

interface SearchSettings {
    types: string[];
}

interface SearchType {
    value: string;
    label: string;
}

const defaultSettings: SearchSettings = {
    types: ['books', 'footnotes', 'metadata'],
};

// Use VueUse's useLocalStorage composable for persistent settings
const settings = useLocalStorage<SearchSettings>(STORAGE_KEY, defaultSettings, {
    mergeDefaults: true,
});

interface State extends SearchSettings {
    isUpdating: boolean;
}

/**
 * Store for managing Search configuration and settings
 *
 * @description Search configuration and settings
 */
export const useSearchSettingsStore = defineStore('search-settings', {
    state: (): State => ({
        ...settings.value!,
        isUpdating: false,
    }),

    getters: {
        getTypes: (state) => state.types,

        /**
         * Available search types with their translations
         * @returns {SearchType[]} Array of search type objects with value and label
         */
        availableSearchTypes: () =>
            [
                { value: 'books', label: 'Bücher' },
                { value: 'footnotes', label: 'Fußnoten' },
                { value: 'metadata', label: 'Metadaten' },
            ] as SearchType[],

        /**
         * Get translated types based on current selection
         * @returns {string[]} Array of translated type labels
         */
        translatedTypes: (state): string[] => {
            const typeMap = new Map(
                useSearchSettingsStore().availableSearchTypes.map(
                    (type: { value: string; label: string }) => [
                        type.value,
                        type.label,
                    ],
                ),
            );

            return state.types.map((type) => typeMap.get(type) || type);
        },
    },

    actions: {
        /**
         * set types
         * @param {string[]} types - Search types to set
         * @returns {void}
         */
        setTypes(types: string[]) {
            // Prevent infinite loop by checking if types actually changed
            if (JSON.stringify(this.types) === JSON.stringify(types)) {
                return;
            }

            // Prevent concurrent updates
            if (this.isUpdating) {
                return;
            }

            this.isUpdating = true;
            this.types = types;

            // Save to localstorage using VueUse
            settings.value = {
                ...settings.value,
                types: this.types,
            };

            // Make API call to save settings
            router.post(
                '/search/settings',
                {
                    types: types,
                },
                {
                    preserveState: true,
                    preserveScroll: true,
                    only: ['filters', 'types'],
                    onFinish: () => {
                        this.isUpdating = false;
                    },
                },
            );
        },
    },
});
