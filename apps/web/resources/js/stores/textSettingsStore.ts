/**
 * @file Text display settings and preferences
 * @module stores/textSettingsStore
 */
import { useLocalStorage } from '@vueuse/core';
import type { MarginSize, TextSettings, ThemeMode } from '@esbo/types';
import { defineStore } from 'pinia';

const STORAGE_KEY = 'esbo-settings-text';

const defaultSettings: TextSettings = {
    fontSize: '2xl',
    lineSpacing: 'normal',
    showVerseNumbers: true,
    flowText: false,
    showFootnotes: true,
    focusedMode: false,
    marginSize: 'margin-normal',
    showChapterNumbers: true,
    showBookNames: true,
    themeMode: 'system',
    colorTheme: 'default',
    useInfiniteScroll: false,
};

// Use VueUse's useLocalStorage composable for persistent settings
const settings = useLocalStorage<TextSettings>(STORAGE_KEY, defaultSettings, {
    mergeDefaults: true,
});

/**
 * Store for managing Text display settings and preferences
 * Uses VueUse's useLocalStorage for persistence
 */
export const useTextSettingsStore = defineStore('text-settings', {
    state: (): TextSettings => settings.value!,
    getters: {
        isInfiniteScrollEnabled(): boolean {
            return this.useInfiniteScroll;
        },
    },
    actions: {
        /**
         * Update settings and persist changes using useLocalStorage
         * @param {Partial<TextSettings>} newSettings
         */
        updateSettings(newSettings: Partial<TextSettings>) {
            // Update the local state
            Object.assign(this, newSettings);
            // Persist changes to localStorage
            settings.value = { ...this };
            // Apply theme mode if it was updated
            if ('themeMode' in newSettings || 'colorTheme' in newSettings) {
                this.applyThemeMode();
            }
        },

        setMarginSize(size: MarginSize) {
            this.marginSize = size;
            settings.value = { ...this };
        },

        /**
         * Set the infinite scroll mode for the bible display.
         * @param value - true to enable infinite scroll, false to disable
         */
        setInfiniteScroll(value: boolean) {
            this.useInfiniteScroll = value;
        },

        toggleFocusedMode() {
            this.focusedMode = !this.focusedMode;
            settings.value = { ...this };
        },

        setThemeMode(mode: ThemeMode) {
            this.themeMode = mode;
            settings.value = { ...this }; // Save to localstorage
            this.applyThemeMode();
        },

        applyThemeMode() {
            // Apply theme mode (light/dark/system)
            if (this.themeMode === 'system') {
                // Remove explicit theme mode to respect system preference
                localStorage.removeItem('theme');
                // Set mode based on system preference
                const isDark = window.matchMedia(
                    '(prefers-color-scheme: dark)',
                ).matches;
                document.documentElement.setAttribute(
                    'data-mode',
                    isDark ? 'dark' : 'light',
                );
            } else {
                // Set explicit theme mode choice
                localStorage.theme = this.themeMode;
                document.documentElement.setAttribute(
                    'data-mode',
                    this.themeMode,
                );
            }

            // Apply color theme (papyrus, etc.)
            document.documentElement.setAttribute(
                'data-theme',
                this.colorTheme,
            );
        },
    },
});
