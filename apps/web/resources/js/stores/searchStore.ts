/**
 * @file Bible search functionality and results
 * @module stores/searchStore
 */
import { defineStore } from 'pinia';

/**
 * Store for managing Bible search functionality and results
 * 
 * @description Bible search functionality and results
 */
export const useSearchStore = defineStore('search', {
        /**
     * State properties for the store
     */
    state: () => ({
                /**
         * query for the store
         * @type {string}
         */
        query: '',
                /**
         * types for the store
         * @type {any}
         */
        types: ['book', 'footnote'] as string[],
    }),

    actions: {
                /**
         * set query
         * @param {Object} params - Parameters for the action
         * @returns {void}
         */
        setQuery(query: string) {
            this.query = query;
        },
        setTypes(types: string[]) {
            this.types = types;
        },
    },
});
