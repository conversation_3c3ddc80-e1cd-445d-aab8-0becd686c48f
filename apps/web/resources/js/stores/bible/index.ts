import { defineStore } from 'pinia';
import { useBibleHighlightStore } from './bibleHighlightStore';
import { useBibleMemoryStore } from './bibleMemoryStore';
import { useBibleStore as dataStore } from './bibleSectionStore';

/**
 * Main Bible store that composes functionality from specialized stores
 * This is the main entry point for components to interact with Bible data
 */
export const useBibleStore = defineStore('bible', () => {
    // Import functionality from specialized stores
    const sectionStore = dataStore();
    const highlightStore = useBibleHighlightStore();
    const memoryStore = useBibleMemoryStore();

    return {
        // Re-export the functionality from specialized stores
        // This maintains the same public API while delegating implementation
        ...sectionStore,
        ...highlightStore,
        ...memoryStore,
    };
});
