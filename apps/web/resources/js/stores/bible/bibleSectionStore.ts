/**
 * @file Bible sections and navigation
 * @module stores/bible/bibleSectionStore
 */
// biblePinia.ts
import { getChapterId, updateUrl } from '@/utils/bibleNavigationUtils';
import { logger } from '@/utils/logger';
import type { Book, DisplayResponse, Footnote, Section } from '@esbo/types';
import { usePage } from '@inertiajs/vue3';
import axios from 'axios';
import { defineStore } from 'pinia';
import { useTextSettingsStore } from '../textSettingsStore';
import { useBibleHighlightStore } from './bibleHighlightStore';
import { useBibleMemoryStore } from './bibleMemoryStore';

// Define the structure coming from props
type PageBooks = {
    sections: Array<{
        name: string;
        books: Book[];
    }>;
    availableBooks: {
        slug: string;
        order: number;
    }[];
};

interface BibleSectionState {
    isInitialized: boolean;
    initializedBookSlug: string | null;
    initializedChapter: number | null;
    isMobile: boolean;
    books: PageBooks;
    detailedBooks: Map<string, Book>;
    detailedBooksLoading: Map<string, boolean>;
    chapters: Map<string, Section>;
    currentBook: Book | null;
    currentChapter: number | null;
    currentVerse: number | null;
    currentVerseEnd: number | null;
    verseRanges: Array<{ start: number; end: number }>;
    isLoadingNext: boolean;
    isLoadingPrevious: boolean;
    scrollHandlingEnabled: boolean;
    isInitialLoad: boolean;
    /**
     * State for the currently displayed footnote tooltip.
     * referenceEl is the DOM element the tooltip should anchor to (for Floating UI).
     */
    footnoteState: {
        footnote: Footnote;
        word: string;
        reference: string;
        x: number;
        y: number;
        isClickLocked: boolean;
        referenceEl: HTMLElement | null;
    } | null;
    currentBookHasContent: boolean;
    verseReference: string | null;
    abortControllers: Map<string, globalThis.AbortController>;
}

/**
 * Store for managing Bible sections and navigation
 *
 * @description Bible sections and navigation
 */
const MOBILE_BREAKPOINT = 768;

export const useBibleStore = defineStore('bible-section', {
    /**
     * State properties for the store
     */
    state: (): BibleSectionState => {
        // Initialize books state correctly based on props type
        const pageBooks = usePage().props.books as PageBooks | undefined;
        return {
            isInitialized: false,
            initializedBookSlug: null,
            initializedChapter: null,
            // Assign the raw structure, or an empty default
            books: pageBooks || { sections: [], availableBooks: [] },
            detailedBooks: new Map<string, Book>(),
            detailedBooksLoading: new Map<string, boolean>(),
            isMobile:
                typeof window !== 'undefined' ? window.innerWidth < 768 : false,
            chapters: new Map<string, Section>(),
            currentBook: null,
            currentChapter: null,
            currentVerse: null,
            currentVerseEnd: null,
            verseRanges: [],
            footnoteState: null,
            currentBookHasContent: true,
            verseReference: null,
            abortControllers: new Map<string, globalThis.AbortController>(),
            isInitialLoad: false,
            isLoadingNext: false,
            isLoadingPrevious: false,
            scrollHandlingEnabled: true,
        };
    },

    getters: {
        // Renamed Getter: Flatten the nested structure from state into a sorted Book[]
        processedBooksList(): Book[] {
            const sections = this.books?.sections;

            // If no sections or not an array, return empty array
            if (!Array.isArray(sections) || sections.length === 0) {
                logger.warn(
                    'No sections found in state.books for processedBooksList getter.',
                );
                return [];
            }

            // Flatten the nested structure
            const flatBooks: Book[] = [];
            sections.forEach((testament) => {
                if (testament.books && Array.isArray(testament.books)) {
                    flatBooks.push(...testament.books);
                }
            });

            // Sort by order property to ensure correct sequence
            return flatBooks.sort((a, b) => a.order - b.order);
        },

        // Get available books from Inertia shared props
        availableBooks(): { slug: string; order: number }[] {
            const pageProps = usePage().props;
            const booksFromProps = pageProps.books as PageBooks | undefined;

            if (!booksFromProps?.availableBooks) return [];

            return booksFromProps.availableBooks;
        },

        // Check if a book is available
        isBookAvailable(): (slug: string) => boolean {
            return (slug: string) => {
                return this.availableBooks.some((book) => book.slug === slug);
            };
        },

        // Get book order (slugs in correct order)
        bookOrder(): string[] {
            // Use the getter which returns the flat, sorted list
            return this.processedBooksList.map((book) => book.slug);
        },

        // Get all visible chapters in correct order
        visibleChapters(): Section[] {
            const chapters = Array.from(this.chapters.values());

            // Sort by book order and chapter number
            return chapters.sort((a, b) => {
                // First sort by book order
                const bookOrderDiff =
                    this.bookOrder.indexOf(a.book.slug) -
                    this.bookOrder.indexOf(b.book.slug);

                if (bookOrderDiff !== 0) return bookOrderDiff;

                // If same book, frontmatter should come first
                const aIsFrontmatter = a.type === 'frontmatter';
                const bIsFrontmatter = b.type === 'frontmatter';

                if (aIsFrontmatter && !bIsFrontmatter) return -1;
                if (!aIsFrontmatter && bIsFrontmatter) return 1;

                // If both are the same type, sort by chapter number
                return a.number - b.number;
            });
        },

        // Get current chapter section
        currentChapterSection(): Section | null {
            if (!this.currentBook || !this.currentChapter) return null;
            const id = `${this.currentBook.slug}${this.currentChapter}`;
            return this.chapters.get(id) || null;
        },

        // Check if a section is the current one
        isCurrentSection(): (section: Section) => boolean {
            return (section: Section): boolean => {
                if (!this.currentBook || this.currentChapter === null)
                    return false;

                if (section.type === 'frontmatter') {
                    return (
                        section.book.slug === this.currentBook.slug &&
                        this.currentChapter === 0
                    );
                } else {
                    return (
                        section.book.slug === this.currentBook.slug &&
                        section.number === this.currentChapter
                    );
                }
            };
        },

        // Get current reference as a string
        currentReference(): string {
            if (!this.currentBook) return '';
            if (this.currentChapter === 0) return this.currentBook.slug;

            let verseRef = '';
            if (this.verseRanges.length > 0) {
                verseRef = this.verseRanges
                    .map((range) =>
                        range.start === range.end
                            ? range.start
                            : `${range.start}-${range.end}`,
                    )
                    .join('+');
            } else if (this.currentVerse) {
                verseRef =
                    this.currentVerseEnd &&
                    this.currentVerseEnd !== this.currentVerse
                        ? `${this.currentVerse}-${this.currentVerseEnd}`
                        : `${this.currentVerse}`;
            }

            return verseRef
                ? `${this.currentBook.slug} ${this.currentChapter},${verseRef}`
                : `${this.currentBook.slug} ${this.currentChapter}`;
        },
    },

    actions: {
        /**
         * Update mobile state based on window width
         */
        updateMobileState() {
            if (typeof window !== 'undefined') {
                this.isMobile = window.innerWidth < MOBILE_BREAKPOINT;
            }
        },

        /**
         * Get the previous chapter or, if at the first chapter, the last chapter of the previous book (if available).
         * @returns {{ book: Book; chapter: number } | null}
         */
        getPreviousChapterOrBook(): { book: Book; chapter: number } | null {
            if (!this.currentBook || this.currentChapter === null) return null;
            // If not at first chapter, just go to previous chapter
            if (this.currentChapter > 1) {
                return {
                    book: this.currentBook,
                    chapter: this.currentChapter - 1,
                };
            }
            // At first chapter: find previous book
            const bookOrder = this.bookOrder;
            const idx = bookOrder.indexOf(this.currentBook.slug);
            if (idx > 0) {
                const prevBookSlug = bookOrder[idx - 1];
                const prevBook = this.findBookBySlug(prevBookSlug);
                if (
                    prevBook &&
                    prevBook.chapterCount &&
                    prevBook.chapterCount > 0
                ) {
                    return { book: prevBook, chapter: prevBook.chapterCount };
                }
            }
            return null;
        },

        /**
         * Get the next chapter or, if at the last chapter, the first chapter of the next book (if available).
         * @returns {{ book: Book; chapter: number } | null}
         */
        getNextChapterOrBook(): { book: Book; chapter: number } | null {
            if (!this.currentBook || this.currentChapter === null) return null;
            // If not at last chapter, just go to next chapter
            if (
                this.currentBook.chapterCount &&
                this.currentChapter < this.currentBook.chapterCount
            ) {
                return {
                    book: this.currentBook,
                    chapter: this.currentChapter + 1,
                };
            }
            // At last chapter: find next book
            const bookOrder = this.bookOrder;
            const idx = bookOrder.indexOf(this.currentBook.slug);
            if (idx >= 0 && idx < bookOrder.length - 1) {
                const nextBookSlug = bookOrder[idx + 1];
                const nextBook = this.findBookBySlug(nextBookSlug);
                if (
                    nextBook &&
                    nextBook.chapterCount &&
                    nextBook.chapterCount > 0
                ) {
                    return { book: nextBook, chapter: 1 };
                }
            }
            return null;
        },
        // Initialize with data from server
        initialize(data: DisplayResponse) {
            // Skip if data is invalid
            if (!data?.reference?.book?.slug) {
                logger.warn('Initialize called with invalid data', data);
                return;
            }

            const newSlug = data.reference.book.slug;
            const newChapter = data.reference.chapter;

            // Add detailed logging BEFORE the guard check
            logger.debug('[Initialize Check]', {
                currentInitializedSlug: this.initializedBookSlug,
                currentInitializedChapter: this.initializedChapter,
                newSlug,
                newChapter,
                isAlreadyInitialized:
                    this.isInitialized &&
                    this.initializedBookSlug === newSlug &&
                    this.initializedChapter === newChapter,
            });

            // Enhanced guard: check if already initialized for this specific book and chapter
            // Only proceed if we're not initialized at all, or if we're changing context
            if (
                this.isInitialized &&
                this.initializedBookSlug === newSlug &&
                this.initializedChapter === newChapter
            ) {
                logger.info(
                    'Store already initialized for:',
                    newSlug,
                    newChapter,
                    '- skipping initialization',
                );
                return; // Already initialized for this context
            }

            // If we're already initialized but for a different context,
            // perform a partial reset before re-initializing
            if (this.isInitialized) {
                logger.info(
                    'Re-initializing store from:',
                    this.initializedBookSlug,
                    this.initializedChapter,
                    'to:',
                    newSlug,
                    newChapter,
                );
                this._resetForNewContext();
            } else {
                logger.info(
                    'Initializing store for first time:',
                    newSlug,
                    newChapter,
                );
            }

            // Set initialization markers *before* potential async operations
            this.initializedBookSlug = newSlug;
            this.initializedChapter = newChapter;
            this.isInitialized = true;

            // Now proceed with setting the state from data
            if (data.reference) {
                // Use findBookBySlug which might trigger async fetchDetailedBookData
                this.currentBook = this.findBookBySlug(newSlug) || null;
                this.currentChapter = newChapter;

                // Reset verse state
                this.currentVerse = null;
                this.currentVerseEnd = null;
                this.verseRanges = [];

                // Handle verse references
                if (
                    data.reference.verseRanges &&
                    data.reference.verseRanges.length > 0
                ) {
                    this.verseRanges = data.reference.verseRanges;
                    this.currentVerse = data.reference.verseRanges[0].start;
                    this.currentVerseEnd =
                        data.reference.verseRanges[
                            data.reference.verseRanges.length - 1
                        ].end;
                } else if (data.reference.verseStart !== null) {
                    const start = data.reference.verseStart;
                    const end = data.reference.verseEnd || start;
                    this.currentVerse = start;
                    this.currentVerseEnd = end;
                    this.verseRanges = [{ start, end }];
                }

                logger.info('Verse state initialized:', {
                    currentVerse: this.currentVerse,
                    currentVerseEnd: this.currentVerseEnd,
                    ranges: this.verseRanges,
                });
            }

            this.chapters.clear();
            this.isInitialLoad = true;

            const textSettings = useTextSettingsStore();
            if (textSettings.isInfiniteScrollEnabled) {
                data.sections.forEach((section) => this.addChapter(section));
            } else {
                // Only add the chapter matching the current reference (from the URL) if infinite scroll is disabled
                const { book, chapter } = data.reference;
                const matchingSection = (data.sections as Section[]).find(
                    (section) =>
                        section.book.slug === book.slug &&
                        section.number === chapter,
                );
                if (matchingSection) {
                    this.addChapter(matchingSection);
                }
            }
            // Set current book and chapter for initial load
            this.currentBook = data.reference.book;
            this.currentChapter = data.reference.chapter;
            setTimeout(() => this.enableScrollHandling(), 500);
        },

        // Find a book by slug
        findBookBySlug(slug: string): Book | undefined {
            // First check if we have the detailed book data
            if (this.detailedBooks.has(slug)) {
                return this.detailedBooks.get(slug);
            }

            // If not, check if we have the basic book data from the page props
            // Use the getter which returns the flat, sorted list
            const basicBook = this.processedBooksList.find(
                (book) => book.slug === slug,
            );

            if (basicBook) {
                // Fetch the detailed book data asynchronously
                this.fetchDetailedBookData(slug);

                // Return the basic book data for now
                return basicBook;
            }

            return undefined;
        },

        // Fetch detailed book data from the API
        async fetchDetailedBookData(slug: string) {
            if (!slug) {
                logger.warn('fetchDetailedBookData called with empty slug');
                return;
            }

            // Add loading state
            this.detailedBooksLoading = this.detailedBooksLoading || new Map();
            if (this.detailedBooksLoading.get(slug)) {
                return; // Already loading
            }
            this.detailedBooksLoading.set(slug, true);

            try {
                const response = await axios.get(`/api/books/${slug}`);

                if (!response?.data?.data) {
                    throw new Error('Invalid response format');
                }

                // Store the detailed book data in our cache
                this.detailedBooks.set(slug, response.data.data);

                // If this is the current book, update it with the detailed data
                if (this.currentBook?.slug === slug) {
                    this.currentBook = response.data.data;
                }

                // Update any frontmatter sections that use this book
                const frontmatterId = slug;
                const frontmatter = this.chapters.get(frontmatterId);
                if (frontmatter) {
                    frontmatter.book = response.data.data;
                }
            } catch (error) {
                logger.error('Error fetching detailed book data:', error);
                // Keep the basic book data as fallback
            } finally {
                this.detailedBooksLoading.set(slug, false);
            }
        },

        // Add a chapter to the store
        addChapter(section: Section) {
            const id = getChapterId(section);
            this.chapters.set(id, section);
        },

        // Update current verse
        updateCurrentVerse(
            chapterNum: number,
            verseNum: number,
            verseEndNum?: number,
        ) {
            if (
                this.currentBook &&
                this.currentChapter === chapterNum &&
                (this.currentVerse !== verseNum ||
                    this.currentVerseEnd !== (verseEndNum || verseNum))
            ) {
                this.currentVerse = verseNum;
                this.currentVerseEnd = verseEndNum || verseNum;
            }
        },

        // Handle footnote click
        handleFootnoteClick(
            event: MouseEvent,
            footnote: Footnote,
            word: string,
            book: string,
            chapter: number,
            verse: number,
            referenceEl: HTMLElement | null,
        ) {
            // Prevent default behavior
            event.preventDefault();

            // Create reference string
            const reference = `${book} ${chapter}:${verse}`;

            // Set footnote state
            this.footnoteState = {
                footnote,
                word,
                reference,
                x: event.clientX,
                y: event.clientY,
                isClickLocked: true,
                referenceEl,
            };

            console.log('[bibleSectionStore] footnoteState set in handleFootnoteClick', { value: this.footnoteState, stack: new Error().stack });
        },

        // Handle footnote hover
        handleFootnoteHover(
            event: MouseEvent,
            footnote: Footnote | null,
            word: string | undefined,
            book: string,
            chapter: number,
            verse: number,
            referenceEl: HTMLElement | null,
        ) {
            // Only handle hover if no click is active
            if (this.footnoteState?.isClickLocked) return;

            if (footnote && word) {
                // Create reference string
                const reference = `${book} ${chapter}:${verse}`;

                // Set footnote state for hover
                this.footnoteState = {
                    footnote,
                    word,
                    reference,
                    x: event.clientX,
                    y: event.clientY,
                    isClickLocked: false,
                    referenceEl,
                };

                console.log('[bibleSectionStore] footnoteState set in handleFootnoteHover', { value: this.footnoteState, stack: new Error().stack });
            } else {
                // Clear hover footnote if not click-locked
                if (this.footnoteState && !this.footnoteState.isClickLocked) {
                    this.footnoteState = null;

                    console.log('[bibleSectionStore] footnoteState cleared in handleFootnoteHover', { value: this.footnoteState, stack: new Error().stack });
                }
            }
        },

        // Close footnote
        closeFootnote() {
            this.footnoteState = null;
        },

        // Clear hover footnote
        clearHoverFootnote() {
            this.footnoteState = null;
        },

        // Load more chapters in a specific direction
        async loadMoreChapters(direction: 'next' | 'previous') {
            if (!this.currentBook || !this.currentChapter) return;

            try {
                if (direction === 'next') {
                    this.isLoadingNext = true;
                } else {
                    this.isLoadingPrevious = true;
                }

                const response = await axios.get(`/api/chapters/fetch`, {
                    params: {
                        bookId: this.currentBook.order,
                        chapter: this.currentChapter,
                        direction: direction,
                    },
                });

                // Add new chapters to the store
                if (response.data && response.data.sections) {
                    response.data.sections.forEach((section: Section) =>
                        this.addChapter(section),
                    );
                    // Log the loaded sections for debugging
                    logger.info(`Loaded ${direction} chapters`, {
                        count: response.data.sections.length,
                        sections: response.data.sections.map((s: Section) =>
                            s.type === 'frontmatter'
                                ? `${s.book.slug} (frontmatter)`
                                : `${s.book.slug}${s.number}`,
                        ),
                    });
                    // Schedule memory cleanup based on scroll direction
                    const memoryStore = useBibleMemoryStore();
                    memoryStore.requestCleanup(direction);
                }
            } catch (error) {
                logger.error('Error loading more chapters', {
                    error,
                    direction,
                });
            } finally {
                if (direction === 'next') {
                    this.isLoadingNext = false;
                } else {
                    this.isLoadingPrevious = false;
                }
            }
        },

        // Update current chapter when scrolling
        updateCurrentChapter(chapterId: string) {
            // Skip if scroll handling is disabled
            if (!this.scrollHandlingEnabled) return;

            const section = this.chapters.get(chapterId);
            console.log('Updating current chapter:', chapterId, section);

            if (!section) return;

            if (section.type === 'frontmatter') {
                this.currentBook = section.book;
                this.currentChapter = 0;
            } else {
                this.currentBook = section.book;
                this.currentChapter = section.number;
            }

            updateUrl({
                book: section.book.slug,
                chapter: section.type === 'frontmatter' ? null : section.number,
            });

            // Clear highlights when chapter changes
            useBibleHighlightStore().clearHighlights();

            // Check if we need to load more chapters
            this.checkAndLoadAdjacentChapters();
        },

        /**
         * Remove a chapter from the store by its ID
         * @param chapterId The ID of the chapter to remove
         * @returns True if the chapter was removed, false if it wasn't found
         */
        removeChapter(chapterId: string): boolean {
            if (this.chapters.has(chapterId)) {
                const newChapters = new Map(this.chapters);
                newChapters.delete(chapterId);
                this.chapters = newChapters;
                return true;
            }
            return false;
        },

        // Disable scroll handling
        disableScrollHandling() {
            this.scrollHandlingEnabled = false;
            logger.info('Scroll handling disabled');
        },

        // Enable scroll handling
        enableScrollHandling() {
            // Small delay before re-enabling to ensure any ongoing scrolls are complete
            setTimeout(() => {
                this.scrollHandlingEnabled = true;
                logger.info('Scroll handling enabled');
            }, 100);
        },

        // Set verse reference
        setVerseReference(reference: string) {
            // Store the reference string for later use
            // This is a placeholder implementation that will be expanded later
            console.log(`Setting verse reference: ${reference}`);
            // You can uncomment the next line when implementing the full functionality
            // this.currentVerseReference = reference;
        },

        // Check and load adjacent chapters if needed
        async checkAndLoadAdjacentChapters() {
            if (!this.currentBook || this.currentChapter === null) return;

            if (!this.currentBook.chapterCount) {
                return;
            }

            // Disable scroll handling during chapter loading
            const wasScrollHandlingEnabled = this.scrollHandlingEnabled;
            this.disableScrollHandling();

            console.log('Checking and loading adjacent chapters...');

            try {
                // Check if we need to load more chapters in the forward direction
                if (this.currentChapter > 0) {
                    // Not frontmatter
                    const nextChapterId = `${this.currentBook.slug}${this.currentChapter + 1}`;
                    const nextNextChapterId = `${this.currentBook.slug}${this.currentChapter + 2}`;

                    if (
                        this.currentChapter < this.currentBook.chapterCount &&
                        (!this.chapters.has(nextChapterId) ||
                            !this.chapters.has(nextNextChapterId))
                    ) {
                        await this.loadMoreChapters('next');
                    }

                    // Check if we need to load more chapters in the backward direction
                    const prevChapterId = `${this.currentBook.slug}${this.currentChapter - 1}`;
                    const prevPrevChapterId = `${this.currentBook.slug}${this.currentChapter - 2}`;

                    if (
                        this.currentChapter > 1 &&
                        (!this.chapters.has(prevChapterId) ||
                            !this.chapters.has(prevPrevChapterId))
                    ) {
                        await this.loadMoreChapters('previous');
                    }
                }

                // Check if we need to load chapters from adjacent books
                // Use the getter which returns the flat, sorted list
                const bookIndex = this.processedBooksList.findIndex(
                    (b) => b.order === this.currentBook?.order,
                );

                // Check if we need to load chapters from previous book
                if (this.currentChapter === 1 && bookIndex > 0) {
                    // Use the getter which returns the flat, sorted list
                    const prevBook = this.processedBooksList[bookIndex - 1];
                    const lastChapterOfPrevBook = `${prevBook.slug}${prevBook.chapterCount}`;

                    if (!this.chapters.has(lastChapterOfPrevBook)) {
                        await this.loadMoreChapters('previous');
                    }
                }

                // Check if we need to load chapters from next book
                if (
                    this.currentChapter === this.currentBook.chapterCount &&
                    // Use the getter which returns the flat, sorted list
                    bookIndex < this.processedBooksList.length - 1
                ) {
                    // Use the getter which returns the flat, sorted list
                    const nextBook = this.processedBooksList[bookIndex + 1];
                    const firstChapterOfNextBook = `${nextBook.slug}1`;
                    const frontmatterOfNextBook = nextBook.slug;

                    if (
                        !this.chapters.has(firstChapterOfNextBook) ||
                        !this.chapters.has(frontmatterOfNextBook)
                    ) {
                        await this.loadMoreChapters('next');
                    }
                }
            } finally {
                // Re-enable scroll handling if it was enabled before
                if (wasScrollHandlingEnabled) {
                    this.enableScrollHandling();
                    this.isInitialLoad = false;
                }

                console.log('Scroll handling re-enabled', this.isInitialLoad);

                // If this was the initial load, maintain the current chapter's position
                if (this.isInitialLoad) {
                    this.isInitialLoad = false;

                    // Force a scroll to the current chapter after a short delay
                    // to ensure all content is properly rendered
                    setTimeout(() => {
                        const currentId =
                            this.currentBook && this.currentChapter !== null
                                ? this.currentChapter === 0
                                    ? this.currentBook.slug
                                    : `${this.currentBook.slug}${this.currentChapter}`
                                : null;

                        if (currentId) {
                            const element = document.querySelector(
                                `[data-chapter-id="${currentId}"]`,
                            );
                            if (element) {
                                element.scrollIntoView({
                                    behavior: 'auto',
                                    block: 'start',
                                });
                            }
                        }
                    }, 100);
                }
            }
        },

        // Override $reset to include new properties
        $reset() {
            this.isInitialized = false;
            this.initializedBookSlug = null;
            this.initializedChapter = null;
            // Reset books state correctly based on props type
            const pageBooks = usePage().props.books as PageBooks | undefined;
            this.books = pageBooks || { sections: [], availableBooks: [] };
            this.detailedBooks = new Map<string, Book>();
            this.detailedBooksLoading = new Map<string, boolean>();
            this.chapters = new Map<string, Section>();
            this.currentBook = null;
            this.currentChapter = null;
            this.currentVerse = null;
            this.currentVerseEnd = null;
            this.verseRanges = [];
            this.isInitialLoad = false;
            this.isLoadingNext = false;
            this.isLoadingPrevious = false;
            this.scrollHandlingEnabled = true;
            this.footnoteState = null;
            this.verseReference = null;
            this.abortControllers.forEach((controller) => controller.abort());
            this.abortControllers.clear();
            logger.info('BibleSectionStore state reset');
        },

        // Helper method to reset state for a new context
        _resetForNewContext() {
            // Reset only the parts needed for a context change
            this.chapters.clear();
            this.scrollHandlingEnabled = false;
            this.currentBook = null;
            this.currentChapter = null;
            this.currentVerse = null;
            this.currentVerseEnd = null;
            this.verseRanges = [];
            this.footnoteState = null;
            this.isInitialLoad = true;
            // Keep detailedBooks cache, but clear loading states
            this.detailedBooksLoading.clear();
        },
    },
});
