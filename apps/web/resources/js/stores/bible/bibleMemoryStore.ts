import { logger } from '@/utils/logger';
import { defineStore } from 'pinia';
import { useBibleStore } from './bibleSectionStore';

/**
 * Store for managing memory usage and chapter cleanup
 */
export const useBibleMemoryStore = defineStore('bible-memory', {
    /**
     * State properties for the store
     */
    state: () => ({
        /**
         * max chapters in memory for the store
         * @type {number}
         */
        maxChaptersInMemory: 5,
        /**
         * is cleaning up for the store
         * @type {boolean}
         */
        isCleaningUp: false,
        /**
         * last cleanup time for the store
         * @type {number}
         */
        lastCleanupTime: 0,
        /**
         * cleanup interval for the store
         * @type {number}
         */
        cleanupInterval: 30000, // 30sec
        /**
         * Timer for debouncing cleanup
         * @type {ReturnType<typeof setTimeout> | null}
         */
        cleanupTimer: null as ReturnType<typeof setTimeout> | null,
    }),

    actions: {
        /**
         * Request a cleanup based on scroll direction (debounced)
         */
        requestCleanup(direction: 'next' | 'previous') {
            if (this.cleanupTimer) clearTimeout(this.cleanupTimer);
            // Debounce removal to avoid erratic scroll spikes
            this.cleanupTimer = setTimeout(() => {
                this.cleanupChapters(direction);
                this.cleanupTimer = null;
            }, 200);
        },

        /**
         * Remove one chapter from top or bottom based on direction
         */
        cleanupChapters(direction: 'next' | 'previous') {
            if (this.isCleaningUp) {
                logger.warn('Already cleaning up chapters');
                return;
            }
            this.isCleaningUp = true;
            try {
                const sectionStore = useBibleStore();
                const chaptersArray = sectionStore.visibleChapters;
                const total = chaptersArray.length;
                const max = this.maxChaptersInMemory;
                if (total <= max) return;
                let toRemove =
                    direction === 'next'
                        ? chaptersArray[0]
                        : chaptersArray[total - 1];
                sectionStore.removeChapter(toRemove.id);
                logger.info('Removed chapter from memory', {
                    chapterId: toRemove.id,
                    direction,
                });
            } catch (error) {
                logger.error('Error during chapter cleanup', { error });
            } finally {
                this.isCleaningUp = false;
            }
        },
    },
});
