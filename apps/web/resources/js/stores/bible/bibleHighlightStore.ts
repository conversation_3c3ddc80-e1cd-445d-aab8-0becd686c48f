import { logger } from '@/utils/logger';
import type { BibleReference } from '@esbo/types';
import { defineStore } from 'pinia';
import { useBibleStore } from './bibleSectionStore';

/**
 * Store for managing word highlighting functionality
 */

export const useBibleHighlightStore = defineStore('bible-highlight', {
        /**
     * State properties for the store
     */
    state: () => ({
                /**
         * highlighted words for the store
         * @type {any}
         */
        highlightedWords: new Set<string>(),
                /**
         * highlighted verses for the store
         * @type {any}
         */
        highlightedVerses: new Set<string>(),
                /**
         * highlighted chapters for the store
         * @type {any}
         */
        highlightedChapters: new Set<string>(),
                /**
         * search term for the store
         * @type {string}
         */
        searchTerm: '',
                /**
         * is highlighting for the store
         * @type {boolean}
         */
        isHighlighting: false,
                /**
         * highlight timeout for the store
         * @type {any}
         */
        highlightTimeout: null as number | null,
    }),

    getters: {
        hasHighlights(): boolean {
            return (
                this.highlightedWords.size > 0 ||
                this.highlightedVerses.size > 0 ||
                this.highlightedChapters.size > 0
            );
        },
    },

    actions: {
        /**
         * Set the search term and highlight matching words
         */
                /**
         * set search term
         * @param {Object} params - Parameters for the action
         * @returns {void}
         */
        setSearchTerm(term: string) {
            this.searchTerm = term;

                    /**
         * if
         * @param {Object} params - Parameters for the action
         * @returns {void}
         */
        if (!term) {
                this.clearHighlights();
                return;
            }

            // Highlight words that match the search term
            this.highlightWords(term);
        },

        /**
         * Highlight words that match the given term
         */
        highlightWords(term: string) {
            if (this.isHighlighting) {
                // Clear any existing timeout
                if (this.highlightTimeout !== null) {
                    window.clearTimeout(this.highlightTimeout);
                }

                // Set a new timeout to avoid too many highlight operations
                this.highlightTimeout = window.setTimeout(() => {
                    this.performHighlighting(term);
                }, 200);

                return;
            }

            this.performHighlighting(term);
        },

        /**
         * Perform the actual highlighting
         */
        performHighlighting(term: string) {
            this.isHighlighting = true;

            try {
                // Clear existing highlights
                this.clearHighlights();

                if (!term) return;

                // Find all text nodes in the chapter content
                const chapterContents =
                    document.querySelectorAll('.chapter-content');
                if (!chapterContents.length) {
                    logger.warn('No chapter content found for highlighting');
                    return;
                }

                // Create a case-insensitive regex for the search term
                const regex = new RegExp(
                    `\\b${this.escapeRegExp(term)}\\b`,
                    'gi',
                );

                // Keep track of highlighted elements
                const highlightedWords = new Set<string>();
                const highlightedVerses = new Set<string>();
                const highlightedChapters = new Set<string>();

                // Process each chapter
                chapterContents.forEach((chapterContent) => {
                    const chapterId = chapterContent
                        .closest('[data-chapter-id]')
                        ?.getAttribute('data-chapter-id');
                    if (!chapterId) return;

                    // Find all word elements in the chapter
                    const wordElements =
                        chapterContent.querySelectorAll('.word');
                    let chapterHasHighlight = false;

                    // Check each word element
                    wordElements.forEach((wordElement) => {
                        const wordText = wordElement.textContent || '';

                        // If the word matches the search term, highlight it
                        if (wordText.match(regex)) {
                            wordElement.classList.add('highlighted-word');

                            // Add to highlighted words
                            const wordId =
                                wordElement.getAttribute('data-word-id');
                            if (wordId) highlightedWords.add(wordId);

                            // Add to highlighted verses
                            const verseElement =
                                wordElement.closest('[data-verse-id]');
                            const verseId =
                                verseElement?.getAttribute('data-verse-id');
                            if (verseId) {
                                highlightedVerses.add(verseId);
                                verseElement?.classList.add(
                                    'highlighted-verse',
                                );
                            }

                            chapterHasHighlight = true;
                        }
                    });

                    // If the chapter has highlights, add it to the set
                    if (chapterHasHighlight) {
                        highlightedChapters.add(chapterId);
                        chapterContent
                            .closest('[data-chapter-id]')
                            ?.classList.add('highlighted-chapter');
                    }
                });

                // Update state with highlighted elements
                this.highlightedWords = highlightedWords;
                this.highlightedVerses = highlightedVerses;
                this.highlightedChapters = highlightedChapters;

                logger.info('Highlighting complete', {
                    term,
                    wordsHighlighted: highlightedWords.size,
                    versesHighlighted: highlightedVerses.size,
                    chaptersHighlighted: highlightedChapters.size,
                });
            } catch (error) {
                logger.error('Error during highlighting', { error });
            } finally {
                this.isHighlighting = false;
            }
        },

        /**
         * Clear all highlights
         */
        clearHighlights() {
            // Remove highlight classes from elements
            document
                .querySelectorAll('.highlighted-word')
                .forEach((element) => {
                    element.classList.remove('highlighted-word');
                });

            document
                .querySelectorAll('.highlighted-verse')
                .forEach((element) => {
                    element.classList.remove('highlighted-verse');
                });

            document
                .querySelectorAll('.highlighted-chapter')
                .forEach((element) => {
                    element.classList.remove('highlighted-chapter');
                });

            // Clear highlight sets
            this.highlightedWords.clear();
            this.highlightedVerses.clear();
            this.highlightedChapters.clear();

            logger.info('Cleared all highlights');
        },

        /**
         * Escape special characters in a string for use in a regex
         */
        escapeRegExp(string: string): string {
            return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        },

        /**
         * Get consistent verse ID format
         */
        getVerseId(reference: {
            book: string;
            chapter: number;
            verse: number;
        }): string {
            return `${reference.book}.${reference.chapter}.${reference.verse}`;
        },

        /**
         * Highlight a specific verse by reference
         */
        highlightVerseByReference(reference: {
            book: string;
            chapter: number;
            verse: number;
        }) {
            const bibleStore = useBibleStore();

            // Clear any existing highlights first
            this.clearHighlights();

            // Get verse ID in consistent format
            const verseId = this.getVerseId(reference);
            logger.info(`Highlighting verse by reference: ${verseId}`);

            // Find the verse element
            const verseElement = document.getElementById(verseId);
            console.log('Getting verse element', verseElement);
            if (!verseElement) {
                logger.warn(`Verse element not found: ${verseId}`);
                return false;
            }

            // Add the verse to highlighted verses and apply highlight class
            this.highlightedVerses.add(verseId);
            verseElement.classList.add('highlighted-verse');

            // Add the chapter to highlighted chapters
            const chapterElement = verseElement.closest('[data-chapter-id]');
            const chapterId = chapterElement?.getAttribute('data-chapter-id');
            if (chapterId) {
                this.highlightedChapters.add(chapterId);
            }

            // Disable scroll handling before calculating positions
            bibleStore.disableScrollHandling();
            logger.info('Disabled scroll handling for verse highlight');

            // Use requestAnimationFrame for the first frame to ensure DOM is ready
            requestAnimationFrame(() => {
                // Get the menu elements
                const submenu = document.querySelector(
                    '.submenu-bar',
                ) as HTMLElement;
                const mainMenu = document.querySelector(
                    '.menubar',
                ) as HTMLElement;

                // Wait another frame to ensure styles are computed
                requestAnimationFrame(() => {
                    // Calculate total menu height (main menu + submenu)
                    const submenuHeight =
                        submenu?.getBoundingClientRect().height || 0;
                    const mainMenuHeight =
                        mainMenu?.getBoundingClientRect().height || 0;
                    const totalMenuHeight = submenuHeight + mainMenuHeight;
                    const extraPadding = 70;

                    // Get updated position after any DOM updates
                    const rect = verseElement.getBoundingClientRect();
                    const absoluteVerseTop = rect.top + window.pageYOffset;
                    const targetPosition =
                        absoluteVerseTop - totalMenuHeight - extraPadding;

                    logger.info('Menu measurements', {
                        submenuHeight,
                        mainMenuHeight,
                        totalMenuHeight,
                        extraPadding,
                        rectTop: rect.top,
                        absoluteVerseTop,
                        targetPosition,
                    });

                    // Scroll to the verse accounting for the menu
                    window.scrollTo({
                        top: targetPosition,
                        behavior: 'smooth',
                    });

                    // Re-enable scroll handling after animation completes
                    setTimeout(() => {
                        bibleStore.enableScrollHandling();
                        logger.info(
                            'Re-enabled scroll handling after verse highlight',
                        );
                    }, 1500);
                });
            });

            return true;
        },

        /**
         * Get the URL representation of currently highlighted verses
         */
        getHighlightedVersesUrl(reference: BibleReference): string {
            const verseRanges: BibleReference[] = [];
            const singleVerses: number[] = [];
            const highlightedVerseNumbers = Array.from(this.highlightedVerses)
                .filter((id) =>
                    id.startsWith(`${reference.book}.${reference.chapter}.`),
                )
                .map((id) => parseInt(id.split('.')[2]))
                .sort((a, b) => a - b);

            if (highlightedVerseNumbers.length === 0) {
                return `/${reference.book}${reference.chapter}`;
            }

            let currentRange: BibleReference | null = null;

            highlightedVerseNumbers.forEach((verse) => {
                if (!currentRange) {
                    currentRange = {
                        book: reference.book,
                        chapter: reference.chapter,
                        verseStart: verse,
                        verseEnd: verse,
                    };
                } else if (verse === (currentRange.verseEnd ?? 0) + 1) {
                    currentRange.verseEnd = verse;
                } else {
                    if (currentRange.verseStart === currentRange.verseEnd) {
                        singleVerses.push(currentRange.verseStart ?? 0);
                    } else {
                        verseRanges.push({ ...currentRange });
                    }
                    currentRange = {
                        book: reference.book,
                        chapter: reference.chapter,
                        verseStart: verse,
                        verseEnd: verse,
                    };
                }
            });

            if (currentRange) {
                // Explicitly type-assert currentRange as BibleReference
                const range = currentRange as BibleReference;
                if (range.verseStart === range.verseEnd) {
                    singleVerses.push(range.verseStart ?? 0);
                } else {
                    verseRanges.push({ ...range });
                }
            }

            const parts: string[] = [];
            if (singleVerses.length > 0) {
                parts.push(singleVerses.join('+'));
            }
            if (verseRanges.length > 0) {
                parts.push(
                    verseRanges
                        .map((range) => `${range.verseStart}-${range.verseEnd}`)
                        .join('+'),
                );
            }

            return `/${reference.book}${reference.chapter},${parts.join('+')}`;
        },

        /**
         * Toggle highlight state for a specific verse or verse range
         */
        toggleVerseHighlight(reference: {
            book: string;
            chapter: number;
            verse: number;
            endVerse?: number;
        }): boolean {
            const hasRange =
                reference.endVerse && reference.endVerse > reference.verse;
            const bibleStore = useBibleStore();

            if (hasRange) {
                // Handle verse range
                const startVerseId = this.getVerseId({
                    book: reference.book,
                    chapter: reference.chapter,
                    verse: reference.verse,
                });

                const isHighlighted = this.highlightedVerses.has(startVerseId);

                if (!reference.endVerse) {
                    console.log(
                        'Editing highlight for reference.verse not working because no endVerse is provided',
                    );
                    return false;
                }

                if (isHighlighted) {
                    // Remove highlights for the entire range
                    console.log(
                        `Removing highlight for verse range: ${reference.verse}-${reference.endVerse}`,
                    );

                    for (
                        let v = reference.verse;
                        v <= reference.endVerse;
                        v++
                    ) {
                        const verseId = `${reference.book}.${reference.chapter}.${v}`;
                        this.highlightedVerses.delete(verseId);

                        // Update DOM
                        const verseElement = document.getElementById(verseId);
                        if (verseElement) {
                            verseElement.classList.remove('highlighted-verse');
                        }
                    }
                } else {
                    // Add highlights for the entire range
                    console.log(
                        `Adding highlight for verse range: ${reference.verse}-${reference.endVerse}`,
                    );

                    for (
                        let v = reference.verse;
                        v <= reference.endVerse;
                        v++
                    ) {
                        const verseId = `${reference.book}.${reference.chapter}.${v}`;
                        this.highlightedVerses.add(verseId);

                        // Update DOM
                        const verseElement = document.getElementById(verseId);
                        if (verseElement) {
                            verseElement.classList.add('highlighted-verse');
                        }
                    }
                }
            } else {
                // Handle single verse
                const verseId = this.getVerseId(reference);
                console.log('Toggling highlight for verseId:', verseId);

                if (this.highlightedVerses.has(verseId)) {
                    console.log('Removing highlight');
                    this.highlightedVerses.delete(verseId);
                } else {
                    console.log('Adding highlight');
                    this.highlightedVerses.add(verseId);
                }

                // Update the verse element's class
                const verseElement = document.getElementById(verseId);
                if (verseElement) {
                    verseElement.classList.toggle('highlighted-verse');
                }
            }

            // Only update URL when at chapter boundaries or when explicitly changing highlights
            const currentChapterId = bibleStore.currentChapterSection?.id;
            if (currentChapterId === `${reference.book}${reference.chapter}`) {
                window.history.replaceState(
                    {},
                    '',
                    this.getHighlightedVersesUrl(reference),
                );
            }

            console.log(
                'Updated highlighted verses:',
                Array.from(this.highlightedVerses),
            );
            return true;
        },

        /**
         * Jump to the next highlighted verse
         */
        jumpToNextHighlight() {
            if (!this.hasHighlights) return;

            // Get all highlighted verses
            const highlightedVerseElements =
                document.querySelectorAll('.highlighted-verse');
            if (!highlightedVerseElements.length) return;

            // Find the first verse that's below the current viewport
            const scrollTop = window.scrollY;
            const viewportHeight = window.innerHeight;
            const viewportBottom = scrollTop + viewportHeight;

            let nextVerseElement: Element | null = null;

            // Find the first verse below the viewport
            for (const verseElement of highlightedVerseElements) {
                const rect = verseElement.getBoundingClientRect();
                const verseTop = rect.top + scrollTop;

                if (verseTop > viewportBottom) {
                    nextVerseElement = verseElement;
                    break;
                }
            }

            // If no verse is found below the viewport, loop back to the first one
            if (!nextVerseElement && highlightedVerseElements.length > 0) {
                nextVerseElement = highlightedVerseElements[0];
            }

            // Scroll to the verse
            if (nextVerseElement) {
                nextVerseElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center',
                });
            }
        },

        /**
         * Jump to the previous highlighted verse
         */
        jumpToPreviousHighlight() {
            if (!this.hasHighlights) return;

            // Get all highlighted verses
            const highlightedVerseElements =
                document.querySelectorAll('.highlighted-verse');
            if (!highlightedVerseElements.length) return;

            // Find the first verse that's above the current viewport
            const scrollTop = window.scrollY;

            let previousVerseElement: Element | null = null;

            // Convert NodeList to array and reverse it to find the previous verse
            const versesArray = Array.from(highlightedVerseElements).reverse();

            // Find the first verse above the viewport
            for (const verseElement of versesArray) {
                const rect = verseElement.getBoundingClientRect();
                const verseBottom = rect.bottom + scrollTop;

                if (verseBottom < scrollTop) {
                    previousVerseElement = verseElement;
                    break;
                }
            }

            // If no verse is found above the viewport, loop back to the last one
            if (!previousVerseElement && highlightedVerseElements.length > 0) {
                previousVerseElement =
                    highlightedVerseElements[
                        highlightedVerseElements.length - 1
                    ];
            }

            // Scroll to the verse
            if (previousVerseElement) {
                previousVerseElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center',
                });
            }
        },
    },
});
