import { setActivePinia, createP<PERSON> } from 'pinia';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { useBibleStore } from '../bibleSectionStore';
// Mock Inertia usePage to provide default books prop for tests
vi.mock('@inertiajs/vue3', () => ({
  usePage: () => ({ props: { books: { sections: [], availableBooks: [] } } }),
}));

import type { Book, Section, DisplayResponse, NavigationState } from '@esbo/types';
import { BookCategory, Testament } from '@esbo/types';
import { getChapterId } from '@/utils/bibleNavigationUtils';

// Define two books for all tests
const mark: Book = {
  id: 1,
  order: 1,
  slug: '<PERSON>',
  name: 'Die Heilsbotschaft nach Markus',
  chapterCount: 2,
  category: BookCategory.GOSPEL,
  hasContent: true,
  abbreviation: 'Mk',
  testament: Testament.NT,
  testamentLabel: 'Neues Testament',
  searchNames: '<PERSON>',
};
const luke: Book = {
  id: 2,
  order: 2,
  slug: 'Lu<PERSON>',
  name: 'Die Heilsbotschaft nach Lukas',
  chapterCount: 2,
  category: BookCategory.GOSPEL,
  hasContent: true,
  abbreviation: 'Lk',
  testament: Testament.NT,
  testamentLabel: 'Neues Testament',
  searchNames: 'Lukas',
};

describe('BibleSectionStore - initial load chapters', () => {
  beforeEach(() => {
    setActivePinia(createPinia());
  });

  it('loads correct chapters for given reference considering book boundaries', () => {
    const store = useBibleStore();
    // Use predefined books
    const book1 = mark;
    const book2 = luke;
    const sections: Section[] = [
      // use slug for url reference ids
      { type: 'frontmatter', book: book1, number: 0, id: book1.slug },
      { type: 'chapter-current', book: book1, number: 1, id: `${book1.slug}1`, verses: [] },
      { type: 'chapter-current', book: book1, number: 2, id: `${book1.slug}2`, verses: [] },
      { type: 'frontmatter', book: book2, number: 0, id: book2.slug },
    ];
    const response: DisplayResponse = {
      reference: {
        book: book1,
        chapter: 2,
        frontmatter: false,
        verseStart: 1,
        verseEnd: 1,
        verseRanges: [],
      },
      sections,
      navigation: { currentBook: book1, previousBook: null, nextBook: null } as NavigationState,
    };

    store.initialize(response);
    // use slug to simulate url-based chapter id
    store.updateCurrentChapter(`${book1.slug}${response.reference.chapter}`);

    // Chapters map keys should match provided sections in order
    const keys = Array.from(store.chapters.keys());
    const expectedKeys = sections.map(section => getChapterId(section));
    expect(keys).toEqual(expectedKeys);

    // Current book and chapter should reflect reference
    expect(store.currentBook?.slug).toBe('Markus');
    expect(store.currentChapter).toBe(2);

    // Current reference string correct
    expect(store.currentReference).toBe('Markus 2,1');
  });

  it('loads adjacent NT books Markus and Lukas correctly', () => {
    const store = useBibleStore();
    // Use predefined books
    const markBook = mark;
    const lukeBook = luke;
    const sections: Section[] = [
      { type: 'frontmatter', book: markBook, number: 0, id: markBook.slug },
      { type: 'chapter-current', book: markBook, number: 1, id: `${markBook.slug}1`, verses: [] },
      { type: 'chapter-current', book: markBook, number: 2, id: `${markBook.slug}2`, verses: [] },
      { type: 'frontmatter', book: lukeBook, number: 0, id: lukeBook.slug },
    ];
    const response: DisplayResponse = {
      reference: {
        book: markBook,
        chapter: 2,
        frontmatter: false,
        verseStart: 1,
        verseEnd: 1,
        verseRanges: [],
      },
      sections,
      navigation: { currentBook: markBook, previousBook: null, nextBook: null } as NavigationState,
    };
    store.initialize(response);
    // simulate scroll to reference chapter to set currentBook and currentChapter
    store.updateCurrentChapter(`${markBook.slug}${response.reference.chapter}`);
    const keys = Array.from(store.chapters.keys());
    const expectedKeys = sections.map(section => getChapterId(section));
    expect(keys).toEqual(expectedKeys);
    expect(store.currentBook?.slug).toBe('Markus');
    expect(store.currentChapter).toBe(2);
  });

  it('formats references with multiple verse ranges correctly', () => {
    const store = useBibleStore();
    const book = mark;
    const sections: Section[] = [
      { type: 'frontmatter', book, number: 0, id: book.slug },
      { type: 'chapter-current', book, number: 2, id: `${book.slug}2`, verses: [] },
    ];
    const response: DisplayResponse = {
      reference: {
        book,
        chapter: 2,
        frontmatter: false,
        verseStart: 1,
        verseEnd: 1,
        verseRanges: [
          { start: 1, end: 1 },
          { start: 3, end: 5 },
          { start: 7, end: 7 },
        ],
      },
      sections,
      navigation: { currentBook: book, previousBook: null, nextBook: null } as NavigationState,
    };
    store.initialize(response);
    store.updateCurrentChapter(`${book.slug}${response.reference.chapter}`);
    expect(store.currentReference).toBe('Markus 2,1+3-5+7');
  });

  it('calls loadMoreChapters for previous book on first chapter boundary', async () => {
    const store = useBibleStore();
    // Setup available books for processedBooksList
    store.books = { sections: [{ name: 'Testament', books: [mark, luke] }], availableBooks: [] };
    const sections: Section[] = [
      { type: 'frontmatter', book: luke, number: 0, id: luke.slug },
      { type: 'chapter-current', book: luke, number: 1, id: `${luke.slug}1`, verses: [] },
    ];
    const response: DisplayResponse = {
      reference: { book: luke, chapter: 1, frontmatter: false, verseStart: 1, verseEnd: 1, verseRanges: [] },
      sections,
      navigation: { currentBook: luke, previousBook: null, nextBook: null } as NavigationState,
    };
    store.initialize(response);
    const loadSpy = vi.spyOn(store, 'loadMoreChapters').mockImplementation(async (direction) => {
      if (direction === 'previous') {
        // simulate loading frontmatter for previous book
        store.addChapter({ type: 'frontmatter', book: mark, number: 0, id: mark.slug });
      }
    });
    await store.checkAndLoadAdjacentChapters();
    expect(loadSpy).toHaveBeenCalledWith('previous');
    // frontmatter for previous book should be included
    expect(store.chapters.has(mark.slug)).toBe(true);
  });

  it('calls loadMoreChapters for next book on last chapter boundary', async () => {
    const store = useBibleStore();
    // Setup available books for processedBooksList
    store.books = { sections: [{ name: 'Testament', books: [mark, luke] }], availableBooks: [] };
    const sections: Section[] = [
      { type: 'frontmatter', book: mark, number: 0, id: mark.slug },
      { type: 'chapter-current', book: mark, number: mark.chapterCount, id: `${mark.slug}${mark.chapterCount}`, verses: [] },
    ];
    const response: DisplayResponse = {
      reference: { book: mark, chapter: mark.chapterCount, frontmatter: false, verseStart: 1, verseEnd: 1, verseRanges: [] },
      sections,
      navigation: { currentBook: mark, previousBook: null, nextBook: null } as NavigationState,
    };
    store.initialize(response);
    const loadSpy = vi.spyOn(store, 'loadMoreChapters').mockImplementation(async (direction) => {
      if (direction === 'next') {
        // simulate loading frontmatter for next book
        store.addChapter({ type: 'frontmatter', book: luke, number: 0, id: luke.slug });
      }
    });
    await store.checkAndLoadAdjacentChapters();
    expect(loadSpy).toHaveBeenCalledWith('next');
    // frontmatter for next book should be included
    expect(store.chapters.has(luke.slug)).toBe(true);
  });
});
