<template>
    <div>
        <!-- Tabs -->
        <div class="dark:border-theme-700 mb-4 flex border-b">
            <button
                class="flex-1 pb-2 text-center text-sm transition-colors"
                :class="{
                    'border-theme-500 text-theme-900 dark:text-theme-400 border-b-1':
                        activeTab === 'book',
                    'text-theme-500 dark:text-theme-400': activeTab !== 'book',
                }"
                @click="activeTab = 'book'"
            >
                Buch
            </button>
            <button
                class="flex-1 pb-2 text-center text-sm transition-colors"
                :class="{
                    'border-theme-500 text-theme-900 dark:text-theme-400 border-b-2':
                        activeTab === 'chapter',
                    'text-theme-500 dark:text-theme-400':
                        activeTab !== 'chapter',
                    'cursor-not-allowed opacity-50': !selectedBook,
                }"
                :disabled="!selectedBook"
                @click="activeTab = 'chapter'"
            >
                Kapitel
            </button>
            <button
                class="flex-1 pb-2 text-center text-sm transition-colors"
                :class="{
                    'border-theme-500 text-theme-900 dark:text-theme-400 border-b-2':
                        activeTab === 'verse',
                    'text-theme-500 dark:text-theme-400': activeTab !== 'verse',
                    'cursor-not-allowed opacity-50':
                        !selectedBook || !selectedChapter,
                }"
                :disabled="!selectedBook || !selectedChapter"
                @click="activeTab = 'verse'"
            >
                Vers
            </button>
        </div>

        <!-- Search Input -->
        <div class="mb-4">
            <input
                v-model="searchQuery"
                type="text"
                :placeholder="
                    activeTab === 'book'
                        ? 'Bibelbuch suchen...'
                        : activeTab === 'chapter'
                          ? 'Kapitel suchen...'
                          : 'Vers suchen...'
                "
                class="dark:bg-theme-700 text-theme-900 dark:text-theme-100 bg-theme border-theme-300 dark:border-theme-600 w-full rounded-lg border px-3 py-2 text-sm placeholder-gray-500 transition-colors focus:border-indigo-500 focus:ring-2 focus:ring-indigo-500 dark:placeholder-gray-400 dark:focus:border-indigo-400 dark:focus:ring-indigo-400"
            />
        </div>

        <!-- Book Tab Content -->
        <div v-if="activeTab === 'book'" class="space-y-6 overflow-y-auto">
            <div
                v-for="testament in filteredCategories"
                :key="testament.name"
                class="space-y-4"
            >
                <h2
                    class="text-theme-900 dark:text-theme-100 text-lg font-semibold"
                >
                    {{ testament.name }}
                </h2>
                <div class="grid grid-cols-6 gap-2">
                    <button
                        v-for="book in testament.books"
                        :key="book.id"
                        class="hover:bg-opacity-90 dark:hover:bg-opacity-90 text-theme-900 dark:text-theme-100 block w-full rounded-lg px-3 py-2 text-center text-sm transition-colors focus:ring-2 focus:ring-indigo-500 focus:outline-none"
                        :class="[
                            categoryColors(book.category),
                            selectedBook?.id === book.id
                                ? 'ring-theme-600 dark:ring-theme-400 ring-2'
                                : '',
                            !isBookAvailable(book.slug)
                                ? 'cursor-not-allowed opacity-50'
                                : '',
                        ]"
                        :disabled="!isBookAvailable(book.slug)"
                        :title="
                            !isBookAvailable(book.slug)
                                ? 'Dieses Buch steht bald zur Verfügung'
                                : book.name
                        "
                        @click.stop="
                            isBookAvailable(book.slug) && selectBook(book)
                        "
                    >
                        {{ book.abbreviation }}
                    </button>
                </div>
            </div>
        </div>

        <!-- Chapter Tab Content -->
        <div v-else-if="activeTab === 'chapter'" class="overflow-y-auto">
            <div class="grid grid-cols-4 gap-2 sm:grid-cols-7 md:grid-cols-8">
                <button
                    v-for="chapter in filteredChapters"
                    :key="chapter"
                    class="dropdown-item hover:bg-theme-100 dark:hover:bg-theme-700 text-theme-700 dark:text-theme-300 rounded-lg px-3 py-2 text-center text-sm transition-colors focus:ring-2 focus:ring-indigo-500 focus:outline-none dark:focus:ring-indigo-400"
                    :class="{
                        'bg-indigo-50 text-indigo-700 dark:bg-indigo-900/30 dark:text-indigo-300':
                            selectedChapter === chapter,
                    }"
                    @click="selectChapter(chapter)"
                >
                    {{ chapter }}
                </button>
            </div>
        </div>

        <!-- Verse Tab Content -->
        <div v-else-if="activeTab === 'verse'" class="overflow-y-auto">
            <div class="grid grid-cols-4 gap-2 sm:grid-cols-7 md:grid-cols-8">
                <button
                    v-for="verse in filteredVerses"
                    :key="verse"
                    class="dropdown-item hover:bg-theme-100 dark:hover:bg-theme-700 text-theme-700 dark:text-theme-300 rounded-lg px-3 py-2 text-center text-sm transition-colors focus:ring-2 focus:ring-indigo-500 focus:outline-none dark:focus:ring-indigo-400"
                    :class="{
                        'bg-indigo-50 text-indigo-700 dark:bg-indigo-900/30 dark:text-indigo-300':
                            selectedVerse === verse,
                    }"
                    @click="selectVerse(verse)"
                >
                    {{ verse }}
                </button>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useBibleStore } from '@/stores/bible/bibleSectionStore';
import { getCategoryBgColorClasses } from '@/utils/categoryColors';
import type { BookCategory, BookView } from '@esbo/types';
import { computed, ref, watch } from 'vue';

// Add the Bible store
const bibleStore = useBibleStore();

const props = defineProps<{
    categories: Array<{
        name: string;
        books: BookView[];
    }>;
    selectedBook?: BookView;
    selectedChapter?: number;
    initialActiveTab?: 'book' | 'chapter' | 'verse';
}>();

const emit = defineEmits<{
    (e: 'select-book', book: BookView): void;
    (e: 'select-chapter', book: BookView, chapter: number): void;
    (e: 'select-verse', book: BookView, chapter: number, verse: number): void;
    (e: 'update:activeTab', tab: 'book' | 'chapter' | 'verse'): void;
}>();

const activeTab = ref<'book' | 'chapter' | 'verse'>(
    props.initialActiveTab || 'book',
);
const searchQuery = ref('');
const selectedChapter = ref<number | undefined>(props.selectedChapter);

// Watch for changes in props.selectedChapter
watch(
    () => props.selectedChapter,
    (newChapter) => {
        if (newChapter !== undefined) {
            selectedChapter.value = newChapter;
        }
    },
);

// Watch for activeTab changes and emit them
watch(activeTab, (newTab) => {
    emit('update:activeTab', newTab);
});

const categoryColors = computed(() => (category: BookCategory) => {
    return getCategoryBgColorClasses(category, 100);
});

const filteredCategories = computed(() => {
    if (!searchQuery.value) return props.categories;

    const query = searchQuery.value.toLowerCase();
    return props.categories
        .map((testament) => ({
            ...testament,
            books: testament.books.filter((book) =>
                book.name.toLowerCase().includes(query),
            ),
        }))
        .filter((testament) => testament.books.length > 0);
});

const filteredChapters = computed(() => {
    if (!props.selectedBook) return [];
    const count = props.selectedBook.chapterCount ?? 0;
    return count > 0 ? Array.from({ length: count }, (_, i) => i + 1) : [];
});

const filteredVerses = computed(() => {
    if (!selectedChapter.value || !props.selectedBook) return [];

    try {
        const bookSlug = props.selectedBook.slug;
        const chapterNumber = selectedChapter.value;

        const book = bibleStore.processedBooksList.find(
            (book) => book.slug === bookSlug,
        );

        if (!book || !chapterNumber) return [];

        const chapter = book.chapters?.find((c) => c.number === chapterNumber);
        console.log('filteredVerses', chapter);
        if (!chapter) return [];

        const verseCount = chapter.verseCount;
        console.log('verseCount', verseCount);
        return Array.from({ length: verseCount }, (_, i) => i + 1);
    } catch (error) {
        console.error('Error in filteredVerses computed property:', error);
        return [];
    }
});

// Watch for changes in activeTab to handle tab transitions properly
watch(activeTab, (newTab, _oldTab) => {
    // When switching to chapter tab, ensure we have the book selected
    if (newTab === 'chapter' && !props.selectedBook) {
        // If no book is selected, go back to book tab
        activeTab.value = 'book';
    }

    // When switching to verse tab, ensure we have both book and chapter selected
    if (newTab === 'verse' && (!props.selectedBook || !selectedChapter.value)) {
        // If missing required selections, go back to appropriate tab
        activeTab.value = !props.selectedBook ? 'book' : 'chapter';
    }
});

// Add this function to check book availability
function isBookAvailable(slug: string): boolean {
    return bibleStore.isBookAvailable(slug);
}

function selectBook(book: BookView) {
    emit('select-book', book);
    searchQuery.value = '';
    activeTab.value = 'chapter';
    // Reset any previously selected chapter and verse
    selectedChapter.value = undefined;
    selectedVerse.value = undefined;
}

function selectChapter(chapter: number) {
    if (!props.selectedBook) return;
    emit('select-chapter', props.selectedBook, chapter);
    // Reset search query
    searchQuery.value = '';
    selectedChapter.value = chapter;
    // Reset any previously selected verse
    selectedVerse.value = undefined;
    // Move to verse tab
    activeTab.value = 'verse';
    console.log('selectedChapter.value', selectedChapter.value, props.selectedBook);
}

function selectVerse(verse: number) {
    if (!props.selectedBook || !selectedChapter.value) return;
    // Store the selected verse
    selectedVerse.value = verse;
    // Emit the verse selection event with all necessary data
    emit('select-verse', props.selectedBook, selectedChapter.value, verse);
    // Reset search query
    searchQuery.value = '';
    // We don't reset to book tab here as the parent component will handle navigation
    // The parent component (BibleNavigationAside) will close the navigation panel
}

const selectedVerse = ref<number | undefined>();

// Watch for changes in props.verseCount to ensure we have verses to select
watch(() => selectedChapter.value, (newChapter) => {
    console.log('selectedChapter.value', newChapter);
});
</script>
