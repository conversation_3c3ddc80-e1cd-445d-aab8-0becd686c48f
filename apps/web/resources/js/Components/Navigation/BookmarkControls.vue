<template>
    <div
        class="flex items-center space-x-2"
        role="navigation"
        aria-label="Bookmark controls"
    >
        <a
            v-if="hasBookmarks"
            href="/bookmarks"
            class="text-theme-600 dark:text-theme-400 hover:text-theme-900 dark:hover:text-theme-200 mr-2 text-sm"
        >
            Lesezeichen
        </a>
        <button
            class="hover:bg-theme-100 dark:hover:bg-theme-700 text-theme-600 hover:text-theme-900 dark:text-theme-400 dark:hover:text-theme-200 rounded-md p-1.5"
            aria-label="Toggle bookmark"
            :aria-pressed="isBookmarked"
            @click="toggleBookmark"
        >
            <Icon
                name="Bookmark"
                class="h-5 w-5"
                :class="{ 'fill-current': isBookmarked }"
                :aria-label="'Toggle bookmark'"
            />
        </button>
    </div>
</template>

<script setup lang="ts">
import { Icon } from '@/Components/Icons';
import { useLocalStorage } from '@vueuse/core';
import type { Bookmark } from '@esbo/types';
import { useBibleStore } from '@/stores/bible/bibleSectionStore';
import { computed, watch } from 'vue';

const bibleStore = useBibleStore();
const BOOKMARKS_KEY = 'esbo-bookmarks';
const bookmarkList = useLocalStorage<Bookmark[]>(BOOKMARKS_KEY, []);

const currentReference = computed(() => {
    if (!bibleStore.currentChapterSection || !bibleStore.currentBook)
        return null;

    return {
        book: bibleStore.currentBook.slug,
        chapter: bibleStore.currentChapter,
        verse: bibleStore.currentVerse,
    };
});


const isBookmarked = computed(() => {
    if (!currentReference.value) return false;

    return bookmarkList.value.some(
        (b) =>
            b.book === currentReference.value?.book &&
            b.chapter === currentReference.value?.chapter &&
            b.verse === currentReference.value?.verse,
    );
});

const hasBookmarks = computed(() => bookmarkList.value.length > 0);


watch(
    () => [
        bibleStore.currentBook?.slug,
        bibleStore.currentChapter,
        bibleStore.currentVerse,
    ],
    () => {
        // reactive update of bookmark status
    },
);

const toggleBookmark = () => {
    if (!currentReference.value) return;

    if (isBookmarked.value) {
        bookmarkList.value = bookmarkList.value.filter(
            (b) =>
                !(
                    b.book === currentReference.value?.book &&
                    b.chapter === currentReference.value?.chapter &&
                    b.verse === currentReference.value?.verse
                ),
        );
    } else {
        bookmarkList.value.push({
            book: currentReference.value.book,
            chapter: currentReference.value.chapter!,
            verse: currentReference.value.verse,
        });
    }


};
</script>
