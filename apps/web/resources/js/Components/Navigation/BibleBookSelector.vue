<template>
    <div
        class="bible-selector dropdown-container relative z-[1]"
        style="position: relative; z-index: 1; transform: translateZ(0)"
    >
        <div class="flex items-center gap-2">
            <button
                type="button"
                class="hover:text-theme-900 hover:bg-theme flex items-center gap-1 rounded-lg p-1 transition-colors duration-200 focus:ring-2 focus:ring-blue-500 focus:outline-none"
                :class="{
                    'text-blue-600': selectedBook,
                    'bg-theme': isOpen,
                    'text-theme-600': inSearchComponent,
                    'text-theme-900 dark:text-theme-100':
                        !inSearchComponent && !selectedBook,
                }"
                :aria-expanded="isOpen"
                :aria-controls="'bible-navigation'"
                role="button"
                aria-label="Toggle Bible navigation"
                @click="handleNavigationClick"
            >
                <Icon name="BookOpen" class="h-5 w-5" />
            </button>
        </div>

        <!-- Desktop Navigation Dropdown -->
        <template v-if="!isMobile">
            <BibleBookDropdown
                id="bible-navigation"
                v-model="isOpen"
                :categories="categories"
                :selected-book="selectedBook"
                :search-query="searchQuery"
                :verse-count="calculateVerseCount()"
                @select-book="handleBookSelect"
                @select-chapter="handleChapterSelect"
                @select-verse="handleVerseSelect"
            />
            <Overlay :show="isOpen" @click="closeNavigation" />
        </template>
    </div>
</template>

<script setup lang="ts">
import Overlay from '@/Components/common/Overlay.vue';
import { Icon } from '@/Components/Icons';
import BibleBookDropdown from '@/Components/Navigation/BibleBookDropdown.vue';

import { useToggle } from '@vueuse/core';
import { useResponsiveStore } from '@/composables/useResponsiveStore';
import { useNavigationStore } from '@/stores/navigationStore';
import { logger } from '@/utils/logger';
import type { BookView } from '@esbo/types';
import { Testament } from '@esbo/types';
import { router, usePage } from '@inertiajs/vue3';
import { onMounted, ref, watch } from 'vue';

const props = defineProps<{
    modelValue?: BookView;
    inSearchComponent?: boolean;
}>();

const emit = defineEmits<{
    (e: 'update:modelValue', value: BookView): void;
    (e: 'update:searchQuery', value: string): void;
    (e: 'book-select', book: BookView): void;
}>();

const navigationStore = useNavigationStore();

// Use VueUse's useToggle for dropdown open/close state
const [isOpen, toggleDropdown] = useToggle(false);
const selectedBook = ref<BookView | undefined>(props.modelValue);
const { isMobile } = useResponsiveStore();
const searchQuery = ref('');
const selectedChapter = ref<number | undefined>();
const selectedVerse = ref<number | undefined>();

// Fetch books when component is mounted
const categories = ref<
    Array<{
        name: string;
        books: BookView[];
    }>
>([]);

// Watch for external model value changes
watch(
    () => props.modelValue,
    (newValue) => {
        selectedBook.value = newValue;
    },
);

onMounted(async () => {
    const { books } = usePage().props;

    if (!books || !books.sections || !Array.isArray(books.sections)) {
        logger.error('No books data available from Inertia props');
        return;
    }

    const otBooks = books.sections[0].books.filter(
        (book) => book.testament === Testament.OT,
    );
    const ntBooks = books.sections[1].books.filter(
        (book) => book.testament === Testament.NT,
    );

    categories.value = [
        {
            name: 'Altes Testament',
            books: otBooks,
        },
        {
            name: 'Neues Testament',
            books: ntBooks,
        },
    ];
});

function handleBookSelect(book: BookView) {
    selectedBook.value = book;
    emit('update:modelValue', book);
    emit('update:searchQuery', book.name);
}

function handleChapterSelect(book: BookView, chapter: number) {
    selectedBook.value = book;
    selectedChapter.value = chapter;
    //emit('select-chapter', book, chapter);
}

/**
 * Calculate the verse count for the currently selected book and chapter
 * @returns The number of verses in the current chapter or a default value
 */
function calculateVerseCount(): number {
    if (!selectedBook.value || !selectedChapter.value) {
        return 30; // Default value if no book or chapter is selected
    }

    // Try to find the book in the categories
    const book = categories.value
        .flatMap((category) => category.books)
        .find((book) => book.slug === selectedBook.value?.slug);

    if (!book || !book.chapters) {
        return 30; // Default value if book or chapters not found
    }

    // Find the chapter and get its verse count
    const chapter = book.chapters.find(
        (chapter) =>
            typeof chapter === 'object' && chapter !== null && 'number' in chapter
                ? chapter.number === selectedChapter.value
                : chapter === selectedChapter.value
    );

    return (chapter && typeof chapter === 'object' && 'verseCount' in chapter && typeof chapter.verseCount === 'number')
    ? chapter.verseCount
    : 30;
}

function handleVerseSelect(book: BookView, chapter: number, verse: number) {
    selectedBook.value = book;
    selectedChapter.value = chapter;
    selectedVerse.value = verse;
    closeNavigation();

    // Format book name: replace period with hyphen and remove spaces
    const bookName = book.slug
        .replace(/(\d+)\s+(\w+)/, '$1-$2')
        .replace(/\s+/g, '');
    const url = `/${bookName}${chapter},${verse}`;

    // Navigate to the book and chapter
    router.visit(url);
}

function closeNavigation() {
    isOpen.value = false;
}

function handleNavigationClick() {
    if (isMobile.value) {
        navigationStore.openNavigationAside();
    } else {
        toggleDropdown();
    }
}
</script>
