<template>
    <div
        v-show="modelValue"
        ref="dropdownElement"
        class="bible-dropdown show-scrollbar-on-hover dark:bg-theme-800 bg-theme border-theme-300 dark:border-theme-600 absolute -left-2 z-[9999] mt-1 hidden rounded-b-lg border p-4 shadow-lg md:block dark:shadow-gray-900/30"
        :style="{
            width: dropdownWidth,
            maxHeight: maxHeight,
        }"
        role="menu"
        aria-orientation="vertical"
        aria-labelledby="menu-button"
        tabindex="-1"
    >
        <BibleBooksGrid
            :categories="categories"
            :selected-book="selectedBook"
            :initial-active-tab="activeTab"
            @select-book="selectBook"
            @select-chapter="handleChapterSelect"
            @select-verse="handleVerseSelect"
            @update:active-tab="activeTab = $event"
        />
    </div>
</template>

<script setup lang="ts">
import type { BookView } from '@esbo/types';
import { nextTick, onBeforeUnmount, onMounted, ref, watch } from 'vue';
import BibleBooksGrid from './BibleBooksGrid.vue';

const props = defineProps<{
    modelValue: boolean;
    categories: Array<{
        name: string;
        books: BookView[];
    }>;
    selectedBook?: BookView;
    verseCount?: number;
}>();

const emit = defineEmits<{
    (e: 'update:modelValue', value: boolean): void;
    (e: 'select-book', book: BookView): void;
    (e: 'select-chapter', book: BookView, chapter: number): void;
    (e: 'select-verse', book: BookView, chapter: number, verse: number): void;
}>();

const activeTab = ref<'book' | 'chapter' | 'verse'>('book');
const dropdownWidth = ref('300px');
const dropdownElement = ref<HTMLElement | null>(null);
const maxHeight = ref('300px');

// Calculate max height based on dropdown position
const calculateMaxHeight = () => {
    if (!dropdownElement.value) return;

    // Wait for the dropdown to be fully rendered
    nextTick(() => {
        const dropdown = dropdownElement.value;
        if (!dropdown) return;

        // Get the dropdown's position and dimensions
        const dropdownRect = dropdown.getBoundingClientRect();
        const viewportHeight = window.innerHeight;

        // Calculate available space from dropdown's top to viewport bottom with 20px margin
        const spaceBelow = viewportHeight - dropdownRect.top - 20;

        // Set reasonable min/max constraints
        const minHeight = 300; // Minimum height for usability
        const maxAllowedHeight = 600; // Maximum allowed height

        // Calculate the final height, ensuring it's within bounds
        let height = Math.min(spaceBelow, maxAllowedHeight);
        height = Math.max(height, minHeight);

        // Ensure the calculated height doesn't exceed the dropdown's content height
        const contentHeight = dropdown.scrollHeight;
        if (contentHeight < height) {
            height = contentHeight;
        }

        // Apply the calculated height
        maxHeight.value = `${Math.floor(height)}px`;
    });
};

// Watch for changes in visibility
watch(
    () => props.modelValue,
    (isVisible) => {
        if (isVisible) {
            // When dropdown becomes visible, recalculate height
            nextTick(() => {
                calculateMaxHeight();
            });
        }
    },
    { immediate: true },
);

// Update dropdown width based on parent container
const updateDropdownWidth = () => {
    //const parentContainer = document.querySelector('.bible-selector');
    //if (!parentContainer) return;

    const searchContainer = document.querySelector('.search-container');
    //console.log('searchContainer', searchContainer);
    if (!searchContainer) return;

    dropdownWidth.value = `${searchContainer.clientWidth}px`;
};

onMounted(async () => {
    // Wait for next tick to ensure parent elements are rendered
    await nextTick();
    updateDropdownWidth();
    window.addEventListener('resize', updateDropdownWidth);

    // Get reference to the dropdown element
    dropdownElement.value = document.querySelector('.bible-dropdown');

    // Calculate initial height
    calculateMaxHeight();

    // Add resize listener to recalculate height when window size changes
    window.addEventListener('resize', calculateMaxHeight);
});

onBeforeUnmount(() => {
    window.removeEventListener('resize', updateDropdownWidth);
    window.removeEventListener('resize', calculateMaxHeight);
});

function selectBook(book: BookView) {
    emit('select-book', book);
}

function handleChapterSelect(book: BookView, chapter: number) {
    emit('select-chapter', book, chapter);
    activeTab.value = 'chapter';
}

function handleVerseSelect(book: BookView, chapter: number, verse: number) {
    emit('select-verse', book, chapter, verse);
    emit('update:modelValue', false);

    activeTab.value = 'book';
}
</script>

<style>
/* Add debug styles for potential container issues */
.bible-selector,
.search-container,
:deep(.search-container) {
    position: relative;
    z-index: 1;
    overflow: visible !important; /* Force override any overflow */
}

/* Debug styles for parent elements */
:deep(*) {
    outline: 1px dashed rgba(255, 0, 0, 0.2);
}
.bible-dropdown {
    position: absolute;
    z-index: 9999; /* Higher than any other z-index in your app */
    transform: translateZ(0); /* Create a new stacking context */
    will-change: transform;
    border: var(--debug-border, none);
}

.overflow-y-auto {
    scrollbar-gutter: stable;
    scrollbar-width: thin;
}

.overflow-y-auto::-webkit-scrollbar {
    width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
    background: transparent !important;
    background: rgba(156, 163, 175, 0.2);
    border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
    background: transparent;
    border-radius: 3px;
}

.overflow-y-auto:hover::-webkit-scrollbar-thumb {
    background: rgba(156, 163, 175, 0.5); /* gray-400 with opacity */
}

/* Use overlay scrollbar to prevent layout shift */
@supports (overflow-y: overlay) {
    .overflow-y-auto {
        overflow-y: overlay;
    }
}

/* Dark mode adjustments */
.dark .overflow-y-auto:hover::-webkit-scrollbar-thumb {
    background: rgba(156, 163, 175, 0.5);
}

.show-scrollbar-on-hover {
    scrollbar-gutter: stable;
    scrollbar-width: thin;
}

.show-scrollbar-on-hover::-webkit-scrollbar {
    width: 6px;
}

.show-scrollbar-on-hover::-webkit-scrollbar-track {
    background: transparent !important;
    background: rgba(156, 163, 175, 0.2);
    border-radius: 3px;
}

.show-scrollbar-on-hover::-webkit-scrollbar-thumb {
    background: transparent;
    border-radius: 3px;
}

.show-scrollbar-on-hover:hover::-webkit-scrollbar-thumb {
    background: rgba(156, 163, 175, 0.5); /* gray-400 with opacity */
}

/* Use overlay scrollbar to prevent layout shift */
@supports (overflow-y: overlay) {
    .show-scrollbar-on-hover {
        overflow-y: overlay;
    }
}

/* Dark mode adjustments */
.dark .show-scrollbar-on-hover:hover::-webkit-scrollbar-thumb {
    background: rgba(156, 163, 175, 0.5);
}
</style>
