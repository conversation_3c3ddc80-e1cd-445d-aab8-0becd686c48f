<template>
    <div class="relative" role="navigation" aria-label="Bible navigation">
        <!-- Main navbar with slide-up transition -->
        <nav
            v-if="!isMobile"
            class="main-navbar dark:bg-theme-800/75 bg-theme/75 fixed inset-x-0 top-0 z-50 transform backdrop-blur transition-all duration-300 ease-in-out will-change-transform"
            :class="{
                '-translate-y-full': isScrollingDown && !isSearchExpanded,
                'shadow-lg': hideSubmenu,
            }"
            aria-label="Main navigation"
            @mouseenter="handleMouseEnter"
            @mouseleave="handleMouseLeave"
        >
            <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                <div class="flex h-20 items-center justify-between">
                    <!-- Logo (left) -->
                    <div
                        class="w-1/4 flex-shrink-0 relative flex items-center"
                        :class="{ invisible: isSearchExpanded }"
                    >
                        <a href="/" class="flex items-center" aria-label="Home">
                            <ApplicationLogo class="h-10 w-auto dark:invert" />
                        </a>
                        <div class="text-theme-600 dark:text-theme-400 text-xs px-2">Web (<a href="/changelog" class="text-theme-600 dark:text-theme-400 underline">Beta</a>)</div>
                    </div>

                    <!-- Search Bar (center) - fixed width to ensure centering -->
                    <div class="flex flex-1 justify-center">
                        <!-- Mobile view components -->
                        <div
                            class="flex w-full items-center justify-between lg:hidden"
                        >
                            <BibleBookSelector
                                aria-label="Select Bible book"
                                @book-select="handleBookSelect"
                                @open-navigation="openNavigation"
                            />
                            <button
                                class="text-theme-600 hover:text-theme-900 dark:text-theme-400 dark:hover:text-theme-200 p-2"
                                aria-label="Toggle search"
                                aria-expanded="false"
                                :class="{ 'aria-expanded': isSearchExpanded }"
                                @click="toggleSearchExpanded"
                            >
                                <Icon
                                    name="Search"
                                    class="h-5 w-5"
                                    :aria-label="'Toggle search'"
                                />
                            </button>
                        </div>

                        <!-- Desktop Search Field -->
                        <div class="hidden w-full max-w-xl lg:block">
                            <BibleSearch class="in-navbar" />
                        </div>
                    </div>

                    <!-- Text Format Button (right) -->
                    <div
                        class="flex w-1/4 flex-shrink-0 justify-end"
                        :class="{ invisible: isSearchExpanded }"
                    >
                        <button
                            id="txt-format-btn"
                            type="button"
                            class="hover:bg-theme-100 dark:hover:bg-theme-800 text-theme-600 hover:text-theme-900 dark:text-theme-400 dark:hover:text-theme-100 flex h-10 w-10 items-center justify-center rounded-full focus:outline-hidden"
                            :class="{
                                'bg-theme-100 dark:bg-theme-800':
                                    isSettingsOpen,
                            }"
                            aria-label="Text format settings"
                            @click="() => toggleSettings()"
                        >
                            <Icon
                                name="TextSettings"
                                class="h-5 w-5"
                                :aria-label="'Text format settings'"
                            />
                        </button>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Border line that respects max width -->
        <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div class="border-theme-200 dark:border-theme-400 border-b"></div>
        </div>

        <!-- Sub navigation bar - completely independent from main nav -->
        <div
            v-if="!hideSubmenu"
            class="fixed inset-x-0 top-0 z-40 transition-all duration-300 ease-in-out"
            :class="{ 'submenu-visible': isScrollingDown && !isSearchExpanded }"
            :style="submenuTransform"
        >
            <!-- Add the fine spacer line here -->
            <!-- Fine spacer line that respects margins -->
            <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                <div
                    class="border-theme-200 dark:border-theme-400 border-t"
                ></div>
            </div>

            <SubNavigationBar
                @mouseenter="handleMouseEnter"
                @mouseleave="handleMouseLeave"
            />
        </div>
    </div>
</template>

<script setup lang="ts">
/**
 * NavigationBar Component
 *
 * The main navigation bar component that provides primary navigation controls.
 * Handles responsive behavior, search functionality, and integrates with sub-navigation.
 *
 * @component
 * @example
 * <NavigationBar reference="John 3:16" />
 */
import ApplicationLogo from '@/Components/common/ApplicationLogo.vue';
import { Icon } from '@/Components/Icons';
import BibleBookSelector from '@/Components/Navigation/BibleBookSelector.vue';
import SubNavigationBar from '@/Components/Navigation/SubNavigationBar.vue';
import BibleSearch from '@/Components/Search/BibleSearch.vue';

import { router, usePage } from '@inertiajs/vue3';
import { computed, onMounted, onUnmounted, ref } from 'vue';

import { useToggle } from '@vueuse/core';
import type { BookView } from '@esbo/types';

// Settings aside state (open/close)
const [isSettingsOpen, toggleSettings] = useToggle(false);


/**
 * Determines if the current viewport is mobile-sized
 * @returns {boolean} True if viewport width is less than 768px
 */
import { useResponsiveStore } from '@/composables/useResponsiveStore';
const { isMobile } = useResponsiveStore();

function openNavigation() {
    emit('open-navigation');
}

/**
 * Computes the transform style for the submenu based on scroll position
 * @returns {Object} Style object with transform property for desktop, empty object for mobile
 */
const submenuTransform = computed(() => {
    if (isMobile.value) return {};

    return {
        transform: isScrollingDown.value ? 'translateY(0)' : 'translateY(80px)',
    };
});

/**
 * Configuration interface for scroll handling behavior
 * @property {number} threshold - Scroll distance threshold in pixels before considering it a scroll action
 * @property {number} debounceTime - Time in milliseconds to debounce scroll events
 */
interface ScrollConfig {
    threshold: number;
    debounceTime: number;
}



/**
 * Component props definition
 * @property {string} [reference] - Optional Bible reference to display
 */
defineProps<{
    reference?: string;
}>();

const emit = defineEmits<{
    (e: 'open-navigation'): void;
}>();

/**
 * Reactive state for search expansion
 */
const isSearchExpanded = ref(false);

/**
 * Tracks scroll direction (true if scrolling down)
 */
const isScrollingDown = ref(false);

/**
 * Tracks if the mouse is hovering over the navigation
 */
const isHovering = ref(false);

/**
 * Tracks the last scroll position for direction detection
 */
let lastScrollY = 0;

/**
 * Timeout reference for debouncing scroll events
 */
let scrollTimeout: number | null = null;

/**
 * Determines if the submenu should be hidden based on the current page
 * @returns {boolean} True if the current page doesn't require the submenu
 */
const hideSubmenu = computed(() => {
    const page = usePage();
    return (
        page.component === 'SearchResults' ||
        page.component === 'AvailableBooks'
    );
});

/**
 * Configuration for scroll behavior
 */
const scrollConfig: ScrollConfig = {
    threshold: 100, // pixels
    debounceTime: 50, // milliseconds
};

/**
 * Toggles the expanded state of the search input
 * Updates the isSearchExpanded reactive state
 */
const toggleSearchExpanded = () => {
    isSearchExpanded.value = !isSearchExpanded.value;
};

/**
 * Handles scroll events to control navbar visibility
 * Uses debouncing to improve performance by limiting how often the scroll handler runs
 */
const handleScroll = () => {
    if (scrollTimeout) {
        window.clearTimeout(scrollTimeout);
    }

    scrollTimeout = window.setTimeout(() => {
        const currentScrollY = window.scrollY;

        // Only update if scroll position changed significantly
        if (Math.abs(currentScrollY - lastScrollY) > scrollConfig.threshold) {
            isScrollingDown.value = currentScrollY > lastScrollY;
            lastScrollY = currentScrollY > 0 ? currentScrollY : 0;
        }

        scrollTimeout = null;
    }, scrollConfig.debounceTime);
};

/**
 * Handles mouse enter event on the navbar
 * Shows the navbar when mouse enters (desktop only)
 */
const handleMouseEnter = () => {
    if (isMobile.value) return;
    isHovering.value = true;
    isScrollingDown.value = false;
};

/**
 * Handles mouse leave event on the navbar
 * Restores scroll state when mouse leaves (desktop only)
 */
const handleMouseLeave = () => {
    if (isMobile.value) return;
    isHovering.value = false;

    // Only update scroll state if we're not at the top
    if (window.scrollY > 0) {
        isScrollingDown.value = true;
    }
};

/**
 * Sets up scroll event listener when component is mounted
 */
onMounted(() => {
    window.addEventListener('scroll', handleScroll, { passive: true });
});

/**
 * Cleans up event listeners and timeouts when component is unmounted
 */
onUnmounted(() => {
    if (scrollTimeout) {
        window.clearTimeout(scrollTimeout);
    }
    window.removeEventListener('scroll', handleScroll);
});

/**
 * Handles book selection and navigates to the selected book/chapter
 * @param {BookView} book - The selected book
 * @param {number} [chapter] - Optional chapter number (defaults to 1)
 */
const handleBookSelect = (book: BookView, chapter?: number) => {
    const chapterNumber = chapter || 1;
    router.get(
        route('bible.chapter', {
            book: book.slug,
            chapter: chapterNumber,
        }),
    );
};
</script>

<style scoped>
.submenu-visible {
    transform: translateY(0) !important;
}

/* When main nav is hidden, show submenu at top */
:deep(.submenu-visible nav) {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 40;
}
</style>
