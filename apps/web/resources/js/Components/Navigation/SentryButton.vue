<script setup lang="ts">
import Icon from '@/Components/Icons/Icon.vue';
import * as Sentry from '@sentry/vue'
import { onMounted, ref } from 'vue';
import { useAppVersion } from '@/composables/useAppVersion';

const sentryButton = ref<HTMLElement | null>(null);

// Retrieve the app version using the composable
const { suffixVersion } = useAppVersion();

onMounted(() => {
    const feedback = Sentry.getFeedback();
    console.log('feedback',feedback);
    if (sentryButton.value) {
        feedback?.attachTo(sentryButton.value);
    }
})

</script>

<template>
    <button ref="sentryButton" type="button" class="fixed flex bg-white/30 dark:bg-white/70 z-50 backdrop-blur-xs items-center top-auto bottom-20 sm:bottom-12 leading-[1.14em] right-5 cursor-pointer left-auto gap-2 p-2 inset rounded-lg border border-theme-300 dark:border-theme-600 transition-transform ease-in-out duration-200 w-25 h-12 sm:w-45 sm:h-12" aria-hidden="false" aria-label="Fehler melden">
        <Icon name="Megaphone" class="h-6 w-6" aria-hidden="true" />
        <span class="text-sm sm:block hidden">Fehler melden (Beta)</span>
        <span class="text-sm sm:text-sm block sm:hidden" aria-label="App Version">{{ suffixVersion }}</span>
    </button>
</template>
