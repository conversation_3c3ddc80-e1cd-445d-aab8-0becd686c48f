<template>
    <Sheet
        class="bg-theme-100 dark:bg-theme-800"
        :is-open="isOpen"
        position="left"
        :title="
            activeTab === 'chapter' && selectedBook
                ? selectedBook.name
                : 'Bibel Navigation'
        "
        @close="$emit('close')"
    >
        <div class="flex h-full flex-col bg-theme-100 dark:bg-theme-800">
            <BibleBooksGrid
                :categories="sectionsToCategories"
                :selected-book="selectedBook"
                :selected-chapter="selectedChapter"
                :initial-active-tab="activeTab"
                class="flex-1 overflow-y-auto p-4"
                @select-book="handleBookSelect"
                @select-chapter="handleChapterSelect"
                @select-verse="handleVerseSelect"
                @update:active-tab="activeTab = $event"
            />
        </div>
    </Sheet>
</template>

<script setup lang="ts">
import Sheet from '@/Components/common/Sheet.vue';
import type { BookView, NavigationSection } from '@esbo/types';
import { router } from '@inertiajs/vue3';
import { computed, ref, watch } from 'vue';
import BibleBooksGrid from './BibleBooksGrid.vue';

// Props
const props = defineProps<{
    isOpen: boolean;
    sections: NavigationSection[];
}>();

// Emits
const emit = defineEmits<{
    (e: 'close'): void;
    (e: 'select-book', book: BookView): void;
    (e: 'select-chapter', book: BookView, chapter: number): void;
    (e: 'select-verse', book: BookView, chapter: number, verse: number): void;
}>();


// State
const activeTab = ref<'book' | 'chapter' | 'verse'>('book');
const selectedBook = ref<BookView | undefined>(undefined);
const selectedChapter = ref<number | undefined>(undefined);
const selectedVerse = ref<number | undefined>(undefined);

// Convert sections to categories format expected by BibleBooksGrid
const sectionsToCategories = computed(() => {
    return [
        {
            name: 'Altes Testament',
            books: props.sections[0]?.books || []
        },
        {
            name: 'Neues Testament',
            books: props.sections[1]?.books || []
        }
    ];
});

// Methods
function handleBookSelect(book: BookView) {
    selectedBook.value = book;
    //emit('select-book', book);
    activeTab.value = 'chapter';
}

function handleChapterSelect(book: BookView, chapter: number) {
    //emit('select-chapter', book, chapter);
    selectedChapter.value = chapter;
    activeTab.value = 'verse';
}

function handleVerseSelect(book: BookView, chapter: number, verse: number) {
    // Navigate to the selected reference
    //emit('select-verse', book, chapter, verse);
    selectedBook.value = book;
    selectedChapter.value = chapter;
    selectedVerse.value = verse;

    // Format book name: replace period with hyphen and remove spaces
    const bookName = book.slug
        .replace(/(\d+)\s+(\w+)/, '$1-$2')
        .replace(/\s+/g, '');
    const url = `/${bookName}${chapter},${verse}`;

    // Navigate to the book and chapter
    router.visit(url);

    // Close the navigation panel
    emit('close');

    // Reset the active tab to book for the next time the panel is opened
    setTimeout(() => {
        activeTab.value = 'book';
    }, 300); // Short delay to ensure the panel closes smoothly before resetting
}

// Reset state when sidebar closes
watch(
    () => props.isOpen,
    (isOpen) => {
        console.log('isOpen', isOpen);
        if (!isOpen) {
            activeTab.value = 'book';
            selectedBook.value = undefined;
        }
    },
);
</script>
