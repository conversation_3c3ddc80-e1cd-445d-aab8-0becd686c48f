<template>
    <div v-if="!useInfiniteScroll" class="fixed bottom-40 z-40 w-full">
        <!-- Previous Chapter But<PERSON> (Left Margin) -->
        <nav class="fixed left-20" aria-label="Vorheriges Kapitel">
            <a
                :class="[
                    previousChapterColor,
                    'text-theme-700 border-theme-200 dark:text-theme-300 dark:border-theme-700 pointer-events-auto flex h-12 w-12 items-center justify-center rounded-lg border shadow-sm transition-all disabled:cursor-not-allowed disabled:opacity-50',
                ]"
                :disabled="!hasPreviousChapter"
                aria-label="Vorheriges Kapitel"
                :href="previousChapterUrl"
            >
                <Icon name="ArrowLeft" class="h-6 w-6" aria-hidden="true" />
            </a>
        </nav>

        <!-- Next Chapter Button (Right Margin) -->
        <nav class="fixed right-20" aria-label="Nächstes Kapitel">
            <a
                :class="[
                    nextChapterColor,
                    'text-theme-700 border-theme-200 dark:text-theme-300 dark:border-theme-700 pointer-events-auto flex h-12 w-12 items-center justify-center rounded-lg border shadow-sm transition-all disabled:cursor-not-allowed disabled:opacity-50',
                ]"
                :disabled="!hasNextChapter"
                aria-label="Nächstes Kapitel"
                :href="nextChapterUrl"
            >
                <Icon name="ArrowRight" class="h-6 w-6" aria-hidden="true" />
            </a>
        </nav>
    </div>
</template>

<script setup lang="ts">
import Icon from '@/Components/Icons/Icon.vue';
import { useBibleStore } from '@/stores/bible/bibleSectionStore';
import { useTextSettingsStore } from '@/stores/textSettingsStore';
import { getCategoryBgColorClasses } from '@/utils/categoryColors';
import { storeToRefs } from 'pinia';
import { computed } from 'vue';

const bibleStore = useBibleStore();
const textSettingsStore = useTextSettingsStore();
const { useInfiniteScroll } = storeToRefs(textSettingsStore);

// Navigation targets using store helpers
const previousTarget = computed(() => bibleStore.getPreviousChapterOrBook());
const nextTarget = computed(() => bibleStore.getNextChapterOrBook());

/**
 * URL for the previous chapter/book boundary, or '#' if not available
 */
const previousChapterUrl = computed(() => {
    if (!previousTarget.value) return '#';
    return `/${previousTarget.value.book.slug}${previousTarget.value.chapter}`;
});

/**
 * URL for the next chapter/book boundary, or '#' if not available
 */
const nextChapterUrl = computed(() => {
    if (!nextTarget.value) return '#';
    return `/${nextTarget.value.book.slug}${nextTarget.value.chapter}`;
});

/**
 * Whether a previous chapter or book exists
 */
const hasPreviousChapter = computed(() => !!previousTarget.value);

/**
 * Whether a next chapter or book exists
 */
const hasNextChapter = computed(() => !!nextTarget.value);

// Get the color for the previous chapter button based on the previous chapter's book category
const previousChapterColor = computed(() => {
    if (!bibleStore.currentBook || bibleStore.currentChapter === null)
        return 'bg-theme-50/25 dark:bg-theme-800/25';

    if (bibleStore.currentChapter > 1) {
        // Same book, previous chapter
        return getCategoryBgColorClasses(
            bibleStore.currentBook.category,
            100,
            20,
        );
    }

    // Find previous book
    const bookIndex = bibleStore.processedBooksList.findIndex(
        (book) => book.slug === bibleStore.currentBook?.slug,
    );
    if (bookIndex > 0) {
        const previousBook = bibleStore.processedBooksList[bookIndex - 1];
        return getCategoryBgColorClasses(previousBook.category, 100, 20);
    }

    return 'bg-theme-50/25 dark:bg-theme-800/25';
});

// Get the color for the next chapter button based on the next chapter's book category
const nextChapterColor = computed(() => {
    if (!bibleStore.currentBook || bibleStore.currentChapter === null)
        return 'bg-theme-50/25 dark:bg-theme-800/25';

    // Always use the current book's category if we're not at the last chapter
    if (
        bibleStore.currentChapter < (bibleStore.currentBook.chapterCount || 0)
    ) {
        return getCategoryBgColorClasses(
            bibleStore.currentBook.category,
            100,
            20,
        );
    }

    // Only check the next book if we're at the last chapter
    const bookIndex = bibleStore.processedBooksList.findIndex(
        (book) => book.slug === bibleStore.currentBook?.slug,
    );
    if (bookIndex < bibleStore.processedBooksList.length - 1) {
        const nextBook = bibleStore.processedBooksList[bookIndex + 1];
        return getCategoryBgColorClasses(nextBook.category, 100, 20);
    }

    return 'bg-theme-50/25 dark:bg-theme-800/25';
});
</script>
