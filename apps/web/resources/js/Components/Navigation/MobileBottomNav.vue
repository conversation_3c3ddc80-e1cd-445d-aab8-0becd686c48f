<script setup lang="ts">
import Icon from '@/Components/Icons/Icon.vue';
import MobileSearchSheet from '@/Components/Search/MobileSearchSheet.vue';
import { useBibleStore } from '@/stores/bible/bibleSectionStore';
import { useTextSettingsStore } from '@/stores/textSettingsStore';
import { getCategoryBgColorClasses } from '@/utils/categoryColors';
import { storeToRefs } from 'pinia';
import { computed, ref } from 'vue';
import { useNavigationStore } from '@/stores/navigationStore';

const bibleStore = useBibleStore();
const textSettingsStore = useTextSettingsStore();
const navigationStore = useNavigationStore();
const { useInfiniteScroll } = storeToRefs(textSettingsStore);

// Navigation targets using store helpers
const previousTarget = computed(() => bibleStore.getPreviousChapterOrBook());
const nextTarget = computed(() => bibleStore.getNextChapterOrBook());

/**
 * URL for the previous chapter/book boundary, or '#' if not available
 */
const previousChapterUrl = computed(() => {
    if (!previousTarget.value) return '#';
    return `/${previousTarget.value.book.slug}${previousTarget.value.chapter}`;
});

/**
 * URL for the next chapter/book boundary, or '#' if not available
 */
const nextChapterUrl = computed(() => {
    if (!nextTarget.value) return '#';
    return `/${nextTarget.value.book.slug}${nextTarget.value.chapter}`;
});

/**
 * Whether a previous chapter or book exists
 */
const hasPreviousChapter = computed(() => !!previousTarget.value);

/**
 * Whether a next chapter or book exists
 */
const hasNextChapter = computed(() => !!nextTarget.value);

// Get the color for the previous chapter button based on the previous chapter's book category
const previousChapterColor = computed(() => {
    if (!bibleStore.currentBook || bibleStore.currentChapter === null)
        return 'bg-theme-50/25 dark:bg-theme-800/25';

    if (bibleStore.currentChapter > 1) {
        // Same book, previous chapter
        return getCategoryBgColorClasses(
            bibleStore.currentBook.category,
            100,
            20,
        );
    }

    // Find previous book
    const bookIndex = bibleStore.processedBooksList.findIndex(
        (book) => book.slug === bibleStore.currentBook?.slug,
    );
    if (bookIndex > 0) {
        const previousBook = bibleStore.processedBooksList[bookIndex - 1];
        return getCategoryBgColorClasses(previousBook.category, 100, 20);
    }

    return 'bg-theme-50/25 dark:bg-theme-800/25';
});

// Get the color for the next chapter button based on the next chapter's book category
const nextChapterColor = computed(() => {
    if (!bibleStore.currentBook || bibleStore.currentChapter === null)
        return 'bg-theme-50/25 dark:bg-theme-800/25';

    if (
        bibleStore.currentChapter < (bibleStore.currentBook.chapterCount || 0)
    ) {
        // Same book, next chapter
        return getCategoryBgColorClasses(
            bibleStore.currentBook.category,
            100,
            20,
        );
    }

    // Find next book
    const bookIndex = bibleStore.processedBooksList.findIndex(
        (book) => book.slug === bibleStore.currentBook?.slug,
    );
    if (bookIndex < bibleStore.processedBooksList.length - 1) {
        const nextBook = bibleStore.processedBooksList[bookIndex + 1];
        return getCategoryBgColorClasses(nextBook.category, 100, 20);
    }

    return 'bg-theme-50/25 dark:bg-theme-800/25';
});

/**
 * Defines the events that this component can emit
 * @property {() => void} open-settings - Emitted when the settings button is clicked
 */
const emit = defineEmits({
    'open-settings': () => true,
});

/**
 * Whether to show the chapter navigation buttons
 */
const showChapterNav = computed(() => !useInfiniteScroll.value);

/**
 * Controls the visibility of the search sheet
 */
const isSearchSheetOpen = ref(false);

/**
 * Open the search sheet
 */
const openSearch = () => {
    isSearchSheetOpen.value = true;
};

/**
 * Close the search sheet
 */
const closeSearch = () => {
    isSearchSheetOpen.value = false;
};

/**
 * Handle search submission
 */
const handleSearch = () => {
    // You can add any search handling logic here if needed
    closeSearch();
};
</script>

<template>
    <nav
        class="dark:bg-theme-800 dark:border-theme-700 border-theme-200 fixed right-0 bottom-0 left-0 z-40 border-t bg-white shadow-lg md:hidden"
        role="navigation"
        aria-label="Mobile navigation"
    >
        <div class="grid h-16 grid-cols-5 items-center px-2">
            <!-- Previous Chapter Button -->
            <div class="flex justify-center">
                <a
                    v-if="showChapterNav"
                    :class="[
                        previousChapterColor,
                        'border-theme-200 text-theme-700 dark:border-theme-700 dark:text-theme-300 flex h-10 w-10 items-center justify-center rounded-lg border shadow-sm transition-all disabled:pointer-events-none disabled:opacity-50',
                    ]"
                    :disabled="!hasPreviousChapter"
                    :href="previousChapterUrl"
                    aria-label="Vorheriges Kapitel"
                >
                    <Icon name="ArrowLeft" class="h-5 w-5" aria-hidden="true" />
                </a>
                <div v-else class="w-10"></div>
            </div>

            <!-- Book Selector -->
            <div class="flex justify-center">
                <button
                    class="rounded-full p-2 text-gray-600 hover:text-gray-900 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none dark:text-gray-300 dark:hover:text-white dark:focus:ring-offset-gray-800"
                    aria-label="Bücher auswählen"
                    @click="navigationStore.openNavigationAside()"
                >
                    <Icon name="BookOpen" class="dark:text-theme-400 h-7 w-7" />
                </button>
            </div>

            <!-- Search Button -->
            <div class="flex justify-center">
                <button
                    class="rounded-full p-2 text-gray-600 hover:text-gray-900 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none dark:text-gray-300 dark:hover:text-white dark:focus:ring-offset-gray-800"
                    aria-label="Suchen"
                    @click="openSearch"
                >
                    <Icon name="Search" class="dark:text-theme-400 h-6 w-6" />
                </button>
            </div>

            <!-- Settings Button -->
            <div class="flex justify-center">
                <button
                    class="rounded-full p-2 text-gray-600 hover:text-gray-900 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none dark:text-gray-300 dark:hover:text-white dark:focus:ring-offset-gray-800"
                    aria-label="Einstellungen"
                    @click="emit('open-settings')"
                >
                    <Icon
                        name="TextSettings"
                        class="dark:text-theme-400 h-6 w-6"
                        :aria-label="'Text format settings'"
                    />
                </button>
            </div>

            <!-- Next Chapter Button -->
            <div class="flex justify-center">
                <a
                    v-if="showChapterNav"
                    :class="[
                        nextChapterColor,
                        'border-theme-200 text-theme-700 dark:border-theme-700 dark:text-theme-300 flex h-10 w-10 items-center justify-center rounded-lg border shadow-sm transition-all disabled:pointer-events-none disabled:opacity-50',
                    ]"
                    :disabled="!hasNextChapter"
                    :href="nextChapterUrl"
                    aria-label="Nächstes Kapitel"
                >
                    <Icon
                        name="ArrowRight"
                        class="h-5 w-5"
                        aria-hidden="true"
                    />
                </a>
                <div v-else class="w-10"></div>
            </div>
        </div>
    </nav>

    <!-- Mobile Search Sheet -->
    <MobileSearchSheet
        :is-open="isSearchSheetOpen"
        @close="closeSearch"
        @search="handleSearch"
    />
</template>
