<template>
    <nav
        class="sub-navbar dark:bg-theme-800/90 bg-theme/90 relative z-10 shadow-sm backdrop-blur"
        role="navigation"
        aria-label="Chapter navigation"
    >
        <div
            class="mx-auto flex h-12 max-w-7xl items-center justify-between px-4 sm:px-6 lg:px-8"
        >
            <!-- Left side: Chapter and verse info with dropdowns -->
            <div class="flex space-x-4">
                <div
                    v-if="isChapterSection"
                    class="text-sm"
                    style="z-index: 100"
                >
                    <ReferenceSelector />
                </div>
                <div v-if="!isChapterSection && isMobile">
                    <button
                        class="text-theme-700 dark:text-theme-300 hover:text-theme-900 dark:hover:text-theme-100 flex cursor-pointer items-center transition-colors"
                        aria-label="Zurück zur vorherigen Seite"
                        @click="goBack"
                    >
                        <Icon
                            name="ArrowLeft"
                            class="h-5 w-5"
                            aria-hidden="true"
                        />
                        <span class="ml-1 text-sm font-medium">Zurück</span>
                    </button>
                </div>
            </div>

            <!-- Right side: Bookmark buttons -->
            <BookmarkControls />
        </div>
    </nav>
</template>

<script setup lang="ts">
/**
 * SubNavigationBar Component
 *
 * A secondary navigation bar that displays chapter navigation and bookmark controls.
 * Conditionally renders content based on the current section context.
 *
 * @component
 * @example
 * <SubNavigationBar />
 */

import Icon from '@/Components/Icons/Icon.vue';
import BookmarkControls from '@/Components/Navigation/BookmarkControls.vue';
import ReferenceSelector from '@/Components/Navigation/ReferenceSelector.vue';
import { useResponsiveStore } from '@/composables/useResponsiveStore';
import { useBibleStore } from '@/stores/bible/bibleSectionStore';
import { computed } from 'vue';

const goBack = () => {
    if (typeof window !== 'undefined') {
        window.history.back();
    }
};

/**
 * Bible store instance for accessing section state
 */
const bibleStore = useBibleStore();

const { isMobile } = useResponsiveStore();

/**
 * Determines if the current section is a chapter section
 * @returns {boolean} True if current section is a chapter section
 */
const isChapterSection = computed(
    () => bibleStore.currentChapterSection !== null,
);
</script>

<style scoped>
/* Scrollbar styling for dropdown menus */
.overflow-auto {
    scrollbar-width: thin;
    scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

.overflow-auto::-webkit-scrollbar {
    width: 4px;
}

.overflow-auto::-webkit-scrollbar-track {
    background: transparent;
}

.overflow-auto::-webkit-scrollbar-thumb {
    background-color: rgba(156, 163, 175, 0.5);
    border-radius: 20px;
}

/* Dark mode scrollbar */
.dark .overflow-auto {
    scrollbar-color: rgba(75, 85, 99, 0.5) transparent;
}

.dark .overflow-auto::-webkit-scrollbar-thumb {
    background-color: rgba(75, 85, 99, 0.5);
}

/* Ensure dropdowns appear above other elements */
.dropdown-container {
    position: relative;
}
</style>
