<template>
    <div
        v-if="showBar"
        class="bg-notify-bar dark:bg-notify-bar fixed top-0 right-0 left-0 z-60 w-full px-3 py-2 shadow-md transition-transform duration-300 sm:px-6"
        :class="{ '-translate-y-full': isScrollingDown }"
    >
        <div
            class="container mx-auto flex flex-col items-start justify-between gap-2 sm:flex-row sm:items-center sm:gap-0"
        >
            <div class="flex w-full items-center space-x-2 sm:w-auto">
                <span class="text-theme-100 font-medium whitespace-nowrap"
                    >Neu hinzugefügt:</span
                >
                <div
                    class="flex max-w-full flex-wrap gap-2 overflow-x-auto pb-1"
                >
                    <Link
                        v-for="book in filteredRecentBooks"
                        :key="book.slug"
                        :href="`/${book.slug}`"
                        class="dark:bg-theme-700 dark:text-theme-200 hover:bg-theme-200 dark:hover:bg-theme-600 flex-shrink-0 rounded bg-white px-2 py-1 text-sm transition-colors"
                    >
                        {{ book.slug }}
                    </Link>
                </div>
            </div>
            <Link
                href="/available-books"
                class="bg-theme-500 hover:bg-theme-600 mt-1 flex items-center self-start rounded px-3 py-1 text-sm whitespace-nowrap text-white sm:mt-0 sm:self-auto"
            >
                <Icon name="FolderSymlink" class="mr-2 h-4 w-4" /> Verfügbare
                Bücher anzeigen
            </Link>
        </div>
    </div>
</template>

<script setup lang="ts">
import { Icon } from '@/Components/Icons';
import { Link } from '@inertiajs/vue3';
import axios from 'axios';
import { computed, onMounted, onUnmounted, ref } from 'vue';

interface RecentBook {
    slug: string;
    name: string;
    updated_at: string;
}

const showBar = ref(false);
const isScrollingDown = ref(false);
const recentBooks = ref<RecentBook[]>([]);
const isMobile = ref(window.innerWidth < 640);
const isLoading = ref(true);

// Fetch recent books from the processed_books table
const fetchRecentBooks = async () => {
    try {
        const response = await axios.get('/api/books/recent');
        recentBooks.value = response.data.data || [];
        // Only show bar if there are recent books to display
        showBar.value = recentBooks.value.length > 0;
    } catch (error) {
        console.error('Failed to fetch recent books:', error);
        showBar.value = false;
    } finally {
        isLoading.value = false;
    }
};

// Filter books to only show those updated within the last 30 days
const filteredRecentBooks = computed(() => {
    const today = new Date();
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(today.getDate() - 30);

    return recentBooks.value.filter((book) => {
        const bookDate = new Date(book.updated_at);
        return bookDate >= thirtyDaysAgo;
    });
});

// Scroll handling with improved performance
let lastScrollY = 0;
let scrollTimeout: number | null = null;
let resizeTimeout: number | null = null;
const scrollThreshold = 50;
const debounceTime = 50;

const handleScroll = () => {
    if (scrollTimeout) {
        window.clearTimeout(scrollTimeout);
    }

    scrollTimeout = window.setTimeout(() => {
        const currentScrollY = window.scrollY;
        const isScrollingDownwards = currentScrollY > lastScrollY;
        const isPastThreshold = currentScrollY > scrollThreshold;

        // Only hide when scrolling down past threshold
        isScrollingDown.value = isScrollingDownwards && isPastThreshold;

        lastScrollY = currentScrollY;
    }, debounceTime);
};

const handleResize = () => {
    if (resizeTimeout) {
        window.clearTimeout(resizeTimeout);
    }

    resizeTimeout = window.setTimeout(() => {
        isMobile.value = window.innerWidth < 640;
    }, debounceTime);
};

onMounted(async () => {
    // Fetch recent books from the API
    await fetchRecentBooks();

    if (showBar.value) {
        window.addEventListener('scroll', handleScroll, { passive: true });
        window.addEventListener('resize', handleResize, { passive: true });
        handleResize(); // Initial check
    }
});

onUnmounted(() => {
    window.removeEventListener('scroll', handleScroll);
    window.removeEventListener('resize', handleResize);
    if (scrollTimeout) {
        window.clearTimeout(scrollTimeout);
    }
    if (resizeTimeout) {
        window.clearTimeout(resizeTimeout);
    }
});
</script>

<style scoped>
.bg-notify-bar {
    background-color: var(--theme-500);
}
</style>
