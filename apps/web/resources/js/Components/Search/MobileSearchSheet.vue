<template>
    <Sheet :is-open="isOpen" :title="'Bibel Suche'" position="bottom" @close="closeSearch">
        <div class="h-[40vh] p-4">
            <BibleSearch class="in-mobile-search" @search="handleSearch" />
        </div>
    </Sheet>
</template>

<script setup lang="ts">
import Sheet from '@/Components/common/Sheet.vue';
import BibleSearch from './BibleSearch.vue';

defineProps<{
    isOpen: boolean;
}>();

const emit = defineEmits<{
    (e: 'close'): void;
    (e: 'search'): void;
}>();

const closeSearch = () => {
    emit('close');
};

const handleSearch = () => {
    emit('search');
    closeSearch();
};
</script>
