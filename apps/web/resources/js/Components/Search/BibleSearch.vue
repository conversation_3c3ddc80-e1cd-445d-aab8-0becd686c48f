<template>
    <div class="search-container relative z-[1] overflow-visible">
        <div class="absolute inset-y-0 left-0 flex items-center pl-2">
            <BibleBookSelector
                v-model="selectedBook"
                :in-search-component="true"
                @book-select="handleBookSelect"
            />
        </div>
        <form @submit.prevent="handleSubmit">
            <TextInput
                v-model="searchQuery"
                type="text"
                :placeholder="placeholder"
                :aria-label="ariaLabel"
                class="dark:bg-theme-700 text-theme-900 dark:text-theme-100 dark:placeholder:text-theme-400 bg-theme/90 border-theme-300/50 focus:border-theme-200 focus:ring-theme-400 w-full rounded-lg py-3 pr-24 pl-12 placeholder-gray-500 transition-all duration-200 focus:ring-2 dark:ring-gray-600"
                :class="{
                    'border-red-500 focus:border-red-500 focus:ring-red-500':
                        error,
                }"
                @input="handleInput"
                @keydown.down="handleArrowDown"
                @keydown.up="handleArrowUp"
                @keydown.esc="closeSuggestions"
                @blur="handleBlur"
            />

            <Icon
                v-if="searchQuery"
                name="X"
                class="absolute top-1/2 right-10 h-5 w-5 -translate-y-1/2 transform cursor-pointer"
                :aria-label="'Clear search'"
                @click="searchQuery = ''"
            />
        </form>
        <!-- Typeahead Suggestions -->
        <div
            v-show="showSuggestions && suggestions.length > 0"
            class="hide-scrollbar dark:bg-theme-800 bg-theme border-theme-300 dark:border-theme-600 absolute top-full right-0 left-0 z-50 mt-1 max-h-60 overflow-y-auto rounded-lg border shadow-lg"
        >
            <div
                v-for="(suggestion, index) in suggestions"
                :key="index"
                :class="[
                    'hover:bg-theme-100 dark:hover:bg-theme-700 cursor-pointer px-4 py-2',
                    {
                        'bg-theme-100 dark:bg-theme-700':
                            index === selectedIndex,
                    },
                ]"
                class="text-theme-900 dark:text-theme-100"
                @mousedown.prevent="selectSuggestion(suggestion)"
            >
                <div class="flex flex-col">
                    <span class="font-medium">{{ suggestion.display }}</span>
                    <span
                        v-if="suggestion.isReference"
                        class="text-theme-600 dark:text-theme-400 text-sm"
                    >
                        {{ suggestion.versePreview }}
                    </span>
                    <!-- Highlighted content preview for content suggestions -->
                    <span
                        v-if="suggestion._group === 'content' && suggestion.content"
                        class="text-theme-400 dark:text-theme-300 text-xs mt-1 line-clamp-1"
                        aria-label="Gefundener Textausschnitt"
                        v-html="highlightMatch(suggestion.content, searchQuery)"
                    ></span>
                </div>
            </div>
        </div>
        <div class="absolute inset-y-0 right-0 flex items-center pr-2">
            <SearchTypeDropdown />
        </div>
        <InputError :message="error" />
    </div>
</template>

<script setup lang="ts">
const emit = defineEmits<{
    (e: 'search'): void;
}>();

import { useNavigationStore } from '@/stores/navigationStore';
import InputError from '@/Components/common/InputError.vue';
import TextInput from '@/Components/common/TextInput.vue';
import { Icon } from '@/Components/Icons';
import BibleBookSelector from '@/Components/Navigation/BibleBookSelector.vue';
import { useDebounceFn } from '@vueuse/core';
import { useSearchSettingsStore } from '@/stores/searchSettingsStore';
import { useSearchStore } from '@/stores/searchStore';
import { BOOK_SLUGS, CHAPTERS_COUNT } from '@/utils/bibleData';
import { parse } from '@/utils/bibleNavigationUtils';
import { logger } from '@/utils/logger';
import type { BookView, Suggestion } from '@esbo/types';

// Locally extend Suggestion to include _group for typeahead grouping
export type GroupedSuggestion = Suggestion & {
  _group: 'content' | 'reference';
  content?: string;
};
import { router } from '@inertiajs/vue3';
import axios from 'axios';
import { computed, onMounted, ref } from 'vue';
import SearchTypeDropdown from './SearchTypeDropdown.vue';

/**
 * Highlights all matches of the query in the content string by wrapping them in <mark> tags.
 * Used for previewing matched content in typeahead suggestions.
 * Escapes regex characters in the query for safety.
 */
function highlightMatch(content: string, query: string): string {
    if (!query) return content;
    // Escape regex special characters in the query
    const escaped = query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    try {
        const regex = new RegExp(escaped, 'gi');
        // Replace all matches with <mark>wrapped</mark>
        return content.replace(regex, (match) => `<mark class='bg-yellow-200 dark:bg-yellow-600 rounded px-0.5'>${match}</mark>`);
    } catch {
        return content;
    }
}

type RouteParams = {
    [key: string]: string | number;
};

declare function route(name: string, params?: RouteParams): string;

const searchStore = useSearchStore();
const navigationStore = useNavigationStore(); // Pinia store for navigation aside

withDefaults(
    defineProps<{
        placeholder?: string;
        ariaLabel?: string;
    }>(),
    {
        placeholder: 'Bibelstelle oder Suchbegriff ...',
        ariaLabel: 'Bibelsuche',
    },
);

const searchQuery = computed({
    get: () => searchStore.query,
    set: (value: string) => searchStore.setQuery(value),
});

const selectedBook = ref<BookView | undefined>(undefined);
const error = ref('');
const suggestions = ref<GroupedSuggestion[]>([]);
const showSuggestions = ref(false);
const selectedIndex = ref(-1);
const minCharsForTypeahead = 2;

/**
 * Handles book selection from the BibleBookSelector.
 * Opens the navigation aside and navigates to chapter if provided.
 */
const handleBookSelect = (book: BookView, chapter?: number) => {
    selectedBook.value = book;
    navigationStore.openNavigationAside(); // Open aside when book is selected
    if (chapter) {
        router.visit(
            route('books.show', { reference: `${book.slug}${chapter}` }),
        );
    }
};

const handleSubmit = (e: Event) => {
    e.preventDefault();
    const query = searchQuery.value.trim();
    const reference = parse(query);

    // 1. Prefer robust reference navigation if input is a valid reference
    if (reference && reference.reference && reference.reference.book) {
        const bookKey = reference.reference.book;
        const bookSlug = BOOK_SLUGS[bookKey] || bookKey;
        let refUrl = bookSlug;
        if (reference.reference.chapter) {
            refUrl += reference.reference.chapter;
            if (reference.reference.verseStart) {
                refUrl += ',' + reference.reference.verseStart;
            }
        } else {
            refUrl += '1'; // Default to chapter 1 if only book is present
        }
        router.visit(route('books.show', { reference: refUrl }), {
            preserveState: false,
            preserveScroll: false,
        });
        return;
    }

    // 2. If the top suggestion is a reference/book, treat as navigation
    if (
        suggestions.value.length > 0 &&
        suggestions.value[0]._group === 'reference'
    ) {
        selectSuggestion(suggestions.value[0]);
        return;
    }

    // 3. If a suggestion is actively selected, use it
    if (
        showSuggestions.value &&
        suggestions.value.length > 0 &&
        selectedIndex.value >= 0
    ) {
        selectSuggestion(suggestions.value[selectedIndex.value]);
        return;
    }

    // 4. Otherwise, execute a full search
    search();
};

const search = () => {
    if (!searchQuery.value && !selectedBook.value) {
        error.value =
            'Bitte gib einen Suchbegriff ein oder wähle ein Bibelbuch aus.';
        return;
    }

    // Emit search event to notify parent components
    emit('search');

    error.value = '';
    const query = searchQuery.value.trim();

    // If we have a selected book from typeahead, go to Display.vue
    console.log('selectedBook.value', selectedBook.value)
    if (selectedBook.value) {
        router.visit(
            route('books.show', { reference: `${selectedBook.value.slug}1` }),
        );
        return;
    }
    console.log('query', query)

    // Try to parse as a reference first
    const reference = parse(query);
    console.log('reference', reference)

    // Check if it's a valid reference with a known book
    if (reference.reference.book) {
        if (!CHAPTERS_COUNT[reference.reference.book]) {
            error.value =
                'Bitte gib einen Suchbegriff ein oder wähle ein Bibelbuch aus.';
            return;
        }

        // If we have a chapter, validate it
        if (reference.reference.chapter) {
            if (
                reference.reference.chapter <=
                CHAPTERS_COUNT[reference.reference.book]
            ) {
                // Navigate to the reference using named route
                const bookSlug =
                    BOOK_SLUGS[reference.reference.book] ||
                    reference.reference.book;

                // Use router.visit with preserveState: false to ensure fresh data
                router.visit(
                    route('books.show', {
                        reference: `${bookSlug}${reference.reference.chapter}`,
                    }),
                    {
                        preserveState: false,
                        preserveScroll: false,
                    },
                );
                return;
            }
        } else {
            // If we only have a book, go to chapter 1
            const bookSlug =
                BOOK_SLUGS[reference.reference.book] ||
                reference.reference.book;

            // Use router.visit with preserveState: false to ensure fresh data
            router.visit(route('books.show', { reference: `${bookSlug}1` }), {
                preserveState: false,
                preserveScroll: false,
            });
            return;
        }
    }

    // If not a valid reference and query is not empty, perform a search
    if (query && query.trim() !== '') {
        try {
            const url = route('search.query', { query });
            const searchSettingsStore = useSearchSettingsStore();

            // Use router.visit for a full reload, no preserveState/Scroll, no 'only'
            router.visit(
                url,
                {
                    method: 'post',
                    data: {
                        types: searchSettingsStore.types,
                    },
                    preserveState: false,
                    preserveScroll: false,
                }
            );
        } catch (e) {
            logger.error('Error during search:', e);
            error.value = 'Ein Fehler ist während der Suche aufgetreten.';
        }
    }
};

// --- Enhanced typeahead: merge book/reference and content suggestions ---
const debouncedGetSuggestions = useDebounceFn(async (input: string) => {
    if (!input || input.length < minCharsForTypeahead) {
        suggestions.value = [];
        showSuggestions.value = false;
        return;
    }

    try {
        // Fetch book/reference suggestions
        const [bookResp, contentResp] = await Promise.all([
            axios.get(route('api.search.books'), { params: { q: input } }),
            axios.get(route('api.search'), {
                params: {
                    query: input,
                    types: ['verse', 'footnote', 'word'],
                    page: 1,
                },
            }),
        ]);

        // Book/reference suggestions
        const bookSuggestions = (bookResp.data as Suggestion[]).map((s) => ({
            ...s,
            _group: 'reference' as const,
        }));

        // --- Sort reference/book suggestions by best match ---
        function scoreBookSuggestion(s: Suggestion & { _group: 'reference' }, q: string): number {
            if (!s.display || !q) return 1000;
            const normalizedDisplay = s.display.replace(/\W/g, '').toLowerCase();
            const normalizedQ = q.replace(/\W/g, '').toLowerCase();
            if (normalizedDisplay === normalizedQ) return 0; // exact
            if (normalizedDisplay.startsWith(normalizedQ)) return 1; // prefix
            if (normalizedDisplay.includes(normalizedQ)) return 2; // substring
            return 10;
        }
        const sortedBookSuggestions = [...bookSuggestions].sort((a, b) =>
            scoreBookSuggestion(a, input) - scoreBookSuggestion(b, input)
        );

        // Define a type for content API results
        type ContentApiResult = {
            title?: string;
            content?: string;
            url?: string;
            type?: string;
            subtitle?: string;
        };
        // Content suggestions (verses/footnotes/words)
        const contentSuggestions = ((contentResp.data.data || []) as ContentApiResult[]).map((item) => ({
            display: item.title || item.content?.slice(0, 80) || input,
            content: item.content,
            url: item.url,
            type: item.type,
            isReference: false,
            versePreview: item.subtitle || '',
            _group: 'content' as const,
        }));
        // Merge: references first (sorted), then content
        suggestions.value = [
            ...sortedBookSuggestions,
            ...contentSuggestions,
        ];
        showSuggestions.value = suggestions.value.length > 0;
    } catch (error) {
        console.error('Error fetching suggestions:', error);
        suggestions.value = [];
        showSuggestions.value = false;
    }
}, 100);

const handleInput = async (event: Event) => {
    const input = (event.target as HTMLInputElement).value;
    searchQuery.value = input;
    selectedIndex.value = -1;
    await debouncedGetSuggestions(input);
};

const selectSuggestion = (suggestion: Suggestion) => {
    if (!suggestion) {
        closeSuggestions();
        return;
    }

    // Handle reference suggestion
    if (suggestion.isReference && suggestion.book) {
        const slug = suggestion.book.slug;
        const chapter = suggestion.reference?.chapter;
        const verse = suggestion.reference?.verseStart;
        let url = slug;
        if (chapter && verse) {
             url += chapter + ',' + verse;
        } else if (chapter && !verse) {
             url += chapter;
        }

        router.visit(route('books.show', { reference: url }), {
            preserveState: false,
            preserveScroll: false,
        });
        closeSuggestions();
        return;
    }

    // Handle book suggestion
    if (suggestion.book) {
        console.log('suggestion.book', suggestion.book)
        selectedBook.value = suggestion.book;
        searchQuery.value = suggestion.display || suggestion.book.name;

        // Emit event to parent if needed
        router.visit(route('books.show', { reference: suggestion.book.slug + '1' }), {
            preserveState: false,
            preserveScroll: false,
        });
    }

    closeSuggestions();
};

const handleArrowDown = (e: KeyboardEvent) => {
    e.preventDefault();
    selectedIndex.value =
        selectedIndex.value < suggestions.value.length - 1
            ? selectedIndex.value + 1
            : 0;
};

const handleArrowUp = (e: KeyboardEvent) => {
    e.preventDefault();
    selectedIndex.value =
        selectedIndex.value > 0
            ? selectedIndex.value - 1
            : suggestions.value.length - 1;
};

const handleBlur = () => {
    // Delay closing to allow click events on suggestions
    setTimeout(() => {
        closeSuggestions();
    }, 200);
};

const closeSuggestions = () => {
    showSuggestions.value = false;
    selectedIndex.value = -1;
};

// Close suggestions when clicking outside
onMounted(() => {
    document.addEventListener('click', (e) => {
        if (!(e.target as HTMLElement).closest('.search-container')) {
            closeSuggestions();
        }
    });
});
</script>

<style>
.hide-scrollbar {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

.hide-scrollbar::-webkit-scrollbar {
    display: none; /* Chrome, Safari and Opera */
}
</style>
