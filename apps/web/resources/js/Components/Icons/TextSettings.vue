<!-- TextSettings.vue -->
<template>
  <svg
    :width="size"
    :height="size"
    :aria-label="ariaLabel"
    role="img"
    viewBox="0 0 600 600"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill="currentColor"
      fill-rule="evenodd"
      stroke="none"
      d="M 101 538.361206 C 101 489.015472 101.315208 485.229218 103.279457 484.475464 C 104.533157 483.994354 188.782425 483.600769 290.5 483.600769 C 392.21759 483.600769 476.466827 483.994354 477.720551 484.475464 C 479.684753 485.229218 480 489.015472 480 511.855713 L 480 538.361206 L 290.5 538.361206 L 101 538.361206 Z"
    />
    <path
      fill="currentColor"
      fill-rule="evenodd"
      stroke="none"
      d="M 195.239563 377.691772 L 258.426056 376.774933 L 258.857941 249.980713 L 259.289886 123.186493 L 211.257446 123.186493 L 163.225067 123.186493 L 163.225067 154.35849 L 163.225067 185.530487 L 132.05307 185.530487 L 100.881065 185.530487 L 100.881065 133.691284 C 100.881065 86.453247 101.158279 81.330994 104.004089 75.980896 C 105.721764 72.751892 110.336517 67.835144 114.259087 65.054932 C 121.391045 60 121.391045 60 290.287506 60 C 459.183929 60 459.183929 60 466.188751 64.635498 C 470.041382 67.18512 474.725006 72.27301 476.596771 75.942017 C 479.791168 82.203613 480 85.770325 480 134.071716 L 480 185.530487 L 448.828003 185.530487 L 417.656006 185.530487 L 417.656006 154.35849 L 417.656006 123.186493 L 369.623627 123.186493 L 321.591187 123.186493 L 322.023132 249.980713 L 322.455017 376.774933 L 354.048279 377.233337 L 385.64151 377.691772 L 385.64151 408.646301 C 385.64151 435.31842 385.272766 439.742371 382.976227 440.623627 C 381.510315 441.186157 339.617706 441.586548 289.88147 441.513367 C 240.145264 441.440186 198.504196 440.997925 197.345779 440.530457 C 195.767914 439.893799 195.239563 431.905151 195.239563 408.686157 L 195.239563 377.691772 Z"
    />
  </svg>
</template>

<script setup lang="ts">
interface Props {
  size?: number | string
  ariaLabel?: string
}

withDefaults(defineProps<Props>(), {
  size: 24,
  ariaLabel: 'Text settings'
})
</script>
