import {
    Alert<PERSON><PERSON>gle,
    ArrowLeft,
    ArrowRight,
    Bookmark,
    BookOpen,
    CaseSensitive,
    ChevronDown,
    ChevronRight,
    ChevronUp,
    FolderSymlink,
    LetterText,
    PaintBucket,
    Search,
    Settings2,
    Sun,
    Trash,
    X,
    Megaphone,
    Check,
    type LucideIcon,
} from 'lucide-vue-next';
import TextSettings from './TextSettings.vue';

// Custom icons
export const CustomIcons = {
    TextSettings,
} as const;

// Lucide icons
export const LucideIconRegistry = {
    Search,
    X,
    ChevronDown,
    ChevronUp,
    ChevronRight,
    BookOpen,
    AlertTriangle,
    Bookmark,
    ArrowLeft,
    ArrowRight,
    Sun,
    LetterText,
    CaseSensitive,
    PaintBucket,
    Settings2,
    FolderSymlink,
    Trash,
    Megaphone,
    Check,
} as const;

// Combined icon types
export type IconName =
    | keyof typeof CustomIcons
    | keyof typeof LucideIconRegistry;

// Get icon component by name
export const getIcon = (name: IconName): LucideIcon | typeof TextSettings => {
    return (
        CustomIcons[name as keyof typeof CustomIcons] ||
        LucideIconRegistry[name as keyof typeof LucideIconRegistry]
    );
};
