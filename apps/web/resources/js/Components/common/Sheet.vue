<template>
    <aside class="">
        <!-- Backdrop -->
        <div
            v-show="isOpen"
            class="bg-theme-900 dark:bg-theme-600 fixed inset-0 z-50 opacity-30 transition-opacity duration-300 ease-in-out"
            @click="onBackdropClick"
        ></div>

        <!-- Sheet Content -->
        <transition
            :enter-active-class="transitionClasses.enter"
            :enter-from-class="transitionClasses.enterFrom"
            :enter-to-class="transitionClasses.enterTo"
            :leave-active-class="transitionClasses.leave"
            :leave-from-class="transitionClasses.leaveFrom"
            :leave-to-class="transitionClasses.leaveTo"
        >
            <div
                v-show="isOpen"
                ref="sheetContainerRef"
                :class="[
                    'dark:bg-theme-800 bg-theme fixed z-100 shadow-xl',
                    positionClasses,
                    $attrs.class // <-- forward any classes passed to <Sheet>
                ]"
                :style="dragStyle"
                @click.stop
            >
              <!-- Drag handle for bottom sheets -->
              <SheetDragHandle
                v-if="draggable"
                :currentHeight="sheetHeightVh"
                :minHeight="MIN_HEIGHT_VH"
                :maxHeight="MAX_HEIGHT_VH"
                @dragstart="onDragStart"
                @keydown="onHandleKeydown"
              />

                <!-- Sheet header: always two rows (slot + shortcuts), shortcuts row hidden if showShortcuts is false -->
                <header
                    v-if="showHeader"
                    :class="['dark:bg-theme-800 bg-theme dark:border-theme-700 flex items-center justify-between rounded-t-xl p-2 ml-2 mr-2 border-b border-theme-400',
                                headerClass,
                                { 'sticky top-0 z-10': position !== 'bottom' }]"
                    >
                    <!-- First row: slot/header content -->
                    <div class="flex justify-between w-full">
                        <slot name="header">
                            <h4
                                id="sheet-title"
                                class="text-theme-900 dark:text-theme-100 text-lg font-medium"
                            >
                                {{ title }}
                            </h4>
                            <button
                                class="hover:bg-theme-100 dark:hover:bg-theme-700 text-theme-400 hover:text-theme-500 dark:hover:text-theme-300 rounded-lg p-2 focus:ring-2 focus:ring-blue-500 focus:outline-hidden focus:ring-inset"
                                aria-label="Schließen"
                                type="button"
                                @click="$emit('close')"
                            >
                                <span class="sr-only">Schließen</span>
                                <Icon name="X" class="h-5 w-5" :aria-label="'Close'" />
                            </button>
                        </slot>
                    </div>
                    <!-- Second row: shortcuts, only if showShortcuts -->
                    <div v-if="showShortcuts" class="hidden flex-wrap items-center gap-4 bg-theme-600 dark:bg-theme-900 text-theme-200 dark:text-theme-100 rounded-t-xl px-4 py-2 mb-2 text-sm font-medium border-b border-theme-600 dark:border-theme-800" role="note" aria-live="polite">
                        <span class="flex items-center gap-1">
                            <kbd class="keycap" aria-label="Pfeil nach oben">↑</kbd>
                            <kbd class="keycap" aria-label="Pfeil nach unten">↓</kbd>
                            <span class="ml-1">Höhe anpassen</span>
                        </span>
                        <span class="flex items-center gap-1">
                            <kbd class="keycap" aria-label="Eingabetaste">⏎</kbd>
                            <kbd class="keycap" aria-label="Leertaste">␣</kbd>
                            <span class="ml-1">Schließen</span>
                        </span>
                    </div>
                </header>

                <!-- Content -->
                <div
                    :class="[
                        'scrollbar-hide flex flex-col',
                        { 'h-[calc(100vh-73px)]': position !== 'bottom' },
                        { 'max-h-[70vh]': position === 'bottom' },
                    ]"
                >
                    <slot></slot>
                </div>
            </div>
        </transition>
    </aside>
</template>

<script setup lang="ts">
import { Icon } from '@/Components/Icons';
import { computed, watch, ref, onMounted } from 'vue';
import { useSheetKeySlider } from '@/composables/useKeySliderHandler';
import { useSheetDrag } from '@/composables/useSheetDrag';
import SheetDragHandle from './SheetDragHandle.vue';

defineOptions({ inheritAttrs: false });

// --- Prevent immediate close on mobile: ignore first backdrop click after open ---
const ignoreBackdropClickUntil = ref(0);

// --- Drag-to-close logic for bottom sheet ---
const handleRef = ref<HTMLElement | null>(null);

const props = withDefaults(defineProps<{
    isOpen: boolean;
    position?: 'left' | 'right' | 'bottom';
    title?: string;
    hideHeader?: boolean;
    headerClass?: string;
    draggable?: boolean;
    showShortcuts?: boolean;
}>(), {
    showShortcuts: true
});

// Attach ARIA slider keyboard handling
onMounted(() => {
  if (props.draggable && props.position === 'bottom') {
    useSheetKeySlider(handleRef, {
      min: MIN_HEIGHT_VH,
      max: MAX_HEIGHT_VH,
      step: 5,
      value: sheetHeightVh,
      onAdjust: adjustHeight,
      onClose: () => emit('close'),
    });
  }
});
const sheetContainerRef = ref<HTMLElement | null>(null);

function onBackdropClick(_e: MouseEvent) {
    if (Date.now() < ignoreBackdropClickUntil.value) {
        return;
    }
    emit('close');
}

const emit = defineEmits<{ (e: 'close'): void }>();


const showShortcuts = computed(() => props.showShortcuts !== false);


// Default values
const position = computed(() => props.position || 'left');
const showHeader = computed(() => props.hideHeader !== true);

// Drag/slider logic (bottom sheet)
const MIN_HEIGHT_VH = 5;
const MAX_HEIGHT_VH = 50;
const INITIAL_HEIGHT_VH = 40;

const {
  sheetHeightVh,
  dragStyle,
  onDragStart,
  onHandleKeydown,
  adjustHeight
} = useSheetDrag({
  initialHeight: INITIAL_HEIGHT_VH,
  minHeight: MIN_HEIGHT_VH,
  maxHeight: MAX_HEIGHT_VH,
  onClose: () => emit('close')
});

// Position-specific classes
const positionClasses = computed(() => {
    switch (position.value) {
        case 'left':
            return 'inset-0 w-full md:w-96 md:right-auto';
        case 'right':
            return 'top-0 right-0 h-screen w-96';
        case 'bottom':
            return 'bottom-0 left-0 right-0 rounded-t-xl';
        default:
            return 'inset-0 w-full md:w-96 md:right-auto';
    }
});

// Transition classes based on position
const transitionClasses = computed(() => {
    switch (position.value) {
        case 'left':
            return {
                enter: 'transition ease-out duration-300 transform',
                enterFrom: '-translate-x-full',
                enterTo: 'translate-x-0',
                leave: 'transition ease-in duration-300 transform',
                leaveFrom: 'translate-x-0',
                leaveTo: '-translate-x-full',
            };
        case 'right':
            return {
                enter: 'transition ease-out duration-300 transform',
                enterFrom: 'translate-x-full',
                enterTo: 'translate-x-0',
                leave: 'transition ease-in duration-300 transform',
                leaveFrom: 'translate-x-0',
                leaveTo: 'translate-x-full',
            };
        case 'bottom':
            return {
                enter: 'transition ease-out duration-300 transform',
                enterFrom: 'translate-y-full',
                enterTo: 'translate-y-0',
                leave: 'transition ease-in duration-300 transform',
                leaveFrom: 'translate-y-0',
                leaveTo: 'translate-y-full',
            };
        default:
            return {
                enter: 'transition ease-out duration-300 transform',
                enterFrom: '-translate-x-full',
                enterTo: 'translate-x-0',
                leave: 'transition ease-in duration-300 transform',
                leaveFrom: 'translate-x-0',
                leaveTo: '-translate-x-full',
            };
    }
});

// Prevent body scrolling when sheet is open
watch(
    () => props.isOpen,
    (isOpen) => {
        if (isOpen) {
            // Set ignoreBackdropClickUntil to 350ms after opening the sheet
            ignoreBackdropClickUntil.value = Date.now() + 350;
            const scrollY = window.scrollY;
            document.body.style.position = 'fixed';
            document.body.style.top = `-${scrollY}px`;
            document.body.style.width = '100%';
            document.body.style.overflow = 'hidden'; // Prevent background scroll on all browsers
        } else {
            releaseBodyScrollLock();
        }
    },
    { immediate: true },
);

// Always release scroll lock and restore position if unmounted while open
import { onUnmounted } from 'vue';
function releaseBodyScrollLock() {
    const scrollY = document.body.style.top;
    document.body.style.position = '';
    document.body.style.top = '';
    document.body.style.width = '';
    document.body.style.paddingRight = '';
    document.body.style.overflow = '';
    if (scrollY) {
        window.scrollTo(0, parseInt(scrollY || '0') * -1);
    }
}
onUnmounted(() => {
    releaseBodyScrollLock();
});
</script>

<style scoped>
.scrollbar-hide {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}
.drag-handle {
  touch-action: pan-y;
  cursor: grab;
  outline: none;
  -webkit-tap-highlight-color: transparent;
}
.drag-handle:active {
  cursor: grabbing;
}
.drag-handle span {
  pointer-events: none;
}
.keycap {
  display: inline-block;
  min-width: 1.7em;
  padding: 0.18em 0.6em;
  margin: 0 0.1em;
  border-radius: 0.375em;
  background: #444c56;
  color: #fff;
  font-family: 'Menlo', 'Consolas', 'Liberation Mono', monospace;
  font-size: 0.95em;
  font-weight: 500;
  line-height: 1.2;
  box-shadow: 0 1px 0 #222, 0 1.5px 0 #222;
  vertical-align: middle;
  border: none;
  user-select: none;
}
</style>
