<!-- components/LoadingSpinner.vue -->
<template>
    <div
        :class="[
            'flex items-center justify-center gap-2',
            center ? 'absolute inset-0' : 'py-4',
        ]"
    >
        <div
            v-if="variant === 'pulse'"
            class="flex space-x-1"
            role="status"
            aria-label="Loading"
        >
            <div
                v-for="i in 3"
                :key="i"
                :class="[
                    'animate-pulse rounded-full',
                    sizeClasses[size],
                    colorClasses[variant],
                ]"
                :style="{ animationDelay: `${(i - 1) * 0.15}s` }"
            />
        </div>
        <div
            v-else
            :class="[
                'animate-spin rounded-full border-t-2',
                sizeClasses[size],
                colorClasses[variant],
            ]"
            role="status"
            aria-label="Loading"
        >
            <span class="sr-only">Loading...</span>
        </div>
    </div>
</template>

<script setup lang="ts">
import { PropType } from 'vue';

type SpinnerSize = 'sm' | 'md' | 'lg';
type SpinnerVariant = 'primary' | 'secondary' | 'light' | 'pulse';

defineProps({
    size: {
        type: String as PropType<SpinnerSize>,
        default: 'md',
    },
    variant: {
        type: String as PropType<SpinnerVariant>,
        default: 'primary',
    },
    center: {
        type: Boolean,
        default: false,
    },
});

const sizeClasses = {
    sm: 'h-4 w-4 border-2',
    md: 'h-8 w-8 border-2',
    lg: 'h-12 w-12 border-3',
} as const;

const colorClasses = {
    primary:
        'border-blue-600 dark:border-blue-500 bg-blue-600 dark:bg-blue-500',
    secondary:
        'border-theme-600 dark:border-theme-400 bg-theme-600 dark:bg-theme-400',
    light: 'border-white bg-theme',
    pulse: 'bg-theme-400 dark:bg-theme-600',
} as const;
</script>

<style scoped>
.animate-spin {
    animation: spin 1s linear infinite;
}

.animate-pulse {
    animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

@keyframes pulse {
    0%,
    100% {
        opacity: 1;
    }
    50% {
        opacity: 0.3;
    }
}
</style>
