<script setup lang="ts">
withDefaults(
    defineProps<{
        type?: 'button' | 'submit' | 'reset';
    }>(),
    {
        type: 'button',
    },
);
</script>

<template>
    <button
        :type="type"
        class="hover:bg-theme-50 dark:bg-theme-800 dark:hover:bg-theme-700 text-theme-700 dark:text-theme-300 bg-theme border-theme-300 dark:border-theme-500 inline-flex items-center rounded-md border px-4 py-2 text-xs font-semibold tracking-widest uppercase shadow-xs transition duration-150 ease-in-out focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-hidden disabled:opacity-25 dark:focus:ring-offset-gray-800"
    >
        <slot />
    </button>
</template>
