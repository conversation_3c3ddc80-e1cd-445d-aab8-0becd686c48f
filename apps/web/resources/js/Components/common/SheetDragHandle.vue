<template>
  <div
    class="drag-handle z-50 mx-auto mt-2 mb-1 h-6 w-16 flex items-center justify-center rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500 bg-transparent cursor-grab"
    tabindex="0"
    role="slider"
    :aria-valuenow="currentHeight"
    :aria-valuemin="minHeight"
    :aria-valuemax="maxHeight"
    aria-label="Sheet ziehen oder schließen"
    @mousedown="onDragStart"
    @touchstart="onDragStart"
    @keydown="onHandleKeydown"
  >
    <span class="block h-1 w-8 rounded-full bg-theme-200 dark:bg-theme-800" aria-hidden="true"></span>
  </div>
</template>

<script setup lang="ts">
/**
 * SheetDragHandle.vue
 *
 * Accessible drag handle for bottom sheets.
 * Emits drag and keyboard events so parent can adjust height.
 *
 * Props:
 * - currentHeight: number (vh)
 * - minHeight: number (vh)
 * - maxHeight: number (vh)
 *
 * Emits:
 * - dragstart(event: MouseEvent | TouchEvent)
 * - keydown(event: KeyboardEvent)
 */

defineProps<{
  currentHeight: number
  minHeight: number
  maxHeight: number
}>()

const emit = defineEmits<{
  (e: 'dragstart', event: MouseEvent | TouchEvent): void
  (e: 'keydown', event: KeyboardEvent): void
}>()

function onDragStart(e: MouseEvent | TouchEvent) {
  emit('dragstart', e)
}

function onHandleKeydown(e: KeyboardEvent) {
  emit('keydown', e)
}
</script>

<style scoped>
.drag-handle {
  touch-action: pan-y;
  user-select: none;
}
.drag-handle:active {
  cursor: grabbing;
}
</style>
