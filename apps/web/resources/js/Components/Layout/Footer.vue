<template>
    <footer class="bg-theme-50 dark:bg-theme-900 m-2 sm:m-4 rounded-lg">
        <div class="w-full max-w-screen-xl mx-auto p-3 sm:p-4 md:py-8">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <a
                    href="https://ebtc.org/"
                    class="flex items-center justify-center sm:justify-start hover:opacity-90 transition-opacity"
                    aria-label="EBTC Homepage"
                >
                    <img
                        src="/public/images/EBTCLogoFooter.webp"
                        class="h-8 sm:h-10 w-[76px] sm:w-[95px]"
                        alt="EBTC Logo"
                        loading="lazy"
                        width="380"
                        height="160"
                    />
                </a>
                <ul class="flex flex-wrap justify-center sm:justify-end items-center gap-3 sm:gap-4 text-sm font-medium text-theme-700 dark:text-theme-200">
                    <li v-for="(link, index) in links" :key="index">
                        <a
                            :href="link.url"
                            class="hover:text-theme-900 dark:hover:text-theme-50 hover:underline transition-colors"
                            :aria-label="link.label"
                        >
                            {{ link.label }}
                        </a>
                    </li>
                </ul>
            </div>
            <hr class="my-4 sm:my-6 border-theme-200 dark:border-theme-700 lg:my-8" />
            <span class="block text-sm text-theme-700 dark:text-theme-200 text-center">
                Copyright {{ currentYear }} EBTC · Europäisches Bibel Trainings Centrum e. V.
            </span>
        </div>
    </footer>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface FooterLink {
    label: string;
    url: string;
}

const links: FooterLink[] = [
    {
        label: 'Changelog',
        url: '/changelog'
    },
    {
        label: 'Datenschutz',
        url: 'https://www.ebtc.org/datenschutz'
    },
    {
        label: 'Impressum',
        url: 'https://www.ebtc.org/impressum'
    },
    {
        label: 'Kontakt',
        url: 'https://www.ebtc.org/ueber-uns/kontakt'
    },
];

const currentYear = computed(() => new Date().getFullYear());
</script>
