<template>
    <section aria-labelledby="text-display-title">
        <div class="space-y-4">
            <div>
                <div class="mb-2 flex items-center justify-between">
                    <label
                        class="text-theme-700 dark:text-theme-300 text-sm font-medium"
                    >
                        Schriftgröße
                    </label>
                    <span class="text-theme-500 dark:text-theme-400 text-sm">
                        {{ sizeToNumber(fontSize) }}px
                    </span>
                </div>
                <div class="flex items-center justify-center gap-4">
                    <button
                        type="button"
                        class="hover:bg-theme-50 dark:hover:bg-theme-700 text-theme-700 dark:text-theme-300 border-theme-300 dark:border-theme-600 flex h-12 w-12 items-center justify-center rounded-md border text-sm font-medium transition-colors"
                        aria-label="Decrease font size"
                        @click="decreaseFontSize"
                    >
                        <span class="text-s">A</span>
                    </button>
                    <button
                        type="button"
                        class="hover:bg-theme-50 dark:hover:bg-theme-700 text-theme-700 dark:text-theme-300 border-theme-300 dark:border-theme-600 flex h-12 w-12 items-center justify-center rounded-md border text-lg font-medium transition-colors"
                        aria-label="Increase font size"
                        @click="increaseFontSize"
                    >
                        <span class="text-2xl">A</span>
                    </button>
                </div>
            </div>

            <div>
                <label
                    class="text-theme-700 dark:text-theme-300 mb-2 block text-sm font-medium"
                >
                    Textbreite
                </label>
                <div class="flex gap-2">
                    <button
                        v-for="size in marginSizes"
                        :key="size"
                        type="button"
                        class="flex-1 rounded-md px-2 py-1 text-sm font-medium transition-colors duration-200"
                        :class="{
                            'bg-theme-100 dark:bg-theme-700 text-theme-900 dark:text-white':
                                marginSize === size,
                            'hover:bg-theme-50 dark:hover:bg-theme-700 text-theme-500 dark:text-theme-400':
                                marginSize !== size,
                        }"
                        @click="setMarginSize(size)"
                    >
                        {{ marginSizeLabels[size] }}
                    </button>
                </div>
            </div>
        </div>
    </section>
</template>

<script setup lang="ts">
import { useTextSettingsStore } from '@/stores/textSettingsStore';
import type { FontSize, MarginSize } from '@esbo/types';
import { storeToRefs } from 'pinia';

const store = useTextSettingsStore();
const { fontSize, marginSize } = storeToRefs(store);

// TODO: rename this to textWidth
const marginSizes: MarginSize[] = [
    'margin-wide',
    'margin-normal',
    'margin-narrow',
];
const marginSizeLabels: Record<MarginSize, string> = {
    'margin-wide': 'Schmal',
    'margin-normal': 'Normal',
    'margin-narrow': 'Breit',

};

const fontSizeOptions = [
    'xs',
    'sm',
    'base',
    'lg',
    'xl',
    '2xl',
    '3xl',
    '4xl',
] as const satisfies readonly FontSize[];

function sizeToNumber(size: string): number {
    const sizeMap: Record<FontSize, number> = {
        xs: 12,
        sm: 14,
        base: 16,
        lg: 18,
        xl: 20,
        '2xl': 24,
        '3xl': 30,
        '4xl': 36,
    };
    return sizeMap[size as FontSize] || 16;
}

function setFontSize(size: FontSize) {
    store.updateSettings({ fontSize: size });
}

function increaseFontSize() {
    const currentIndex = fontSizeOptions.indexOf(fontSize.value as FontSize);
    if (currentIndex < fontSizeOptions.length - 1) {
        setFontSize(fontSizeOptions[currentIndex + 1] as FontSize);
    }
}

function decreaseFontSize() {
    const currentIndex = fontSizeOptions.indexOf(fontSize.value as FontSize);
    if (currentIndex > 0) {
        setFontSize(fontSizeOptions[currentIndex - 1] as FontSize);
    }
}

function setMarginSize(size: MarginSize) {
    store.setMarginSize(size);
}
</script>
