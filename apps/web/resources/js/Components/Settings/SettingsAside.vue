<template>
    <Sheet
        class="h-screen"
        :is-open="isOpen"
        position="right"
        title="Einstellungen"
        @close="close"
    >
        <div class="space-y-4 p-6 bg-theme-50 dark:bg-theme-700 ">
            <details class="group" :open="openPanel==='text'">
                <summary
                    class="text-theme-900 dark:text-theme-100 focus-visible:ring-theme-500 flex cursor-pointer items-center justify-between rounded-md py-2 text-lg font-medium focus-visible:ring-2 focus-visible:outline-none"
                    @click.prevent.stop="togglePanel('text')"
                >
                    <span class="flex items-center gap-2">
                        <Icon
                            name="CaseSensitive"
                            class="h-5 w-5"
                            :aria-label="'Font settings'"
                        />Textanzeige</span
                    >
                    <Icon
                        name="ChevronUp"
                        class="h-5 w-5 transition-transform group-open:rotate-180"
                        :aria-label="'Font settings'"
                    />
                </summary>
                <div class="mt-2">
                    <TextDisplaySettings />
                </div>
            </details>

            <details class="group" :open="openPanel==='theme'">
                <summary
                    class="text-theme-900 dark:text-theme-100 focus-visible:ring-theme-500 flex cursor-pointer items-center justify-between rounded-md py-2 text-lg font-medium focus-visible:ring-2 focus-visible:outline-none"
                    @click.prevent.stop="togglePanel('theme')"
                >
                    <span class="flex items-center gap-2">
                        <Icon
                            name="PaintBucket"
                            class="h-5 w-5"
                            :aria-label="'Theme settings'"
                        />
                        Farbthema</span
                    >
                    <Icon
                        name="ChevronUp"
                        class="h-5 w-5 transition-transform group-open:rotate-180"
                        :aria-label="'Theme settings'"
                    />
                </summary>
                <div class="mt-2">
                    <ThemeSettings />
                </div>
            </details>

            <details class="group" :open="openPanel==='visibility'">
                <summary
                    class="text-theme-900 dark:text-theme-100 focus-visible:ring-theme-500 flex cursor-pointer items-center justify-between rounded-md py-2 text-lg font-medium focus-visible:ring-2 focus-visible:outline-none"
                    @click.prevent.stop="togglePanel('visibility')"
                >
                    <span class="flex items-center gap-2">
                        <Icon
                            name="LetterText"
                            class="h-5 w-5"
                            :aria-label="'Layout settings'"
                        />Textfluss</span
                    >
                    <Icon
                        name="ChevronUp"
                        class="h-5 w-5 transition-transform group-open:rotate-180"
                        :aria-label="'Layout settings'"
                    />
                </summary>
                <div class="mt-2">
                    <VisibilitySettings />
                </div>
            </details>
        </div>
    </Sheet>
</template>

<script setup lang="ts">
import { Icon } from '@/Components/Icons';
import Sheet from '@/Components/common/Sheet.vue';
import TextDisplaySettings from './TextDisplaySettings.vue';
import ThemeSettings from './ThemeSettings.vue';
import VisibilitySettings from './VisibilitySettings.vue';
import { ref } from 'vue';

defineProps<{
    isOpen: boolean;
}>();

const emit = defineEmits<{
    (e: 'close'): void;
}>();

function close() {
    emit('close');
}

// Accordion open panel state
const openPanel = ref<'text' | 'theme' | 'visibility'>('text');

function togglePanel(panel: 'text' | 'theme' | 'visibility') {
    openPanel.value = openPanel.value !== panel ? panel : 'text';
}
</script>
