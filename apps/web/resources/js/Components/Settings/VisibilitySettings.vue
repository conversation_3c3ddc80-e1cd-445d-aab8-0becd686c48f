<template>
    <section aria-labelledby="visibility-settings-title">
        <div class="space-y-3">
            <div
                v-for="setting in visibilitySettings"
                :key="setting.key"
                class="flex items-center justify-between"
            >
                <label
                    :for="setting.key"
                    class="text-theme-700 dark:text-theme-300 text-sm"
                >
                    {{ setting.label }}
                </label>
                <button
                    :id="setting.key"
                    type="button"
                    role="switch"
                    :aria-checked="setting.value"
                    class="focus:ring-theme-500 relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:ring-2 focus:ring-offset-2 focus:outline-none"
                    :class="[
                        setting.value
                            ? 'bg-theme-600'
                            : 'bg-theme-200 dark:bg-theme-700',
                    ]"
                    @click="toggleSetting(setting.key)"
                >
                    <span
                        class="bg-theme inline-block h-4 w-4 transform rounded-full transition-transform"
                        :class="[
                            setting.value ? 'translate-x-6' : 'translate-x-1',
                        ]"
                    />
                </button>
            </div>
        </div>
    </section>
</template>

<script setup lang="ts">
import { useTextSettingsStore } from '@/stores/textSettingsStore';
import type { TextSettings } from '@esbo/types';
import { storeToRefs } from 'pinia';
import { computed } from 'vue';

const store = useTextSettingsStore();

const {
    showVerseNumbers,
    showChapterNumbers,
    flowText,
    showFootnotes,
    focusedMode,
    useInfiniteScroll,
} = storeToRefs(store);

type TextSettingKey = keyof Pick<
    TextSettings,
    | 'showVerseNumbers'
    | 'showChapterNumbers'
    | 'showBookNames'
    | 'flowText'
    | 'showFootnotes'
    | 'focusedMode'
    | 'useInfiniteScroll'
>;

type VisibilityKey = TextSettingKey;

interface VisibilitySetting {
    key: VisibilityKey;
    label: string;
    value: boolean;
    store: 'text' | 'bible';
}

const visibilitySettings = computed<VisibilitySetting[]>(() => [
    {
        key: 'useInfiniteScroll' as VisibilityKey,
        label: 'Unendliches Scrollen',
        value: useInfiniteScroll.value,
        store: 'bible',
    },
    {
        key: 'showVerseNumbers' as VisibilityKey,
        label: 'Versnummern',
        value: showVerseNumbers.value,
        store: 'text',
    },
    {
        key: 'showChapterNumbers' as VisibilityKey,
        label: 'Kapitelnummern',
        value: showChapterNumbers.value,
        store: 'text',
    },
    {
        key: 'flowText' as VisibilityKey,
        label: 'Fließtext',
        value: flowText.value,
        store: 'text',
    },
    {
        key: 'showFootnotes' as VisibilityKey,
        label: 'Fußnoten',
        value: showFootnotes.value,
        store: 'text',
    },
    {
        key: 'focusedMode' as VisibilityKey,
        label: 'Fokussiertes Lesen',
        value: focusedMode.value,
        store: 'text',
    },
]);

function toggleSetting(setting: VisibilityKey) {
    const settingInfo = visibilitySettings.value.find((s) => s.key === setting);
    if (!settingInfo) return;

    store.updateSettings({
        [setting]: !store.$state[setting as TextSettingKey],
    });
}
</script>
