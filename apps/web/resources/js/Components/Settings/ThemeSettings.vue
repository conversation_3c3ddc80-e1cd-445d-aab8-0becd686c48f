<template>
    <section aria-labelledby="theme-settings-title">
        <div class="space-y-4">
            <div>
                <h4
                    class="text-theme-700 [data-mode='dark']:text-theme-300 mb-2 text-sm font-medium"
                >
                    Erscheinungsbild
                </h4>
                <div class="grid grid-cols-3 gap-2">
                    <button
                        v-for="mode in themeModes"
                        :key="mode.value"
                        type="button"
                        class="flex items-center justify-center rounded-md px-3 py-2 text-sm font-medium transition-colors duration-200"
                        :class="{
                            'bg-theme-100 [data-mode=dark]:bg-theme-700 text-theme-900 [data-mode=dark]:text-white':
                                themeMode === mode.value,
                            'hover:bg-theme-50 [data-mode=dark]:hover:bg-theme-700 text-theme-500 [data-mode=dark]:text-theme-400':
                                themeMode !== mode.value,
                        }"
                        @click="setThemeMode(mode.value)"
                    >
                        {{ mode.label }}
                    </button>
                </div>
            </div>

            <div>
                <h4
                    class="text-theme-700 [data-mode='dark']:text-theme-300 mb-2 text-sm font-medium"
                >
                    Farbschema
                </h4>
                <div class="grid grid-cols-2 gap-2">
                    <button
                        v-for="theme in colorThemes"
                        :key="theme.name"
                        type="button"
                        class="flex items-center gap-2 rounded-md px-3 py-2 text-sm font-medium transition-colors duration-200"
                        :class="{
                            'bg-theme-100 [data-mode=dark]:bg-theme-700 text-theme-900 [data-mode=dark]:text-white':
                                colorTheme === theme.name,
                            'hover:bg-theme-50 [data-mode=dark]:hover:bg-theme-700 text-theme-500 [data-mode=dark]:text-theme-400':
                                colorTheme !== theme.name,
                        }"
                        @click="setColorTheme(theme.name)"
                    >
                        <span
                            class="h-4 w-4 rounded-full"
                            :style="{ backgroundColor: theme.surface }"
                        ></span>
                        {{ theme.label }}
                    </button>
                </div>
            </div>
        </div>
    </section>
</template>

<script setup lang="ts">
import { useTextSettingsStore } from '@/stores/textSettingsStore';
import type { ColorTheme, ThemeMode } from '@esbo/types';
import { storeToRefs } from 'pinia';

const store = useTextSettingsStore();
const { themeMode, colorTheme } = storeToRefs(store);

const themeModes = [
    { value: 'light' as ThemeMode, label: 'Hell' },
    { value: 'dark' as ThemeMode, label: 'Dunkel' },
    { value: 'system' as ThemeMode, label: 'System' },
];

const colorThemes = [
    {
        name: 'default' as ColorTheme,
        label: 'Standard',
        surface: '#fff',
    },
    {
        name: 'papyrus' as ColorTheme,
        label: 'Papyrus',
        surface: 'oklch(0.55 0.08 69)',
    },
    {
        name: 'high-contrast' as ColorTheme,
        label: 'Kontrast',
        surface: 'oklch(0.2 0.15 220)',
    },
];

function setThemeMode(mode: ThemeMode) {
    store.setThemeMode(mode);
}

function setColorTheme(theme: ColorTheme) {
    store.updateSettings({ colorTheme: theme });
}
</script>
