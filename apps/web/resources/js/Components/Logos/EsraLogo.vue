<template>
    <svg
        width="311"
        height="243"
        viewBox="0 0 311 243"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        :class="classes"
    >
        <path
            d="M164.372 50.063L162.737 51.5807L161 52.981C168.409 62.1884 168.315 68.8613 168.221 75.3073C168.213 76.0191 168.197 76.7232 168.197 77.4194V87.988C169.723 88.5591 171.209 89.1302 172.664 89.7091V77.7793C173.439 75.5341 178.829 61.9146 195.859 59.1532C198.558 58.7698 201.437 58.4491 204.558 58.3005V58.3474C204.957 58.3318 205.356 58.3239 205.755 58.3239C219.28 58.3239 230.287 67.9616 230.287 79.7975C230.287 91.6335 219.28 101.271 205.755 101.271H194.279C195.202 101.975 196.101 102.687 196.962 103.43C197.822 104.173 198.636 104.948 199.418 105.73H205.763C219.288 105.73 230.295 115.368 230.295 127.204C230.295 139.04 219.288 148.677 205.763 148.677C205.364 148.677 204.965 148.67 204.566 148.654V148.701C201.437 148.552 198.558 148.231 195.859 147.848C178.821 145.079 173.439 131.452 172.664 129.222V116.103C171.225 115.532 169.739 114.969 168.197 114.406V129.582C168.197 130.278 168.205 130.982 168.221 131.694C168.315 138.14 168.409 144.813 161 154.02L162.737 155.421L164.372 156.938C172.25 148.474 178.657 149.546 189.272 151.329C191.103 151.634 193.051 151.963 195.139 152.26C195.139 152.26 195.139 152.26 195.147 152.26C201.108 153.105 208.156 153.7 216.754 152.847C235.216 151.032 243.117 143.616 249.469 137.663C254.507 132.938 258.488 129.191 266.538 129.425L268.83 129.465V124.88L266.538 124.943C258.473 125.154 254.507 121.431 249.469 116.706C244.54 112.082 238.665 106.583 227.651 103.501C238.665 100.418 244.532 94.919 249.469 90.2958C254.507 85.5708 258.481 81.8471 266.538 82.0583L268.83 82.0975V77.5133L266.538 77.5759C258.481 77.7949 254.507 74.0634 249.469 69.3385C243.117 63.3853 235.216 55.9693 216.754 54.1544C208.156 53.3095 201.108 53.8962 195.147 54.7411C195.147 54.7411 195.147 54.7411 195.139 54.7411C193.058 55.0384 191.11 55.3591 189.28 55.672C178.664 57.4478 172.265 58.5273 164.38 50.063H164.372ZM172.508 68.6344C172.242 65.9355 171.64 63.002 170.325 59.8103C173.329 61.0306 176.341 61.4218 179.478 61.3514C176.466 63.6669 174.198 66.2484 172.516 68.6266L172.508 68.6344ZM170.317 147.191C171.632 144.007 172.234 141.074 172.5 138.375C174.182 140.753 176.458 143.334 179.462 145.65C176.325 145.58 173.314 145.971 170.317 147.191ZM246.41 119.96C249.351 122.713 252.199 125.389 255.821 127.188C252.199 128.987 249.343 131.655 246.41 134.416C241.325 139.188 235.669 144.477 224.326 147.121C230.694 142.357 234.754 135.207 234.754 127.219C234.754 119.232 230.655 112.012 224.24 107.248C235.646 109.884 241.309 115.188 246.41 119.968V119.96ZM246.41 72.5927C249.351 75.3464 252.199 78.0218 255.821 79.821C252.199 81.6203 249.343 84.2878 246.41 87.0493C241.309 91.829 235.638 97.1407 224.24 99.7692C230.663 95.0051 234.754 87.8237 234.754 79.7975C234.754 71.7713 230.694 64.6526 224.326 59.8963C235.669 62.5404 241.325 67.8287 246.41 72.6006V72.5927Z"
            fill="#50433C"
        />
        <path
            d="M123.787 167.499C125.884 169.072 128.129 170.527 130.515 171.856C140.239 177.278 151.472 180 162.338 180C172.688 180 182.693 177.528 190.727 172.568C196.923 168.743 201.569 163.658 204.519 157.627C202.938 157.556 201.335 157.447 199.692 157.29C196.633 162.657 192.331 166.334 188.38 168.774C173.227 178.13 150.322 177.794 132.69 167.961C130.585 166.787 128.606 165.512 126.744 164.128L126.768 164.088C126.627 164.002 126.486 163.908 126.345 163.822C113.829 154.341 107.031 140.229 107.031 123.363H102.564V168.633L106.773 169.666C106.773 169.666 108.736 166.091 112.765 165.011C115.933 164.159 119.641 164.996 123.779 167.491L123.787 167.499ZM107.031 162.923V146.292C109.213 151.415 112.178 156.093 115.878 160.247C114.416 160.193 112.992 160.341 111.615 160.717C109.792 161.21 108.267 162.023 107.031 162.923Z"
            fill="#50433C"
        />
        <path
            d="M142.632 129.582L138.267 128.933C138.079 129.551 133.613 143.436 117.529 147.332C118.366 148.787 119.297 150.195 120.306 151.541C120.728 151.47 121.151 151.4 121.565 151.329C132.173 149.546 138.58 148.474 146.465 156.938L148.1 155.421L149.837 154.02C142.429 144.813 142.523 138.14 142.617 131.694C142.624 130.982 142.64 130.278 142.64 129.582H142.632ZM131.36 145.65C134.371 143.334 136.64 140.753 138.322 138.375C138.588 141.074 139.19 144.007 140.504 147.199C137.5 145.979 134.489 145.587 131.352 145.658L131.36 145.65Z"
            fill="#50433C"
        />
        <path
            d="M61.3693 137.663C67.7214 143.616 75.6224 151.032 94.0843 152.847C95.4611 152.98 96.7988 153.082 98.1052 153.144V147.793C87.959 145.157 80.543 136.928 80.543 127.204C80.543 115.36 91.5497 105.73 105.075 105.73C110.919 105.73 115.863 107.052 122.434 113.623L126.244 112.043V94.9582L122.434 93.378C115.863 99.9492 110.919 101.271 105.075 101.271C91.5497 101.271 80.543 91.6335 80.543 79.7976C80.543 67.9617 91.5497 58.324 105.075 58.324C105.474 58.324 105.873 58.3318 106.272 58.3475V58.3005C109.401 58.4492 112.28 58.7699 114.979 59.1532C133.159 62.1102 138.072 77.4273 138.268 78.0688L142.633 77.4273C142.633 76.7311 142.625 76.027 142.609 75.3151C142.515 68.8691 142.421 62.1963 149.83 52.9888L148.093 51.5885L146.458 50.0709C138.58 58.5352 132.174 57.4635 121.558 55.6799C119.727 55.3748 117.78 55.0462 115.699 54.7568C115.699 54.7568 115.691 54.7568 115.683 54.7568C109.73 53.9119 102.674 53.3174 94.0765 54.1701C75.6146 55.985 67.7136 63.401 61.3615 69.3541C56.3236 74.0791 52.3418 77.8341 44.2921 77.5916L42 77.5525V82.1366L44.2921 82.074C52.3496 81.8472 56.3236 85.5865 61.3615 90.3115C66.2898 94.9347 72.1648 100.434 83.1793 103.516C72.1648 106.599 66.2976 112.098 61.3615 116.721C56.3236 121.446 52.3418 125.17 44.2921 124.959L42 124.92V129.504L44.2921 129.441C52.3418 129.199 56.3236 132.954 61.3615 137.679L61.3693 137.663ZM121.777 99.9648V107.029C119.907 105.542 118.061 104.377 116.152 103.493C118.061 102.609 119.907 101.443 121.777 99.957V99.9648ZM140.513 59.8103C139.198 62.9942 138.596 65.9278 138.33 68.6345C136.648 66.2563 134.38 63.6748 131.368 61.3592C134.505 61.4296 137.517 61.0385 140.52 59.8181L140.513 59.8103ZM64.4202 87.0415C61.4788 84.2879 58.6313 81.6125 55.0093 79.8132C58.6313 78.014 61.4866 75.3464 64.4202 72.585C69.505 67.8131 75.1609 62.5248 86.504 59.8807C80.1362 64.6448 76.0762 71.7949 76.0762 79.782C76.0762 87.769 80.1753 94.9895 86.59 99.7536C75.1844 97.1173 69.5206 91.8134 64.4202 87.0337V87.0415ZM64.4202 119.96C69.5206 115.18 75.1922 109.868 86.59 107.24C80.1675 112.004 76.0762 119.185 76.0762 127.212C76.0762 135.238 80.1362 142.357 86.504 147.113C75.1609 144.469 69.505 139.181 64.4202 134.409C61.4788 131.655 58.6313 128.98 55.0093 127.18C58.6313 125.381 61.4866 122.714 64.4202 119.952V119.96Z"
            fill="#50433C"
        />
        <path
            d="M117.443 50.4933C118.765 44.3133 123.42 35.9507 138.44 32.6182C148.711 30.3418 161.392 31.4839 171.35 35.9351C171.702 36.1307 172.07 36.2949 172.43 36.4357C178.031 39.1346 182.67 42.9209 185.377 47.771L189.523 46.269L186.456 30.1149L182.623 29.0197C182.576 29.0667 178.305 33.5413 174.37 32.3835C163.23 27.0014 148.946 25.7107 137.47 28.2609C123.913 31.2727 115.183 39.0251 113.023 49.7893L116.403 50.3369C116.755 50.3838 117.099 50.4386 117.451 50.4933H117.443ZM181.575 35.3875C181.997 35.1528 182.396 34.9025 182.772 34.6521L183.483 38.4071C182.529 37.5388 181.512 36.7252 180.44 35.9586C180.816 35.7865 181.191 35.5987 181.575 35.3875Z"
            fill="#50433C"
        />
        <path
            d="M156.096 105.456C186.847 115.845 204.808 123.019 203.533 144.148L204.746 144.195C205.121 144.211 205.45 144.218 205.755 144.218C206.561 144.218 207.343 144.172 208.125 144.093C208.266 142.341 208.297 140.55 208.219 138.719C207.601 124.802 203.095 114.664 194.036 106.802C185.384 99.2999 173.189 94.2385 157.527 88.9503C155.118 88.1367 152.662 87.3623 150.276 86.6113C135.154 81.8394 121.715 77.5759 117.96 64.4101C116.81 64.0581 115.581 63.7687 114.275 63.5575C113.868 63.5027 113.477 63.4479 113.086 63.401C114.518 75.1587 118.906 84.2019 126.4 90.8904C134.583 98.1969 145.394 101.842 155.853 105.378L156.104 105.464L156.096 105.456ZM122.419 78.9136C129.436 84.7025 139.269 87.816 148.93 90.8669C151.293 91.6101 153.733 92.3845 156.096 93.1824C178.289 100.677 186.034 105.777 191.103 110.174C195.1 113.647 198.073 117.543 200.138 122.111C191.001 112.536 174.972 107.123 157.52 101.224L157.269 101.138C143.188 96.382 129.639 91.8056 122.411 78.9058L122.419 78.9136Z"
            fill="#50433C"
        />
    </svg>
</template>

<script setup lang="ts">
import { computed } from 'vue';

const props = withDefaults(
    defineProps<{
        class?: string;
    }>(),
    {
        class: '',
    },
);

const classes = computed(() => {
    const defaultClasses = 'h-auto w-70';
    const customClasses = props.class || '';

    // If custom classes include width/height, remove the default ones
    const hasCustomWidth = /\bw-\w+/g.test(customClasses);
    const hasCustomHeight = /\bh-\w+/g.test(customClasses);

    let result = defaultClasses;
    if (hasCustomWidth) result = result.replace(/\bw-\w+\s*/g, '');
    if (hasCustomHeight) result = result.replace(/\bh-\w+\s*/g, '');

    return `${result} ${customClasses}`.trim();
});
</script>
