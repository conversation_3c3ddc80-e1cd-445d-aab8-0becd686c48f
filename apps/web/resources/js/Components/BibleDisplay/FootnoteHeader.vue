<template>
  <div
    class="grid grid-cols-3 justify-between items-center border-theme-600 dark:border-theme-600 text-sm font-medium"
    role="banner"
  >
    <!-- Left: Back button or word -->
    <div class="flex items-center min-h-6">
      <button
        v-if="canGoBack"
        aria-label="Zurück"
        class="text-theme-300 hover:text-theme-100 dark:text-theme-400 dark:hover:text-theme-100 flex h-6 w-6 items-center justify-center rounded-full focus:outline-none focus-visible:ring-2 focus-visible:ring-theme-500"
        type="button"
        @click="goBack"
      >
        <Icon name="ArrowLeft" class="h-5 w-5" />
      </button>
      <span
        v-else
        class="text-theme-100 dark:text-theme-100 font-medium whitespace-nowrap"
      >
        »{{ currentFootnote?.word }}«
      </span>
    </div>

    <!-- Center: Reference (or word+reference if canGoBack) -->
    <h3
      id="footnote-title"
      class="flex flex-col items-center justify-center text-center min-h-6 w-full"
    >
        <span class="text-theme-400 dark:text-theme-400">{{ currentFootnote?.reference }}</span>
    </h3>

    <!-- Right: Close button -->
    <div class="flex justify-end min-h-6">
      <button
        class="text-theme-300 hover:text-theme-100 dark:text-theme-400 dark:hover:text-theme-100 flex h-6 w-6 items-center justify-center rounded-full focus:outline-none focus-visible:ring-2 focus-visible:ring-theme-500"
        type="button"
        aria-label="Schließe Fußnote"
        @click="onClose"
      >
        <Icon name="X" class="h-5 w-5" />
      </button>
    </div>
  </div>
</template>
<script setup lang="ts">
import type { FootnoteState } from '@esbo/types';
import Icon from '@/Components/Icons/Icon.vue';

defineProps<{
    canGoBack: boolean;
    currentFootnote: FootnoteState | null;
    goBack: () => void;
    onClose: () => void;
}>();
</script>
