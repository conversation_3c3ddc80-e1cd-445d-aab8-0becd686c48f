<script lang="ts" setup>
import ChapterNumber from './ChapterNumber.vue';
import VerseContent from './VerseContent.vue';
import type { Verse, ChapterSection, TextSettings, FootnoteClickEvent, FootnoteHoverEvent } from '@esbo/types';

import { computed } from 'vue';

const props = defineProps<{
  verse: Verse | null;
  verseNumber: number;
  groupIndex: number;
  showChapterNumber: boolean;
  chapter: ChapterSection;
  categoryColors: string;
  textSettings: TextSettings;
}>();

const verseId = computed(() => `${props.chapter.book?.slug ?? 'unknown'}.${props.chapter.number}.${props.verseNumber}`);

const emit = defineEmits<{
  (e: 'footnote-click', payload: FootnoteClickEvent): void;
  (e: 'footnote-hover', payload: FootnoteHoverEvent): void;
}>();

function handleFootnoteClick(v: FootnoteClickEvent) {
  emit('footnote-click', v);
}
function handleFootnoteHover(v: FootnoteHoverEvent) {
  emit('footnote-hover', v);
}
</script>

<template>
  <div
    :id="verseId"
    class="verse-unit leading-10 xs:leading-5"
    :data-verse="verseId"
    :data-reference="`${props.chapter.book?.slug ?? 'unknown'} ${props.chapter.number},${props.verseNumber}`"
    :data-category="props.chapter.book?.category ?? ''"
    role="group"
    :aria-label="`Vers ${props.verseNumber}`"
  >
    <ChapterNumber
      v-if="props.showChapterNumber"
      :class="props.categoryColors"
      :number="props.chapter.number"
    />
    <VerseContent
      v-if="props.verse"
      :verse="props.verse"
      :text-settings="props.textSettings"
      @footnote-click="handleFootnoteClick"
      @footnote-hover="handleFootnoteHover"
    />
  </div>
</template>
