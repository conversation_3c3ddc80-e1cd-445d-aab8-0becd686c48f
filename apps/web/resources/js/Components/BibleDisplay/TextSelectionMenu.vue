<template>
    <div
        v-if="isVisible"
        ref="menuRef"
        class="text-selection-menu dark:bg-theme-800 border-theme-200 dark:border-theme-700 absolute z-50 rounded-md border bg-white py-1 shadow-lg"
        :style="positionStyle"
        role="menu"
        aria-label="Text selection options"
    >
        <button
            class="hover:bg-theme-100 dark:hover:bg-theme-700 text-theme-800 dark:text-theme-200 flex w-full items-center gap-2 px-4 py-2 text-left"
            role="menuitem"
            @click="copyText(false)"
        >
            <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4"
                viewBox="0 0 20 20"
                fill="currentColor"
            >
                <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" />
                <path
                    d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z"
                />
            </svg>
            <PERSON><PERSON><PERSON>
        </button>
        <button
            class="hover:bg-theme-100 dark:hover:bg-theme-700 text-theme-800 dark:text-theme-200 flex w-full items-center gap-2 px-4 py-2 text-left"
            role="menuitem"
            @click="copyText(true)"
        >
            <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4"
                viewBox="0 0 20 20"
                fill="currentColor"
            >
                <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" />
                <path
                    fill-rule="evenodd"
                    d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z"
                    clip-rule="evenodd"
                />
            </svg>
            Kopieren mit Versnummern
        </button>
        <button
            class="hover:bg-theme-100 dark:hover:bg-theme-700 text-theme-800 dark:text-theme-200 flex w-full items-center gap-2 px-4 py-2 text-left"
            role="menuitem"
            @click="shareText"
        >
            <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4"
                viewBox="0 0 20 20"
                fill="currentColor"
            >
                <path
                    d="M15 8a3 3 0 10-2.977-2.63l-4.94 2.47a3 3 0 100 4.319l4.94 2.47a3 3 0 10.895-1.789l-4.94-2.47a3.027 3.027 0 000-.74l4.94-2.47C13.456 7.68 14.19 8 15 8z"
                />
            </svg>
            Teilen
        </button>
    </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref } from 'vue';

interface Props {
    isVisible: boolean;
    position: { x: number; y: number };
}

const props = defineProps<Props>();

const emit = defineEmits<{
    (e: 'copy', withVerseNumbers: boolean): void;
    (e: 'share'): void;
    (e: 'close'): void;
}>();

const menuRef = ref<HTMLElement | null>(null);

const positionStyle = computed(() => {
    return {
        top: `${props.position.y}px`,
        left: `${props.position.x}px`,
    };
});

// Handle clicks outside the menu to close it
const handleClickOutside = (event: MouseEvent) => {
    if (menuRef.value && !menuRef.value.contains(event.target as Node)) {
        emit('close');
    }
};

const handleKeyDown = (e: KeyboardEvent) => {
    if (e.key === 'Escape') {
        emit('close');
    }
};

// Copy the selected text
const copyText = (withVerseNumbers: boolean) => {
    emit('copy', withVerseNumbers);
    emit('close');
};

// Share the selected text
const shareText = () => {
    emit('share');
    emit('close');
};

onMounted(() => {
    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
            emit('close');
        }
    });
});

onUnmounted(() => {
    document.removeEventListener('mousedown', handleClickOutside);
    document.removeEventListener('keydown', handleKeyDown);
});
</script>

<style scoped>
.text-selection-menu {
    min-width: 200px;
}
</style>
