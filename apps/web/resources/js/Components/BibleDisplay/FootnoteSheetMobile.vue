<template>
  <Sheet
    class="bg-theme-700 dark:bg-theme-700"
    :is-open="hasFootnote"
    position="bottom"
    draggable
    :header-class="headerClass"
    aria-labelledby="footnote-title"
    aria-describedby="footnote-content"
    @close="onClose"
  >
    <template #header>
      <FootnoteHeader
        class="text-xl font-medium"
        :can-go-back="canGoBack"
        :current-footnote="currentFootnote"
        :go-back="goBack"
        :on-close="onClose"
      />
    </template>

    <section
      id="footnote-content"
      class="h-[40vh] flex-1 p-4"
      aria-modal="true"
      role="dialog"
      aria-labelledby="footnote-title"
    >
      <FootnoteContent
        v-if="currentFootnote"
        :footnote="currentFootnote.footnote"
        @open-link="(linkData) => $emit('open-link', linkData)"
      />
    </section>
  </Sheet>
</template>

<script setup lang="ts">
import Sheet from '@/Components/common/Sheet.vue';
import FootnoteContent from './FootnoteContent.vue';
import type { FootnoteState } from '@esbo/types';
import { computed } from 'vue';
import FootnoteHeader from './FootnoteHeader.vue';

const props = defineProps<{
  currentFootnote: FootnoteState | null;
  canGoBack: boolean;
}>();

const hasFootnote = computed(() => !!props.currentFootnote);
const headerClass = computed(() => 'w-full bg-theme-700 dark:bg-theme-700 text-theme-100');

interface LinkData {
  href?: string;
  title?: string;
  content?: string;
}

const emit = defineEmits<{
  (e: 'close'): void;
  (e: 'go-back'): void;
  (e: 'open-link', linkData: LinkData): void;
}>();


function goBack() {
  emit('go-back');
}

function onClose() {
  emit('close');
}
</script>
