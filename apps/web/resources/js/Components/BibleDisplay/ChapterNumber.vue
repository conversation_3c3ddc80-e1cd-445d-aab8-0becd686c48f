<template>
    <span
        v-if="showChapterNumbers"
        class="font-thanatos ml-[-.3rem] inline-block text-[4em] select-none float-left pt-0 mt-0 leading-none"
        aria-label="Kapitelnummer"
        role="text"
    >
    {{ number }}&nbsp;
    </span>
</template>

<script setup lang="ts">
/**
 * ChapterNumber.vue
 *
 * @component
 * @description
 *   Displays the chapter number as a large, stylized number at the start of a chapter.
 *   Visibility is controlled by user text settings. Used for accessibility and navigation.
 *
 * @prop {number} number - The chapter number to display.
 *
 * @accessibility
 *   - Uses <span> with aria-label and role="text" for screen readers.
 */
import { useTextSettingsStore } from '@/stores/textSettingsStore';
import { storeToRefs } from 'pinia';

const settingsStore = useTextSettingsStore();
const { showChapterNumbers } = storeToRefs(settingsStore);

/**
 * Props for ChapterNumber component.
 * @typedef {object} Props
 * @property {number} number - The chapter number to display.
 */
defineProps<{
    number: number;
}>();
</script>
