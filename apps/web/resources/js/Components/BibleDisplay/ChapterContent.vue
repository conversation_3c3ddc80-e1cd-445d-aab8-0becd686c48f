<template>
    <article
        v-bind="{ ...$attrs }"
        :id="chapter.number.toString()"
        :data-chapter-id="dataChapterId"
        :class="[
            articleClasses,
            textSettings.flowText ? 'flow-mode' : 'verse-mode',
            `text-${textSettings.fontSize}`,
        ]"
        class="prose prose-lg dark:prose-invert font-calluna max-w-full px-2 tracking-[0.01em]"
        role="region"
        aria-label="Bibeltext"
    >
        <header v-if="showBookHeader">
            <h1
                class="font-thanatos text-theme-900 dark:text-theme-100 mb-8 text-center text-4xl"
            >
                {{ chapter.book.name }}
            </h1>
        </header>
        <!-- Render each group as a separate block -->
        <section
            v-for="(group, index) in displayGroups"
            :key="group.paragraphGroupId || index"
            class="pericope"
            :class="{ 'pericope-start': group.isPericopeStart && index > 0 }"
            :aria-label="'Perikope ' + (index + 1)"
            role="list"
        >
            <template v-if="isPoetryChapter(props.chapter)">
                <VerseUnit
                    v-if="makePoetryVerse(group)"
                    :verse="makePoetryVerse(group)"
                    :verse-number="group.verseNumbers[0]"
                    :group-index="index"
                    :show-chapter-number="group.verseNumbers[0] === 1 && index === 0"
                    :chapter="chapter"
                    :category-colors="categoryColors"
                    :text-settings="textSettings"
                    @footnote-click="(v: FootnoteClickEvent) => { const verse = getVerse(group.verseNumbers[0]); if (verse) handleFootnoteClick(v, verse); }"
                    @footnote-hover="(v: FootnoteHoverEvent) => { const verse = getVerse(group.verseNumbers[0]); if (verse) handleFootnoteHover(v, verse); }"
                />
            </template>
            <template v-else>
                <VerseUnit
                    v-for="verseNumber in group.verseNumbers"
                    :key="verseNumber"
                    :verse="getVerse(verseNumber)"
                    :verse-number="verseNumber"
                    :group-index="index"
                    :show-chapter-number="verseNumber === 1 && index === 0"
                    :chapter="chapter"
                    :category-colors="categoryColors"
                    :text-settings="textSettings"
                    @footnote-click="(v: FootnoteClickEvent) => { const verse = getVerse(verseNumber); if (verse) handleFootnoteClick(v, verse); }"
                    @footnote-hover="(v: FootnoteHoverEvent) => { const verse = getVerse(verseNumber); if (verse) handleFootnoteHover(v, verse); }"
                />
            </template>
        </section>
        <footer
            v-if="!textSettings.isInfiniteScrollEnabled"
            class="flex flex-col gap-2 pt-12 text-center text-sm"
        >
            <div>{{ COPYRIGHT_NOTICE }}</div>
            <div>{{ PUBLISHING_NOTE }}</div>
            <div><a href="/changelog">Changelog</a> | <a href="https://ebtc.org/impressum" target="_blank">Impressum</a></div>
        </footer>
    </article>
</template>

<script setup lang="ts">
// We'll use type assertions instead of global declarations

/**
 * ChapterContent.vue
 *
 * @component
 * @description
 *   Renders the content of a full Bible chapter, including pericope (paragraph) grouping, book/chapter headings,
 *   and all verses. Handles dynamic highlighting, scroll-based navigation, and accessibility features for screen readers.
 *   Integrates with Pinia stores for navigation and text settings, and propagates footnote events to parent components.
 *
 * @prop {ChapterSection} chapter - The chapter data to display, including all verses and book metadata.
 *
 * @slot default - Used to render the full content of the chapter (internal to the component).
 *
 * @accessibility
 *   - Uses <article> and <section> for semantic structure.
 *   - Pericope sections have ARIA labels for screen readers.
 *   - Verse divs have IDs and data attributes for navigation and highlighting.
 */
import { useBibleStore } from '@/stores/bible/bibleSectionStore';
import { useTextSettingsStore } from '@/stores/textSettingsStore';
import { getChapterId } from '@/utils/bibleNavigationUtils';
import { getCategoryTextColorClasses } from '@/utils/categoryColors';
import { COPYRIGHT_NOTICE, PUBLISHING_NOTE } from '@/utils/copyright';
import type {
    ChapterSection,
    Verse,
    WordParagraphGroup,
} from '@esbo/types';
import { computed, nextTick, onMounted, onUnmounted } from 'vue';
import { groupWordsByParagraphGroup } from '@/utils/bibleDisplay';
import { isPoetryChapter } from '@/utils/bibleDisplay';
import VerseUnit from './VerseUnit.vue';

/**
 * For poetry: create a synthetic verse object for each paragraph group (poetic line),
 * copying all metadata from the original verse but replacing words with the group's words.
 * This allows us to reuse VerseContent to render poetic lines with all features (footnotes, spacing, verse numbers, etc.).
 */
function makePoetryVerse(group: WordParagraphGroup) {
    // Find the first verse in the group (all words in a poetic line should belong to the same verse in most cases)
    const verseNumber = group.verseNumbers[0];
    const origVerse = getVerse(verseNumber);
    if (!origVerse) return null;

    // Ensure we have words to work with
    if (!group.words || group.words.length === 0) {
        return null;
    }

    // Return a shallow copy with words replaced
    return {
        ...origVerse,
        words: group.words,
    };
}

/**
 * Props for ChapterContent component.
 * @typedef {object} Props
 * @property {ChapterSection} chapter - The chapter data to display, including verses and book info..
 */
interface Props {
    chapter: ChapterSection;
}

const showBookHeader = computed(() => {
    return props.chapter.number == 1;
});
/**
 * Component props, strictly typed.
 * @type {Props}
 */
const props = defineProps<Props>();
/**
 * Reactive store for user text display settings (font size, flow mode, etc).
 */
const textSettings = useTextSettingsStore();
/**
 * Store for Bible navigation and state (current chapter/verse, scroll handling, etc).
 */
const bibleStore = useBibleStore();

/**
 * Compute CSS classes for the chapter <article> element, including current-section highlighting.
 * @returns {Record<string, boolean>} Classes for the article element.
 */
const articleClasses = computed(() => ({
    'chapter-content': true,
    'text-theme-900 dark:text-theme-100': true,
    current: bibleStore.isCurrentSection(props.chapter),
}));

/**
 * Compute a unique string ID for the chapter, used for DOM and accessibility.
 * @returns {string}
 */
const dataChapterId = computed(() => getChapterId(props.chapter));

/**
 * Compute the text color CSS classes for the chapter/category.
 * @returns {string}
 */
const categoryColors = computed(() => {
    if (props.chapter?.book && 'category' in props.chapter.book) {
        return getCategoryTextColorClasses(props.chapter.book.category, 100);
    }
    return 'text-theme-500 dark:text-theme-100';
});

function getVerse(verseNumber: number): Verse | null {
  return props.chapter.verses.find(v => v.number === verseNumber) ?? null;
}

// For poetry: always group by paragraphGroupId (one line per poetic line)
const poetryGroups = computed(() => groupWordsByParagraphGroup(props.chapter.verses));
// For prose: group by verse (verse-by-verse mode) or by paragraphGroupId (flow mode)
const proseVerseGroups = computed(() => props.chapter.verses.map(v => ({
    paragraphGroupId: `verse_${v.number}_${v.paragraphGroupId || 'single'}`,
    paragraphStyle: v.paragraphStyle || null,
    words: v.words || [], // Ensure words is always an array
    verseNumbers: [v.number],
    isPericopeStart: v.isPericopeStart || false,
})));
const proseParagraphGroups = computed(() => groupWordsByParagraphGroup(props.chapter.verses));

const displayGroups = computed(() => {
    if (isPoetryChapter(props.chapter)) {
        return poetryGroups.value;
    }
    return textSettings.flowText ? proseParagraphGroups.value : proseVerseGroups.value;
});

/**
 * Abbreviation of the book for use in references and footnotes.
 * @returns {string}
 */
const bookAbbreviation = computed(() => {
    const book = props.chapter.book;
    if (!book) return '';
    return book.abbreviation;
});

// Update current verse based on scroll position
/**
 * Updates the current verse in the store based on scroll position.
 * Called on scroll events for dynamic verse highlighting.
 * @returns {void}
 */
const updateVisibleVerse = () => {
    // Only update verse if scroll handling is enabled
    if (!bibleStore.scrollHandlingEnabled) {
        return;
    }

    // Find the first verse unit that's visible in the viewport
    const verses = document.querySelectorAll('.verse-unit');
    if (!verses.length) return;

    // Find the first verse that's fully or partially visible
    const viewportHeight = window.innerHeight;
    const buffer = 50; // Small buffer to avoid flickering

    let currentVerseNum = null;
    for (const verse of verses) {
        const rect = verse.getBoundingClientRect();
        // Check if the verse is visible in the viewport
        if (rect.top < viewportHeight - buffer && rect.bottom > buffer) {
            currentVerseNum = parseInt(verse.getAttribute('data-verse') || '1');
            break;
        }
    }

    // If no verse is found visible, don't update
    if (currentVerseNum === null) return;

    // Only update if we're in the current chapter and the verse has changed
    if (
        !isNaN(currentVerseNum) &&
        bibleStore.currentChapter === props.chapter.number &&
        bibleStore.currentVerse !== currentVerseNum
    ) {
        bibleStore.updateCurrentVerse(props.chapter.number, currentVerseNum);
    }
};

// Handle footnote interactions
/**
 * Handles click events on footnote markers in verses.
 * @param {{ event: MouseEvent; footnote: Footnote; word: string }} param0 - Footnote click event data.
 * @param {Verse} verse - The verse containing the footnote.
 * @returns {void}
 */
import type { FootnoteClickEvent, FootnoteHoverEvent } from '@esbo/types';

function handleFootnoteClick(
    payload: FootnoteClickEvent,
    verse: Verse,
) {
    bibleStore.handleFootnoteClick(
        payload.event,
        payload.footnote,
        payload.word,
        bookAbbreviation.value,
        props.chapter.number,
        verse.number,
        payload.referenceEl
    );
}

/**
 * Handles hover events on footnote markers in verses.
 * @param {{ event: MouseEvent; footnote: Footnote | null; word?: string }} param0 - Footnote hover event data.
 * @param {Verse} verse - The verse containing the footnote.
 * @returns {void}
 */
function handleFootnoteHover(
    payload: FootnoteHoverEvent,
    verse: Verse,
) {
    bibleStore.handleFootnoteHover(
        payload.event,
        payload.footnote,
        payload.word,
        bookAbbreviation.value,
        props.chapter.number,
        verse.number,
        payload.referenceEl
    );
}

// Set up scroll listener and initialize
onMounted(() => {
    // Initial update after ensuring content is positioned correctly
    nextTick(() => {
        if (props.chapter.verses.length > 0) {
            // Only set initial verse if we're on the current chapter
            if (bibleStore.isCurrentSection(props.chapter)) {
                //console.log('Setting initial verse for chapter:', props);
                const initialVerse = props.chapter.verses[0].number;
                bibleStore.updateCurrentVerse(
                    props.chapter.number,
                    initialVerse,
                );
            }
        }
    });
    window.addEventListener('scroll', updateVisibleVerse, { passive: true });
});

onUnmounted(() => {
    window.removeEventListener('scroll', updateVisibleVerse);
});
</script>

<style scoped>
.highlighted-verse {
    text-decoration: underline;
    text-decoration-style: solid;
    text-decoration-thickness: 0.05em;
    text-underline-offset: 0.375em; /* Add some space between text and underline */
    border-radius: 0.25em;
    transition: text-decoration-color 0.2s ease;
}

/* Category-specific underline colors */
.highlighted-verse[data-category='history'] {
    text-decoration-color: var(--color-history-100);
}

.highlighted-verse[data-category='wisdom'],
.highlighted-verse[data-category='law'] {
    text-decoration-color: var(--color-poetic-100);
}

.highlighted-verse[data-category='prophecy'] {
    text-decoration-color: var(--color-prophecy-100);
}

.highlighted-verse[data-category='gospel'] {
    text-decoration-color: var(--color-gospel-100);
}

.highlighted-verse[data-category='epistle'] {
    text-decoration-color: var(--color-epistle-100);
}

.highlighted-verse[data-category='apocalypse'] {
    text-decoration-color: var(--color-revelation-100);
}

/* Dark mode styles - slightly higher opacity for better visibility */
:global(.dark) .highlighted-verse[data-category='history'] {
    text-decoration-color: var(--color-history-100);
}

:global(.dark) .highlighted-verse[data-category='wisdom'],
:global(.dark) .highlighted-verse[data-category='law'] {
    text-decoration-color: var(--color-poetic-100);
}

:global(.dark) .highlighted-verse[data-category='prophecy'] {
    text-decoration-color: var(--color-prophecy-100);
}

:global(.dark) .highlighted-verse[data-category='gospel'] {
    text-decoration-color: var(--color-gospel-100);
}

:global(.dark) .highlighted-verse[data-category='epistle'] {
    text-decoration-color: var(--color-epistle-100);
}

:global(.dark) .highlighted-verse[data-category='apocalypse'] {
    text-decoration-color: var(--color-revelation-100);
}
</style>
