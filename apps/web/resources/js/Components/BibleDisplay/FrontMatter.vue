<template>
    <template v-if="bookData">
        <article
            class="prose prose-lg dark:prose-invert font-franklin max-w-full px-2"
            :data-chapter-id="book?.slug"
        >
            <header class="mb-12 text-center">
                <h1
                    class="font-thanatos mb-2 text-4xl font-bold dark:text-white"
                    :class="categoryColors"
                >
                    {{ bookData?.name }}
                </h1>
                <div class="text-theme-600 dark:text-theme-400 text-sm">
                    {{ bookCategoryLabels[bookData?.category] }} •
                    {{ testamentLabels[bookData?.testament] }}
                </div>
            </header>

            <div
                v-if="bookData.metadata"
                class="mb-12 grid grid-cols-1 gap-8 md:grid-cols-2"
            >
                <div class="space-y-6">
                    <InfoSection title="Hintergrund">
                        <InfoItem
                            label="Autor(en)"
                            :value="
                                formatStringArray(bookData.metadata?.authors)
                            "
                        />
                        <InfoItem
                            label="Geschrieben"
                            :value="
                                formatWrittenYear(
                                    bookData.metadata?.writtenYear,
                                )
                            "
                        />
                        <InfoItem
                            label="Ort"
                            :value="formatString(bookData.metadata?.location)"
                        />
                        <InfoItem
                            label="Historische Periode"
                            :value="
                                formatString(
                                    bookData.metadata?.historicalPeriod,
                                )
                            "
                        />
                        <InfoItem
                            label="Originalsprache"
                            :value="
                                formatLanguage(
                                    bookData.metadata?.originalLanguage,
                                )
                            "
                        />
                    </InfoSection>

                    <InfoSection title="Schlüsselelemente">
                        <InfoItem
                            label="Hauptpersonen"
                            :value="formatString(bookData.metadata?.keyPeople)"
                        />
                        <InfoItem
                            label="Schlüsselwörter"
                            :value="formatString(bookData.metadata?.keyWords)"
                        />
                    </InfoSection>
                </div>

                <div class="space-y-6">
                    <InfoSection title="Hauptbotschaft">
                        <InfoItem
                            label="Thema"
                            :value="formatString(bookData.metadata?.theme)"
                            class="italic"
                        />
                        <InfoItem
                            label="Kernlehren"
                            :value="
                                formatString(bookData.metadata?.keyTeachings)
                            "
                            class="list-disc pl-4"
                        />
                    </InfoSection>

                    <InfoSection title="Geistlicher Fokus">
                        <InfoItem
                            label="Eigenschaften Gottes"
                            :value="
                                formatString(bookData.metadata?.attributesOfGod)
                            "
                            class="list-disc pl-4"
                        />
                        <InfoItem
                            label="Bündnisse"
                            :value="formatString(bookData.metadata?.covenants)"
                            class="list-disc pl-4"
                        />
                    </InfoSection>
                </div>
            </div>

            <div
                v-if="bookData.metadata"
                class="bg-theme-50 dark:bg-theme-800 mb-12 rounded-lg p-6"
            >
                <h2
                    class="text-theme-900 mb-4 text-xl font-semibold dark:text-white"
                >
                    Schlüsselthemen
                </h2>
                <div class="space-y-4">
                    <InfoItem
                        label="Kernlehren"
                        :value="formatString(bookData.metadata?.keyTeachings)"
                        class="list-disc pl-4"
                    />
                </div>
            </div>

            <div v-else class="mb-12 p-6 text-center">
                <div role="status" class="flex justify-center">
                    <svg
                        aria-hidden="true"
                        class="text-theme-200 dark:text-theme-600 h-8 w-8 animate-spin fill-blue-600"
                        viewBox="0 0 100 101"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                            fill="currentColor"
                        />
                        <path
                            d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                            fill="currentFill"
                        />
                    </svg>
                    <span class="sr-only">Loading...</span>
                </div>
                <p class="text-theme-600 dark:text-theme-400 mt-4">
                    Lade Buchinformationen...
                </p>
            </div>

            <div class="text-theme-600 dark:text-theme-400 text-center text-sm">
                {{ formatChapterCount(bookData?.chapterCount) }}
            </div>
        </article>
    </template>

    <div v-else class="py-4 text-center text-red-600 dark:text-red-400">
        Buchdaten nicht gefunden
    </div>
</template>

<script setup lang="ts">
/**
 * FrontMatter.vue
 *
 * @component
 * @description
 *   Displays the frontmatter/introduction and metadata for a Bible book, including background, key themes, and metadata.
 *
 * @prop {Book} initialBook - The book object to display frontmatter for.
 *
 * @accessibility
 *   - Uses <article> and <section> for semantic grouping and screen reader support.
 */
import { getCategoryTextColorClasses } from '@/utils/categoryColors';
import {
    OriginalLanguage,
    bookCategoryLabels,
    testamentLabels,
} from '@esbo/enums';
import type { Book } from '@esbo/types';
import { computed } from 'vue';
import InfoItem from './InfoItem.vue';
import InfoSection from './InfoSection.vue';

interface Props {
    initialBook: Book;
}

const props = defineProps<Props>();

// For array properties
/**
 * Formats an array or string of items as a bullet-separated string for display.
 * @param {string[] | string | null | undefined} items
 * @returns {string}
 */
function formatStringArray(
    items: string[] | string | null | undefined,
): string {
    if (!items) return '';
    if (typeof items === 'string') return items;
    return items.join(' • ');
}

// For single string properties
/**
 * Formats a single string for display, returns empty string if nullish.
 * @param {string | null | undefined} value
 * @returns {string}
 */
function formatString(value: string | null | undefined): string {
    if (!value) return '';
    return value;
}

/**
 * Formats a year as a readable string (B.C./A.D.).
 * @param {number | null | undefined} year
 * @returns {string}
 */
function formatWrittenYear(year: number | null | undefined): string {
    if (!year) return 'Unbekannt';
    return year < 0 ? `${Math.abs(year)} v. Chr.` : `${year} n. Chr.`;
}

/**
 * Formats an OriginalLanguage enum value as a readable string.
 * @param {OriginalLanguage | undefined} language
 * @returns {string}
 */
function formatLanguage(language: OriginalLanguage | undefined): string {
    if (!language) return 'Unbekannt';

    const languages = {
        [OriginalLanguage.HEBREW]: 'Hebräisch',
        [OriginalLanguage.ARAMAIC]: 'Aramäisch',
        [OriginalLanguage.GREEK]: 'Griechisch',
        [OriginalLanguage.MIXED]: 'Hebräisch und Aramäisch',
    };

    return languages[language] || 'Unbekannt';
}

/**
 * Formats the chapter count for display.
 * @param {number | undefined} count
 * @returns {string}
 */
function formatChapterCount(count: number | undefined): string {
    if (!count) return '';
    return `${count} ${count === 1 ? 'Kapitel' : 'Kapitel'}`;
}

/**
 * The book object provided as a prop, as a computed ref.
 * @type {import('vue').ComputedRef<Book>}
 */
const book = computed<Book>(() => {
    return props.initialBook;
});

/**
 * Computed book metadata for rendering frontmatter sections.
 * @type {import('vue').ComputedRef<any>}
 */
const bookData = computed(() => {
    if (!book.value) return null;
    console.log('Book data in FrontMatter', book.value);

    return {
        name: book.value.name,
        category: book.value.category,
        testament: book.value.testament,
        metadata: book.value.metadata,
        chapterCount: book.value.chapterCount || 0,
    };
});
/**
 * Computes text color classes for the book category.
 * @type {import('vue').ComputedRef<string>}
 */
const categoryColors = computed(() => {
    if (book.value && 'category' in book.value) {
        return getCategoryTextColorClasses(book.value.category, 100);
    }
    return 'text-theme-500 dark:text-theme-100';
});
</script>

<style scoped>
.prose {
    max-width: none;
}
</style>
