<template>
    <template v-if="footnote?.contentStructure">
        <template v-for="(element, index) in filteredElements" :key="index">
            <template v-if="element.type === 'ref'">
                <a
                    v-if="element.href"
                    :href="element.href"
                    :class="getElementClasses(element)"
                    :title="element.title || element.content"
                    :aria-label="element.title || element.content"
                    @click.stop.prevent="onRefClick(element)"
                >
                    {{ element.content }}
                </a>
                <span v-else :class="getElementClasses(element)">
                    {{ element.content }}
                </span>
                <span v-if="index < filteredElements.length - 1"> </span>
            </template>
            <template v-else>
                <span :class="getElementClasses(element)">
                    {{ element.content }}
                </span>
                <span v-if="index < filteredElements.length - 1"> </span>
            </template>
        </template>
    </template>
    <template v-else>
        {{ footnote?.searchableText || '' }}
    </template>
</template>

<script setup lang="ts">
/**
 * FootnoteContent.vue
 *
 * @component
 * @description
 *   Renders the content of a Bible footnote, supporting styled spans, references,
 *   and semantic markup for various content types.
 *
 * @prop {Footnote} footnote - The footnote object to render.
 *
 * @accessibility
 *   - Uses semantic HTML elements with proper ARIA attributes
 *   - References are rendered as clickable links with descriptive titles
 *   - Supports keyboard navigation and screen readers
 */
import type { Footnote, FootnoteContentElement } from '@esbo/types';
import { computed } from 'vue';

const props = defineProps<{
    footnote: Footnote | null;
}>();

interface LinkData {
    href?: string;
    title?: string;
    content?: string;
}

const emit = defineEmits<{
    (e: 'open-link', linkData: LinkData): void;
}>();

/**
 * Computed property that filters out 'caller' elements from the footnote content structure.
 */
const filteredElements = computed(() => {
    if (!props.footnote?.contentStructure?.elements) return [];
    return props.footnote.contentStructure.elements.filter(
        (element) => element.type !== 'caller',
    );
});

/**
/**
 * Returns the CSS classes for a given footnote content element based on its type and style.
 * @param {FootnoteContentElement} element
 * @returns {string[]} Array of CSS class names.
 */
function getElementClasses(element: FootnoteContentElement): string[] {
    const classes: string[] = [];

    // Base styling for different element types
    switch (element.type) {
        case 'ref':
            classes.push(
                'text-theme-300',
                'underline',
                'decoration-dotted',
                'cursor-pointer',
                'font-medium',
            );
            break;
        case 'caller':
            classes.push('font-medium', 'text-theme-300');
            break;
        case 'styled':
        case 'char':
            classes.push('text-theme-300');
            // Handle different style types
            if (element.style) {
                switch (element.style) {
                    case 'it':
                        classes.push('italic');
                        break;
                    case 'b':
                        classes.push('font-bold');
                        break;
                    case 'u':
                        classes.push('underline');
                        break;
                    // Add more style cases as needed
                }
            }
            break;
        default:
            classes.push('text-theme-200');
            break;
    }

    return classes;
}

/**
 * Handles click on a reference link.
 * Emits 'open-link' with the referenced footnote data.
 */
function onRefClick(element: FootnoteContentElement) {
    // You may need to fetch or build the new footnote state here.
    // For demo purposes, we'll emit the `href` and `title` (customize as needed).
    emit('open-link', {
        href: element.href,
        title: element.title || element.content,
        // Optionally, pass more info or fetch the actual footnote data here.
    });
}
</script>
