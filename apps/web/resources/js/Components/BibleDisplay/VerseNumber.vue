<template>
    <sup
        :class="['max-w-[2rem] cursor-pointer text-[0.6em] select-none',
            versColor,
            flowText ? 'pr-1 pl-1' : 'pr-2',
            isHighlighted ? 'font-bold' : '',
        ]"
        aria-hidden="false"
        :data-verse-number="number"
        role="button"
        :aria-pressed="isHighlighted"
        @click="handleClick"
    >
        {{ number }}
    </sup>
</template>

<script setup lang="ts">
/**
 * VerseNumber.vue
 *
 * @component
 * @description
 *   Displays a verse number as a superscript with highlighting and click-to-highlight support.
 *
 * @prop {number} number - The verse number to display.
 * @prop {boolean} flowText - Whether to display in flow text mode.
 * @prop {boolean} [isHighlighted] - Whether the verse is currently highlighted.
 *
 * @accessibility
 *   - Uses <sup> with role="button" and ARIA attributes for navigation and screen readers.
 */
import { useBibleHighlightStore } from '@/stores/bible/bibleHighlightStore';
import { useBibleStore } from '@/stores/bible/bibleSectionStore';
import { computed, ref } from 'vue';

const bibleStore = useBibleStore();
const highlightStore = useBibleHighlightStore();
const lastClickedVerse = ref<number | null>(null);

const props = defineProps<{
    number: number;
    flowText: boolean;
    isHighlighted?: boolean;
}>();

/**
 * Computes the color classes for the verse number based on the current book category.
 * @type {import('vue').ComputedRef<string>}
 */
const versColor = computed(() => {
    const currentChapter = bibleStore.currentChapterSection;
    const classes =
        currentChapter?.book && 'category' in currentChapter.book
            ? `text-${currentChapter.book.category}-100 dark:text-theme-100`
            : 'text-theme-500 dark:text-theme-100';
    return classes;
});

/**
 * Handles click events on the verse number to toggle highlighting, supporting shift-click for ranges.
 * @param {MouseEvent} event
 */
const handleClick = (event: MouseEvent) => {
    const currentChapter = bibleStore.currentChapterSection;
    if (!currentChapter?.book) return;

    // Check if shift key is pressed and we have a previous verse click
    if (event.shiftKey && lastClickedVerse.value !== null) {
        // Determine the range (start and end verse)
        const startVerse = Math.min(lastClickedVerse.value, props.number);
        const endVerse = Math.max(lastClickedVerse.value, props.number);

        console.log('Toggling highlight for verse range:', {
            book: currentChapter.book.slug,
            chapter: currentChapter.number,
            verse: startVerse,
            endVerse: endVerse,
        });

        // Toggle highlight for the verse range
        highlightStore.toggleVerseHighlight({
            book: currentChapter.book.slug,
            chapter: currentChapter.number,
            verse: startVerse,
            endVerse: endVerse,
        });

        // Reset the last clicked verse
        lastClickedVerse.value = null;
    } else {
        // Single verse toggle
        console.log('Toggling highlight for verse:', {
            book: currentChapter.book.slug,
            chapter: currentChapter.number,
            verse: props.number,
        });

        // Toggle highlight for a single verse
        highlightStore.toggleVerseHighlight({
            book: currentChapter.book.slug,
            chapter: currentChapter.number,
            verse: props.number,
        });

        // Store this verse as the last clicked
        lastClickedVerse.value = props.number;
    }
};
</script>
<style scoped>
sup {
    display: inline;
    bottom: 1.2em;
}
.flex sup {
    top: 0.8em;
}
</style>
