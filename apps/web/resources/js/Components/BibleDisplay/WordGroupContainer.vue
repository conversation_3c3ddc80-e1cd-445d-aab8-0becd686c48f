<template>
    <span
        :class="groupClasses"
        :data-variant-type="group.isVariant ? group.wordType : undefined"
        :data-word-type="getGroupType()"
        :role="group.isOtQuote ? 'mark' : undefined"
        :aria-label="getAriaLabel()"
    >
        <template v-for="(word, index) in group.words" :key="index">
            <span class="inline">
                <span
                    :class="['word-text', getFootnoteClasses(word)]"
                    @click.stop="(event) => handleFootnoteClick(event, word)"
                    @mouseenter.stop="(event) => handleFootnoteHover(event, word, true)"
                    @mouseleave.stop="(event) => handleFootnoteHover(event, word, false)"
                >{{ word.text.trim() }}</span>
                <span v-if="word.textAfter" class="text-after">{{ word.textAfter }}</span>
                <span v-if="getSpaceAfter(index)" class="space">{{ getSpaceAfter(index) }}</span>
            </span>
        </template>
    </span>
</template>

<script setup lang="ts">
/**
 * WordGroupContainer.vue
 *
 * @component
 * @description
 *   Renders a group of words within a Bible verse, handling display of text variants,
 *   Old Testament quotes, additions, and footnotes. Provides accessibility features
 *   and emits events for user interaction with footnotes.
 *
 * @prop {WordGroup} group - The group of words to render, including variant and quote metadata.
 * @prop {boolean} showFootnotes - Whether to display footnote markers and interactions.
 *
 * @emits footnoteClick - Fired when a footnote word is clicked. Payload: [event: MouseEvent, footnote: Footnote, word: string]
 * @emits footnoteHover - Fired when a footnote word is hovered. Payload: [event: MouseEvent, footnote: Footnote|null, word: string]
 *
 * @accessibility
 *   - Uses <span> with ARIA labels for group semantics.
 *   - Emits events for screen reader support on footnotes.
 */
import type { Footnote, Word, WordGroup } from '@esbo/types';
import { computed } from 'vue';
import { getSpace } from '@/utils/spacing';

/**
 * Props for WordGroupContainer component.
 * @typedef {object} Props
 * @property {WordGroup} group - The group of words to render.
 * @property {boolean} showFootnotes - Whether to display footnote markers and interactions.
 */
const props = defineProps<{
    group: WordGroup;
    showFootnotes: boolean;
}>();

/**
 * Emits footnoteClick and footnoteHover events for parent listeners.
 */
const emit = defineEmits<{
    footnoteClick: [event: MouseEvent, footnote: Footnote, word: string, referenceEl: HTMLElement];
    footnoteHover: [event: MouseEvent, footnote: Footnote | null, word: string, referenceEl: HTMLElement];
}>();

const variantTypeClasses: Record<string, string> = {
    add: 'addition',
    om: 'omission',
    va: 'alternative',
    vp: 'primary',
    xot: 'ot-quote',
};

/**
 * Returns the type for the entire group for the data-word-type attribute.
 * @returns {string|undefined} The group type string or undefined if not applicable.
 */
const getGroupType = (): string | undefined => {
    if (props.group.wordType == 'va') return 'variant';
    if (props.group.wordType === 'xot') return 'ot-quote';
    if (props.group.wordType === 'add') return 'addition';
    return undefined;
};

/**
 * Returns an aria-label for accessibility for the group container.
 * @returns {string|undefined} The aria-label describing the group, or undefined if not applicable.
 */
const getAriaLabel = (): string | undefined => {
    if (props.group.isOtQuote) return 'Zitat aus dem Alten Testament';
    if (props.group.isVariant) {
        return `Text variant: ${props.group.wordType || 'Alternative Lesart'}`;
    }
    if (props.group.isAddition) {
        return `Text nicht im Originaltext enthalten`;
    }
    return undefined;
};

/**
 * Computes CSS classes for individual words, especially for footnote markers.
 * @param {Word} word - The word object to evaluate.
 * @returns {string} The computed class string for the word.
 */
const getFootnoteClasses = (word: Word) => {
    if (word.isFootnote && props.showFootnotes) {
        return 'relative cursor-pointer text-theme-900 dark:text-theme-100 border-theme-400 dark:border-theme-500 decoration-theme-400 underline decoration-dotted decoration-from-font underline-offset-4';
    }
    return '';
};

/**
 * Computes CSS classes for the word group container based on group type (variant, addition, OT quote).
 * @returns {string} The computed class string for the group container.
 */
const groupClasses = computed(() => {
    const classes: string[] = ['inline'];

    // Handle variant groups
    if (
        props.group.isVariant ||
        props.group.isAddition ||
        props.group.isOtQuote
    ) {
        classes.push('variant-group');
        //if (props.group.wordType === null)
        //    console.debug('wordType = null. Group is  ', props.group);

        // Add variant-specific classes based on type
        if (props.group.wordType) {
            classes.push(variantTypeClasses[props.group.wordType]);
            classes.push('text-theme-800 dark:text-theme-400');
        }
    }

    // Handle OT quotes
    if (props.group.isOtQuote) {
        classes.push('ot-quote');
    }

    // Return space-separated class string
    return classes.join(' ');
});

/**
 * Returns the space that should be added after a word, if any.
 * Uses the shared spacing utility to ensure consistent behavior.
 * @param {number} index - The index of the current word.
 * @returns {string} A space if needed, otherwise an empty string.
 */
const getSpaceAfter = (index: number): string => {
    return getSpace(index, props.group.words);
};
/**
 * Handles click events on footnote words and emits the footnoteClick event.
 * @param {MouseEvent} event - The click event object.
 * @param {Word} word - The word object that was clicked.
 */
import { useResponsiveStore } from '@/composables/useResponsiveStore';
const { isMobile } = useResponsiveStore();

const handleFootnoteClick = (event: MouseEvent, word: Word) => {
    if (word.isFootnote && word.footnote) {
        emit('footnoteClick', event, word.footnote, word.text, event.currentTarget as HTMLElement);
    }
};

/**
 * Handles hover events on footnote words and emits the footnoteHover event.
 * @param {MouseEvent} event - The mouse event object.
 * @param {Word} word - The word object being hovered.
 * @param {boolean} isHovering - True if entering, false if leaving.
 */
const handleFootnoteHover = (event: MouseEvent, word: Word, isHovering: boolean) => {
    // Centralized mobile detection: bail on mobile
    if (isMobile.value) {
        return;
    }

    if (word.isFootnote && word.footnote) {
        emit(
            'footnoteHover',
            event,
            isHovering ? word.footnote : null,
            word.text,
            event.currentTarget as HTMLElement
        );
    }
};

// addSpace function has been moved to @/utils/spacing
</script>
<style scoped>
.word-text {
    text-decoration-skip-ink: auto;
}

.text-after {
    white-space: nowrap;
}

.variant-group {
    display: inline;
    position: relative;
}

.variant-group::before,
.variant-group::after {
    position: absolute;
    font-size: 0.8em;
    line-height: 1;
    opacity: 0.7;
}

.variant-group[data-variant-type="addition"]::before {
    content: '⌜';
    left: -0.3em;
    top: -0.3em;
}

.variant-group[data-variant-type="addition"]::after {
    content: '⌝';
    right: -0.3em;
    bottom: -0.3em;
}
</style>
