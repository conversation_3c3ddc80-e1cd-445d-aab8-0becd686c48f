<template>
    <Transition
      name="footnote"
      enter-active-class="transition-all duration-300 ease-out"
      leave-active-class="transition-all duration-300 ease-in"
    >
      <div
        v-if="currentFootnote"
        ref="tooltipRef"
        class="fixed z-50 rounded-lg shadow-lg transition-transform bg-theme-700 dark:bg-theme-700 max-w-[18.5em] min-w-[10.25em]"
        :style="floatingStyles"
        :data-placement="placement"
        role="tooltip"
        aria-labelledby="footnote-title"
        aria-describedby="footnote-content"
        tabindex="-1"
      >
        <FootnoteHeader
          class="border-b border-theme-600 dark:border-theme-600"
          :can-go-back="canGoBack"
          :current-footnote="currentFootnote"
          :go-back="goBack"
          :on-close="onClose"
        />
        <section
          id="footnote-content"
          class="text-theme-200 dark:text-theme-200 overflow-y-auto text-sm px-3 pb-3"
          tabindex="0"
        >
          <FootnoteContent :footnote="currentFootnote.footnote" @open-link="$emit('open-link', $event)" />
        </section>
        <!-- Arrow for desktop only -->
        <div
          ref="arrowRef"
          :style="{
            position: 'absolute',
            width: `${arrowLen}px`,
            height: `${arrowLen}px`,
            backgroundColor: 'var(--color-theme-700)',
            transform: 'rotate(45deg)',
            left: middlewareData.arrow?.x != null ? `${middlewareData.arrow.x}px` : '',
            top: middlewareData.arrow?.y != null ? `${middlewareData.arrow.y}px` : '',
            ...arrowStaticSide
          }"
        />
      </div>
    </Transition>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import FootnoteContent from './FootnoteContent.vue';
import type { FootnoteState } from '@esbo/types';
import FootnoteHeader from './FootnoteHeader.vue';

defineProps<{
  currentFootnote: FootnoteState | null;
  canGoBack: boolean;
  floatingStyles: Record<string, string>;
  placement: string;
  middlewareData: { arrow?: { x?: number; y?: number } };
  arrowLen: number;
  arrowStaticSide: { [key: string]: string | undefined };

}>();
interface LinkData {
  href?: string;
  title?: string;
  content?: string;
}

const emit = defineEmits<{ (e: 'close'): void; (e: 'go-back'): void; (e: 'open-link', linkData: LinkData): void }>();

const tooltipRef = ref<HTMLElement | null>(null);
const arrowRef = ref<HTMLElement | null>(null);

// Expose tooltip ref and arrow ref for parent component access
defineExpose({
  $el: tooltipRef,
  arrowRef: arrowRef
});

function goBack() {
  emit('go-back');
}
function onClose() {
  emit('close');
}
</script>
