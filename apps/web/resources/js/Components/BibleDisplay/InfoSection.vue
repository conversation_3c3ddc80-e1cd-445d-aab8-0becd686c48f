<!-- components/Bible/InfoSection.vue -->
<template>
    <section>
        <h2 class="text-theme-900 mb-3 text-lg font-semibold dark:text-white">
            {{ title }}
        </h2>
        <div class="space-y-2">
            <slot></slot>
        </div>
    </section>
</template>

<script setup lang="ts">
/**
 * InfoSection.vue
 *
 * @component
 * @description
 *   Displays a section of book metadata with a title and slot for InfoItems.
 *
 * @prop {string} title - The title of the section.
 */
defineProps<{
    title: string;
}>();
</script>
