<template>
    <template v-for="(group, index) in wordGroups" :key="index">
        <template v-if="showVerseNumberForGroup(group)">
            <VerseNumber
                :number="verse.number"
                :flow-text="flowText"
                :is-highlighted="isVerseHighlighted"
            class="font-thanatos verse-number"
        />
        </template>
        <WordGroupContainer
            :group="group"
            :show-footnotes="textSettings.showFootnotes"
            @footnote-click="onFootnoteClick"
            @footnote-hover="onFootnoteHover"
        />
        <span v-if="needsSpaceAfterGroup(index)" class="inline-block w-1 sm:whitespace-nowrap" aria-hidden="true">{{ spaceChar }}</span>
    </template>
</template>

<script setup lang="ts">
/**
 * VerseContent.vue
 *
 * @component
 * @description
 *   Renders the content of a single Bible verse, including words, variant readings,
 *   Old Testament quotes, additions, and footnotes. Uses pre-grouped words from the API.
 *   Handles highlighting, navigation, and footnote events.
 *
 * @prop {Verse} verse - The verse object to display, including words and metadata.
 * @prop {TextSettings} textSettings - User's text display preferences.
 * @prop {boolean} [isHighlighted] - Whether this verse is highlighted.
 *
 * @emits footnoteClick - Fired when a footnote is clicked. Payload: `{ event: MouseEvent, footnote: Footnote, word: string }`
 * @emits footnoteHover - Fired when a footnote is hovered. Payload: `{ event: MouseEvent, footnote: Footnote|null, word?: string }`
 *
 * @accessibility
 *   - Uses semantic HTML for verse numbers and word groups.
 *   - Emits events for screen reader support on footnotes.
 */
import { useBibleHighlightStore } from '@/stores/bible/bibleHighlightStore';
import { needsSpaceAfterGroup as checkSpaceAfterGroup } from '@/utils/spacing';
import type {
    Footnote,
    TextSettings,
    Verse,
    WordGroup,
} from '@esbo/types';
import { computed, ref } from 'vue';
import VerseNumber from './VerseNumber.vue';
import WordGroupContainer from './WordGroupContainer.vue';

const emit = defineEmits<{
    (e: 'footnoteClick', payload: { event: MouseEvent; footnote: Footnote; word: string; referenceEl: HTMLElement }): void;
    (e: 'footnoteHover', payload: { event: MouseEvent; footnote: Footnote | null; word?: string; referenceEl: HTMLElement }): void;
}>();

/**
 * Props for VerseContent component.
 * @typedef {object} Props
 * @property {Verse} verse - The verse object to display.
 * @property {TextSettings} textSettings - User's text display preferences.
 * @property {boolean} [isHighlighted] - Whether this verse is highlighted.
 */
interface Props {
    verse: Verse;
    textSettings: TextSettings;
    isHighlighted?: boolean;
}

/**
 * Component props, strictly typed.
 * @type {Props}
 */
const props = defineProps<Props>();
const highlightStore = useBibleHighlightStore();

function showVerseNumberForGroup(group: WordGroup) {
    //console.log('group', group, group.words.length, group.words[0].position);
    return props.textSettings.showVerseNumbers && props.verse.number > 1 &&
           group.words.length > 0 &&
           group.words[0].position === 0; // First word in verse
}

/**
 * Whether to render this verse in flow text mode, based on user settings.
 */
const flowText = computed(() => props.textSettings.flowText);

/**
 * Space character constant for consistent spacing
 */
const spaceChar = ref('\u00A0'); // Unicode non-breaking space

/**
 * Whether this verse is currently highlighted in the Bible view.
 */
const isVerseHighlighted = computed(() => {
    if (!highlightStore.isHighlighting) return false;
    return props.isHighlighted || false;
});

/**
 * Get the word groups from the verse data, or fall back to a single group if not available
 */
const wordGroups = computed<WordGroup[]>(() => {
    if (props.verse.wordGroups) {
        return props.verse.wordGroups;
    }

    // Fallback for backward compatibility
    return [{
        words: Array.isArray(props.verse.words) ? props.verse.words : [],
        isVariant: false,
        isOtQuote: false,
        isAddition: false,
        wordGroupId: null,
        wordType: null,
        variantGroupId: null,
        paragraphStyle: null,
    }];
});

/**
 * Emits a footnoteClick event when a footnote is clicked.
 * @param {MouseEvent} event - The click event.
 * @param {Footnote} footnote - The footnote object.
 * @param {string} word - The word associated with the footnote.
 */
function onFootnoteClick(
    event: MouseEvent,
    footnote: Footnote,
    word: string,
    referenceEl: HTMLElement
) {
    emit('footnoteClick', { event, footnote, word, referenceEl });
}

/**
 * Emits a footnoteHover event when a footnote is hovered.
 * @param {MouseEvent} event - The hover event.
 * @param {Footnote|null} footnote - The footnote object, or null if none exists.
 * @param {string} [word] - The word associated with the footnote, if any.
 */
function onFootnoteHover(
    event: MouseEvent,
    footnote: Footnote | null,
    word: string,
    referenceEl: HTMLElement
) {
    emit('footnoteHover', { event, footnote, word, referenceEl });
}

/**
 * Determines if a space is needed after a word group based on
 * punctuation rules and the next group's characteristics.
 * Uses the shared spacing utility for consistent behavior.
 * @param {number} groupIndex - The index of the current group
 * @returns {boolean} Whether a space is needed after this group
 */
function needsSpaceAfterGroup(groupIndex: number): boolean {
    // Use the shared utility for consistent spacing behavior
    return checkSpaceAfterGroup(wordGroups.value, groupIndex);
}
</script>

<style scoped>
.variant-group {
    display: inline;
}

.verse-highlight {
    background-color: #ffffe0;
    padding: 0.25rem;
    margin: -0.25rem;
    border-radius: 0.25rem;
}

.verse.pericope-start {
    margin-left: 4em;
}
</style>
