<template>
  <FootnoteSheetMobile
    v-if="isMobile"
    :key="currentFootnote?.footnote?.id || 'none'"
    ref="mobileSheetRef"
    :current-footnote="currentFootnote"
    :can-go-back="canGoBack"
    :arrowStaticSide="arrowStaticSide"
    @close="onClose"
    @go-back="goBack"
    @open-link="openLinkedFootnote"
  />
  <FootnoteTooltipDesktop
    v-if="!isMobile"
    ref="desktopTooltipRef"
    :current-footnote="currentFootnote"
    :can-go-back="canGoBack"
    :floating-styles="floatingStyles"
    :placement="placement"
    :middleware-data="middlewareData"
    :arrow-len="arrowLen"
    :arrow-static-side="arrowStaticSide"
    @close="onClose"
    @go-back="goBack"
    @open-link="openLinkedFootnote"
  />
</template>


<script setup lang="ts">
// -------------------- Imports --------------------
import { ref, watch, nextTick, computed } from 'vue';
import FootnoteSheetMobile from './FootnoteSheetMobile.vue';
import FootnoteTooltipDesktop from './FootnoteTooltipDesktop.vue';
import { useFootnoteStack } from '@/composables/useFootnoteStack';
import { useFootnoteTooltipEvents } from '@/composables/useFootnoteTooltipEvents';
import { useAbortableFetch } from '@/composables/useAbortableFetch';
import { useFloating, flip, shift, offset, arrow, autoUpdate, size } from '@floating-ui/vue';
import type { FootnoteState, Verse, WordGroup, Word } from '@esbo/types';

// -------------------- Types --------------------

interface LinkData {
  href?: string;
  title?: string;
  content?: string;
}

interface ApiResponse {
  book: { name: string; slug: string; };
  reference: { chapter: number; verseStart?: number; verseEnd?: number; };
  verses: Verse[]; // Use the correct Verse type, which includes wordGroups
}

// -------------------- Props & Emits --------------------
const props = defineProps<{
  footnoteState: FootnoteState | null,
  referenceEl?: HTMLElement | null
}>();
const emit = defineEmits<{ (e: 'close'): void }>();

// -------------------- Refs & Reactive State --------------------
import { useResponsiveStore } from '@/composables/useResponsiveStore';
const { isMobile } = useResponsiveStore();

// Refs for tooltip/sheet DOM nodes
const referenceRef = ref<HTMLElement | null>(null);
const floatingRef = ref<HTMLElement | null>(null);
const arrowRef = ref<HTMLElement | null>(null);
const desktopTooltipRef = ref<InstanceType<typeof FootnoteTooltipDesktop> | null>(null);
const mobileSheetRef = ref<InstanceType<typeof FootnoteSheetMobile> | null>(null);

const arrowLen = 14;

// Unified ref for the currently active tooltip/sheet element
const activeTooltipEl = computed(() => {
  return isMobile.value ? mobileSheetRef.value?.$el : desktopTooltipRef.value?.$el;
});


// -------------------- Computed --------------------
const arrowStaticSide = computed(() => {
  const side = placement.value?.split('-')[0];
  switch (side) {
    case 'top': return { bottom: `${-arrowLen / 2}px` };
    case 'bottom': return { top: `${-arrowLen / 2}px` };
    case 'left': return { right: `${-arrowLen / 2}px` };
    case 'right': return { left: `${-arrowLen / 2}px` };
    default: return {};
  }
});
const canGoBack = computed(() => footnoteStack.value.length > 1);

// -------------------- Footnote Stack --------------------
const {
  footnoteStack,
  currentFootnote,
  pushFootnote,
  popFootnote,
  resetFootnotes,
  setFootnote,
} = useFootnoteStack(props.footnoteState);

// -------------------- Watchers --------------------
watch(
  () => props.footnoteState,
  (state) => {
    setFootnote(state);
    referenceRef.value = state?.referenceEl ?? null;
    tooltipEvents.markOpened();
  }
);

watch(
  () => props.referenceEl,
  (el) => {
    referenceRef.value = el ?? null;
  },
  { immediate: true }
);


const mobileStyles = {
  bottom: 0,
  left: 0,
  right: 0,
  top: "auto",
  width: "100%",
};

// -------------------- Floating UI --------------------
const { floatingStyles, middlewareData, placement } = useFloating(
  referenceRef,
  floatingRef,
  {
    placement: 'bottom',
    transform: false,
    whileElementsMounted: autoUpdate,
    middleware: [
      offset(8),
      flip(),
      shift(),
      arrow({ element: arrowRef }),
      size({
        apply({ elements, x, y }) {
          Object.assign(elements.floating.style, {
            top: `${y}px`,
            left: `${x}px`,
            bottom: "auto",
            right: "auto",
            width: "auto",
            ...(isMobile.value && { ...mobileStyles }),
          });
        },
      }),
    ],
  }
);

// Update floating ref when desktop tooltip mounts
watch(desktopTooltipRef, (tooltip) => {
  if (tooltip && !isMobile.value) {
    nextTick(() => {
      floatingRef.value = tooltip.$el;
      arrowRef.value = tooltip.arrowRef;
    });
  }
}, { immediate: true });

// -------------------- Methods --------------------
function focusPopup() {
  nextTick(() => {
    if (isMobile.value) {
      // Focus the mobile sheet
      const mobileEl = mobileSheetRef.value?.$el;
      if (mobileEl) {
        mobileEl.focus();
      }
    } else {
      // Focus the desktop tooltip
      const desktopEl = desktopTooltipRef.value?.$el;
      if (desktopEl) {
        const closeBtn = desktopEl.querySelector('button[aria-label="Schließe Fußnote"]') as HTMLElement;
        if (closeBtn) {
          closeBtn.focus();
        } else {
          desktopEl.focus();
        }
      }
    }
  });
}

const { fetchWithAbort } = useAbortableFetch();
async function openLinkedFootnote(linkData: LinkData) {
  try {
    const reference = linkData.href?.replace(/^\//, '') || '';
    if (!reference) {
      console.warn('No reference found in link data:', linkData);
      return;
    }
    const response = await fetchWithAbort(
      `/api/bible/${reference}?footnotes=true&words=false&wordGroups=true`
    );
    if (!response.ok) {
      console.error(
        `Failed to fetch verse content for ${reference}: ${response.status} ${response.statusText}`
      );
      return;
    }
      const data = (await response.json()) as ApiResponse;
    console.log('data', data);
    // Extract and join all words from all wordGroups in all verses
    const verseContent = (data.verses || [])
      .map((verse: Verse) =>
        (verse.wordGroups || [])
          .map((group: WordGroup) =>
            (group.words || [])
              .map((word: Word) => word.text)
              .join(' ')
          )
          .join(' ')
      )
        .join(' ') || '';

    // Note: This concatenates all words, preserving verse and group order. Adjust if punctuation/spacing is needed.
    const verseReference = `${data.book.slug} ${data.reference.chapter}${
      data.reference.verseStart ? `,${data.reference.verseStart}` : ''
    }${
      data.reference.verseEnd && data.reference.verseEnd !== data.reference.verseStart
        ? `-${data.reference.verseEnd}`
        : ''
    }`;

    const verseFootnote: FootnoteState = {
      footnote: {
        id: -1,
        searchableText: verseContent,
        contentStructure: {
          elements: [
            { type: 'text', content: verseContent },
          ],
          metadata: { has_references: false, total_elements: 1 },
        },
        isReference: true,
        referencedWord: null,
        verseId: -1,
        hasItalics: false,
        position: 0,
      },
      word: reference,
      reference: verseReference,
      x: currentFootnote.value?.x || 0,
      y: currentFootnote.value?.y || 0,
      isClickLocked: true,
      referenceEl: null,
    };

    console.log('verseFootnote', verseFootnote, linkData);

    pushFootnote(verseFootnote);
    nextTick(() => focusPopup());
  } catch (error) {
    if (error instanceof Error && error.name === 'AbortError') {
      console.error('Request timeout or aborted while fetching verse content');
    } else {
      console.error('Error fetching verse content:', error);
    }
  }
}

function goBack() {
  popFootnote();
  tooltipEvents.markWentBack();
}
function onClose(reason = 'unknown') {
  // For debugging, keep this log:
  console.log('[FootnoteTooltip] onClose called', { reason, stack: new Error().stack });
  resetFootnotes();
  emit('close');
}

// -------------------- Event Handling via Composable --------------------
const tooltipEvents = useFootnoteTooltipEvents({
  isMobile,
  currentFootnote,
  canGoBack,
  getActiveEl: () => activeTooltipEl.value,
  onClose,
  goBack,
});
</script>
