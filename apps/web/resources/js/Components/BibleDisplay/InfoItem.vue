<!-- components/Bible/InfoItem.vue -->
<template>
    <div>
        <dt class="text-theme-600 dark:text-theme-400 text-sm">{{ label }}</dt>
        <dd class="text-theme-900 dark:text-theme-100" :class="className">
            {{ value }}
        </dd>
    </div>
</template>

<script setup lang="ts">
/**
 * InfoItem.vue
 *
 * @component
 * @description
 *   Displays a label and value pair for book metadata in the frontmatter view.
 *
 * @prop {string} label - The label for the metadata item.
 * @prop {string} value - The value to display.
 * @prop {string} [className] - Optional CSS class for the value.
 */
defineProps<{
    label: string;
    value: string;
    className?: string;
}>();
</script>
