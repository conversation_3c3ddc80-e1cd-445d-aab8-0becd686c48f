<template>
    <template v-if="isFrontmatterSection(section)">
        <Frontmatter
            :id="section.book.slug"
            :ref="(el) => setRef(el)"
            :initial-book="section.book"
            :data-chapter-id="section.book.slug"
        />
    </template>
    <template v-else>
        <ChapterContent
            :id="normalizedChapterId"
            :ref="(el) => setRef(el)"
            :chapter="section"
            :data-chapter-id="normalizedChapterId"
            @mouseup="handleTextSelection"
            @contextmenu.prevent="handleCustomContextMenu"
            @pointerdown="handlePointerDown"
            @pointerup="handlePointerUp"
            @pointercancel="handlePointerCancel"
            @pointermove="handlePointerMove"
        />
        <!-- Text selection context menu -->
        <TextSelectionMenu
            ref="selectionMenuRef"
            :is-visible="showSelectionMenu"
            :position="menuPosition"
            @copy="handleCopy"
            @share="handleShare"
            @close="closeSelectionMenu"
        />
    </template>
</template>

<script setup lang="ts">
/**
 * ChapterWrapper.vue
 *
 * @component
 * @description
 *   Decides whether to render a chapter as frontmatter or regular chapter content. Passes refs and IDs for navigation.
 *
 * @prop {Section} section - The section (chapter or frontmatter) to render.
 *
 * @emits setRef - Fired to provide a ref to the parent. Payload: el: Element | ComponentPublicInstance | null
 *
 * @accessibility
 *   - Passes ARIA attributes to child components.
 */
import ChapterContent from '@/Components/BibleDisplay/ChapterContent.vue';
import Frontmatter from '@/Components/BibleDisplay/FrontMatter.vue';
import {
    getChapterId,
    isFrontmatterSection,
} from '@/utils/bibleNavigationUtils';
import type { Section } from '@esbo/types';
import type { ComponentPublicInstance } from 'vue';
import { computed } from 'vue';
import TextSelectionMenu from './TextSelectionMenu.vue';
// --- Text selection composable integration ---
import { useTextSelection } from '@/composables/useTextSelection';
import { ref as vueRef } from 'vue';

const selectionMenuRef = vueRef<HTMLElement | null>(null);
const {
    showSelectionMenu,
    menuPosition,
    handleTextSelection,
    handleCopy,
    handleShare,
    hideMenu: closeSelectionMenu,
    handleCustomContextMenu,
    handlePointerDown,
    handlePointerUp,
    handlePointerCancel,
    handlePointerMove,
} = useTextSelection(selectionMenuRef);

const props = defineProps<{
    section: Section;
}>();

const normalizedChapterId = computed(() => {
    return getChapterId(props.section);
});

const emit = defineEmits<{
    (e: 'setRef', el: Element | ComponentPublicInstance | null): void;
}>();

const setRef = (el: Element | ComponentPublicInstance | null) => {
    emit('setRef', el);
};
</script>
