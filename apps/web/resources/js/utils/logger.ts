type LogLevel = 'log' | 'info' | 'warn' | 'error' | 'debug';

class Logger {
    private static instance: Logger;
    private isProduction: boolean;

    private constructor() {
        this.isProduction = import.meta.env.MODE === 'production';
    }

    public static getInstance(): Logger {
        if (!Logger.instance) {
            Logger.instance = new Logger();
        }
        return Logger.instance;
    }

    public shouldLog(level: LogLevel): boolean {
        // In production, only show error and warn
        if (this.isProduction) {
            return ['error', 'warn'].includes(level);
        }
        // In development, show all logs
        return true;
    }

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    public debug(...args: any[]): void {
        if (this.shouldLog('debug')) {
            console.debug(...args);
        }
    }

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    public info(...args: any[]): void {
        if (this.shouldLog('info')) {
            console.info(...args);
        }
    }

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    public warn(...args: any[]): void {
        if (this.shouldLog('warn')) {
            console.warn(...args);
        }
    }

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    public error(...args: any[]): void {
        if (this.shouldLog('error')) {
            console.error(...args);
        }
    }

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    public log(...args: any[]): void {
        if (this.shouldLog('log')) {
            console.log(...args);
        }
    }
}

export const logger = Logger.getInstance();
