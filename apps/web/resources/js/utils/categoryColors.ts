import { BookCategory, Mode, StyleType, Weight } from '@esbo/types';

export interface CategoryColorClasses {
    className: string;
}

type OpacityValue = 10 | 20 | 50 | 70 | 80 | undefined;

// Category-specific class mappings
const categoryClassMap: Record<
    StyleType,
    Record<
        Mode,
        Record<
            Weight,
            Partial<Record<BookCategory, string>> & { default: string }
        >
    >
> = {
    text: {
        light: {
            100: {
                [BookCategory.LAW]: 'text-law-100',
                [BookCategory.HISTORY]: 'text-history-100',
                [BookCategory.WISDOM]: 'text-poetic-100',
                [BookCategory.PROPHECY]: 'text-prophecy-100',
                [BookCategory.GOSPEL]: 'text-gospel-100',
                [BookCategory.EPISTLE]: 'text-epistle-100',
                [BookCategory.APOCALYPTIC]: 'text-revelation-100',
                default: 'text-theme-500',
            },
            200: {
                [BookCategory.LAW]: 'text-law-200',
                [BookCategory.HISTORY]: 'text-history-200',
                [BookCategory.WISDOM]: 'text-poetic-200',
                [BookCategory.PROPHECY]: 'text-prophecy-200',
                [BookCategory.GOSPEL]: 'text-gospel-200',
                [BookCategory.EPISTLE]: 'text-epistle-200',
                [BookCategory.APOCALYPTIC]: 'text-revelation-200',
                default: 'text-theme-700',
            },
            500: {
                default: 'text-theme-500',
            },
        },
        dark: {
            100: { default: 'text-theme-100' },
            200: { default: 'text-theme-100' },
            500: { default: 'text-theme-100' },
        },
    },
    bg: {
        light: {
            100: {
                [BookCategory.LAW]: 'bg-law-100',
                [BookCategory.HISTORY]: 'bg-history-100',
                [BookCategory.WISDOM]: 'bg-poetic-100',
                [BookCategory.PROPHECY]: 'bg-prophecy-100',
                [BookCategory.GOSPEL]: 'bg-gospel-100',
                [BookCategory.EPISTLE]: 'bg-epistle-100',
                [BookCategory.APOCALYPTIC]: 'bg-revelation-100',
                default: 'bg-theme-100',
            },
            200: {
                [BookCategory.LAW]: 'bg-law-200',
                [BookCategory.HISTORY]: 'bg-history-200',
                [BookCategory.WISDOM]: 'bg-poetic-200',
                [BookCategory.PROPHECY]: 'bg-prophecy-200',
                [BookCategory.GOSPEL]: 'bg-gospel-200',
                [BookCategory.EPISTLE]: 'bg-epistle-200',
                [BookCategory.APOCALYPTIC]: 'bg-revelation-200',
                default: 'bg-theme-700',
            },
            500: { default: 'bg-theme-100' },
        },
        dark: {
            100: {
                [BookCategory.LAW]: 'bg-law-200',
                [BookCategory.HISTORY]: 'bg-history-200',
                [BookCategory.WISDOM]: 'bg-poetic-200',
                [BookCategory.PROPHECY]: 'bg-prophecy-200',
                [BookCategory.GOSPEL]: 'bg-gospel-200',
                [BookCategory.EPISTLE]: 'bg-epistle-200',
                [BookCategory.APOCALYPTIC]: 'bg-revelation-200',
                default: 'bg-theme-700',
            },
            200: {
                [BookCategory.LAW]: 'bg-law-200',
                [BookCategory.HISTORY]: 'bg-history-200',
                [BookCategory.WISDOM]: 'bg-poetic-200',
                [BookCategory.PROPHECY]: 'bg-prophecy-200',
                [BookCategory.GOSPEL]: 'bg-gospel-200',
                [BookCategory.EPISTLE]: 'bg-epistle-200',
                [BookCategory.APOCALYPTIC]: 'bg-revelation-200',
                default: 'bg-theme-700',
            },
            500: { default: 'bg-theme-700' },
        },
    },
    border: {
        light: {
            100: {
                [BookCategory.LAW]: 'border-law-100',
                [BookCategory.HISTORY]: 'border-history-100',
                [BookCategory.WISDOM]: 'border-poetic-100',
                [BookCategory.PROPHECY]: 'border-prophecy-100',
                [BookCategory.GOSPEL]: 'border-gospel-100',
                [BookCategory.EPISTLE]: 'border-epistle-100',
                [BookCategory.APOCALYPTIC]: 'border-revelation-100',
                default: 'border-theme-100',
            },
            200: {
                [BookCategory.LAW]: 'border-law-200',
                [BookCategory.HISTORY]: 'border-history-200',
                [BookCategory.WISDOM]: 'border-poetic-200',
                [BookCategory.PROPHECY]: 'border-prophecy-200',
                [BookCategory.GOSPEL]: 'border-gospel-200',
                [BookCategory.EPISTLE]: 'border-epistle-200',
                [BookCategory.APOCALYPTIC]: 'border-revelation-200',
                default: 'border-theme-700',
            },
            500: { default: 'border-theme-100' },
        },
        dark: {
            100: {
                [BookCategory.LAW]: 'border-law-200',
                [BookCategory.HISTORY]: 'border-history-200',
                [BookCategory.WISDOM]: 'border-poetic-200',
                [BookCategory.PROPHECY]: 'border-prophecy-200',
                [BookCategory.GOSPEL]: 'border-gospel-200',
                [BookCategory.EPISTLE]: 'border-epistle-200',
                [BookCategory.APOCALYPTIC]: 'border-revelation-200',
                default: 'border-theme-700',
            },
            200: {
                [BookCategory.LAW]: 'border-law-200',
                [BookCategory.HISTORY]: 'border-history-200',
                [BookCategory.WISDOM]: 'border-poetic-200',
                [BookCategory.PROPHECY]: 'border-prophecy-200',
                [BookCategory.GOSPEL]: 'border-gospel-200',
                [BookCategory.EPISTLE]: 'border-epistle-200',
                [BookCategory.APOCALYPTIC]: 'border-revelation-200',
                default: 'border-theme-700',
            },
            500: { default: 'border-theme-700' },
        },
    },
};

// Helper function to get class name
const getClassFromMap = (
    type: StyleType,
    category: BookCategory,
    weight: Weight,
    opacity?: OpacityValue,
): string => {
    const lightClass =
        categoryClassMap[type]['light'][weight][category] ||
        categoryClassMap[type]['light'][weight].default;
    const darkClass =
        categoryClassMap[type]['dark'][weight][category] ||
        categoryClassMap[type]['dark'][weight].default;

    const opacityClass = opacity ? `/${opacity}` : '';
    return `${lightClass}${opacityClass} dark:${darkClass}${opacityClass}`;
};

export const getCategoryColorClasses = (
    type: StyleType,
    category: BookCategory,
    weight: Weight = 100,
    opacity?: OpacityValue,
): string => getClassFromMap(type, category, weight, opacity);

export const getCategoryTextColorClasses = (
    category: BookCategory,
    weight: Weight = 100,
): string => getClassFromMap('text', category, weight);

export const getCategoryBgColorClasses = (
    category: BookCategory,
    weight: 100 | 200 = 100,
    opacity?: OpacityValue,
): string => getClassFromMap('bg', category, weight, opacity);

export const getCategoryBorderColorClasses = (
    category: BookCategory,
    weight: 100 | 200 = 100,
): string => getClassFromMap('border', category, weight);
