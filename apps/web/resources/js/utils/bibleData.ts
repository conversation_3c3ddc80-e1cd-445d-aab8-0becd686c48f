import { ParsedReference } from '@esbo/types';

export const CHAPTERS_COUNT: Record<string, number> = {
    '1. <PERSON><PERSON>': 50,
    '2. <PERSON><PERSON>': 40,
    '3. <PERSON><PERSON>': 27,
    '4. <PERSON><PERSON>': 36,
    '5. <PERSON><PERSON>': 34,
    <PERSON><PERSON><PERSON>: 24,
    <PERSON>: 21,
    <PERSON>: 4,
    '1. <PERSON>': 31,
    '2. <PERSON>': 24,
    '1. <PERSON><PERSON><PERSON><PERSON>': 22,
    '2. <PERSON><PERSON><PERSON><PERSON>': 25,
    '1. Chronik': 29,
    '2. Chronik': 36,
    <PERSON><PERSON><PERSON>: 10,
    <PERSON><PERSON><PERSON>: 13,
    <PERSON><PERSON>: 10,
    <PERSON><PERSON>: 42,
    <PERSON><PERSON><PERSON>: 150,
    <PERSON><PERSON><PERSON><PERSON><PERSON>: 31,
    <PERSON><PERSON><PERSON>: 12,
    <PERSON><PERSON><PERSON>: 8,
    <PERSON><PERSON><PERSON>: 66,
    <PERSON><PERSON><PERSON>: 52,
    <PERSON><PERSON><PERSON><PERSON>: 5,
    <PERSON><PERSON><PERSON><PERSON>: 48,
    <PERSON>: 12,
    <PERSON><PERSON>: 14,
    <PERSON>: 4,
    <PERSON>: 9,
    <PERSON><PERSON><PERSON>: 1,
    <PERSON><PERSON>: 4,
    <PERSON><PERSON>: 7,
    <PERSON><PERSON>: 3,
    <PERSON><PERSON><PERSON><PERSON>: 3,
    <PERSON><PERSON><PERSON><PERSON>: 3,
    <PERSON><PERSON><PERSON>: 2,
    <PERSON><PERSON><PERSON><PERSON>: 14,
    <PERSON><PERSON>: 3,
    <PERSON><PERSON><PERSON><PERSON>: 28,
    <PERSON>: 16,
    <PERSON><PERSON>: 24,
    <PERSON>: 21,
    <PERSON><PERSON><PERSON><PERSON><PERSON>: 28,
    <PERSON><PERSON><PERSON>: 16,
    '1. <PERSON><PERSON><PERSON>': 16,
    '2. <PERSON><PERSON><PERSON>': 13,
    <PERSON><PERSON>: 6,
    <PERSON><PERSON><PERSON>: 6,
    <PERSON><PERSON>: 4,
    <PERSON><PERSON><PERSON>: 4,
    '1. <PERSON><PERSON><PERSON><PERSON>r': 5,
    '2. <PERSON><PERSON><PERSON><PERSON><PERSON>': 3,
    '1. <PERSON><PERSON><PERSON>': 6,
    '2. <PERSON><PERSON>': 4,
    <PERSON>: 3,
    <PERSON><PERSON>: 1,
    <PERSON><PERSON><PERSON><PERSON><PERSON>: 13,
    <PERSON><PERSON>: 5,
    '1. <PERSON><PERSON>': 5,
    '2. <PERSON><PERSON>': 3,
    '1. <PERSON>': 5,
    '2. <PERSON>': 1,
    '3. <PERSON>': 1,
    <PERSON><PERSON>: 1,
    <PERSON><PERSON><PERSON><PERSON>: 22,
};

// <PERSON> of book names to s<PERSON><PERSON>
export con<PERSON> <PERSON>OOK_SLUGS: Record<string, string> = {
    '1. Mose': '1.Mose',
    '2. Mose': '2.Mose',
    '3. Mose': '3.Mose',
    '4. Mose': '4.Mose',
    '5. Mose': '5.Mose',
    Josua: 'Josua',
    Richter: 'Richter',
    Ruth: 'Ruth',
    '1. Samuel': '1.Samuel',
    '2. Samuel': '2.Samuel',
    '1. Könige': '1.Könige',
    '2. Könige': '2.Könige',
    '1. Chronik': '1.Chronik',
    '2. Chronik': '2.Chronik',
    Esra: 'Esra',
    Nehemia: 'Nehemia',
    Ester: 'Ester',
    Hiob: 'Hiob',
    Psalm: 'Psalm',
    Sprüche: 'Sprueche',
    Prediger: 'Prediger',
    Hohelied: 'Hohelied',
    Jesaja: 'Jesaja',
    Jeremia: 'Jeremia',
    Klagelieder: 'Klagelieder',
    Hesekiel: 'Hesekiel',
    Daniel: 'Daniel',
    Hosea: 'Hosea',
    Joel: 'Joel',
    Amos: 'Amos',
    Obadja: 'Obadja',
    Jona: 'Jona',
    Micha: 'Micha',
    Nahum: 'Nahum',
    Habakuk: 'Habakuk',
    Zefanja: 'Zefanja',
    Haggai: 'Haggai',
    Sacharja: 'Sacharja',
    Maleachi: 'Maleachi',
    Matthäus: 'Matthäus',
    Markus: 'Markus',
    Lukas: 'Lukas',
    Johannes: 'Johannes',
    Apostelgeschichte: 'Apostelgeschichte',
    Römer: 'Römer',
    '1. Korinther': '1.Korinther',
    '2. Korinther': '2.Korinther',
    Galater: 'Galater',
    Epheser: 'Epheser',
    Philipper: 'Philipper',
    Kolosser: 'Kolosser',
    '1. Thessalonicher': '1.Thessalonicher',
    '2. Thessalonicher': '2.Thessalonicher',
    '1. Timotheus': '1.Timotheus',
    '2. Timotheus': '2.Timotheus',
    Titus: 'Titus',
    Philemon: 'Philemon',
    Hebräer: 'Hebräer',
    Jakobus: 'Jakobus',
    '1. Petrus': '1.Petrus',
    '2. Petrus': '2.Petrus',
    '1. Johannes': '1.Johannes',
    '2. Johannes': '2.Johannes',
    '3. Johannes': '3.Johannes',
    Judas: 'Judas',
    Offenbarung: 'Offenbarung',
};

/**
 * Validates if a reference points to a valid book and chapter combination
 */
export function isValidReference(reference: ParsedReference): boolean {
    const { book, chapter } = reference.reference;
    if (!book || !CHAPTERS_COUNT[book]) return false;
    if (!chapter) return false;
    return chapter > 0 && chapter <= CHAPTERS_COUNT[book];
}
