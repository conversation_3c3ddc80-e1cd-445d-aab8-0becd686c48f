import { describe, it, expect } from 'vitest';
import { addSpace, needsSpaceAfterGroup } from '@/utils/spacing';
import type { Word, WordGroup } from '@esbo/types';

// Helper function to create a simple word for testing
const createWord = (text: string, textAfter = ''): Word => ({
    text,
    textAfter,
    isAddition: false,
    isFootnote: false,
    isVariant: false,
    wordType: null,
    wordGroupId: null,
    variantGroupId: null,
    footnoteGroupId: null,
    footnote: null,
    paragraphGroupId: null,
    paragraphStyle: 'p',
    verseNumber: 1,
    isOtQuote: false,
    otQuoteGroupId: null,
    isEmphasized: false,
    position: 0,
});

// Helper function to create a word group for testing
const createWordGroup = (words: Word[]): WordGroup => ({
    words,
    wordGroupId: null,
    isVariant: false,
    isAddition: false,
    isOtQuote: false,
    wordType: null,
    variantGroupId: null,
    paragraphStyle: 'p',
});

describe('spacing utilities', () => {
    describe('addSpace', () => {
        it('should not add space after the last word', () => {
            const words = [createWord('word1'), createWord('word2')];
            expect(addSpace(1, words)).toBe('');
        });

        it('should not add space before punctuation', () => {
            const words = [createWord('Hello'), createWord(',', ' world')];
            expect(addSpace(0, words)).toBe('');
        });

        it('should add space after word ending with bracket', () => {
            const words = [createWord('word', ']'), createWord('next')];
            expect(addSpace(0, words)).toBe(' ');
        });

        it('should add space before word starting with bracket', () => {
            const words = [createWord('word'), createWord('[note]')];
            expect(addSpace(0, words)).toBe(' ');
        });

        it('should add space between regular words', () => {
            const words = [createWord('Hello'), createWord('world')];
            expect(addSpace(0, words)).toBe(' ');
        });
    });

    describe('needsSpaceAfterGroup', () => {
        it('should not add space after the last group', () => {
            const groups = [
                createWordGroup([createWord('Group'), createWord('1')]),
                createWordGroup([createWord('Group'), createWord('2')])
            ];
            expect(needsSpaceAfterGroup(groups, 1)).toBe(false);
        });

        it('should not add space before punctuation', () => {
            const groups = [
                createWordGroup([createWord('Hello')]),
                createWordGroup([createWord(',', ' world')])
            ];
            expect(needsSpaceAfterGroup(groups, 0)).toBe(false);
        });

        it('should add space after group ending with bracket', () => {
            const groups = [
                createWordGroup([createWord('word', ']')]),
                createWordGroup([createWord('next')])
            ];
            expect(needsSpaceAfterGroup(groups, 0)).toBe(true);
        });

        it('should add space before group starting with bracket', () => {
            const groups = [
                createWordGroup([createWord('word')]),
                createWordGroup([createWord('[note]')])
            ];
            expect(needsSpaceAfterGroup(groups, 0)).toBe(true);
        });

        it('should add space between regular word groups', () => {
            const groups = [
                createWordGroup([createWord('Hello')]),
                createWordGroup([createWord('world')])
            ];
            expect(needsSpaceAfterGroup(groups, 0)).toBe(true);
        });

        it('should handle empty groups', () => {
            const groups = [
                createWordGroup([]),
                createWordGroup([createWord('word')])
            ];
            expect(needsSpaceAfterGroup(groups, 0)).toBe(false);
        });
    });
});
