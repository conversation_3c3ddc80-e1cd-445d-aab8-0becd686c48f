// Utility to group words by paragraph_group_id (or fallback to verse grouping)
import type { ChapterSection, Verse, WordParagraphGroup } from '@esbo/types';

/**
 * Groups all words in a chapter by their paragraph_group_id (word-level),
 * falling back to verse-level if not present.
 * Returns an array of groups for rendering, with all metadata needed.
 */
export function groupWordsByParagraphGroup(verses: Verse[]): WordParagraphGroup[] {
    const groupMap = new Map<string, WordParagraphGroup>();
    const groupOrder: string[] = [];

    for (const verse of verses) {
        // If we have wordGroups, use them directly
        if (verse.wordGroups && verse.wordGroups.length > 0) {
            for (const wordGroup of verse.wordGroups) {
                // Skip empty word groups
                if (!wordGroup.words || wordGroup.words.length === 0) {
                    continue;
                }

                const groupId = wordGroup.words[0]?.paragraphGroupId ||
                               wordGroup.paragraphStyle ||
                               `single_${verse.number}`;

                if (!groupMap.has(groupId)) {
                    groupMap.set(groupId, {
                        paragraphGroupId: groupId,
                        paragraphStyle: wordGroup.paragraphStyle || verse.paragraphStyle || null,
                        words: [],
                        verseNumbers: [],
                        isPericopeStart: verse.isPericopeStart || false,
                    });
                    groupOrder.push(groupId);
                }

                const group = groupMap.get(groupId)!;
                group.words.push(...wordGroup.words);
                if (!group.verseNumbers.includes(verse.number)) {
                    group.verseNumbers.push(verse.number);
                }
            }
        }
        // Fallback to using words array if no wordGroups
        else if (Array.isArray(verse.words) && verse.words.length > 0) {
            const groupId = `verse_${verse.number}`;

            if (!groupMap.has(groupId)) {
                groupMap.set(groupId, {
                    paragraphGroupId: groupId,
                    paragraphStyle: verse.paragraphStyle || null,
                    words: [],
                    verseNumbers: [verse.number],
                    isPericopeStart: verse.isPericopeStart || false,
                });
                groupOrder.push(groupId);
            }

            const group = groupMap.get(groupId)!;
            group.words.push(...verse.words);
        }
    }

    return groupOrder
        .map((groupId) => groupMap.get(groupId)!)
        .filter((group): group is WordParagraphGroup => group !== undefined);
}

/**
 * Returns true if the chapter contains any poetic paragraph styles (q, q1, q2, ...)
 * @param chapter The chapter section to check
 * @returns boolean indicating if the chapter contains poetic paragraph styles
 */
export function isPoetryChapter(chapter: ChapterSection): boolean {
    if (!chapter?.verses?.length) return false;

    for (const verse of chapter.verses) {
        // Use wordGroups if available, otherwise fall back to words array
        const wordGroups = verse.wordGroups || [{
            words: Array.isArray(verse.words) ? verse.words : [],
            paragraphStyle: verse.paragraphStyle || null,
        }];

        for (const group of wordGroups) {
            // Check paragraph style at the group level first (more efficient)
            if (group.paragraphStyle && /^q\d?$/.test(group.paragraphStyle)) {
                return true;
            }

            // Fallback: check individual words if needed
            if (group.words && Array.isArray(group.words)) {
                for (const word of group.words) {
                    if (word?.paragraphStyle && /^q\d?$/.test(word.paragraphStyle)) {
                        return true;
                    }
                }
            }
        }
    }
    return false;
}
