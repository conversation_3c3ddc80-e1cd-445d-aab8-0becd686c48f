import type { Word, WordGroup } from '@esbo/types';

/**
 * Determines if a space should be added after a word based on punctuation rules.
 * @param {number} index - The index of the current word.
 * @param {Word[]} words - The array of words in the group.
 * @returns {string} A space if needed, otherwise an empty string.
 */
export function addSpace(index: number, words: Word[]): string {
    // Don't add space if this is the last word in the group
    if (index === words.length - 1) return '';

    const currentWord = words[index];
    const nextWord = words[index + 1];

    // Get the combined text for the current and next words
    const currentText = currentWord.text + (currentWord.textAfter || '');
    const nextText = nextWord.text;

    // Don't add space if the next word starts with certain punctuation
    if (/^[.,;:!?'")\]}]/.test(nextText)) return '';

    // Add space if current word ends with ']' and next word doesn't start with punctuation
    if (currentText.endsWith(']')) return ' ';

    // Add space if next word starts with '[' and current doesn't end with punctuation
    if (nextText.startsWith('[') && !/[.,;:!?'")\]}]$/.test(currentText)) return ' ';

    // Default case: add a space
    return ' ';
}

/**
 * Determines if a space is needed after a word group based on punctuation rules
 * and the next group's characteristics.
 * @param {WordGroup[]} groups - The array of all word groups.
 * @param {number} groupIndex - The index of the current group.
 * @returns {boolean} Whether a space is needed after this group.
 */
export function needsSpaceAfterGroup(groups: WordGroup[], groupIndex: number): boolean {
    // Don't add space after the last group
    if (groupIndex === groups.length - 1) return false;

    const currentGroup = groups[groupIndex];
    const nextGroup = groups[groupIndex + 1];

    // If either group has no words, no space needed
    if (!currentGroup.words.length || !nextGroup.words.length) return false;

    const lastWord = currentGroup.words[currentGroup.words.length - 1];
    const firstWord = nextGroup.words[0];

    // Get the combined text for the current and next words
    const currentText = lastWord.text + (lastWord.textAfter || '');
    const nextText = firstWord.text;

    // Check if current text ends with punctuation that doesn't need a space after it
    const endsWithNoSpacePunctuation = /[.,;:!?'")\]}]$/.test(currentText);
    
    // Check if next text starts with punctuation that doesn't need a space before it
    const startsWithNoSpacePunctuation = /^[.,;:!?'")\]}]/.test(nextText);

    // Check for bracket cases
    const currentEndsWithBracket = currentText.endsWith(']');
    const nextStartsWithBracket = nextText.startsWith('[');

    // Special cases where we want to force a space:
    // 1. Current ends with ] and next doesn't start with punctuation
    // 2. Next starts with [ and current doesn't end with punctuation
    const needsBracketSpace =
        (currentEndsWithBracket && !startsWithNoSpacePunctuation) ||
        (nextStartsWithBracket && !endsWithNoSpacePunctuation);

    // Return true (need space) unless specific conditions are met
    return needsBracketSpace || !(endsWithNoSpacePunctuation || startsWithNoSpacePunctuation);
}

/**
 * Returns the space that should be added after a word, if any.
 * This is a convenience wrapper around addSpace for use in templates.
 * @param {number} index - The index of the current word.
 * @param {Word[]} words - The array of words in the group.
 * @returns {string} A space if needed, otherwise an empty string.
 */
export function getSpace(index: number, words: Word[]): string {
    // Don't add space after the last word in a group
    if (index === words.length - 1) return '';
    return addSpace(index, words);
}
