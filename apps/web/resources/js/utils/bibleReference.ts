/**
 * Bible reference utilities for parsing and formatting Bible references
 */

/**
 * Maps German book abbreviations to their full slugs
 */
const BOOK_ABBREVIATIONS: Record<string, string> = {
    '1. Mose': '1Mose',
    '2. <PERSON><PERSON>': '2Mose',
    '3. <PERSON><PERSON>': '3Mose',
    '4. <PERSON><PERSON>': '4Mose',
    '5. <PERSON><PERSON>': '5Mose',
    '1. <PERSON>': '1Samuel',
    '2. <PERSON>': '2Samuel',
    '1. <PERSON><PERSON><PERSON>': '1Könige',
    '2. <PERSON><PERSON><PERSON>': '2Könige',
    '1. Chr': '1Chronik',
    '2. Chr': '2Chronik',
    '1. Kor': '1Korinther',
    '2. <PERSON><PERSON>': '2Korinther',
    '1. Thess': '1Thessalonicher',
    '2. Thess': '2Thessalonicher',
    '1. <PERSON>': '1Timotheus',
    '2. <PERSON>': '2Timotheus',
    '1. <PERSON><PERSON>': '1P<PERSON><PERSON>',
    '2. <PERSON><PERSON>': '2<PERSON><PERSON><PERSON>',
    '1. <PERSON><PERSON>': '1<PERSON><PERSON><PERSON><PERSON>',
    '2. <PERSON><PERSON>': '2<PERSON><PERSON><PERSON><PERSON>',
    '3. <PERSON><PERSON>': '3<PERSON><PERSON><PERSON><PERSON>',
    '<PERSON><PERSON>': '<PERSON><PERSON><PERSON>',
    '<PERSON>r': '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON>': '<PERSON><PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>': 'He<PERSON><PERSON><PERSON>',
    '<PERSON>': '<PERSON>',
    '<PERSON><PERSON>': '<PERSON><PERSON>',
    '<PERSON>': '<PERSON>',
    '<PERSON>': '<PERSON>',
    '<PERSON>b<PERSON>': '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON>': '<PERSON><PERSON>',
    '<PERSON>': '<PERSON><PERSON>',
    '<PERSON>': '<PERSON><PERSON>',
    '<PERSON>b': '<PERSON><PERSON><PERSON><PERSON>',
    '<PERSON><PERSON>': 'Zefanja',
    'Hag': 'Haggai',
    'Sach': 'Sacharja',
    'Mal': 'Maleachi',
    'Mt': 'Matthaeus',
    'Mk': 'Markus',
    'Lk': 'Lukas',
    'Joh': 'Johannes',
    'Apg': 'Apostelgeschichte',
    'Röm': 'Römer',
    'Gal': 'Galater',
    'Eph': 'Epheser',
    'Phil': 'Philipper',
    'Kol': 'Kolosser',
    '1Thess': '1Thessalonicher',
    '2Thess': '2Thessalonicher',
    '1Tim': '1Timotheus',
    '2Tim': '2Timotheus',
    'Tit': 'Titus',
    'Phlm': 'Philemon',
    'Hebr': 'Hebraeer',
    'Jak': 'Jakobus',
    '1Petr': '1Petrus',
    '2Petr': '2Petrus',
    '1Joh': '1Johannes',
    '2Joh': '2Johannes',
    '3Joh': '3Johannes',
    'Jud': 'Judas',
    'Offb': 'Offenbarung',
};

/**
 * Parses a Bible reference string into its components
 * @param refStr - The reference string (e.g., "Tit 1,4", "V. 30")
 * @param currentBook - Optional current book context for relative references
 * @returns Object with book, chapter, and verse components
 */
export function parseBibleReference(
    refStr: string,
    currentBook?: string
): { book: string | null; chapter: number | null; verse: number | null } {
    // Trim and normalize spaces
    refStr = refStr.replace(/\s+/g, ' ').trim();

    // Handle verse-only references (e.g., "V. 30")
    const verseOnlyMatch = refStr.match(/^(?:V\.?\s*)(\d+)$/i);
    if (verseOnlyMatch && currentBook) {
        return {
            book: currentBook,
            chapter: null, // Will be filled in by the component
            verse: parseInt(verseOnlyMatch[1], 10),
        };
    }


    // Handle book chapter,verse format (e.g., "Tit 1,4", "1. Mose 1,1")
    const bookMatch = refStr.match(
        // eslint-disable-next-line no-useless-escape
        /^((?:\d+\.\s*)?[A-Za-zäöüßÄÖÜ]+)(?:\s+(\d+)(?:[,\.]\s*(\d+))?)?/
    );

    if (bookMatch) {
        let bookName = bookMatch[1].trim();
        const chapter = bookMatch[2] ? parseInt(bookMatch[2], 10) : null;
        const verse = bookMatch[3] ? parseInt(bookMatch[3], 10) : null;

        // Look up the book slug
        const bookSlug = BOOK_ABBREVIATIONS[bookName] ||
                        Object.entries(BOOK_ABBREVIATIONS).find(
                            ([abbr]) => abbr.toLowerCase() === bookName.toLowerCase()
                        )?.[1];

        if (bookSlug) {
            return { book: bookSlug, chapter, verse };
        } else if (chapter !== null) {
            // If we have a chapter but couldn't map the book, return the original book name
            return { book: bookName.toLowerCase().replace(/\s+/g, '-'), chapter, verse };
        }
    }

    // Return null values if parsing fails
    return { book: null, chapter: null, verse: null };
}

/**
 * Converts a Bible reference to a route path
 * @param ref - The reference object with book, chapter, and verse
 * @param _currentBook - Optional current book context for relative references
 * @returns The route path (e.g., "/Titus1,4")
 */
export function referenceToPath(
    ref: { book: string | null; chapter: number | null; verse: number | null },
    _currentBook?: string
): string {
    if (!ref.book || ref.chapter === null) {
        return '';
    }

    // If we have a verse, include it in the path
    if (ref.verse !== null) {
        return `/${ref.book}${ref.chapter},${ref.verse}`;
    }

    // Otherwise, just include book and chapter
    return `/${ref.book}${ref.chapter}`;
}

/**
 * Formats a Bible reference as a display string
 * @param ref - The reference object with book, chapter, and verse
 * @returns Formatted reference string (e.g., "Titus 1,4")
 */
export function formatBibleReference(ref: {
    book: string;
    chapter: number | null;
    verse: number | null;
}): string {
    if (!ref.book) return '';

    // Format the book name (convert slug to title case)
    const bookName = ref.book
        .split('-')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');

    if (ref.chapter === null) {
        return bookName;
    }

    if (ref.verse === null) {
        return `${bookName} ${ref.chapter}`;
    }

    return `${bookName} ${ref.chapter},${ref.verse}`;
}
