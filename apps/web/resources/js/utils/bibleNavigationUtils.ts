import { logger } from '@/utils/logger';
import type {
    BibleReference,
    ChapterSection,
    ChapterWindow,
    FrontmatterSection,
    ParsedReference,
    Section,
} from '@esbo/types';
import { router } from '@inertiajs/vue3';
import { CHAPTERS_COUNT } from './bibleData';

/**
 * Type guard to check if a section is a frontmatter section
 * @param section The section to check
 * @returns True if the section is a frontmatter section, false otherwise
 */
export function isFrontmatterSection(
    section: Section,
): section is FrontmatterSection {
    return section.type === 'frontmatter';
}

/**
 * Type guard to check if a section is a chapter section
 * @param section The section to check
 * @returns True if the section is a chapter section, false otherwise
 */
export function isChapterSection(section: Section): section is ChapterSection {
    return (
        section.type === 'chapter-current' ||
        section.type === 'chapter-previous' ||
        section.type === 'chapter-next' ||
        section.type === 'chapter-next-next'
    );
}

/**
 * Get the ID for a chapter section
 * @param chapter The chapter section
 * @param normalize Whether to normalize special characters like umlauts
 * @returns The chapter ID in the format bookSlug + chapterNumber (e.g., "Johannes1")
 */
export function getChapterId(
    chapter: Section,
    normalize: boolean = false,
): string {
    const id =
        chapter.number > 0
            ? `${chapter.book.slug}${chapter.number}`
            : chapter.book.slug;

    // Normalize the ID to handle special characters like umlauts if requested
    return normalize ? id.normalize('NFD').replace(/[\u0300-\u036f]/g, '') : id;
}

/**
 * Create a chapter window from a set of sections
 * @param {Section[]} sections - The sections to create the window from
 * @param {number} targetChapter - The target chapter number
 * @returns {ChapterWindow} The created chapter window
 * @throws {Error} If no chapter sections are found for the target chapter
 */
export function createChapterWindowFromSections(
    sections: Section[],
    targetChapter: number,
): ChapterWindow {
    // Initialize with undefined values for optional properties
    let current: ChapterSection | undefined;
    let previous: ChapterSection | undefined;
    let next: ChapterSection | undefined;
    let nextNext: ChapterSection | undefined;
    let frontmatter: FrontmatterSection | undefined;

    // Filter and categorize sections
    for (const section of sections) {
        if (section.type === 'frontmatter') {
            frontmatter = section as FrontmatterSection;
            continue;
        }

        if (!isChapterSection(section)) continue;

        // Process chapter sections
        if (section.number === targetChapter) {
            current = section;
        } else if (section.number === targetChapter - 1) {
            previous = section;
        } else if (section.number === targetChapter + 1) {
            next = section;
        } else if (section.number === targetChapter + 2) {
            nextNext = section;
        }
    }

    // Ensure we have a current chapter
    if (!current) {
        logger.error('Could not find target chapter in sections', {
            targetChapter,
            availableSections: sections.map((s) =>
                isChapterSection(s) ? getChapterId(s) : s.id,
            ),
        });

        // Try to find the closest chapter
        const chapterSections = sections.filter((s) =>
            isChapterSection(s),
        ) as ChapterSection[];

        if (chapterSections.length > 0) {
            // Sort by how close they are to the target
            chapterSections.sort(
                (a, b) =>
                    Math.abs(a.number - targetChapter) -
                    Math.abs(b.number - targetChapter),
            );

            current = chapterSections[0];
            logger.warn('Using closest chapter as current', {
                targetChapter,
                usingChapter: current.number,
            });
        } else {
            throw new Error(
                `No chapter sections found for target chapter ${targetChapter}`,
            );
        }
    }

    return {
        current,
        previous,
        next,
        nextNext,
        frontmatter,
    };
}

/**
 * Interface for URL update options
 */
export interface UrlUpdateOptions {
    book: string;
    chapter: number | null;
    verse?: number | null;
    verseEnd?: number | null;
}

/**
 * Update the URL based on the current Bible navigation state
 * @param {UrlUpdateOptions} options - URL update options containing book, chapter, and optional verses
 * @param {boolean} replaceOnly - If true, only replace the URL without navigation
 * @returns {string} The generated URL
 */
export function updateUrl(
    options: UrlUpdateOptions,
    replaceOnly: boolean = true,
): string {
    const { book, chapter, verse, verseEnd } = options;

    if (!book) {
        logger.error('Book slug is required for URL updates');
        return '';
    }

    // Build the URL path
    let url = '';

    // Handle frontmatter (chapter is null or 0) vs regular chapter
    if (chapter === null || chapter === 0) {
        url = `/${book}`;
    } else {
        url = `/${book}${chapter}`;

        // Add verse to URL if available
        if (verse) {
            url += `/${verse}`;
            // Add verse range if available
            if (verseEnd && verseEnd !== verse) {
                url += `-${verseEnd}`;
            }
        }
    }

    if (replaceOnly) {
        // Replace URL without navigation
        window.history.replaceState({}, '', url);
    } else {
        // Navigate using Inertia router
        router.visit(url, {
            preserveState: true,
            preserveScroll: true,
            replace: true,
        });
    }

    return url;
}

export function parse(reference: string): ParsedReference {
    const result: BibleReference = {
        book: '',
        chapter: undefined,
        verseStart: undefined,
        verseEnd: undefined,
    };

    // Extract verse range if present (e.g., "3:16-18" or "3,16-18")
    const verseMatch = reference.match(/[,.](\d+)(?:-(\d+))?$/);
    if (verseMatch) {
        result.verseStart = parseInt(verseMatch[1]);
        result.verseEnd = verseMatch[2]
            ? parseInt(verseMatch[2])
            : result.verseStart;
        // Remove verse part from reference
        reference = reference.replace(/[,.]\d+(?:-\d+)?$/, '');
    }

    // Extract chapter if present at the end of the string
    const chapterMatch = reference.match(/(\d+)$/);
    if (chapterMatch) {
        result.chapter = parseInt(chapterMatch[1]);
        // Remove chapter number from reference
        reference = reference.replace(/\d+$/, '');
    }

    // What remains should be the book name
    const bookName = reference.trim();
    if (bookName && CHAPTERS_COUNT[bookName]) {
        result.book = bookName;
    }

    // Determine the type based on what we found
    let type: 'book' | 'chapter' | 'verse' | 'verse-range' = 'book';
    if (result.verseStart !== undefined) {
        type = result.verseEnd !== undefined ? 'verse-range' : 'verse';
    } else if (result.chapter !== undefined) {
        type = 'chapter';
    }

    return {
        type,
        reference: result,
    };
}
