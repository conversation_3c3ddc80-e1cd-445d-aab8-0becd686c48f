import { Book } from './bible';
import { User } from './user';

/** Represents a search result */
export interface SearchResult {
    id: number; // Unique identifier for the search result
    name: string; // Name of the search result
}

/** Represents search properties */
export interface SearchProps {
    results?: Array<{ id: number; name: string }>; // Optional array of search results
    searchQuery?: string; // Optional search query
}

/** Represents page properties */
export interface PageProps<
    T extends Record<string, unknown> = Record<string, unknown>,
> extends Record<string, unknown> {
    auth: {
        user: User;
    };
    ziggy?: {
        location: string;
    };
    books?: {
        sections: Array<{
            books: Book[];
        }>;
    };
    props?: T;
    flash?: {
        message?: string;
        success?: boolean;
    };
}
