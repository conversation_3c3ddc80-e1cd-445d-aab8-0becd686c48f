/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_APP_NAME: string;
  readonly VITE_APP_ENV: string;
  // Add other environment variables here
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}

/**
 * Application version injected by Vite from package.json
 * This is defined in vite.config.ts using the define option
 */
declare const __APP_VERSION__: string;

declare global {
  // This augments the global scope
  const __APP_VERSION__: string;
}
