declare module '@pinia/testing' {
    import { Pinia } from 'pinia';

    export interface TestingOptions {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        createSpy?: (fn?: (...args: any[]) => any) => (...args: any[]) => any;
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        initialState?: Record<string, any>;
        stubActions?: boolean;
        stubPatch?: boolean;
    }

    export function createTestingPinia(options?: TestingOptions): Pinia;
}
