@import 'tailwindcss';

@plugin '@tailwindcss/forms';
@plugin '@tailwindcss/typography';
@plugin 'tailwind-scrollbar' {
    nocompatible: true;
}

/* Define dark mode based on data-mode */
@custom-variant dark (&:where([data-mode=dark], [data-mode=dark] *));

@theme {
    --font-sans:
        franklin-gothic-urw, ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji',
        'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
    --font-calluna: calluna, serif;
    --font-thanatos: ThanatosText, serif;
    --font-franklin: franklin-gothic-urw, sans-serif;

    /* Remove default colors */
    --color-*: initial;

    --color-theme: #fff;

    /* Default <PERSON>le */
    --color-theme-50: oklch(0.985 0.002 247.839);
    --color-theme-100: oklch(0.967 0.003 264.542);
    --color-theme-200: oklch(0.928 0.006 264.531);
    --color-theme-300: oklch(0.872 0.01 258.338);
    --color-theme-400: oklch(0.707 0.022 261.325);
    --color-theme-500: oklch(0.551 0.027 264.364);
    --color-theme-600: oklch(0.446 0.03 256.802);
    --color-theme-700: oklch(0.373 0.034 259.733);
    --color-theme-800: oklch(0.278 0.033 256.848);
    --color-theme-900: oklch(0.21 0.034 264.665);
    --color-theme-950: oklch(0.13 0.028 261.692);

    --color-white: #fff;
    --color-black: #3f4244;

    /* Bible category colors */
    --color-law-200: #8b4513;
    --color-law-100: #d2691e;

    --color-history-100: #9eaaa0;
    --color-history-200: #60726a;

    --color-poetic-200: #00829b;
    --color-poetic-100: #00b5cc;

    --color-prophecy-100: #54c2b1;
    --color-prophecy-200: #004d42;

    --color-gospel-200: #1a3a5f;
    --color-gospel-100: #0077a0;

    --color-acts-100: #67b9d1;
    --color-acts-200: #006da6;

    --color-epistle-200: #008675;
    --color-epistle-100: #9dcdcb;

    --color-revelation-200: #8c7842;
    --color-revelation-100: #ffffff;
}

/* Custom Animations */
@theme {
    --animate-wiggle: wiggle 1s ease-in-out infinite;
    @keyframes wiggle {
        0%,
        100% {
            transform: rotate(-10deg);
        }
        50% {
            transform: rotate(10deg);
        }
    }
}

/* Variables of Variables */
@theme inline {
    --color-type-book: var(--color-epistle-100);
    --color-type-verse: var(--color-history-200);
    --color-type-word: var(--color-poetic-200);
    --color-type-metadata: var(--color-gospel-100);
    --color-type-footnote: var(--color-acts-100);

    --color-notify-bar: var(--color-epistle-200);

    --color-surface: var(--color-theme-50);
    --color-text: var(--color-theme-900);
    --color-background: var(--color-theme-50);
    --color-foreground: var(--color-theme-900);
}

@layer base {
    *,
    ::after,
    ::before,
    ::backdrop,
    ::file-selector-button {
        border-color: var(--color-gray-200, currentColor);
    }

    /* Sentry Feedback button styling */
    #sentry-feedback {
        --inset: auto 0px 30px auto;
    }
    @media (max-width: 768px) {
        #sentry-feedback {
            --inset: auto 0px 56px auto;
        }
    }
}

@layer base {
    /* Papyrus theme - warm, aged paper look */
    [data-theme='papyrus'] {
        --color-theme: var(--color-theme-50);

        --color-background: var(--color-theme-50);
        --color-foreground: var(--color-theme-900);

        --color-theme-50: oklch(0.985 0.02 75);
        --color-theme-100: oklch(0.95 0.035 74);
        --color-theme-200: oklch(0.9 0.05 73);
        --color-theme-300: oklch(0.85 0.065 72);
        --color-theme-400: oklch(0.75 0.08 71);
        --color-theme-500: oklch(0.65 0.085 70);
        --color-theme-600: oklch(0.55 0.08 69);
        --color-theme-700: oklch(0.45 0.075 68);
        --color-theme-800: oklch(0.35 0.07 67);
        --color-theme-900: oklch(0.25 0.065 66);
        --color-theme-950: oklch(0.15 0.05 65);

        --color-white: #fff;
        --color-black: var(--color-theme-950);
        --color-surface: var(--color-theme-50);
        --color-text: var(--color-theme-900);

        --color-type-book: var(--color-epistle-100);
        --color-type-verse: var(--color-history-200);
        --color-type-word: var(--color-poetic-200);
        --color-type-metadata: var(--color-gospel-100);
        --color-type-footnote: var(--color-revelation-100);

        --color-notify-bar: var(--color-acts-text);

        /* Bible category colors */
        --color-law-200: #8b5e3c;
        --color-law-100: #c17f59;

        --color-history-100: #c9b18f;
        --color-history-200: #5d4a2e;

        --color-poetic-100: #b89a6c;
        --color-poetic-200: #3d2e13;

        --color-prophecy-100: #d5bc8d;
        --color-prophecy-200: #6b5730;

        --color-gospel-100: #a68a5b;
        --color-gospel-200: #2e2311;

        --color-acts-100: #c4a97a;
        --color-acts-200: #5c4824;

        --color-epistle-100: #b29864;
        --color-epistle-200: #3b2f18;

        --color-revelation-100: #d8c297;
        --color-revelation-200: #4e3c1e;
    }

    /* High contrast black and white theme */
    [data-theme='high-contrast'] {
        --color-theme: var(--color-theme-50);

        --color-background: var(--color-theme-50);
        --color-foreground: var(--color-theme-900);

        /* High Contrast Theme */
        --color-theme-50: oklch(0.99 0.01 220);
        --color-theme-100: oklch(0.95 0.02 220);
        --color-theme-200: oklch(0.9 0.04 220);
        --color-theme-300: oklch(0.8 0.1 220);
        --color-theme-400: oklch(0.7 0.15 220);
        --color-theme-500: oklch(0.55 0.2 220);
        --color-theme-600: oklch(0.4 0.22 220);
        --color-theme-700: oklch(0.3 0.2 220);
        --color-theme-800: oklch(0.2 0.15 220);
        --color-theme-900: oklch(0.1 0.1 220);
        --color-theme-950: oklch(0.05 0.05 220);

        --color-white: #fff;
        --color-black: var(--color-theme-950);

        --color-surface: var(--color-theme-50);
        --color-text: var(--color-theme-900);

        --color-type-book: var(--color-epistle-100);
        --color-type-verse: var(--color-history-200);
        --color-type-word: var(--color-poetic-100);
        --color-type-metadata: var(--color-gospel-200);
        --color-type-footnote: var(--color-revelation-200);

        --color-notify-bar: var(--color-acts-100);

        /* Bible category colors */
        --color-law-200: #000000;
        --color-law-100: #ffffff;

        --color-history-200: #000000;
        --color-history-100: #ffffff;

        --color-poetic-200: #000000;
        --color-poetic-100: #ffffff;

        --color-prophecy-200: #000000;
        --color-prophecy-100: #ffffff;

        --color-gospel-200: #000000;
        --color-gospel-100: #ffffff;

        --color-acts-200: #000000;
        --color-acts-100: #ffffff;

        --color-epistle-200: #000000;
        --color-epistle-100: #ffffff;

        --color-revelation-200: #000000;
        --color-revelation-100: #ffffff;
    }

    /* Dark mode overrides for each theme */
    .dark[data-mode='dark'][data-theme='papyrus'] {
        --color-theme: var(--color-theme-50);

        --color-background: var(--color-theme-50);
        --color-foreground: var(--color-theme-900);

        /* Bible category colors - Darker versions */
        --color-law-200: oklch(0.2 0.08 30);
        --color-law-100: oklch(0.6 0.1 40);

        --color-history-200: oklch(0.15 0.07 65);
        --color-history-100: oklch(0.65 0.09 75);

        --color-poetic-200: oklch(0.18 0.08 70);
        --color-poetic-100: oklch(0.7 0.1 80);

        --color-prophecy-200: oklch(0.16 0.09 60);
        --color-prophecy-100: oklch(0.68 0.11 70);

        --color-gospel-200: oklch(0.17 0.08 55);
        --color-gospel-100: oklch(0.72 0.1 65);

        --color-acts-200: oklch(0.19 0.07 50);
        --color-acts-100: oklch(0.67 0.09 60);

        --color-epistle-200: oklch(0.21 0.08 45);
        --color-epistle-100: oklch(0.69 0.1 55);

        --color-revelation-200: oklch(0.22 0.09 40);
        --color-revelation-100: oklch(0.71 0.11 50);
    }

    .dark[data-mode='dark'][data-theme='high-contrast'] {
        --color-theme: var(--color-theme-50);

        --color-background: var(--color-theme-50);
        --color-foreground: var(--color-theme-900);

        --color-law-100: #ffffff;
        --color-law-200: #000000;

        --color-history-100: #ffffff;
        --color-history-200: #000000;

        --color-poetic-100: #ffffff;
        --color-poetic-200: #000000;

        --color-prophecy-100: #ffffff;
        --color-prophecy-200: #000000;

        --color-gospel-100: #ffffff;
        --color-gospel-200: #000000;

        --color-acts-100: #ffffff;
        --color-acts-200: #000000;

        --color-epistle-100: #ffffff;
        --color-epistle-200: #000000;

        --color-revelation-100: #ffffff;
        --color-revelation-200: #000000;
    }
}

/* Bible text styling */
@layer components {
    /* Old Testament quote styling */
    .ot-quote {
        color: var(--color-prophecy-text);
        position: relative;
    }

    /* Add a left border for OT quotes in non-flow mode */
    .verse:not(.flow-text) .ot-quote {
        border-left: 2px solid var(--color-prophecy-text);
        padding-left: 0.5rem;
        margin-left: 0.25rem;
    }

    /* Add paragraph indentation in flow text mode */
    .flow-mode:first-child {
        text-indent: 1.5rem;
    }

    /* Existing variant group styling can be placed here */
    .variant-group.addition {
        font-size: 0.75em;
    }

    .variant-group.omission {
        text-decoration: line-through;
    }

    .variant-group.alternative {
    }

    /* Flow text mode - pericope styling */
    .flow-mode .verse-unit {
        display: inline;
    }

    .flow-mode .pericope-start {
        text-indent: 1.5em;
    }

    .flow-mode .verse-content {
        display: inline;
    }

    .flow-mode .ot-quote {
        color: var(--color-prophecy-text);
        border-bottom: 1px solid var(--color-prophecy-text);
    }

    .dark[data-mode='dark'] .flow-mode .ot-quote {
        color: var(--color-prophecy-surface);
        border-bottom: 1px solid var(--color-prophecy-surface);
    }

    /* Verse mode - pericope styling */
    .verse-mode .pericope {
        margin-bottom: 0;
    }
    .verse-mode .pericope-title {
        display: none;
    }
    .verse-mode .verse-unit {
        display: block;
    }
}

@theme inline {
    --color-background: var(--background);
    --color-foreground: var(--foreground);
    --color-card: var(--card);
    --color-card-foreground: var(--card-foreground);
    --color-popover: var(--popover);
    --color-popover-foreground: var(--popover-foreground);
    --color-primary: var(--primary);
    --color-primary-foreground: var(--primary-foreground);
    --color-secondary: var(--secondary);
    --color-secondary-foreground: var(--secondary-foreground);
    --color-muted: var(--muted);
    --color-muted-foreground: var(--muted-foreground);
    --color-accent: var(--accent);
    --color-accent-foreground: var(--accent-foreground);
    --color-destructive: var(--destructive);
    --color-destructive-foreground: var(--destructive-foreground);
    --color-border: var(--border);
    --color-input: var(--input);
    --color-ring: var(--ring);
    --color-chart-1: var(--chart-1);
    --color-chart-2: var(--chart-2);
    --color-chart-3: var(--chart-3);
    --color-chart-4: var(--chart-4);
    --color-chart-5: var(--chart-5);
    --radius-sm: calc(var(--radius) - 4px);
    --radius-md: calc(var(--radius) - 2px);
    --radius-lg: var(--radius);
    --radius-xl: calc(var(--radius) + 4px);
    --color-sidebar: var(--sidebar);
    --color-sidebar-foreground: var(--sidebar-foreground);
    --color-sidebar-primary: var(--sidebar-primary);
    --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
    --color-sidebar-accent: var(--sidebar-accent);
    --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
    --color-sidebar-border: var(--sidebar-border);
    --color-sidebar-ring: var(--sidebar-ring);
}

:root {
    --background: oklch(1 0 0);
    --foreground: oklch(0.145 0 0);
    --card: oklch(1 0 0);
    --card-foreground: oklch(0.145 0 0);
    --popover: oklch(1 0 0);
    --popover-foreground: oklch(0.145 0 0);
    --primary: oklch(0.205 0 0);
    --primary-foreground: oklch(0.985 0 0);
    --secondary: oklch(0.97 0 0);
    --secondary-foreground: oklch(0.205 0 0);
    --muted: oklch(0.97 0 0);
    --muted-foreground: oklch(0.556 0 0);
    --accent: oklch(0.97 0 0);
    --accent-foreground: oklch(0.205 0 0);
    --destructive: oklch(0.577 0.245 27.325);
    --destructive-foreground: oklch(0.577 0.245 27.325);
    --border: oklch(0.922 0 0);
    --input: oklch(0.922 0 0);
    --ring: oklch(0.708 0 0);
    --chart-1: oklch(0.646 0.222 41.116);
    --chart-2: oklch(0.6 0.118 184.704);
    --chart-3: oklch(0.398 0.07 227.392);
    --chart-4: oklch(0.828 0.189 84.429);
    --chart-5: oklch(0.769 0.188 70.08);
    --radius: 0.625rem;
    --sidebar: oklch(0.985 0 0);
    --sidebar-foreground: oklch(0.145 0 0);
    --sidebar-primary: oklch(0.205 0 0);
    --sidebar-primary-foreground: oklch(0.985 0 0);
    --sidebar-accent: oklch(0.97 0 0);
    --sidebar-accent-foreground: oklch(0.205 0 0);
    --sidebar-border: oklch(0.922 0 0);
    --sidebar-ring: oklch(0.708 0 0);
}

.dark {
    --background: oklch(0.145 0 0);
    --foreground: oklch(0.985 0 0);
    --card: oklch(0.145 0 0);
    --card-foreground: oklch(0.985 0 0);
    --popover: oklch(0.145 0 0);
    --popover-foreground: oklch(0.985 0 0);
    --primary: oklch(0.985 0 0);
    --primary-foreground: oklch(0.205 0 0);
    --secondary: oklch(0.269 0 0);
    --secondary-foreground: oklch(0.985 0 0);
    --muted: oklch(0.269 0 0);
    --muted-foreground: oklch(0.708 0 0);
    --accent: oklch(0.269 0 0);
    --accent-foreground: oklch(0.985 0 0);
    --destructive: oklch(0.396 0.141 25.723);
    --destructive-foreground: oklch(0.637 0.237 25.331);
    --border: oklch(0.269 0 0);
    --input: oklch(0.269 0 0);
    --ring: oklch(0.439 0 0);
    --chart-1: oklch(0.488 0.243 264.376);
    --chart-2: oklch(0.696 0.17 162.48);
    --chart-3: oklch(0.769 0.188 70.08);
    --chart-4: oklch(0.627 0.265 303.9);
    --chart-5: oklch(0.645 0.246 16.439);
    --sidebar: oklch(0.205 0 0);
    --sidebar-foreground: oklch(0.985 0 0);
    --sidebar-primary: oklch(0.488 0.243 264.376);
    --sidebar-primary-foreground: oklch(0.985 0 0);
    --sidebar-accent: oklch(0.269 0 0);
    --sidebar-accent-foreground: oklch(0.985 0 0);
    --sidebar-border: oklch(0.269 0 0);
    --sidebar-ring: oklch(0.439 0 0);
}
