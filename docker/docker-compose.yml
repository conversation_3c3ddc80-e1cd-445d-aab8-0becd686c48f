# docker-compose.yml
# version: '3.8' - Removed as it's obsolete

services:
  app:
    build:
      context: ..
      dockerfile: docker/editor/Dockerfile
    container_name: esb-editor
    restart: unless-stopped
    working_dir: /var/www
    volumes:
      - ../apps/editor:/var/www
    ports:
      - "8000:8000"
    environment:
      - OCTANE_SERVER=frankenphp
      - OCTANE_WORKERS=${OCTANE_WORKERS:-4}
    depends_on:
      - postgres
      - redis

  postgres:
    image: postgres:15
    container_name: esb-editor-db
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${DB_DATABASE}
      POSTGRES_USER: ${DB_USERNAME}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  redis:
    image: redis:alpine
    container_name: esb-editor-redis
    restart: unless-stopped
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"

volumes:
  postgres_data:
  redis_data:
