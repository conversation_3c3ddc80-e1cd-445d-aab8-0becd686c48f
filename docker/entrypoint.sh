#!/bin/bash

# Wait for MySQL to be ready
until nc -z -v -w30 mysql 3306
do
  echo "Waiting for MySQL to be ready..."
  sleep 2
done

cd /var/www/html/apps/web

# Generate application key if not set
php artisan key:generate --no-interaction --force

# Run migrations
php artisan migrate --force

# Cache configuration
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Start Apache
exec "$@"
