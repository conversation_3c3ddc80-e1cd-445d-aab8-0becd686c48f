{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env"], "tasks": {"build": {"dependsOn": ["^build"], "outputs": ["dist/**", "public/build/**", "bootstrap/cache/**", "docs/build/**"]}, "test": {"dependsOn": ["^build"], "inputs": ["src/**/*.php", "tests/**/*.php", "composer.json", "phpunit.xml", "pest.php"]}, "test:web": {"cache": false, "inputs": ["resources/js/**/*.{js,ts,vue}", "resources/js/**/__tests__/**/*", "vitest.config.ts", "package.json"]}, "lint": {"outputs": []}, "dev": {"cache": false, "persistent": true}, "clean": {"cache": false}, "scout:import": {"cache": false}, "scout:flush": {"cache": false}, "scout:sync": {"cache": false, "dependsOn": ["build"]}, "docs:build": {"outputs": ["docs/build/**", "docs/.docusaurus/**"], "dependsOn": ["^build"]}}}